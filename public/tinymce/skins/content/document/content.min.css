@media screen {
	html {
		background: #f4f4f4;
		min-height: 100%
	}
}

body {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
}

@media screen {
	body {
		background-color: #fff;
		box-shadow: 0 0 4px rgba(0, 0, 0, .15);
		box-sizing: border-box;
		margin: 1rem auto 0;
		max-width: 820px;
		min-height: calc(100vh - 1rem);
		padding: 4rem 6rem 6rem 6rem
	}
}

table {
	border-collapse: collapse
}

table:not([cellpadding]) td,
table:not([cellpadding]) th {
	padding: .4rem
}

table[border]:not([border="0"]):not([style*=border-width]) td,
table[border]:not([border="0"]):not([style*=border-width]) th {
	border-width: 1px
}

table[border]:not([border="0"]):not([style*=border-style]) td,
table[border]:not([border="0"]):not([style*=border-style]) th {
	border-style: solid
}

table[border]:not([border="0"]):not([style*=border-color]) td,
table[border]:not([border="0"]):not([style*=border-color]) th {
	border-color: #ccc
}

figure figcaption {
	color: #999;
	margin-top: .25rem;
	text-align: center
}

hr {
	border-color: #ccc;
	border-style: solid;
	border-width: 1px 0 0 0
}

.mce-content-body:not([dir=rtl]) blockquote {
	border-left: 2px solid #ccc;
	margin-left: 1.5rem;
	padding-left: 1rem
}

.mce-content-body[dir=rtl] blockquote {
	border-right: 2px solid #ccc;
	margin-right: 1.5rem;
	padding-right: 1rem
}