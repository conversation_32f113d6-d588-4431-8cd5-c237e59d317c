#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2000;
    width: 100%;
    height: 2px;
    background: rgb(var(--ui-primary));
  }

  .peg {
    position: absolute;
    right: 0;
    display: block;
    width: 100px;
    height: 100%;
    box-shadow: 0 0 10px rgb(var(--ui-primary)), 0 0 5px rgb(var(--ui-primary));
    opacity: 1;
    transform: rotate(3deg) translate(0, -4px);
  }

  .spinner {
    position: fixed;
    top: 11px;
    right: 14px;
    z-index: 2000;
    display: block;

    .spinner-icon {
      box-sizing: border-box;
      width: 18px;
      height: 18px;
      border: solid 2px transparent;
      border-top-color: rgb(var(--ui-primary));
      border-left-color: rgb(var(--ui-primary));
      border-radius: 50%;
      animation: nprogress-spinner 400ms linear infinite;
    }
  }
}

.nprogress-custom-parent {
  position: relative;
  overflow: hidden;

  #nprogress .spinner,
  #nprogress .bar {
    position: absolute;
  }
}

@keyframes nprogress-spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes nprogress-spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
