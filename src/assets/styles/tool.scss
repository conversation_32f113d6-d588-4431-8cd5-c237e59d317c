.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .page-header {
    margin-bottom: 0;
  }

  // 让 page-main 的高度自适应
  .page-main {
    flex: 1;
    overflow: auto;

    :deep(.main-container) {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: auto;
    }
  }
}

/* 去除弹出盒子边框 */
:deep(.el-collapse) {
  border: none !important;
}

.custom-style .el-segmented {
  :deep(.is-selected) {
    border-bottom: 2px solid #18a058 !important;
  }

  --el-segmented-item-selected-color: #18a058;

  /* 激活颜色 */
  --el-segmented-item-selected-bg-color: transparent;

  /* 激活背景色 */
  --el-border-radius-base: 0px;

  /* 激活圆角 */
  font-size: 18px;
  font-weight: 600;
  color: #8f9294;
}

.left-box-new {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 15rem;
  height: 100%;
  padding: 1.25rem;
  overflow-y: auto;
  border-right: 1px solid #e9e8e8;
}

.left-def-css {
  color: #999;
  background-color: transparent;
}

.left-act-css {
  color: #409eff;
  background-color: #edf6ff;
}

.text-e1 {
  /* 将对象作为弹性伸缩盒子模型显示 */
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;

  /* 限制在一个块元素显示的文本的行数 */

  /* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端； */
  -webkit-line-clamp: 1;

  /* 设置或检索伸缩盒对象的子元素的排列方式 */
  -webkit-box-orient: vertical;
}
