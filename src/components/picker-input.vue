<template>
  <div class="picker-input">
    <el-select
      v-model="selectedValue"
      :placeholder="placeholder"
      style="width: 100%"
      @change="handleChange"
    >
      <el-option
        v-for="item in options"
        :key="item[valueKey]"
        :label="item[displayKey]"
        :value="item[valueKey]"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  modelValue?: any
  columns?: any[][]
  displayKey?: string
  valueKey?: string
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  columns: () => [[]],
  displayKey: 'label',
  valueKey: 'value',
  placeholder: '请选择',
})

const emit = defineEmits<Emits>()

// 响应式数据
const selectedValue = ref(props.modelValue)

// 计算选项列表
const options = computed(() => {
  return props.columns[0] || []
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue
})

// 处理选择变化
function handleChange(value: any) {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped>
.picker-input {
  width: 100%;
}
</style>
