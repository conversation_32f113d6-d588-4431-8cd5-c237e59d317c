<template>
  <div class="employee-picker">
    <el-input
      v-model="displayValue"
      :placeholder="placeholder"
      readonly
      @click="openDialog"
    >
      <template #suffix>
        <el-icon class="cursor-pointer">
          <ArrowDown />
        </el-icon>
      </template>
    </el-input>

    <!-- 员工选择弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择员工"
      width="700px"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <div>
        <!-- 部门选择 -->
        <el-form-item label="选择部门" style="margin-bottom: 20px;">
          <DepartmentTreeSelect
            v-model="selectedDepartmentId"
            placeholder="请选择部门"
            style="width: 100%"
            @change="onDepartmentChange"
          />
        </el-form-item>

        <!-- 员工列表 -->
        <div v-if="selectedDepartmentId" v-loading="loadingEmployees">
          <el-table
            :data="departmentEmployees"
            style="width: 100%"
            max-height="400px"
            @row-click="selectEmployee"
          >
            <el-table-column prop="realName" label="姓名" width="150" />
            <el-table-column prop="position" label="职位" width="150" />
            <el-table-column prop="phone" label="联系电话" width="150" />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="selectEmployee(row)">
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-if="!loadingEmployees && departmentEmployees.length === 0" style="text-align: center; padding: 20px; color: #999;">
            该部门暂无员工
          </div>
        </div>
        <div v-else style="text-align: center; padding: 20px; color: #999;">
          请先选择部门
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

interface Props {
  modelValue?: string
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请选择员工',
})

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const selectedDepartmentId = ref('')
const departmentEmployees = ref([])
const loadingEmployees = ref(false)
const selectedEmployee = ref<any>(null)

// 计算显示值
const displayValue = computed(() => {
  return selectedEmployee.value?.realName || ''
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (!newValue) {
    selectedEmployee.value = null
  }
})

// 获取部门员工列表
async function getEmployeesByDepartment(departmentId: string) {
  try {
    loadingEmployees.value = true
    // 这里应该调用实际的 API
    // const response = await roleApi.getEmpByUnitId(departmentId)
    // departmentEmployees.value = response.content || []
    departmentEmployees.value = []
  } catch (error) {
    console.error('获取员工列表失败:', error)
    ElMessage.error('获取员工列表失败')
    departmentEmployees.value = []
  } finally {
    loadingEmployees.value = false
  }
}

// 部门选择变化
async function onDepartmentChange(departmentId: string) {
  selectedDepartmentId.value = departmentId
  if (departmentId) {
    await getEmployeesByDepartment(departmentId)
  } else {
    departmentEmployees.value = []
  }
}

// 打开选择弹窗
function openDialog() {
  selectedDepartmentId.value = ''
  departmentEmployees.value = []
  dialogVisible.value = true
}

// 选择员工
function selectEmployee(employee: any) {
  selectedEmployee.value = employee
  emit('update:modelValue', employee.id)
  emit('change', employee.id)
  dialogVisible.value = false
}

// 关闭弹窗
function closeDialog() {
  dialogVisible.value = false
}
</script>

<style scoped>
.employee-picker {
  width: 100%;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
