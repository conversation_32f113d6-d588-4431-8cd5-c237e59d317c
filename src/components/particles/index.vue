<script>
/* eslint-disable */
import particlesJs from "./particles.js";
import particlesConfig from "./particles.json";
export default {
  data() {
    return {};
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      particlesJS("particles-js", particlesConfig);
      // document.body.style.overflow = "hidden";
    },
  },
};
</script>

<template>
  <div class="particles-js-box">
    <div id="particles-js"></div>
  </div>
</template>

<style scoped>
.particles-js-box {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
}

#particles-js {
  width: 100%;
  height: 100%;

  /* background-color: #2d3a4b; */
  background-image: url("@/assets/images/loginbgimgs.jpg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
