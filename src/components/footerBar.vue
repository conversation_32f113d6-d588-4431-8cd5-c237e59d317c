<template>
  <div class="footer-bar" :style="{ backgroundColor: backgroundColor }">
    <div class="footer-content">
      <div class="button-group">
        <el-button
          v-for="(button, index) in buttons"
          :key="index"
          :type="getButtonType(button.type)"
          :style="{
            backgroundColor: button.bgColor,
            color: button.textColor,
            borderColor: button.bgColor,
          }"
          @click="handleButtonClick(button)"
        >
          {{ button.text }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ButtonConfig {
  text: string
  type: string
  slotName?: string
  bgColor?: string
  textColor?: string
  action?: () => void
}

interface Props {
  buttons?: ButtonConfig[]
  backgroundColor?: string
}

interface Emits {
  (e: 'button-click', button: ButtonConfig): void
}

const props = withDefaults(defineProps<Props>(), {
  buttons: () => [],
  backgroundColor: '#ffffff',
})

const emit = defineEmits<Emits>()

// 获取按钮类型
function getButtonType(type: string) {
  switch (type) {
    case 'primary':
    case 'submit':
      return 'primary'
    case 'cancel':
    case 'secondary':
      return 'default'
    case 'danger':
      return 'danger'
    case 'warning':
      return 'warning'
    case 'success':
      return 'success'
    default:
      return 'default'
  }
}

// 处理按钮点击
function handleButtonClick(button: ButtonConfig) {
  if (button.action && typeof button.action === 'function') {
    button.action()
  }
  emit('button-click', button)
}
</script>

<style scoped>
.footer-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px 20px;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.button-group .el-button {
  min-width: 100px;
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 6px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .footer-content {
    padding: 12px 16px;
  }
  
  .button-group {
    flex-direction: column;
    gap: 12px;
  }
  
  .button-group .el-button {
    width: 100%;
    min-width: auto;
  }
}
</style>
