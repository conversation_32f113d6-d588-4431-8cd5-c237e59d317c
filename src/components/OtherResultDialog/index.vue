<script lang="ts" setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import contractApi from '@/api/review/contract'

// Props
interface Props {
  visible: boolean
  contractId?: string | number
  contractInfo?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  contractInfo: null,
})

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

// 响应式数据
const dialogVisible = ref(false)
const activeTab = ref('result')
const loading = ref(false)
const aiReviewData = ref<any>(null)
const reviewRecords = ref<any[]>([])
const tableLoading = ref(false)

// 审查操作相关数据
const reviewDecision = ref('') // 'pass' 或 'reject'
const reviewComment = ref('')
const submitting = ref(false)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initDialog()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 初始化弹窗
async function initDialog() {
  if (props.contractId) {
    await Promise.all([
      getAiReviewData(),
      getReviewRecords(),
    ])
  }
}

// 获取AI审查数据
async function getAiReviewData() {
  try {
    loading.value = true
    const response = await contractApi.aiContract(props.contractId)
    aiReviewData.value = response
  }
  catch (error) {
    console.error('获取AI审查数据失败:', error)
    ElMessage.error('获取AI审查数据失败')
  }
  finally {
    loading.value = false
  }
}

// 获取审查记录
async function getReviewRecords() {
  try {
    tableLoading.value = true
    const response = await contractApi.queryAllReviews(props.contractId!)
    reviewRecords.value = response.data || response || []
  }
  catch (error) {
    console.error('获取审查记录失败:', error)
    ElMessage.error('获取审查记录失败')
  }
  finally {
    tableLoading.value = false
  }
}

// 关闭弹窗
function handleClose() {
  dialogVisible.value = false
  // 重置数据
  activeTab.value = 'result'
  aiReviewData.value = null
  reviewRecords.value = []
  reviewDecision.value = ''
  reviewComment.value = ''
}

// 提交审查结果
async function handleSubmitReview() {
  // 验证输入
  if (!reviewDecision.value) {
    ElMessage.warning('请选择审查结果')
    return
  }

  if (reviewDecision.value === 'reject' && !reviewComment.value.trim()) {
    ElMessage.warning('选择不通过时必须输入意见')
    return
  }

  // 验证AI审查内容不能为空
  if (!aiReviewData.value || !aiReviewData.value.trim()) {
    ElMessage.warning('AI审查内容不能为空，请先获取审查结果')
    return
  }

  try {
    submitting.value = true

    // 准备接口参数
    const params = {
      contractId: props.contractId, // 合同主键ID
      content: aiReviewData.value, // AI生成的内容
      opinion: reviewComment.value.trim(), // 审查意见
      conclusion: reviewDecision.value === 'pass' ? 1 : 2, // 1代表通过，2代表不通过
    }

    // 调用提交审查结果的API
    await contractApi.contractMessageReview(params)

    ElMessage.success('审查结果提交成功')
    handleClose()

    // 返回到合同审查列表页面
    window.location.href = '/monitor/examination/contractReview'
  }
  catch (error) {
    console.error('提交审查结果失败:', error)
    ElMessage.error('提交审查结果失败')
  }
  finally {
    submitting.value = false
  }
}

// 获取审查状态标签类型
function getStatusType(status: string) {
  const statusMap: Record<string, string> = {
    PENDING: 'warning',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    REJECTED: 'danger',
  }
  return statusMap[status] || 'info'
}

// 获取审查状态文本
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    PENDING: '待审查',
    IN_PROGRESS: '审查中',
    COMPLETED: '已完成',
    REJECTED: '已拒绝',
  }
  return statusMap[status] || status
}
</script>

<template>
  <el-dialog v-model="dialogVisible" title="审查结果" width="900px" :close-on-click-modal="false" @close="handleClose">
    <el-tabs v-model="activeTab" class="review-tabs">
      <!-- 审查结果 Tab -->
      <el-tab-pane label="审查结果" name="result">
        <div v-loading="loading" class="result-content">
          <div v-if="aiReviewData" class="review-info">
            <!-- 基本信息 -->
            <div class="info-section">
              <h3 class="section-title">
                基本信息
              </h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">合同名称：</span>
                    <span class="value">{{ contractInfo?.name || '-' }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
            <!-- 审查内容 -->
            <div class="info-section">
              <h3 class="section-title">
                审查内容
              </h3>
              <div class="content-box">
                <div v-if="aiReviewData" class="result-text">
                  <el-scrollbar height="400px">
                    <pre class="whitespace-pre-wrap p-4">{{ aiReviewData }}</pre>
                  </el-scrollbar>
                </div>
                <div v-else class="no-content">
                  暂无审查内容
                </div>
              </div>
            </div>
          </div>
          <div v-else class="no-data">
            <el-empty description="暂无审查结果" />
          </div>
        </div>
      </el-tab-pane>

      <!-- 审查记录 Tab -->
      <el-tab-pane label="审查记录" name="records">
        <div v-loading="tableLoading" class="records-content">
          <el-table :data="reviewRecords" style="width: 100%" max-height="500px" empty-text="暂无审查记录">
            <el-table-column prop="opinion" label="审查意见" width="120" />
            <el-table-column prop="reviewer" label="审查人" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" width="160" />
            <el-table-column prop="completedAt" label="完成时间" width="160" />
            <el-table-column prop="description" label="审查记录" min-width="200" show-overflow-tooltip />
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <!-- 审查操作区域 -->
        <div class="review-actions">
          <div class="decision-section">
            <span class="decision-label">审查结果：</span>
            <el-radio-group v-model="reviewDecision" class="decision-options">
              <el-radio value="pass" size="large">
                <span class="pass-text">通过</span>
              </el-radio>
              <el-radio value="reject" size="large">
                <span class="reject-text">不通过</span>
              </el-radio>
            </el-radio-group>
          </div>

          <div class="comment-section">
            <el-input
              v-model="reviewComment"
              type="textarea"
              :rows="3"
              :placeholder="reviewDecision === 'reject' ? '请输入不通过的原因（必填）' : '请输入审查意见（可选）'"
              :class="{ 'required-field': reviewDecision === 'reject' }"
              maxlength="500"
              show-word-limit
            />
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="button-section">
          <el-button @click="handleClose">
            关闭
          </el-button>

          <el-button
            type="primary"
            :loading="submitting"
            @click="handleSubmitReview"
          >
            确认
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.review-tabs {
  :deep(.el-tabs__content) {
    padding: 0;
  }
}

.result-content {
  max-height: 600px;
  overflow-y: auto;
}

.review-info {
  .info-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e4e7ed;
    }

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .label {
        font-weight: 500;
        color: #606266;
        min-width: 80px;
        margin-right: 8px;
      }

      .value {
        color: #303133;
        flex: 1;
      }
    }
  }
}

.content-box {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  line-height: 1.6;
  color: #495057;

  .result-text {
    white-space: pre-wrap;
  }

  .no-content {
    color: #909399;
    text-align: center;
    font-style: italic;
  }
}

.risk-list {
  .risk-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.records-content {
  min-height: 300px;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.dialog-footer {
  .review-actions {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .decision-section {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .decision-label {
        font-weight: 500;
        color: #303133;
        margin-right: 16px;
        min-width: 80px;
      }

      .decision-options {
        .pass-text {
          color: #67c23a;
          font-weight: 500;
        }

        .reject-text {
          color: #f56c6c;
          font-weight: 500;
        }
      }
    }

    .comment-section {
      :deep(.el-textarea) {
        .el-textarea__inner {
          border-radius: 4px;
        }

        &.required-field .el-textarea__inner {
          border-color: #f56c6c;

          &:focus {
            border-color: #f56c6c;
            box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
          }
        }
      }
    }
  }

  .button-section {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
