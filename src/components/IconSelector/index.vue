<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import SvgIcon from '@/components/SvgIcon/index.vue'
import { allIconOptions } from '@/utils/elementPlusIcons'

interface Props {
  modelValue?: string
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请选择图标',
})

const emit = defineEmits<Emits>()

// 响应式数据
const selectedIcon = ref(props.modelValue)
const searchText = ref('')
const showGrid = ref(false)
const currentPage = ref(1)
const pageSize = 64

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedIcon.value = newValue
})

// 过滤图标
const filteredIcons = computed(() => {
  if (!searchText.value) {
    return allIconOptions
  }
  return allIconOptions.filter(icon =>
    icon.toLowerCase().includes(searchText.value.toLowerCase()),
  )
})

// 分页图标
const paginatedIcons = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredIcons.value.slice(start, end)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredIcons.value.length / pageSize)
})

// 下拉框过滤方法
function filterIcons(query: string) {
  // 这里不需要额外处理，因为 el-select 的 filterable 会自动处理
  return true
}

// 处理图标选择
function handleIconChange(value: string) {
  selectedIcon.value = value
  emit('update:modelValue', value)
  emit('change', value)
}

// 从网格选择图标
function selectIcon(icon: string) {
  selectedIcon.value = icon
  emit('update:modelValue', icon)
  emit('change', icon)
}

// 监听搜索文本变化，重置页码
watch(searchText, () => {
  currentPage.value = 1
})
</script>

<template>
  <div class="icon-selector">
    <div class="selector-wrapper">
      <el-select
        v-model="selectedIcon"
        placeholder="请选择图标"
        filterable
        clearable
        :filter-method="filterIcons"
        @change="handleIconChange"
        class="w-full"
      >
        <template #prefix>
          <SvgIcon v-if="selectedIcon" :name="selectedIcon" :size="16" />
        </template>

        <el-option
          v-for="icon in filteredIcons"
          :key="icon"
          :label="icon"
          :value="icon"
        >
          <div class="flex items-center gap-2">
            <SvgIcon :name="icon" :size="16" />
            <span>{{ icon }}</span>
          </div>
        </el-option>
      </el-select>

      <!-- 切换网格显示按钮 -->
      <el-button
        type="text"
        size="small"
        class="toggle-btn"
        @click="showGrid = !showGrid"
      >
        {{ showGrid ? '隐藏' : '显示' }}图标网格
      </el-button>
    </div>

    <!-- 图标预览网格 - 使用绝对定位避免影响布局 -->
    <div v-if="showGrid" class="icon-grid-overlay">
      <div class="icon-grid">
        <div class="grid-header mb-3 flex items-center justify-between">
          <span class="text-sm text-gray-600">图标预览 ({{ filteredIcons.length }} 个)</span>
          <el-input
            v-model="searchText"
            placeholder="搜索图标..."
            size="small"
            style="width: 200px"
            clearable
          >
            <template #prefix>
              <SvgIcon name="ep:search" :size="14" />
            </template>
          </el-input>
        </div>

        <div class="grid grid-cols-8 max-h-60 gap-2 overflow-y-auto border rounded p-3">
          <div
            v-for="icon in paginatedIcons"
            :key="icon"
            class="icon-item flex flex-col cursor-pointer items-center rounded p-2 transition-colors hover:bg-blue-50"
            :class="{ 'bg-blue-100 border-blue-300': selectedIcon === icon }"
            @click="selectIcon(icon)"
          >
            <SvgIcon :name="icon" :size="20" class="mb-1" />
            <span class="w-full truncate text-center text-xs text-gray-500" :title="icon">
              {{ icon.split(':')[1] || icon }}
            </span>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="mt-3 flex justify-center">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="filteredIcons.length"
            layout="prev, pager, next"
            small
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.icon-selector {
  width: 100%;
  position: relative;
}

.selector-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toggle-btn {
  align-self: flex-start;
  margin-top: 4px;
}

.icon-grid-overlay {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 8px;
}

.icon-grid {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 600px;
}

.grid-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.icon-item {
  border: 1px solid transparent;
  min-height: 60px;
}

.icon-item:hover {
  border-color: #e5e7eb;
}
</style>
