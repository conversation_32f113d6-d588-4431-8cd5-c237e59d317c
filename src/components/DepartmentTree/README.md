# DepartmentTree 部门树形组件

基于 Element Plus 的 `el-tree` 组件封装的部门树形展示组件，用于展示组织架构的层级结构。

## 功能特性

- 📊 **层级展示**：支持公司、分公司、部门的多级树形结构
- 🎨 **图标区分**：不同类型节点使用不同图标和颜色
- ☑️ **多选支持**：可选择性开启复选框功能
- 🔍 **搜索过滤**：支持按名称或编码搜索过滤
- 🎯 **高亮选中**：支持当前节点高亮显示
- 🔄 **数据刷新**：支持手动刷新组织树数据
- 📱 **响应式**：适配不同屏幕尺寸

## 数据来源

组件从 `localStorage` 中读取 `orgTree` 数据，该数据在用户登录成功后自动存储。

## 基本用法

```vue
<template>
  <DepartmentTree
    :default-expand-all="true"
    :show-type="true"
    @node-click="handleNodeClick"
  />
</template>

<script setup>
import DepartmentTree from '@/components/DepartmentTree/index.vue'

function handleNodeClick(data, node, component) {
  console.log('点击节点:', data)
}
</script>
```

## 带复选框

```vue
<template>
  <DepartmentTree
    ref="treeRef"
    :show-checkbox="true"
    :default-expand-all="true"
    @check-change="handleCheckChange"
  />
  <el-button @click="getCheckedNodes">获取选中节点</el-button>
</template>

<script setup>
import { ref } from 'vue'
import DepartmentTree from '@/components/DepartmentTree/index.vue'

const treeRef = ref()

function handleCheckChange(data, checked, indeterminate) {
  console.log('复选框状态改变:', { data, checked, indeterminate })
}

function getCheckedNodes() {
  const checkedNodes = treeRef.value.getCheckedNodes()
  console.log('选中的节点:', checkedNodes)
}
</script>
```

## 搜索过滤

```vue
<template>
  <el-input
    v-model="filterText"
    placeholder="搜索部门"
    clearable
  />
  <DepartmentTree
    :filter-text="filterText"
    :highlight-current="true"
  />
</template>

<script setup>
import { ref } from 'vue'
import DepartmentTree from '@/components/DepartmentTree/index.vue'

const filterText = ref('')
</script>
```

## Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| showCheckbox | 是否显示复选框 | boolean | — | false |
| defaultExpandAll | 是否默认展开所有节点 | boolean | — | false |
| expandOnClickNode | 是否在点击节点时展开/收缩节点 | boolean | — | false |
| checkOnClickNode | 是否在点击节点时选中节点 | boolean | — | false |
| highlightCurrent | 是否高亮当前选中节点 | boolean | — | true |
| nodeKey | 节点的唯一标识 | string | — | 'id' |
| defaultExpandedKeys | 默认展开的节点key数组 | array | — | [] |
| defaultCheckedKeys | 默认勾选的节点key数组 | array | — | [] |
| showCode | 是否显示节点编码 | boolean | — | false |
| showType | 是否显示节点类型标签 | boolean | — | true |
| filterText | 过滤关键字 | string | — | '' |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| node-click | 节点被点击时触发 | (data, node, component) |
| check-change | 复选框状态改变时触发 | (data, checked, indeterminate) |
| current-change | 当前选中节点改变时触发 | (data, node) |

## Methods

通过 ref 可以获取到组件实例并调用以下方法：

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| getCheckedNodes | 获取选中的节点 | (leafOnly, includeHalfChecked) | 选中的节点数组 |
| getCheckedKeys | 获取选中的节点key | (leafOnly) | 选中的节点key数组 |
| setCheckedNodes | 设置选中的节点 | (nodes) | — |
| setCheckedKeys | 设置选中的节点key | (keys) | — |
| getCurrentNode | 获取当前选中的节点 | — | 当前选中的节点 |
| setCurrentNode | 设置当前选中的节点 | (node) | — |
| setCurrentKey | 设置当前选中的节点key | (key) | — |
| filter | 过滤节点 | (value) | — |
| refresh | 刷新数据 | — | — |

## 数据结构

组件期望的数据结构如下：

```typescript
interface TreeNode {
  id: number                    // 节点ID
  name: string                  // 节点名称
  code: string                  // 节点编码
  type: 'COMPANY' | 'SUBSIDIARY' | 'DEPARTMENT'  // 节点类型
  level: number                 // 层级
  status: number                // 状态
  sortOrder: number             // 排序
  description: string           // 描述
  parentId: number | null       // 父节点ID
  children: TreeNode[]          // 子节点
}
```

## 样式定制

组件使用 CSS 变量，可以通过覆盖以下变量来定制样式：

```css
:root {
  --el-color-primary: #409eff;
  --el-color-danger: #f56c6c;
  --el-color-warning: #e6a23c;
  --el-color-success: #67c23a;
  --el-tree-node-hover-bg-color: #f5f7fa;
}
```

## 注意事项

1. 组件依赖 `localStorage` 中的 `orgTree` 数据，请确保在使用前已正确存储数据
2. 数据格式必须符合预期的 TreeNode 接口结构
3. 如果数据为空或格式错误，组件会显示空状态
4. 建议在登录成功后调用相关API获取并存储组织树数据

## 示例页面

可以查看 `/src/views/example/department-tree.vue` 文件了解更多使用示例。