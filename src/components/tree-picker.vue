<template>
  <div class="tree-picker">
    <el-input
      v-model="displayValue"
      :placeholder="placeholder"
      readonly
      @click="openDialog"
    >
      <template #suffix>
        <el-icon class="cursor-pointer">
          <ArrowDown />
        </el-icon>
      </template>
    </el-input>

    <!-- 树形选择弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="600px"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <div>
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :show-checkbox="multiple"
          :check-strictly="checkStrictly"
          :default-expand-all="defaultExpandAll"
          node-key="id"
          @node-click="handleNodeClick"
          @check="handleCheck"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="confirmSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'

interface Props {
  modelValue?: any
  placeholder?: string
  title?: string
  multiple?: boolean
  checkStrictly?: boolean
  defaultExpandAll?: boolean
  treeData?: any[]
  treeProps?: any
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any, nodes?: any): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请选择',
  title: '选择',
  multiple: false,
  checkStrictly: true,
  defaultExpandAll: false,
  treeData: () => [],
  treeProps: () => ({
    children: 'children',
    label: 'name',
    value: 'id',
  }),
})

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const treeRef = ref()
const selectedNodes = ref<any[]>([])

// 计算显示值
const displayValue = computed(() => {
  if (props.multiple) {
    return selectedNodes.value.map(node => node.name).join(', ')
  } else {
    return selectedNodes.value[0]?.name || ''
  }
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (!newValue) {
    selectedNodes.value = []
  }
})

// 打开选择弹窗
function openDialog() {
  dialogVisible.value = true
}

// 关闭弹窗
function closeDialog() {
  dialogVisible.value = false
}

// 处理节点点击
function handleNodeClick(data: any, node: any) {
  if (!props.multiple) {
    selectedNodes.value = [data]
  }
}

// 处理复选框选择
function handleCheck(data: any, checkedInfo: any) {
  if (props.multiple) {
    selectedNodes.value = checkedInfo.checkedNodes
  }
}

// 确认选择
function confirmSelection() {
  if (props.multiple) {
    const values = selectedNodes.value.map(node => node.id)
    emit('update:modelValue', values)
    emit('change', values, { nodes: selectedNodes.value })
  } else {
    const value = selectedNodes.value[0]?.id || ''
    emit('update:modelValue', value)
    emit('change', value, { nodes: selectedNodes.value })
  }
  dialogVisible.value = false
}
</script>

<style scoped>
.tree-picker {
  width: 100%;
}

.cursor-pointer {
  cursor: pointer;
}

.dialog-footer {
  text-align: right;
}
</style>
