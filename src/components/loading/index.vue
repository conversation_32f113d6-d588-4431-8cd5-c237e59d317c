<script setup lang="ts">
// 基于 SpinKit https://tobiasahlin.com/spinkit/
import 'spinkit/spinkit.min.css'

defineOptions({
  name: 'SpinkitLoading',
})
const show: any = ref(false)
// type?: 'plane' | 'chase' | 'bounce' | 'wave' | 'pulse' | 'flow' | 'swing' | 'circle' | 'circle-fade' | 'grid' | 'fold' | 'wander'
const type: any = ref('plane')
const size: any = ref(50)
const color: any = ref('#fff')
const fixed: any = ref(true)
const background: any = ref('#000000')

function open(time: any, system: any) {
  show.value = true
  if (system) {
    type.value = system.type ? system.type : 'plane'
    size.value = system.size ? system.size : 50
    color.value = system.color ? system.color : '#fff'
    background.value = system.background ? system.background : '#000'
    fixed.value = system.fixed
  }
  else {
    type.value = 'custom'
  }
  if (time) {
    if (time !== true) {
      setTimeout(() => {
        show.value = false
      }, time)
    }
  }
  else {
    setTimeout(() => {
      show.value = false
    }, 0)
  }
}
// 抛出方法
defineExpose({
  open,
})
</script>

<template>
  <div v-if="show && type === 'custom'" class="loaderbox">
    <div class="loader" />
    <div class="text">
      Loading...
    </div>
  </div>
  <transition v-if="show && type !== 'custom' " name="spinkit-transition">
    <div class="spinkit-container" :style="{ position: fixed ? 'fixed' : 'absolute', background }">
      <div class="spinkit" :style="{ '--sk-size': `${size}px`, '--sk-color': color }">
        <div v-if="type === 'plane'" class="sk-plane" />
        <div v-if="type === 'chase'" class="sk-chase">
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
          <div class="sk-chase-dot" />
        </div>
        <div v-if="type === 'bounce'" class="sk-bounce">
          <div class="sk-bounce-dot" />
          <div class="sk-bounce-dot" />
        </div>
        <div v-if="type === 'wave'" class="sk-wave">
          <div class="sk-wave-rect" />
          <div class="sk-wave-rect" />
          <div class="sk-wave-rect" />
          <div class="sk-wave-rect" />
          <div class="sk-wave-rect" />
        </div>
        <div v-if="type === 'pulse'" class="sk-pulse" />
        <div v-if="type === 'flow'" class="sk-flow">
          <div class="sk-flow-dot" />
          <div class="sk-flow-dot" />
          <div class="sk-flow-dot" />
        </div>
        <div v-if="type === 'swing'" class="sk-swing">
          <div class="sk-swing-dot" />
          <div class="sk-swing-dot" />
        </div>
        <div v-if="type === 'circle'" class="sk-circle">
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
          <div class="sk-circle-dot" />
        </div>
        <div v-if="type === 'circle-fade'" class="sk-circle-fade">
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
          <div class="sk-circle-fade-dot" />
        </div>
        <div v-if="type === 'grid'" class="sk-grid">
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
          <div class="sk-grid-cube" />
        </div>
        <div v-if="type === 'fold'" class="sk-fold">
          <div class="sk-fold-cube" />
          <div class="sk-fold-cube" />
          <div class="sk-fold-cube" />
          <div class="sk-fold-cube" />
        </div>
        <div v-if="type === 'wander'" class="sk-wander">
          <div class="sk-wander-cube" />
          <div class="sk-wander-cube" />
          <div class="sk-wander-cube" />
        </div>
      </div>
    </div>
  </transition>
</template>

<style scoped lang="scss">
  /* stylelint-disable */
  .loaderbox {
    background-color: rgba($color: #f5f5f5, $alpha: 1);
    z-index: 9999;
    position: absolute;
    right: 0 !important;
    left: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .loader {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    position: relative;
    border: 4px solid transparent;
    border-top-color: #4bc0c8;
    animation: run 2s linear infinite;
  }

  /* 利用伪元素 */
  .loader::before {
    content: "";
    position: absolute;
    inset: 5px;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: #c779d0;

    /* 动画时间不同 */
    animation: run 3s linear infinite;
  }

  .loader::after {
    content: "";
    position: absolute;
    inset: 15px;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: #feac5e;

    /* 动画时间不同 */
    animation: run 1.5s linear infinite;
  }

  .text {
    color: var(--el-text-color-primary);
    font-size: 12px;
    position: absolute;
  }

  /* 定义动画 */
  @keyframes run {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  /* 系统样式 */

  .spinkit-container {
    /* position: fixed; */
    width: 100%;
    inset: 0;
    z-index: 10001;
    // background-color: rgb(0 0 0 / 70%);
  }

  .spinkit {
    @include position-center(xy);
  }

  .spinkit-transition-leave-active,
  .spinkit-transition-enter-active {
    transition: all 0.3s;
  }

  .spinkit-transition-enter,
  .spinkit-transition-leave-to {
    opacity: 0;
  }
</style>
