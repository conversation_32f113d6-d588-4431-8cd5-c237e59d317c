<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Search } from '@element-plus/icons-vue'
import systemApi from '@/api/complianceApi/one/systemManagement.ts'

// Props
interface Props {
  modelValue: boolean
  selectedValues?: string[] // 已选中的ID数组
  multiple?: boolean // 是否多选，默认为true
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  selectedValues: () => [],
  multiple: true,
})

const emit = defineEmits<Emits>()

// Emits
interface Emits {
  'update:modelValue': [value: boolean]
  'confirm': [selectedIds: string[], selectedItems: any[]]
}

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const regulationList = ref<any[]>([])
const selectedIds = ref<string[]>([])
const selectedSingleId = ref<string>('')
const searchTimer = ref<number | null>(null)

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 初始化选中状态
    if (props.multiple) {
      selectedIds.value = [...props.selectedValues]
    }
    else {
      selectedSingleId.value = props.selectedValues.length > 0 ? props.selectedValues[0] : ''
    }
    // 重置搜索和分页
    searchKeyword.value = ''
    currentPage.value = 1
    // 获取数据
    fetchRegulations()
  }
})

// 监听弹窗关闭
watch(visible, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})

// 获取制度列表
async function fetchRegulations() {
  loading.value = true

  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    title: searchKeyword.value || undefined, // 搜索关键词
    status: 'PUBLISHED', // 只获取已发布的制度
  }

  try {
    const res: any = await systemApi.lawsSystem(params)
    if (res) {
      const data = res.data || res
      if (data.content) {
        regulationList.value = data.content
        total.value = data.totalElements || 0
      }
      else if (Array.isArray(data)) {
        regulationList.value = data
        total.value = data.length
      }
      else {
        regulationList.value = []
        total.value = 0
      }
    }
  }
  catch (error: any) {
    console.error('获取制度列表失败:', error)
    ElMessage.error('获取制度列表失败')
    regulationList.value = []
    total.value = 0
  }
  finally {
    loading.value = false
  }
}

// 搜索处理（防抖）
function handleSearch() {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  searchTimer.value = setTimeout(() => {
    currentPage.value = 1 // 重置到第一页
    fetchRegulations()
  }, 300)
}

// 分页大小改变
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchRegulations()
}

// 当前页改变
function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchRegulations()
}

// 关闭弹窗
function handleClose() {
  visible.value = false
}

// 确认选择
function handleConfirm() {
  if (props.multiple) {
    // 多选模式
    const selectedItems = regulationList.value.filter(item =>
      selectedIds.value.includes(item.id),
    )
    emit('confirm', selectedIds.value, selectedItems)
  }
  else {
    // 单选模式
    if (selectedSingleId.value) {
      const selectedItem = regulationList.value.find(item => item.id === selectedSingleId.value)
      if (selectedItem) {
        emit('confirm', [selectedSingleId.value], [selectedItem])
      }
      else {
        emit('confirm', [], [])
      }
    }
    else {
      emit('confirm', [], [])
    }
  }
  handleClose()
}

// 组件挂载时的初始化
onMounted(() => {
  // 组件挂载时不自动加载数据，等待弹窗打开时再加载
})
</script>

<template>
  <el-dialog
    v-model="visible"
    title="选择关联制度"
    width="800px"
    :before-close="handleClose"
  >
    <!-- 搜索栏 -->
    <div class="mb-4">
      <el-input
        v-model="searchKeyword"
        placeholder="请输入制度名称进行搜索"
        clearable
        @input="handleSearch"
        @clear="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 列表 -->
    <div class="regulation-list" style="max-height: 400px; overflow-y: auto;">
      <!-- 多选模式 -->
      <el-checkbox-group v-if="props.multiple" v-model="selectedIds">
        <div v-for="item in regulationList" :key="item.id" class="regulation-item">
          <el-checkbox :label="item.id" class="w-full">
            <div class="w-full">
              <div class="title-ellipsis text-gray-900 font-medium">
                {{ item.title }}
              </div>
              <div class="mt-1 text-sm text-gray-500">
                编号：{{ item.code || '暂无' }} |
                类型：{{ item.lawType }} |
              </div>
            </div>
          </el-checkbox>
        </div>
      </el-checkbox-group>

      <!-- 单选模式 -->
      <el-radio-group v-else v-model="selectedSingleId">
        <div v-for="item in regulationList" :key="item.id" class="regulation-item">
          <el-radio :label="item.id" class="w-full">
            <div class="w-full">
              <div class="title-ellipsis text-gray-900 font-medium">
                {{ item.title }}
              </div>
              <div class="mt-1 text-sm text-gray-500">
                编号：{{ item.code || '暂无' }} |
                类型：{{ item.lawType }} |
              </div>
            </div>
          </el-radio>
        </div>
      </el-radio-group>

      <!-- 空状态 -->
      <div v-if="regulationList.length === 0 && !loading" class="py-8 text-center text-gray-500">
        <el-empty description="暂无数据" />
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="py-8 text-center">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span class="ml-2">加载中...</span>
      </div>
    </div>

    <!-- 分页 -->
    <div class="mt-4 flex justify-center">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-500">
          已选择 {{ props.multiple ? selectedIds.length : (selectedSingleId ? 1 : 0) }} 项
        </div>
        <div>
          <el-button @click="handleClose">
            取消
          </el-button>
          <el-button type="primary" @click="handleConfirm">
            确定
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.regulation-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s;

  &:hover {
    border-color: #409eff;
    background-color: #f5f7fa;
  }

  :deep(.el-checkbox),
  :deep(.el-radio) {
    width: 100%;

    .el-checkbox__label,
    .el-radio__label {
      width: 100%;
      padding-left: 8px;
      word-break: break-all;
      white-space: normal;
    }
  }

  .title-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.el-pagination {
  justify-content: center;
}
</style>
