<script setup lang="ts">
import type { UploadProps } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import axios from 'axios'

import { getCurrentInstance, ref, watch } from 'vue'
import useUserStore from '@/store/modules/user'
import userApi from '@/api/modules/users/user.ts'
import uploadApi from '@/api/upload'
// console.log(props.notip, '123333')
defineOptions({
  name: 'ImageUpload',
})
const props = withDefaults(
  defineProps<{
    action: UploadProps['action']
    // headers?: UploadProps['headers']
    headers?: any
    data?: UploadProps['data']
    name?: UploadProps['name']
    url?: string
    size?: number
    width?: number
    height?: number
    placeholder?: string
    notip?: boolean
    ext?: string[]
    dirname?: string
    sizeT?: string
    widtht?: string
    heightt?: string
    imgtype?: string
    code?: string
    isneedwh?: boolean
  }>(),
  {
    name: 'file',
    dirname: 'picture', // 上传类型，picture-图片，banner-轮播
    url: '',
    size: 10,
    sizeT: 'MB', // 大小单位
    width: 150,
    height: 150,
    placeholder: '',
    notip: false,
    ext: () => ['jpg', 'png', 'gif', 'bmp'],
    action: `${(import.meta.env.DEV && import.meta.env.VITE_OPEN_PROXY === 'true') ? '/proxy/' : import.meta.env.VITE_APP_API_BASEURL}whiskerguardgeneralservice/api/file/upload?serviceName=whiskerguardgeneralservice&categoryName=uploadFile`,
    headers: {
      Authorization: `Bearer ${useUserStore().token}`,
    },
    widtht: '',
    heightt: '',
    imgtype: 'xx200', // 回显尺寸
    code: '', // 上传目录code
    isneedwh: false,
  },
)
// 主目录：dirname-字段
// adv（广告位模块）；
// user（用户模块）：/user/merchant/（汽配商户），/user/member/（用户），/user/car/（汽修厂）
// find（发现模块）；/find/trends/（动态），/find/memorabilia/（大事记），/find/business/（汽配商圈）
// set（系统）；/set/vehiclemodel/（品牌），/set/carseries/（车系），/set/category/（类目），/set/agreement/（协议）

// 引入自定义挂载公共方法
const emits = defineEmits<{
  'update:url': [
    url: string,
  ]
  'onSuccess': [
    res: any,
  ]
}>()
// 自定义图片上传
// function httpRequest(file: any) {

//   // console.log(fd, file, '添加参数')
//
// }
const percent: any = ref(false)
const percentnum: any = ref(0)
const _loadicon = new URL('@/assets/images/loadicon.gif', import.meta.url).href
const fullscreenLoading = ref(false)
const { pictureEcho, _pictureType } = getCurrentInstance()!.appContext.config.globalProperties.$tools()
const uploadData = ref({
  imageViewerVisible: false,
  progress: {
    preview: '',
    percent: 0,
  },
})

// 存储真实的图片URL
const realImageUrl = ref('')

// 监听url变化，获取真实的图片地址
watch(
  () => props.url,
  async (newUrl) => {
    if (newUrl && newUrl !== '') {
      try {
        // 如果url看起来已经是完整的URL，直接使用
        if (newUrl.startsWith('http://') || newUrl.startsWith('https://')) {
          realImageUrl.value = newUrl
        }
        else {
          // 否则通过API获取真实地址
          const realUrl = await uploadApi.getFileUrl(newUrl)
          realImageUrl.value = realUrl
        }
      }
      catch (error) {
        // console.error('获取图片真实地址失败:', error)
        // 如果获取失败，回退到使用pictureEcho
        realImageUrl.value = pictureEcho(newUrl, props.imgtype)
      }
    }
    else {
      realImageUrl.value = ''
    }
  },
  { immediate: true },
)
// 自定义上传
async function httpRequest(file: any) {
  // if (pictureType(props.dirname) === false) {
  //   ElMessage.error('未配置图片上传参数项')
  //   return false
  // }
  const fd = new FormData()
  // fd.append('dirname', !props.code ? pictureType(props.dirname) : `${pictureType(props.dirname)}/${props.code}`)// 传参数
  fd.append('file', file.file)// 传文件
  // const loadingInstance = ElLoading.service({
  //   lock: true,
  //   text: 'Loading',
  //   background: 'rgba(0, 0, 0, 0.7)',
  // })
  fullscreenLoading.value = true
  const res = await axios({
    method: 'POST',
    url: props.action,
    data: fd,
    headers: props.headers,
  })
  // console.log('ImageUpload-单图', res.data)
  if (res.data) {
    fullscreenLoading.value = false

    // 获取上传返回的文件key
    const fileKey = res.data.key

    // 立即获取真实的图片URL
    try {
      const realUrl = await uploadApi.getFileUrl(fileKey)
      realImageUrl.value = realUrl
      console.log(realImageUrl.value, 'realUrl')
    }
    catch (error) {
      // console.error('获取上传图片真实地址失败:', error)
      // 如果获取失败，回退到使用pictureEcho
      realImageUrl.value = pictureEcho(fileKey, props.imgtype)
    }

    emits('update:url', res.data.key)
    emits('onSuccess', res.data.key)
  }
  else {
    fullscreenLoading.value = false
  }
}

// 获取7牛token
function _qiniu_token(file: any) {
  // console.log(file)
  return new Promise((resolve: any, reject: any) => {
    userApi.getToken({
      ext: file.file.name.substring(file.file.name.lastIndexOf('.') + 1),
    }).then((res: any) => {
      // console.log(res, 'resres')
      resolve(res.token)
    }).catch((err: any) => {
      ElMessageBox.alert('七牛token验证失败，请联系管理员', '提示', {
        callback: (_action: any) => { },
      })
      reject(err)
    })
  })
}
function _closeLoad() {
  fullscreenLoading.value = false
}

// 预览
function preview() {
  uploadData.value.imageViewerVisible = true
}
// 关闭预览
function previewClose() {
  uploadData.value.imageViewerVisible = false
}
// 移除
function remove() {
  emits('update:url', '')
}
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const fileName = file.name.split('.')
  const fileExt = fileName.at(-1) ?? ''
  const isTypeOk = props.ext.includes(fileExt)
  const isSizeOk = file.size / 1024 / 1024 < props.size
  if (!isTypeOk) {
    ElMessage.error(`上传图片只支持 ${props.ext.join(' / ')} 格式！`)
  }
  if (!isSizeOk) {
    ElMessage.error(`上传图片大小不能超过 ${props.size}${props.sizeT}！`)
  }
  if (isTypeOk && isSizeOk) {
    uploadData.value.progress.preview = URL.createObjectURL(file)
  }

  return isTypeOk && isSizeOk
}
const onProgress: UploadProps['onProgress'] = (file) => {
  // console.log(file, '路劲')
  uploadData.value.progress.percent = file.percent
}
const onSuccess: UploadProps['onSuccess'] = (res) => {
  uploadData.value.progress.preview = ''
  uploadData.value.progress.percent = 0
  if (res) {
    emits('onSuccess', res)
  }
}
</script>

<template>
  <div class="upload-container">
    <el-upload
      :show-file-list="false" :headers="headers" :action="action" :data="data" :name="name"
      :before-upload="beforeUpload" :on-progress="onProgress" :on-success="onSuccess" :http-request="httpRequest"
      drag class="image-upload"
    >
      <el-image
        v-if="url === ''" :src="url === '' ? placeholder : url"
        :style="`width:${width}px;height:${height}px;`" fit="fill"
      >
        <template #error>
          <div
            class="image-slot"
            :style="`width:${widtht ? widtht : width}px;height:${heightt ? heightt : height}px;`"
          >
            <svg-icon name="ep:plus" />
          </div>
        </template>
      </el-image>
      <div v-else-if="realImageUrl" class="image">
        <el-image :src="realImageUrl" :style="`width:${width}px;height:${height}px;`" fit="fill" />
        <div class="mask">
          <div class="actions">
            <span title="预览" @click.stop="preview">
              <svg-icon name="ep:zoom-in" />
            </span>
            <span title="移除" @click.stop="remove">
              <svg-icon name="ep:delete" />
            </span>
          </div>
        </div>
      </div>
      <div v-else class="image">
        <div class="image-slot" :style="`width:${width}px;height:${height}px;`">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <div>加载中...</div>
        </div>
      </div>
      <div
        v-show="url === '' && uploadData.progress.percent" class="progress"
        :style="`width:${width}px;height:${height}px;`"
      >
        <el-image
          :src="uploadData.progress.preview" :style="`width:${width}px;height:${height}px;`"
          fit="fill"
        />
        <el-progress
          type="circle" :width="Math.min(width, height) * 0.8"
          :percentage="uploadData.progress.percent"
        />
      </div>
    </el-upload>
    <div v-if="notip" class="el-upload__tip">
      <div style="display: inline-block;">
        <!-- 上传图片支持 ${ext.join(' / ')} 格式，且 -->
        <el-alert
          :title="`图片大小不超过${size} ${sizeT}，建议图片尺寸为${isneedwh ? '16:9 或' : ''} ${width}:${height}`"
          type="info" show-icon :closable="false"
        />
      </div>
    </div>
    <el-image-viewer
      v-if="uploadData.imageViewerVisible" :url-list="[realImageUrl]" teleported
      @close="previewClose"
    />
    <!-- 上传时的加载遮罩层 -->
    <!-- <div v-if="fullscreenLoading" class="loading-mask">
      <div class="flex justify-center items-center d-c ">
        <div class=" c-[#409eff]">
          <img class="w-5 h-5" :src="loadicon" alt>
        </div>
        <div class="my-4 c-[#409eff]">上传中...</div>
        <div style="position: fixed; top:60px;right:60px; z-index:1000000;  border-radius: 50%; background-color: rgba(255, 255, 255, 0.8);"
          class="w-16 h-16 flex justify-center items-center font-size-10 cursor-pointer c-[#333]"
          @click="closeLoad">
          <svg-icon name="ep:close" />
        </div>
      </div>
    </div> -->
    <div
      v-if="percent" class="demo-progress fixed left-50% top-50% w-130 bg-[#fff]"
      style=" z-index: 99999; border-radius: 8px; box-shadow: 0 0 8px 0 #999;transform: translate(-50%, -50%);"
    >
      <!-- <div class="w-full text-end pr-4 pt-4 font-size-6 cursor-pointer" @click="closepercent">
        <svg-icon name="ep:close" />
      </div> -->
      <div class="d-c h-16 h-40 w-full flex items-center justify-center py-2" style="">
        <el-progress
          :width="500" striped striped-flow :text-inside="true" :stroke-width="24"
          :percentage="percentnum" status="success"
        />
        <div>
          <div v-if="percentnum > 0" class="my-4 c-[#67c23a]">
            上传中...
          </div>
          <div v-else class="my-4 c-[#e67c28]">
            正在解析文件流...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .upload-container {
    line-height: initial;
  }

  .loading-mask {
    position: fixed;
    inset: 0;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgb(0 0 0 / 70%);

    /* 确保在el-loading之上 */
  }

  .el-loading {
    position: relative;
  }

  .demo-progress .el-progress--line {
    width: 450px;
    height: 30px;
    margin-bottom: 15px;
  }

  .el-loading .el-button {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .el-image {
    display: block;
  }

  .image {
    position: relative;
    overflow: hidden;
    border-radius: 6px;

    .mask {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: var(--el-overlay-color-lighter);
      opacity: 0;
      transition: opacity 0.3s;

      .actions {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 100px;

        @include position-center(xy);

        span {
          width: 50%;
          color: var(--el-color-white);
          text-align: center;
          cursor: pointer;
          transition: color 0.1s, transform 0.1s;

          &:hover {
            transform: scale(1.5);
          }

          .icon {
            font-size: 24px;
          }
        }
      }
    }

    &:hover .mask {
      opacity: 1;
    }
  }

  .image-upload {
    display: inline-block;
    vertical-align: top;
  }

  :deep(.el-upload) {
    .el-upload-dragger {
      display: inline-block;
      padding: 0;

      &.is-dragover {
        border-width: 1px;
      }

      .image-slot {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: var(--el-text-color-placeholder);
        background-color: transparent;

        .icon {
          font-size: 30px;
        }
      }

      .progress {
        position: absolute;
        top: 0;

        &::after {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          content: "";
          background-color: var(--el-overlay-color-lighter);
        }

        .el-progress {
          z-index: 1;

          @include position-center(xy);

          .el-progress__text {
            color: var(--el-text-color-placeholder);
          }
        }
      }
    }
  }

  .cancel-loading {
    position: absolute;
    top: 62%;
    left: 50%;
    z-index: 9999;
    transform: translate(-50%, -50%);

    /* 确保 z-index 高于遮罩层 */
  }
</style>
