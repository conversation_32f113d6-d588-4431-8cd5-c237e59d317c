<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import organizationalApi from '@/api/organizational/index'

interface Props {
  modelValue?: any
  placeholder?: string
  width?: string
  multiple?: boolean
  clearable?: boolean
  disabled?: boolean
  autoLoad?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择部门',
  width: '100%',
  multiple: false,
  clearable: true,
  disabled: false,
  autoLoad: true,
})

const emit = defineEmits<Emits>()

// 部门树形数据
const departmentTree = ref([])
const loading = ref(false)

// 获取部门树形数据
async function fetchDepartmentTree() {
  try {
    loading.value = true
    const response = await organizationalApi.organizationalUnitTreeApi()
    departmentTree.value = response || []
  }
  catch (error) {
    console.error('获取部门树失败:', error)
    ElMessage.error('获取部门树失败')
  }
  finally {
    loading.value = false
  }
}

// 处理值更新
function handleUpdate(value: any) {
  emit('update:modelValue', value)
  emit('change', value)
}

// 暴露方法供外部调用
defineExpose({
  fetchDepartmentTree,
  departmentTree,
  loading,
})

// 组件挂载时自动加载数据
onMounted(() => {
  if (props.autoLoad) {
    fetchDepartmentTree()
  }
})
</script>

<template>
  <!-- disabled: (data: any) => data.type !== 'DEPARTMENT' -->
  <el-tree-select
    :model-value="modelValue"
    :data="departmentTree"
    :loading="loading"
    :placeholder="placeholder"
    :style="{ width }"
    :props="{
      value: 'id',
      label: 'name',
      children: 'children',
    }"
    :multiple="multiple"
    :clearable="clearable"
    :disabled="disabled"
    check-strictly
    :render-after-expand="false"
    :show-checkbox="multiple"
    node-key="id"
    @update:model-value="handleUpdate"
  />
</template>
