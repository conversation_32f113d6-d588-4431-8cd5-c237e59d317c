<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleCheck, Document, Upload } from '@element-plus/icons-vue'
import FileUpload from '@/components/FileUpload/index.vue'
import uploadApi from '@/api/upload'

defineOptions({
  name: 'DocumentUpload',
})

const props = withDefaults(defineProps<Props>(), {
  action: `${(import.meta.env.DEV && import.meta.env.VITE_OPEN_PROXY === 'true') ? '/proxy/' : import.meta.env.VITE_APP_API_BASEURL}whiskerguardgeneralservice/api/file/upload?serviceName=whiskerguardgeneralservice&categoryName=uploadFile`,
  ext: () => ['pdf', 'doc', 'docx'],
  placeholder: '导入文档，系统自动填充所有信息',
  tipText: '支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB',
  maxSize: 10,
})

const emits = defineEmits<{
  'update:modelValue': [value: string]
  'onSuccess': [fileKey: string, fileInfo: FileItem]
  'onRemove': []
}>()

interface FileItem {
  name: string
  url: string
  size?: string
}

interface Props {
  modelValue?: string // v-model绑定的文件key
  action?: string // 上传接口地址
  ext?: string[] // 支持的文件格式
  placeholder?: string // 上传提示文字
  tipText?: string // 底部提示文字
  maxSize?: number // 最大文件大小(MB)
}

// 上传文件列表
const uploadedFiles = ref<FileItem[]>([])

// 计算是否有文件
const hasFiles = computed(() => uploadedFiles.value.length > 0)

// 监听modelValue变化，同步文件列表
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && typeof newValue === 'string' && uploadedFiles.value.length === 0) {
      // 如果有值但文件列表为空，说明是外部设置的值，需要构造文件信息
      const fileName = newValue.split('/').pop() || '文档文件'
      uploadedFiles.value = [{
        name: fileName,
        url: newValue,
        size: '未知大小',
      }]
    }
    else if (!newValue) {
      // 如果值为空，清空文件列表
      uploadedFiles.value = []
    }
  },
  { immediate: true },
)

// 处理文件上传成功
function handleFileUploadSuccess(fileKeys: string[]) {
  if (fileKeys && fileKeys.length > 0) {
    const fileKey = fileKeys[0]
    // 从URL中提取文件名
    const fileName = typeof fileKey === 'string' ? (fileKey.split('/').pop() || '未知文件') : '未知文件'

    const fileInfo: FileItem = {
      name: fileName,
      url: fileKey,
      size: '未知大小',
    }

    // 添加到上传文件列表
    uploadedFiles.value = [fileInfo] // 只保留最新上传的文件

    // 更新v-model值
    emits('update:modelValue', fileKey)
    emits('onSuccess', fileKey, fileInfo)

    ElMessage.success('文件上传成功')
  }
}

// 删除已上传的文件
function removeUploadedFile(index: number) {
  uploadedFiles.value.splice(index, 1)

  // 更新v-model值
  emits('update:modelValue', '')
  emits('onRemove')

  ElMessage.success('文件已删除')
}

// 预览文件
async function previewFile(file: FileItem) {
  try {
    // 先获取真实的文件地址
    const realUrl = await uploadApi.getFileUrl(file.url)
    // 根据文件类型决定预览方式
    const fileExt = typeof file.name === 'string' ? file.name.split('.').pop()?.toLowerCase() : ''
    if (fileExt === 'pdf') {
      // PDF文件在新窗口打开
      window.open(realUrl, '_blank')
    }
    else {
      // 其他文件类型直接下载
      const link = document.createElement('a')
      link.href = realUrl
      link.download = file.name
      link.click()
    }
  }
  catch (error) {
    console.error('获取文件地址失败:', error)
    ElMessage.error('文件预览失败，请稍后重试')
  }
}
</script>

<template>
  <!-- 上传区域 - 当没有文件时显示 -->
  <div v-if="!hasFiles" class="mb-4 h-32 flex flex-col items-center justify-center border-2 border-gray-300 rounded-lg border-dashed transition-colors hover:border-blue-400">
    <el-icon class="mb-2 text-3xl text-gray-400">
      <Upload />
    </el-icon>
    <p class="mb-2 text-gray-500">
      {{ placeholder }}
    </p>
    <FileUpload
      :action="action"
      :notip="true"
      :ext="ext"
      @on-success="handleFileUploadSuccess"
    />
  </div>

  <!-- 已上传文件展示区域 - 当有文件时显示 -->
  <div v-else class="mb-4">
    <!-- <div class="mb-3 flex items-center justify-between">
      <h4 class="flex items-center text-sm text-gray-700 font-medium">
        <el-icon class="mr-2 text-green-500">
          <CircleCheck />
        </el-icon>
        文件上传成功
      </h4>
      <span class="text-xs text-gray-500">{{ tipText.split('，')[0] }}</span>
    </div> -->
    <div
      v-for="(file, index) in uploadedFiles"
      :key="index"
      class="flex items-center justify-between border border-green-200 rounded-lg bg-green-50 p-4 shadow-sm"
    >
      <div class="flex items-center space-x-3">
        <div class="h-10 w-10 flex items-center justify-center rounded-full bg-green-100">
          <el-icon class="text-green-600">
            <Document />
          </el-icon>
        </div>
        <div>
          <p class="text-sm text-gray-900 font-medium">
            {{ file.name }}
          </p>
          <!-- <p class="text-xs text-gray-500">
            {{ file.size }} • 上传完成
          </p> -->
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <el-button
          size="small"
          type="primary"
          plain
          @click="previewFile(file)"
        >
          <el-icon class="mr-1">
            <Document />
          </el-icon>
          预览
        </el-button>
        <el-button
          size="small"
          type="danger"
          plain
          @click="removeUploadedFile(index)"
        >
          删除
        </el-button>
      </div>
    </div>
  </div>

  <!-- 底部提示信息 -->
  <div class="relative my-6">
    <div class="relative flex justify-between">
      <span class="text-right text-xs text-gray-500">{{ tipText }}</span>
      <span class="bg-white px-4 text-sm text-gray-500">或手动填写信息</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 组件样式可以根据需要添加
</style>
