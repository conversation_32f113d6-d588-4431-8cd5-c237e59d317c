<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    width?: string
  }>(),
  {
    width: '0px',
  },
)

// 注册多个事件，无需每次单独组成
const emits = defineEmits(['close', 'setContent'])
// const show: any = ref(false)
onMounted(() => {
})
watch(() => props.width, (val) => {
  console.log(val, '宽度监听')
})
function drawerdel() {
  emits('close', Math.floor(Date.now() / 1000)) // 向父组件传递数据
}
</script>

<template>
  <div class="css-drawer" :style="{ width }">
    <slot />
    <div class="drawerdel" :style="{ display: width === '0px' ? 'none' : '' }" @click="drawerdel()">
      <svg-icon name="ep:close" style="color: #999;" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .css-drawer {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    overflow: unset;
    background-color: #fff;
    box-shadow: var(--el-box-shadow-dark);
    transition: width 0.5s ease;
    transition: height 0.5s ease;
    transition: all 0.5s;
  }

  .drawerdel {
    position: absolute;
    top: 8px;
    left: -20px;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 35px;
    cursor: pointer;
    background-color: #fff;
    border-radius: 8px 0 0 8px;
    box-shadow: var(--el-box-shadow-dark);
  }
</style>
