# UploadMbb 组件

一个基于 Element Plus 的文件上传组件，支持多文件上传、预览、下载等功能。

## 功能特性

- 支持多文件上传
- 文件预览（PDF、图片等）
- 文件下载
- 文件删除
- 拖拽上传
- 文件类型和大小限制
- 灵活的文件路径返回格式

## 基本用法

```vue
<script setup>
import { ref } from 'vue'
import UploadMbb from '@/components/uploadMbb/index.vue'

const fileList1 = ref([])
const fileList2 = ref([])

function handleUploadSuccess(files) {
  console.log('上传成功:', files)
}
</script>

<template>
  <div>
    <!-- 基本用法 - 返回 fileUrl -->
    <UploadMbb
      v-model="fileList1"
      :max="3"
      @upload-success="handleUploadSuccess"
    />

    <!-- 使用 filePath - 返回 filePath 而不是 fileUrl -->
    <UploadMbb
      v-model="fileList2"
      :max="5"
      :use-file-path="true"
      @upload-success="handleUploadSuccess"
    />
  </div>
</template>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 文件列表，支持 v-model | Array | [] |
| action | 上传地址 | String | 默认上传接口 |
| headers | 请求头 | Object | 包含 Authorization |
| size | 文件大小限制（MB） | Number | 10 |
| max | 最大文件数量 | Number | 5 |
| accept | 接受的文件类型 | String | '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png' |
| tipText | 提示文本 | String | '支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 10MB' |
| serviceName | 服务名称 | String | 'whiskerguardregulatoryservice' |
| categoryName | 分类名称 | String | 'duty' |
| autoUpload | 是否自动上传 | Boolean | false |
| **useFilePath** | **是否返回 filePath 而不是 fileUrl** | **Boolean** | **false** |
| **attachmentType** | **附件类型，如果传入则会在返回的文件对象中包含此字段** | **String** | **undefined** |
| **readonly** | **只读模式，隐藏上传和删除功能，仅显示文件列表和预览下载** | **Boolean** | **false** |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 文件列表更新时触发 | (files: Array) |
| uploadSuccess | 上传成功时触发 | (files: Array) |
| uploadError | 上传失败时触发 | (error: any) |

### 文件对象格式

#### 当 useFilePath = false 时（默认）：
```javascript
{
  fileName: 'example.pdf',
  fileUrl: 'file-key-or-url',  // 文件URL
  fileType: 'pdf',
  fileSize: 1024000,
  AttachmentType: 'document' // 仅当传入 attachmentType 时存在
}
```

#### 当 useFilePath = true 时：
```javascript
{
  fileName: 'example.pdf',
  filePath: 'file-key-or-path', // 文件路径
  fileType: 'pdf',
  fileSize: 1024000,
  AttachmentType: 'document' // 仅当传入 attachmentType 时存在
}
```

## 使用场景

### 场景1：需要 fileUrl 的接口
```vue
<UploadMbb
  v-model="attachments"
  :use-file-path="false"
/>
```

### 场景2：需要 filePath 的接口
```vue
<UploadMbb
  v-model="documents"
  :use-file-path="true"
/>
```

### 场景3：需要附件类型的接口
```vue
<UploadMbb
  v-model="attachments"
  attachment-type="contract"
/>
```

### 场景4：只读模式（详情页面）
```vue
<UploadMbb
  :model-value="attachments"
  :readonly="true"
/>
```

## 注意事项

1. `useFilePath` 属性控制返回的字段名：
   - `false`：返回 `fileUrl` 字段
   - `true`：返回 `filePath` 字段

2. 组件会自动识别传入的数据格式，支持 `filePath`、`fileUrl` 和 `url` 字段

3. 文件上传后的实际值是相同的，只是字段名不同，方便适配不同的后端接口需求

4. `attachmentType` 属性是可选的：
   - 如果传入该属性，返回的文件对象会包含 `AttachmentType` 字段
   - 如果不传入，返回的文件对象不会包含该字段

5. `readonly` 属性用于只读模式：
   - 设置为 `true` 时，隐藏上传按钮和删除按钮
   - 仅保留文件列表展示、预览和下载功能
   - 适用于详情页面等只需要查看文件的场景
