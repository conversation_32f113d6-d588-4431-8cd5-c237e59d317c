<script setup lang="ts">
import { ref, watch } from 'vue'
import axios from 'axios'
import type { UploadProps, UploadUserFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Document, UploadFilled } from '@element-plus/icons-vue'
import useUserStore from '@/store/modules/user'
import uploadApi from '@/api/upload'

defineOptions({
  name: 'UploadMbb',
})

const props = withDefaults(
  defineProps<{
    modelValue?: any[]
    action?: string
    headers?: any
    size?: number
    max?: number
    accept?: string
    tipText?: string
    serviceName?: string
    categoryName?: string
    autoUpload?: boolean
    useFilePath?: boolean
    attachmentType?: string
    readonly?: boolean
  }>(),
  {
    modelValue: () => [],
    action: `${(import.meta.env.DEV && import.meta.env.VITE_OPEN_PROXY === 'true') ? '/proxy/' : import.meta.env.VITE_APP_API_BASEURL}whiskerguardgeneralservice/api/file/upload`,
    headers: () => ({
      Authorization: `Bearer ${useUserStore().token}`,
    }),
    size: 10,
    max: 5,
    accept: '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png',
    tipText: '支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 10MB',
    serviceName: 'whiskerguardregulatoryservice',
    categoryName: 'duty',
    autoUpload: false,
    useFilePath: false,
    attachmentType: undefined,
    readonly: false,
  },
)

const emits = defineEmits<{
  'update:modelValue': [value: any[]]
  'uploadSuccess': [files: any[]]
  'uploadError': [error: any]
}>()

const fileList = ref<any[]>([])
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const previewUrl = ref('')
const previewType = ref('')
const previewName = ref('')
const pendingFiles = ref<any[]>([])
const uploading = ref(false)

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && Array.isArray(newVal)) {
      // 转换processFiles格式到组件内部格式
      fileList.value = newVal.map(file => ({
        id: file.fileName || Date.now().toString(),
        name: file.fileName || file.name || '',
        type: getFileType(file.fileName || file.name || ''),
        size: formatFileSize(file.fileSize || file.size || 0),
        rawSize: file.fileSize || file.size || 0,
        uploadTime: new Date().toLocaleString('zh-CN'),
        url: file.filePath || file.fileUrl || file.url || '',
        status: 'success',
      }))
    }
  },
  { immediate: true, deep: true },
)

// 文件类型判断
function getFileType(fileName: string) {
  const ext = fileName.split('.').pop()?.toLowerCase()
  if (['pdf'].includes(ext || '')) {
    return 'pdf'
  }
  if (['doc', 'docx'].includes(ext || '')) {
    return 'doc'
  }
  if (['xls', 'xlsx'].includes(ext || '')) {
    return 'xls'
  }
  if (['jpg', 'jpeg', 'png', 'gif'].includes(ext || '')) {
    return 'image'
  }
  return 'other'
}

// 文件大小格式化
function formatFileSize(size: number) {
  if (size < 1024) {
    return `${size} B`
  }
  if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)} KB`
  }
  return `${(size / 1024 / 1024).toFixed(2)} MB`
}

// 上传前验证
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const fileName = file.name
  const fileExt = fileName.split('.').pop()?.toLowerCase()
  const acceptedExts = props.accept.split(',').map(ext => ext.replace('.', '').toLowerCase())
  const isTypeOk = acceptedExts.includes(fileExt || '')
  const isSizeOk = file.size / 1024 / 1024 < props.size

  if (!isTypeOk) {
    ElMessage.error(`上传文件只支持 ${props.accept} 格式！`)
    return false
  }
  if (!isSizeOk) {
    ElMessage.error(`上传文件大小不能超过 ${props.size}MB！`)
    return false
  }
  return true
}

// 文件选择处理
function handleFileChange(file: any) {
  if (!beforeUpload(file.raw)) {
    return
  }

  const fileInfo = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    name: file.name,
    type: getFileType(file.name),
    size: formatFileSize(file.size),
    rawSize: file.size,
    uploadTime: new Date().toLocaleString('zh-CN'),
    url: '',
    status: 'pending',
    raw: file.raw,
  }

  pendingFiles.value.push(fileInfo)

  if (props.autoUpload) {
    uploadFile(fileInfo)
  }
}

// 上传文件
async function uploadFile(fileInfo: any) {
  try {
    uploading.value = true
    fileInfo.status = 'uploading'

    const formData = new FormData()
    formData.append('file', fileInfo.raw)

    // 构建上传URL
    const uploadUrl = `${props.action}?serviceName=${props.serviceName}&categoryName=${props.categoryName}`

    const response = await axios({
      method: 'POST',
      url: uploadUrl,
      data: formData,
      headers: {
        ...props.headers,
        'Content-Type': 'multipart/form-data',
      },
    })

    if (response.data && response.data.key) {
      fileInfo.url = response.data.key
      fileInfo.status = 'success'

      // 添加到文件列表
      fileList.value.push(fileInfo)

      // 从待上传列表中移除
      const index = pendingFiles.value.findIndex(f => f.id === fileInfo.id)
      if (index > -1) {
        pendingFiles.value.splice(index, 1)
      }

      // 更新modelValue
      updateModelValue()
      emits('uploadSuccess', fileList.value)

      ElMessage.success('文件上传成功')
    }
    else {
      throw new Error('上传响应格式错误')
    }
  }
  catch (error) {
    console.error('文件上传失败:', error)
    fileInfo.status = 'error'
    emits('uploadError', error)
    ElMessage.error('文件上传失败')
  }
  finally {
    uploading.value = false
  }
}

// 确认上传（手动上传模式）
function confirmUpload() {
  if (pendingFiles.value.length === 0) {
    showUploadDialog.value = false
    return
  }

  pendingFiles.value.forEach((file) => {
    uploadFile(file)
  })

  showUploadDialog.value = false
}

// 更新v-model值
function updateModelValue() {
  // 转换内部格式到processFiles格式
  const processFiles = fileList.value.map((file) => {
    const fileObj: any = {
      fileName: file.name,
      ...(props.useFilePath ? { filePath: file.url } : { fileUrl: file.url }),
      fileType: file.type,
      fileSize: file.rawSize,
    }

    // 只有当父组件传入attachmentType时才添加该字段
    if (props.attachmentType) {
      fileObj.AttachmentType = props.attachmentType
    }

    return fileObj
  })
  emits('update:modelValue', processFiles)
}

// 移除文件
function removeFile(fileInfo: any) {
  const index = fileList.value.findIndex(f => f.id === fileInfo.id)
  if (index > -1) {
    fileList.value.splice(index, 1)
    updateModelValue()
    ElMessage.success('文件已删除')
  }
}

// 预览文件
async function previewFile(fileInfo: any) {
  try {
    const response = await uploadApi.getFileUrl(fileInfo.url)
    const realUrl = response

    previewUrl.value = realUrl
    previewType.value = fileInfo.type
    previewName.value = fileInfo.name
    showPreviewDialog.value = true
  }
  catch (error) {
    console.error('获取文件URL失败:', error)
    ElMessage.error('获取文件预览链接失败')
  }
}

// 下载文件
async function downloadFile(fileInfo: any) {
  try {
    const response = await uploadApi.getFileUrl(fileInfo.url)
    const realUrl = response

    const link = document.createElement('a')
    link.href = realUrl
    link.download = fileInfo.name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  catch (error) {
    console.error('获取文件URL失败:', error)
    ElMessage.error('获取文件下载链接失败')
  }
}

// 打开上传对话框
function openUploadDialog() {
  if (fileList.value.length >= props.max) {
    ElMessage.warning(`最多只能上传 ${props.max} 个文件`)
    return
  }
  showUploadDialog.value = true
}
</script>

<template>
  <div class="upload-mbb">
    <!-- 上传按钮 -->
    <el-button v-if="!readonly" type="primary" @click="openUploadDialog">
      <el-icon><UploadFilled /></el-icon>
      上传文档
    </el-button>

    <!-- 文件列表 -->
    <div v-if="fileList.length > 0" class="file-list">
      <div
        v-for="file in fileList"
        :key="file.id"
        class="file-item"
      >
        <div class="file-info">
          <el-icon class="file-icon">
            <Document />
          </el-icon>
          <div class="file-details">
            <div class="file-name" @click="previewFile(file)">
              {{ file.name }}
            </div>
            <div class="file-meta">
              <span class="file-size">{{ file.size }}</span>
              <span class="file-time">{{ file.uploadTime }}</span>
            </div>
          </div>
        </div>
        <div class="file-actions">
          <el-button
            type="text"
            size="small"
            @click="previewFile(file)"
          >
            预览
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="downloadFile(file)"
          >
            下载
          </el-button>
          <el-button
            v-if="!readonly"
            type="text"
            size="small"
            class="delete-btn"
            @click="removeFile(file)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传文档"
      width="500px"
    >
      <el-upload
        class="upload-area"
        drag
        multiple
        :action="action"
        :auto-upload="false"
        :on-change="handleFileChange"
        :show-file-list="false"
        :accept="accept"
      >
        <el-icon class="el-icon--upload">
          <UploadFilled />
        </el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            {{ tipText }}
          </div>
        </template>
      </el-upload>

      <!-- 待上传文件列表 -->
      <div v-if="pendingFiles.length > 0" class="pending-files">
        <div class="pending-title">
          待上传文件：
        </div>
        <div
          v-for="file in pendingFiles"
          :key="file.id"
          class="pending-file"
        >
          <span>{{ file.name }}</span>
          <span class="file-size">{{ file.size }}</span>
        </div>
      </div>

      <template #footer>
        <el-button @click="showUploadDialog = false">
          取消
        </el-button>
        <el-button
          v-if="!autoUpload"
          type="primary"
          :loading="uploading"
          @click="confirmUpload"
        >
          确认上传
        </el-button>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="`预览 - ${previewName}`"
      width="80%"
    >
      <div v-if="previewType === 'pdf'" class="preview-content">
        <iframe :src="previewUrl" class="preview-iframe" />
      </div>
      <div v-else-if="previewType === 'image'" class="preview-content">
        <img :src="previewUrl" class="preview-image" alt="预览图片">
      </div>
      <div v-else class="preview-placeholder">
        <div class="placeholder-content">
          <el-icon class="placeholder-icon">
            <Document />
          </el-icon>
          <p class="placeholder-text">
            该文件类型不支持在线预览
          </p>
          <el-button type="primary" @click="downloadFile({ url: previewUrl, name: previewName })">
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.upload-mbb {
  width: 100%;
}

.file-list {
  margin-top: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background-color: #f5f7fa;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #303133;
  cursor: pointer;
  margin-bottom: 4px;
}

.file-name:hover {
  color: #409eff;
}

.file-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.delete-btn {
  color: #f56c6c;
}

.delete-btn:hover {
  color: #f56c6c;
}

.upload-area {
  text-align: center;
}

.el-upload-dragger {
  width: 100%;
  padding: 40px 0;
}

.pending-files {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pending-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #303133;
}

.pending-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 13px;
  color: #606266;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.preview-iframe {
  width: 100%;
  height: 70vh;
  border: none;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

.preview-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.placeholder-content {
  text-align: center;
}

.placeholder-icon {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.placeholder-text {
  font-size: 16px;
  color: #909399;
  margin-bottom: 16px;
}
</style>
