import dictApi from '@/api/modules/system/dict'

/**
 * 获取字典数据
 * @param {number|string} types - 字典类型
 * @returns {Promise<Array>} 字典数据数组
 */
export default async function getDictData(types) {
  try {
    const response = await dictApi.dictAll(types)
    return response || []
  } catch (error) {
    console.error('获取字典数据失败:', error)
    return []
  }
}

/**
 * 根据字典值获取字典标签
 * @param {Array} dictData - 字典数据数组
 * @param {string|number} value - 字典值
 * @returns {string} 字典标签
 */
export function getDictLabel(dictData, value) {
  if (!Array.isArray(dictData) || !value) {
    return ''
  }
  
  const item = dictData.find(dict => dict.value === value || dict.value === String(value))
  return item ? item.name || item.label : ''
}

/**
 * 根据字典标签获取字典值
 * @param {Array} dictData - 字典数据数组
 * @param {string} label - 字典标签
 * @returns {string|number} 字典值
 */
export function getDictValue(dictData, label) {
  if (!Array.isArray(dictData) || !label) {
    return ''
  }
  
  const item = dictData.find(dict => dict.name === label || dict.label === label)
  return item ? item.value : ''
}

/**
 * 格式化字典数据为选择器选项
 * @param {Array} dictData - 字典数据数组
 * @returns {Array} 格式化后的选项数组
 */
export function formatDictOptions(dictData) {
  if (!Array.isArray(dictData)) {
    return []
  }
  
  return dictData.map(item => ({
    label: item.name || item.label,
    value: item.value,
    ...item
  }))
}
