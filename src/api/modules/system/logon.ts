import api from '@/api/index'

export default {
  // 列表
  list: (data: any) => api.get('loginlog/index', {
    params: data,
}
),
  // 删除
  logondelete: (data: any) => api.post('user/delete', { ...data }, {
  }),

  // 列表
  actionloglist: (data: any) => api.get('actionlog/index', {
    params: data,
  }),
  // 删除
  actionlogdel: (data: any) => api.post('user/delete', { ...data }, {
  }),

  // 根据员工获取承诺书（检查是否已签约）
  checkEmployeeCommitment: () => api.get('whiskerguardorgservice/api/letter/commitments/employee'),
  
  // 创建新的承诺书记录
  createCommitment: (data: any) => api.post('whiskerguardorgservice/api/letter/commitments', data),

}
