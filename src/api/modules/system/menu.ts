import api from '@/api/index'

export default {
  // 菜单--新增/修改
  edit: (data: any) => api.post('AdminMenu/edit', {
    ...data,
  }, {
    // baseURL: '/mock/',
  }),
  // 列表
  list: () => api.get('AdminMenu/index', {
    // baseURL: '/mock/',
  }),

  // 删除
  AdminMenudelete: (data: any) => api.post('AdminMenu/delete', {
    ...data,
  }, {
    // baseURL: '/mock/',
  }),

  // 详情
  detail: (data: any) => api.post('AdminMenu/info', {
    ...data,
  }, {
    // baseURL: '/mock/',
  }),
}
