import api from '@/api/index'

export default {
  // 列表
  list: (data: any) => api.get('user/index', {
    params: data,
    // baseURL: '/mock/',
  }),
  // 角色
  role: () => api.get('role/getRoleList', {
  }),
  // 职级
  level: () => api.get('level/getLevelList', {
  }),
  // 岗位
  position: () => api.get('position/getPositionList', {
  }),
  // 部门
  dept: () => api.get('dept/getDeptList', {
  }),

  // 新增/修改
  edit: (data: any) => api.post('user/edit', {
    ...data,
  }, {
    // baseURL: '/mock/',
  }),
  // 查询
  info: (data: any) => api.get('user/info', {
    params: data,
  }),
  // 重置密码
  resetPwd: (data: any) => api.post('user/resetPwd', { ...data }, {
    // baseURL: '/mock/',
  }),
  // 删除
  userdelete: (data: any) => api.post('user/delete', { ...data }, {
    // baseURL: '/mock/',
  }),
  // 获取指定ID的员工
  getEmpById(id: any) {
    return api.get(`whiskerguardorgservice/api/employees/${id}`)
  },
}
