import api from '@/api/index'

export default {
  // 菜单--新增/修改
  edit: (data: any) => api.post('role/edit', {
    ...data,
  }, {
    // baseURL: '/mock/',
  }),
  // 列表
  list: (data: any) => api.get('role/index', {
    ...data,
  }),

  // 删除
  delete: (data: any) => api.post('role/delete', {
    ...data,
  }, {
    // baseURL: '/mock/',
  }),

  // 详情
  detail: (data: any) => api.post('role/info', {
    ...data,
  }, {
    // baseURL: '/mock/',
  }),
}
