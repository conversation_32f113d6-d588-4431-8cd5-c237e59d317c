import api from '../index'
import storage from '@/utils/storage'
export default {
  // 登录
  // login: (data: any) => api.post('login/login', data),
  login: (data : any) => api.post('whiskerguardauthservice/api/sessions/login', data),
  logout: (data : any) => api.post(`whiskerguardauthservice/api/auth/logout?token=${storage.local.get('token')}`, data),
  // 获取验证码
  captcha: (data : any) => api.post('login/captcha', data),
  // // 获取权限
  // permission: () => api.get('user/permission', {
  //   baseURL: '/mock/',
  // }),
  // 获取权限
  permission: () => api.get('index/getUserInfo', {}),
  // 修改密码
  passwordEdit: (data : {
    password : string
    newpassword : string
  }) => api.post('user/password/edit', data, {
    baseURL: '/mock/',
  }),
  // 修改密码
  // updatePwd: (data : any) => api.post('index/updatePwd', data, {
  //   // baseURL: '/mock/',
  // }),
  // 修改密码
  updatePwd: (data : any) => api.put(`whiskerguardorgservice/api/employees/${data.id}/password`, data),

  // 获取偏好设置
  preferences: () => api.get('user/preferences', {
    baseURL: '/mock/',
  }),

  // 修改偏好设置
  preferencesEdit: (preferences : string) => api.post('user/preferences/edit', {
    preferences,
  }, {
    baseURL: '/mock/',
  }),

  // 获取标签栏固定标签页数据
  tabbar: () => api.get('user/tabbar', {
    baseURL: '/mock/',
  }),

  // 修改标签栏固定标签页数据
  tabbarEdit: (tabbar : string) => api.post('user/tabbar/edit', {
    tabbar,
  }, {
    baseURL: '/mock/',
  }),

  // 获取收藏夹
  favorites: () => api.get('user/favorites', {
    baseURL: '/mock/',
  }),

  // 修改收藏夹
  favoritesEdit: (favorites : string) => api.post('user/favorites/edit', {
    favorites,
  }, {
    baseURL: '/mock/',
  }),
  getPermissionList: (data : any) => api.get('role/getPermissionList', {
    params: data,
    // baseURL: '/mock/',
  }),
  // 角色权限编辑
  savePermission: (data : any) => api.post('role/savePermission', data, {
    // baseURL: '/mock/',
  }),
  // 配置详情
  settingInfo: (data : any) => api.post('setting/info', data, {
    // baseURL: '/mock/',
  }),
  // 配置详情
  settingEdit: (data : any) => api.post('setting/edit', data, {
    // baseURL: '/mock/',
  }),

}
