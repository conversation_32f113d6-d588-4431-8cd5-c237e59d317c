import api from '@/api/index'

export default {
  //  城市编辑
  edit: (data: any) => api.post('CitySetting/index', data, {
    // baseURL: '/mock/',
  }),
  // 城市列表
  list: (data: any) => api.post('CitySetting/index', data, {
    // baseURL: '/mock/',
  }),
  //  城市编辑
  delete: (data: any) => api.post('CitySetting/delete', data, {
    // baseURL: '/mock/',
  }),
  // 城市列表
  info: (data: any) => api.post('CitySetting/info', data, {
    // baseURL: '/mock/',
  }),
  // 城市列表
  selectindex: (data: any) => api.post('CitySetting/getSelect', data, {
  // baseURL: '/mock/',
  }),

}
