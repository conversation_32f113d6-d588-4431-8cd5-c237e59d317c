import api from '@/api/index'

export default {
  //  员工编辑
  edit: (data: any) => api.post('AppletUser/edit', data, {
    // baseURL: '/mock/',
  }),
  // 会员列表
  index: (data: any) => api.post('AppletUser/index', data, {
    // baseURL: '/mock/',
  }),
  // 上传 Upload/uploadImage /  Upload/QiniuUpload
  upload: (data: any) => api.post('Upload/QiniuUpload', data, {
    // baseURL: '/mock/',
  }),
  // 统计/CarParts/statistics
  statistics: (data: any) => api.get('AppletUser/statistics', data),
  // 图表阶段统计
  stageStatistics: (data: any) => api.get('AppletUser/stageStatistics', data),
  // 文件上传获取token
  getToken: (data: any) => api.post('upload/getToken', data, {}),
}
