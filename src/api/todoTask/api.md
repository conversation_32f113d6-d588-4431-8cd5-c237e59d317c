---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 09-审批流程服务/待办事件

## POST 分页查询所有待办事件

POST /whiskerguardapprovalservice/api/todo/events/page

描述：分页查询所有待办事件。

> Body 请求参数

```json
{
  "tenantId": 0,
  "assigneeId": 0,
  "eventType": "CONTRACT_APPROVAL",
  "title": "string",
  "eventStatus": "PENDING",
  "completedTimeStart": "string",
  "completedTimeEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |当前页码|
|size|query|integer| 否 |记录数|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[TodoEventReq](#schematodoeventreq)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "assigneeId": 0,
      "businessId": 0,
      "eventType": "",
      "title": "",
      "description": "",
      "status": "",
      "approvalProcessId": "",
      "approvalInstanceId": "",
      "completedTime": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "total": 0,
  "empty": false,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "totalPages": 0,
  "totalElements": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageTodoEventDTO](#schemaresponseentitypagetodoeventdto)|

## GET 根据ID查询待办事件

GET /whiskerguardapprovalservice/api/todo/events/{id}

描述：根据ID查询待办事件。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |待办事件ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "assigneeId": 0,
  "businessId": 0,
  "eventType": "",
  "title": "",
  "description": "",
  "status": "",
  "approvalProcessId": "",
  "approvalInstanceId": "",
  "completedTime": {
    "seconds": 0,
    "nanos": 0
  },
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityTodoEventDTO](#schemaresponseentitytodoeventdto)|

## DELETE 根据ID删除待办事件

DELETE /whiskerguardapprovalservice/api/todo/events/{id}

描述：根据ID删除待办事件。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |待办事件ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_TodoEventDTO">TodoEventDTO</h2>

<a id="schematodoeventdto"></a>
<a id="schema_TodoEventDTO"></a>
<a id="tocStodoeventdto"></a>
<a id="tocstodoeventdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "assigneeId": 0,
  "businessId": 0,
  "eventType": "string",
  "title": "string",
  "description": "string",
  "status": "string",
  "approvalProcessId": "string",
  "approvalInstanceId": "string",
  "completedTime": {},
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID|
|assigneeId|integer(int64)|true|none||指派人ID|
|businessId|integer(int64)|false|none||关联的业务对象ID|
|eventType|string|true|none||事件类型|
|title|string|true|none||事件标题|
|description|string|false|none||事件描述|
|status|string|false|none||事件状态|
|approvalProcessId|string|false|none||审批流程ID|
|approvalInstanceId|string|false|none||审批实例ID|
|completedTime|object|false|none||完成时间|
|metadata|string|false|none||补充字段，存储额外的元数据信息|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|object|false|none||最后更新时间|
|isDeleted|boolean|true|none||是否删除：false表示正常，true表示已删除|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ResponseEntityPageTodoEventDTO">ResponseEntityPageTodoEventDTO</h2>

<a id="schemaresponseentitypagetodoeventdto"></a>
<a id="schema_ResponseEntityPageTodoEventDTO"></a>
<a id="tocSresponseentitypagetodoeventdto"></a>
<a id="tocsresponseentitypagetodoeventdto"></a>

```json
{
  "content": "new ArrayList<>()",
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "total": 0,
  "empty": true,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "totalPages": 0,
  "totalElements": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|content|[[TodoEventDTO](#schematodoeventdto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|total|integer(int64)|false|none||none|
|empty|boolean|false|none||Returns whether the current{@link Streamable} is empty.|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE",
  "ascending": true,
  "descending": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|
|ascending|boolean|false|none||Returns whether sorting for this property shall be ascending.|
|descending|boolean|false|none||Returns whether sorting for this property shall be descending.|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

<h2 id="tocS_TodoEventReq">TodoEventReq</h2>

<a id="schematodoeventreq"></a>
<a id="schema_TodoEventReq"></a>
<a id="tocStodoeventreq"></a>
<a id="tocstodoeventreq"></a>

```json
{
  "tenantId": 0,
  "assigneeId": 0,
  "eventType": "CONTRACT_APPROVAL",
  "title": "string",
  "eventStatus": "PENDING",
  "completedTimeStart": "string",
  "completedTimeEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|integer(int64)|true|none||租户ID|
|assigneeId|integer(int64)|false|none||指派人ID|
|eventType|string|false|none||事件类型|
|title|string|false|none||事件标题|
|eventStatus|string|false|none||事件状态|
|completedTimeStart|string|false|none||完成时间起始点|
|completedTimeEnd|string|false|none||完成时间结束点|

#### 枚举值

|属性|值|
|---|---|
|eventType|CONTRACT_APPROVAL|
|eventType|DECISION_APPROVAL|
|eventType|SUPPLEMENTAL_APPROVAL|
|eventType|REGULATORY_APPROVAL|
|eventType|ENTERPRISE_APPROVAL|
|eventType|VIOLATION_APPROVAL|
|eventStatus|PENDING|
|eventStatus|COMPLETED|
|eventStatus|CANCELLED|
|eventStatus|EXPIRED|

<h2 id="tocS_ResponseEntityTodoEventDTO">ResponseEntityTodoEventDTO</h2>

<a id="schemaresponseentitytodoeventdto"></a>
<a id="schema_ResponseEntityTodoEventDTO"></a>
<a id="tocSresponseentitytodoeventdto"></a>
<a id="tocsresponseentitytodoeventdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "assigneeId": 0,
  "businessId": 0,
  "eventType": "string",
  "title": "string",
  "description": "string",
  "status": "string",
  "approvalProcessId": "string",
  "approvalInstanceId": "string",
  "completedTime": {},
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID|
|assigneeId|integer(int64)|true|none||指派人ID|
|businessId|integer(int64)|false|none||关联的业务对象ID|
|eventType|string|true|none||事件类型|
|title|string|true|none||事件标题|
|description|string|false|none||事件描述|
|status|string|false|none||事件状态|
|approvalProcessId|string|false|none||审批流程ID|
|approvalInstanceId|string|false|none||审批实例ID|
|completedTime|object|false|none||完成时间|
|metadata|string|false|none||补充字段，存储额外的元数据信息|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|object|false|none||最后更新时间|
|isDeleted|boolean|true|none||是否删除：false表示正常，true表示已删除|

