import api from '@/api/index'

export default {
  // 待办事件管理
  eventsApi(paging: any, params: any, key: any) {
    switch (key) {
      case 'detail':
        return api.get(`/whiskerguardapprovalservice/api/todo/events/${params.id}`, {
        })
      case 'create':
        return api.post(`/whiskerguardapprovalservice/api/todo/events`, params)
      case 'update':
        return api.patch(`/whiskerguardapprovalservice/api/todo/events/${params.id}`, params)
      case 'delete':
        return api.delete(`/whiskerguardapprovalservice/api/todo/events/${params.id}`)
      case 'list':
      default:
        // 使用POST方式进行分页查询
        return api.post(`/whiskerguardapprovalservice/api/todo/events/page?page=${paging.page}&size=${paging.size}`, params)
    }
  },
}
