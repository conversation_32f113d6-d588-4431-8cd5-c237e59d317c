import api from '@/api/index'

export default {
  // 教材介绍
  Introduction_edit: (data: any) => api.post('Introduction/edit', data, {}),
  Introduction_list: (data: any) => api.post('Introduction/index', data, {}),
  Introduction_delete: (data: any) => api.post('Introduction/delete', data, {}),
  Introduction_info: (data: any) => api.post('Introduction/info', data, {}),

  // 教材培训
  Dynamics_edit: (data: any) => api.post('Dynamics/edit', data, {}),
  Dynamics_list: (data: any) => api.post('Dynamics/index', data, {}),
  Dynamics_delete: (data: any) => api.post('Dynamics/delete', data, {}),
  Dynamics_info: (data: any) => api.post('Dynamics/info', data, {}),

  // 通用分类
  group_edit: (data: any) => api.post('group/edit', data, {}),
  group_list: (data: any) => api.post('group/index', data, {}),
  group_delete: (data: any) => api.post('group/delete', data, {}),
  group_info: (data: any) => api.post('group/info', data, {}),

  // 会员信息
  Sluser_edit: (data: any) => api.post('Sluser/edit', data, {}),
  Sluser_list: (data: any) => api.post('Sluser/index', data, {}),
  Sluser_delete: (data: any) => api.post('Sluser/delete', data, {}),
  Sluser_info: (data: any) => api.post('Sluser/info', data, {}),

  // 书本
  book_edit: (data: any) => api.post('book/edit', data, {}),
  book_list: (data: any) => api.post('book/index', data, {}),
  book_delete: (data: any) => api.post('book/delete', data, {}),
  book_info: (data: any) => api.post('book/info', data, {}),

  // 书本单元
  BookUnit_edit: (data: any) => api.post('BookUnit/edit', data, {}),
  BookUnit_list: (data: any) => api.post('BookUnit/index', data, {}),
  BookUnit_delete: (data: any) => api.post('BookUnit/delete', data, {}),
  BookUnit_info: (data: any) => api.post('BookUnit/info', data, {}),

  // 书本单元内容
  BookUnitContent_edit: (data: any) => api.post('BookUnitContent/edit', data, {}),
  BookUnitContent_list: (data: any) => api.post('BookUnitContent/index', data, {}),
  BookUnitContent_delete: (data: any) => api.post('BookUnitContent/delete', data, {}),
  BookUnitContent_info: (data: any) => api.post('BookUnitContent/info', data, {}),

  // 生成码
  ActivationCode_edit: (data: any) => api.post('ActivationCode/edit', data, {}),
  ActivationCode_list: (data: any) => api.post('ActivationCode/index', data, {}),
  ActivationCode_delete: (data: any) => api.post('ActivationCode/delete', data, {}),
  ActivationCode_info: (data: any) => api.post('ActivationCode/info', data, {}),

  // 生成码记录
  CodeLog_edit: (data: any) => api.post('CodeLog/edit', data, {}),
  CodeLog_list: (data: any) => api.post('CodeLog/index', data, {}),
  CodeLog_delete: (data: any) => api.post('CodeLog/delete', data, {}),
  CodeLog_info: (data: any) => api.post('CodeLog/info', data, {}),

  // 激活码
  Code_edit: (data: any) => api.post('Code/edit', data, {}),
  Code_list: (data: any) => api.post('Code/index', data, {}),
  Code_delete: (data: any) => api.post('Code/delete', data, {}),
  Code_info: (data: any) => api.post('Code/info', data, {}),

  // 激活码导出
  CodeExportExcel: (data: any) => api.post('Export/CodeExportExcel', data, {}),
  RichText_info: (data: any) => api.post('RichText/info', data, {}),
  RichText_edit: (data: any) => api.post('RichText/edit', data, {}),

  // 轮播
  Banner_edit: (data: any) => api.post('Banner/edit', data, {}),
  Banner_list: (data: any) => api.post('Banner/index', data, {}),
  Banner_delete: (data: any) => api.post('Banner/delete', data, {}),
  Banner_info: (data: any) => api.post('Banner/info', data, {}),
}
