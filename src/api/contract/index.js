import api from '@/api/index'

export default {
  // 合同审查
  contractReview(paging, params, key) {
    switch (key) {
      case 'info': // 获取合同审查
        return api.get(`/whiskerguardcontractservice/api/contract/reviews/${params.id}`, {
        })
      case 'create': // 创建合同审查
        return api.post(`/whiskerguardcontractservice/api/contract/reviews`, params)
      case 'update': // 更新合同审查
        return api.patch(`/whiskerguardcontractservice/api/contract/reviews/${params.id}`, params)
      case 'publish': // 批量发布合同审查
        return api.post(`/whiskerguardcontractservice/api/contract/reviews/publish/batch`, params)
      case 'delete': // 删除合同审查
        return api.delete(`/whiskerguardcontractservice/api/contract/reviews/${params.id}`)
      default: // 分页查询合同审查
        return api.post(`/whiskerguardcontractservice/api/contract/reviews/page?page=${paging.page ? paging.page - 1 : 0}&size=${paging.limit ? paging.limit : 10}`, params)
    }
  },

  // 合同审查列表
  contractlist(paging, params) {
    const page = paging?.page
    const size = paging?.size
    return api.post(`/whiskerguardcontractservice/api/contract/reviews/page?page=${page - 1}&size=${size}`, params)
  },

  // 合同审查详情查询
  contractDetail(id) {
    return api.get(`/whiskerguardcontractservice/api/contract/reviews/${id}`)
  },

  // 合同审查搜索
  contractSearch(params) {
    return api.post('/whiskerguardcontractservice/api/contract/reviews/search', params)
  },

  // 获取合规流程
  getComplianceProcess(params) {
    return api.get(
      `/whiskerguardcontractservice/api/compliance/reviews/process`,
      { params },
    )
  },

  // 根据流程类型获取审批流程
  getApprovalProcess(processType) {
    return api.get(
            `/whiskerguardapprovalservice/api/approval/processes/processType?processType=${processType}`,
            {})
  },

  // 创建合同审查记录（就是当complianceReview不为null时进入的页面提交的接口）
  createReview(params) {
    return api.post(
            `whiskerguardcontractservice/api/compliance/reviews`,
            params, 'post')
  },

  // ai智能审查
  aiContract(contractId, params) {
    return api.get(
      `/whiskerguardcontractservice/api/contract/message/review/ai/review/${contractId}`,
      { params },
    )
  },

  // 查询所有审查记录
  queryAllReviews(contractId, params = {}) {
    return api.get(
      `/whiskerguardcontractservice/api/contract/message/review/list/${contractId}`,
      { params },
    )
  },

  // 合同通过或者不通过
  contractMessageReview(params = {}) {
    return api.post(`/whiskerguardcontractservice/api/contract/message/review`,
      params,
    )
  },
}
