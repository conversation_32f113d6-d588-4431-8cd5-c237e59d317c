import api from '@/api/index'

export default {
  // 课程管理 - 课程信息管理接口

  courseInfo(paging: any, params: any, key: any) {
    switch (key) {
      case 'info': // 获取课程详情
        return api.get(`/whiskerguardtrainingservice/api/course/infos/${params.id}`)
      case 'create': // 创建课程
        return api.post(`/whiskerguardtrainingservice/api/course/infos`, params)
      case 'update': // 更新课程
        return api.put(`/whiskerguardtrainingservice/api/course/infos/${params.id}`, params)
      case 'delete': // 删除课程
        return api.delete(`/whiskerguardtrainingservice/api/course/infos/${params.id}`)
      case 'query': // 根据条件查询课程
        return api.post(`/whiskerguardtrainingservice/api/course/infos/query`, {
          courseCode: params.courseCode || '',
          courseName: params.courseName || '',
          courseType: params.courseType || '',
          trainingTheme: params.trainingTheme || '',
          difficultyLevel: params.difficultyLevel || '',
          status: params.status || '',
          instructor: params.instructor || '',
          startDate: params.startDate || '',
          endDate: params.endDate || '',
          searchTerm: params.searchTerm || '',
          page: paging.page || 0,
          size: paging.size || 10,
          sort: params.sort || '',
        })
      default: // 分页查询课程（默认）
        return api.post(`/whiskerguardtrainingservice/api/course/infos/query?page=${paging.page}&size=${paging.size}`, {
          // courseCode: params.courseCode || '',
          // courseName: params.courseName || '',
          // courseType: params.courseType || '',
          // trainingTheme: params.trainingTheme || '',
          // difficultyLevel: params.difficultyLevel || '',
          // status: params.status || '',
          // instructor: params.instructor || '',
          // startDate: params.startDate || '',
          // endDate: params.endDate || '',
          // searchTerm: params.searchTerm || '',
          // page: paging.page || 0,
          // size: paging.size || 10,
          // sort: params.sort || '',
        })
    }
  },

  // 保留原有的courseReview方法以兼容现有代码
  courseReview(paging: any, params: any, key: any) {
    return this.courseInfo(paging, params, key)
  },

  // 简化调用的方法
  system(paging: any, params: any = {}, key: any = 'default') {
    return this.courseInfo(paging, params, key)
  },
}
