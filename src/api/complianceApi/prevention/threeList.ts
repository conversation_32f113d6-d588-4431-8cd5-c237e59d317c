import api from '@/api/index'

export default {
  // 获取风险清单列表
  getRiskIdentificationList: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/page', params)
  },

  // 更新合规风险识别清单
  updateComplianceRisk: (params: any) => {
    return api.put('/compliancelistservice/api/compliance/risk/list/update', params)
  },

  // 获取合规风险识别清单详情
  getComplianceRiskDetail: (complianceRiskMainId: string | number) => {
    return api.get(`/compliancelistservice/api/compliance/risk/list/${complianceRiskMainId}`)
  },

  // 保存合规风险识别清单草稿
  saveComplianceRiskDraft: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/draft', params)
  },
  // 提交合规风险识别清单
  submitComplianceRisk: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/submit', params)
  },

  // 导出合规风险识别清单
  exportComplianceRiskList: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/export', params, {
      responseType: 'blob',
    })
  },

  // 下载合规风险识别清单导入模板
  downloadComplianceRiskTemplate: () => {
    return api.get('/compliancelistservice/api/compliance/risk/list/template', {
      responseType: 'blob',
    })
  },

  // 导入合规风险识别清单
  importComplianceRiskList: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/compliancelistservice/api/compliance/risk/list/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 下载职责清单导入模板（基于接口文档）
  downloadDutyPositionTemplate: () => {
    return api.get('/compliancelistservice/api/duty/positions/template', {
      responseType: 'blob',
    })
  },

  // 导入职责清单（基于接口文档）
  importDutyPositionList: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/compliancelistservice/api/duty/positions/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 职责清单
  // 分页获取职责主数据列表
  getDutyMainList: (params: any) => {
    return api.post('/compliancelistservice/api/duty/positions/page',
      params,
    )
  },

  // 创建职责主数据
  createDutyMain: (data: any) => {
    return api.post('/compliancelistservice/api/duty/positions/create', data)
  },

  // 获取职责主数据详情
  getDutyMainDetail: (id: number) => {
    return api.get(`/compliancelistservice/api/duty/positions/${id}`)
  },

  // 更新职责主数据
  updateDutyMain: (id: number, data: any) => {
    return api.put(`/compliancelistservice/api/duty/positions/update`, data)
  },
  // 保存草稿
  draftDuty(params: any) {
    return api.post('/compliancelistservice/api/duty/positions/submit', params)
  },
  // 提交职责清单
  submitDuty(params: any) {
    return api.post('/compliancelistservice/api/duty/positions/submit', params)
  },

  // 删除职责主数据
  deleteDutyMain: (id: number) => {
    return api.delete(`/compliancelistservice/api/duty/mains/${id}`)
  },

  // 流程清单
  getProcessList: (params: any) => {
    return api.post('/compliancelistservice/api/v2/biz/process/list/page', params)
  },
  // 创建流程清单草稿
  draftProcess: (params: any) => {
    return api.post('/compliancelistservice/api/v2/biz/process/list/draft', params)
  },
  // 提交流程清单
  submitProcess: (params: any) => {
    return api.post('/compliancelistservice/api/v2/biz/process/list/submit', params)
  },
  // 获取流程清单详情
  getProcessDetail: (id: number) => {
    return api.get(`/compliancelistservice/api/v2/biz/process/list/${id}/details`)
  },
  // 更新流程清单
  updateProcess: (id: any, params: any) => {
    return api.put(`/compliancelistservice/api/v2/biz/process/list/${id}`, params)
  },

  // 流程ai智能分析
  getProcessAI: (params: any) => {
    return api.post(`/compliancelistservice/api/v2/biz/process/ai/analyze`, params)
  },

  // 删除流程清单
  deleteProcess: (id: number) => {
    return api.delete(`/compliancelistservice/api/v2/biz/process/${id}`)
  },

  // 导出重点岗位职责清单
  exportDutyPositionList: (params: any) => {
    return api.post('/compliancelistservice/api/duty/positions/export', params, {
      responseType: 'blob',
    })
  },

  // 导出业务流程管控清单
  exportProcessList: (params: any) => {
    return api.post('/compliancelistservice/api/v2/biz/process/list/export', params, {
      responseType: 'blob',
    })
  },

  // 下载业务流程管控清单导入模板
  downloadProcessTemplate: () => {
    return api.get('/compliancelistservice/api/v2/biz/process/template', {
      responseType: 'blob',
    })
  },

  // 导入业务流程管控清单
  importProcessList: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/compliancelistservice/api/v2/biz/process/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
}
