import api from '@/api/index'

export default {
  // 课程管理
  system(params: any, key: any) {
    switch (key) {
      case 'detail': // 获取课程详情
        return api.get(`/whiskerguardtrainingservice/api/course/infos/${params.id}`, {
          params,
        })
      case 'info': // 兼容旧版获取课程详情
        return api.get(`/whiskerguardtrainingservice/api/course/infos/${params.id}`, {
          params,
        })
      case 'create': // 创建课程
        return api.post(`/whiskerguardtrainingservice/api/course/infos`, params)
      case 'update': // 更新课程
        return api.put(`/whiskerguardtrainingservice/api/course/infos/${params.id}`, params)
      case 'delete': // 删除课程
        return api.delete(`/whiskerguardtrainingservice/api/course/infos/${params.id}`)
      default: // 分页查询课程
        return api.post(`/whiskerguardtrainingservice/api/course/infos/query`, params)
    }
  },
}
