import api from '@/api/index'

export default {
  // 重大决策审查
  decisionReview(paging: any, params: any, key: any) {
    switch (key) {
      case 'info': // 获取重大决策审查
        return api.get(`/whiskerguardcontractservice/api/decision/reviews/${params.id}`, {
        })
      case 'create': // 创建重大决策审查
        return api.post(`/whiskerguardcontractservice/api/decision/reviews`, params)
      case 'update': // 更新重大决策审查
        return api.post(`/whiskerguardcontractservice/api/decision/reviews/${params.id}`, params)
      case 'publish': // 批量发布重大决策审查
        return api.post(`/whiskerguardcontractservice/api/decision/reviews/publish/batch`, params)
      case 'delete': // 删除重大决策审查
        return api.delete(`/whiskerguardcontractservice/api/decision/reviews/${params.id}`)
      default: // 分页查询重大决策审查
        return api.post(`/whiskerguardcontractservice/api/decision/reviews/page?page=${paging.page ? paging.page - 1 : 0}&size=${paging.limit ? paging.limit : 10}`, params)
    }
  },
}
