import api from '@/api/index'

export default {
  // 用户管理
  userApi(paging: any, params: any, key: any) {
    switch (key) {
      case 'detail':
        return api.get(`/whiskerguardorgservice/api/employees/${params.id}`, {
        })
      case 'create':
        return api.post(`/whiskerguardorgservice/api/employees`, params)
      case 'update':
        return api.patch(`/whiskerguardorgservice/api/employees/${params.id}`, params)
      case 'delete':
        return api.delete(`/whiskerguardorgservice/api/employees/${params.id}`)
      default:
        return api.get(`/whiskerguardorgservice/api/employees?page=${paging.page}&size=${paging.size}`, {})
    }
  },
}
