{"modules": {"home": {"name": "multilevelMenuExample", "path": "/multilevel_menu_example", "component": "Layout", "meta": {"title": "一体", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "page1", "path": "/one/1", "component": "() => import('@/views/hegui/one/system.vue')", "meta": {"title": "制度管理"}}, {"name": "multilevelMenuExample2", "path": "/one/2", "component": "() => import('@/views/hegui/one/database.vue')", "meta": {"title": "合规数据库"}}]}, "page2": {"name": "multilevelMenuExample", "path": "/multilevel_menu_example", "component": "Layout", "meta": {"title": "预防之翼", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "multilevelMenuExample1", "path": "page", "component": "() => import('@/views/multilevel_menu_example/page.vue')", "meta": {"title": "导航1"}}, {"name": "multilevelMenuExample2", "path": "level2", "meta": {"title": "导航2"}, "children": [{"name": "multilevelMenuExample2-1", "path": "page", "component": "() => import('@/views/multilevel_menu_example/level2/page.vue')", "meta": {"title": "导航2-1"}}, {"name": "multilevelMenuExample2-2", "path": "level3", "meta": {"title": "导航2-2"}, "children": [{"name": "multilevelMenuExample2-2-1", "path": "page1", "component": "() => import('@/views/multilevel_menu_example/level2/level3/page1.vue')", "meta": {"title": "导航2-2-1"}}, {"name": "multilevelMenuExample2-2-2", "path": "page2", "component": "() => import('@/views/multilevel_menu_example/level2/level3/page2.vue')", "meta": {"title": "导航2-2-2"}}]}]}]}, "page3": {"name": "multilevelMenuExample", "path": "/multilevel_menu_example", "component": "Layout", "meta": {"title": "监控之翼", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "multilevelMenuExample1", "path": "page", "component": "() => import('@/views/multilevel_menu_example/page.vue')", "meta": {"title": "导航1"}}, {"name": "multilevelMenuExample2", "path": "level2", "meta": {"title": "导航2"}, "children": [{"name": "multilevelMenuExample2-1", "path": "page", "component": "() => import('@/views/multilevel_menu_example/level2/page.vue')", "meta": {"title": "导航2-1"}}, {"name": "multilevelMenuExample2-2", "path": "level3", "meta": {"title": "导航2-2"}, "children": [{"name": "multilevelMenuExample2-2-1", "path": "page1", "component": "() => import('@/views/multilevel_menu_example/level2/level3/page1.vue')", "meta": {"title": "导航2-2-1"}}, {"name": "multilevelMenuExample2-2-2", "path": "page2", "component": "() => import('@/views/multilevel_menu_example/level2/level3/page2.vue')", "meta": {"title": "导航2-2-2"}}]}]}]}, "page4": {"name": "multilevelMenuExample", "path": "/multilevel_menu_example", "component": "Layout", "meta": {"title": "应对之翼", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "multilevelMenuExample1", "path": "page", "component": "() => import('@/views/multilevel_menu_example/page.vue')", "meta": {"title": "导航1"}}, {"name": "multilevelMenuExample2", "path": "level2", "meta": {"title": "导航2"}, "children": [{"name": "multilevelMenuExample2-1", "path": "page", "component": "() => import('@/views/multilevel_menu_example/level2/page.vue')", "meta": {"title": "导航2-1"}}, {"name": "multilevelMenuExample2-2", "path": "level3", "meta": {"title": "导航2-2"}, "children": [{"name": "multilevelMenuExample2-2-1", "path": "page1", "component": "() => import('@/views/multilevel_menu_example/level2/level3/page1.vue')", "meta": {"title": "导航2-2-1"}}, {"name": "multilevelMenuExample2-2-2", "path": "page2", "component": "() => import('@/views/multilevel_menu_example/level2/level3/page2.vue')", "meta": {"title": "导航2-2-2"}}]}]}]}, "monitor": {"cockpit": {"name": "/monitor/cockpit", "path": "/monitor/cockpit", "component": "Layout", "meta": {"title": "合规驾驶舱", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "/monitor/cockpit/1", "path": "/monitor/cockpit/1", "component": "() => import('@/views/hegui/monitor/cockpit/realTimeMonitoring/index.vue')", "meta": {"title": "实时监控"}, "children": [{"name": "/monitor/cockpit/1/detail", "path": "/monitor/cockpit/1/detail", "component": "() => import('@/views/hegui/monitor/cockpit/realTimeMonitoring/detail.vue')", "meta": {"title": "实时监控详情", "sidebar": false, "breadcrumb": false}}]}, {"name": "/monitor/cockpit/2", "path": "/monitor/cockpit/2", "component": "() => import('@/views/hegui/monitor/cockpit/analysisReport/index.vue')", "meta": {"title": "风险分析报告"}}, {"name": "/monitor/cockpit/3", "path": "/monitor/cockpit/3", "component": "() => import('@/views/hegui/monitor/cockpit/earlyWarning/index.vue')", "meta": {"title": "预警管理"}, "children": [{"name": "/monitor/cockpit/3/detail", "path": "/monitor/cockpit/3/detail", "component": "() => import('@/views/hegui/monitor/cockpit/earlyWarning/detail.vue')", "meta": {"title": "实时监控详情", "sidebar": false, "breadcrumb": false}}]}]}, "examination": {"name": "/monitor/examination", "path": "/monitor/examination", "component": "Layout", "meta": {"title": "合规审查", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "/monitor/examination/contractReview", "path": "/monitor/examination/contractReview", "component": "() => import('@/views/hegui/monitor/examination/contractReview/index.vue')", "meta": {"title": "合同审查"}, "children": [{"name": "/monitor/examination/contractReview/detail", "path": "/monitor/examination/contractReview/detail", "component": "() => import('@/views/hegui/monitor/examination/contractReview/detail.vue')", "meta": {"title": "合同审查详情", "sidebar": false, "breadcrumb": false}}, {"name": "/monitor/examination/contractReview/addEdit", "path": "/monitor/examination/contractReview/addEdit", "component": "() => import('@/views/hegui/monitor/examination/contractReview/addEdit.vue')", "meta": {"title": "合同审查新增修改", "sidebar": false, "breadcrumb": false}}]}, {"name": "/monitor/examination/decisionReview", "path": "/monitor/examination/decisionReview", "component": "() => import('@/views/hegui/monitor/examination/decisionReview/index.vue')", "meta": {"title": "重大决策审查"}, "children": [{"name": "/monitor/examination/decisionReview/detail", "path": "/monitor/examination/decisionReview/detail", "component": "() => import('@/views/hegui/monitor/examination/decisionReview/detail.vue')", "meta": {"title": "重大决策审查详情", "sidebar": false, "breadcrumb": false}}, {"name": "/monitor/examination/decisionReview/addEdit", "path": "/monitor/examination/decisionReview/addEdit", "component": "() => import('@/views/hegui/monitor/examination/decisionReview/addEdit.vue')", "meta": {"title": "重大决策审查新增修改", "sidebar": false, "breadcrumb": false}}]}, {"name": "/monitor/examination/ohter", "path": "/monitor/examination/ohter", "component": "() => import('@/views/hegui/monitor/examination/otherReviews/index.vue')", "meta": {"title": "其他审查"}, "children": [{"name": "/monitor/examination/ohter/detail", "path": "/monitor/examination/ohter/detail", "component": "() => import('@/views/hegui/monitor/examination/otherReviews/detail.vue')", "meta": {"title": "其他审查详情", "sidebar": false, "breadcrumb": false}}, {"name": "/monitor/examination/ohter/addEdit", "path": "/monitor/examination/ohter/addEdit", "component": "() => import('@/views/hegui/monitor/examination/otherReviews/addEdit.vue')", "meta": {"title": "其他审查新增修改", "sidebar": false, "breadcrumb": false}}]}]}, "intelligentReporting": {"name": "/monitor/intelligentReporting", "path": "/monitor/intelligentReporting", "component": "Layout", "meta": {"title": "智能举报管理", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "/monitor/intelligentReporting/reportAcceptance", "path": "/monitor/intelligentReporting/reportAcceptance", "component": "() => import('@/views/hegui/monitor/intelligentReporting/reportAcceptance/index.vue')", "meta": {"title": "举报受理"}, "children": [{"name": "/monitor/intelligentReporting/reportAcceptance/detail", "path": "/monitor/intelligentReporting/reportAcceptance/detail", "component": "() => import('@/views/hegui/monitor/intelligentReporting/reportAcceptance/detail.vue')", "meta": {"title": "举报详情", "sidebar": false, "breadcrumb": false}}]}, {"name": "/monitor/intelligentReporting/3", "path": "/monitor/intelligentReporting/3", "component": "() => import('@/views/hegui/monitor/intelligentReporting/reportStatisticsAndAnalysis/index.vue')", "meta": {"title": "举报统计分析"}}, {"name": "/monitor/intelligentReporting/reportingPolicyManagement", "path": "/monitor/intelligentReporting/reportingPolicyManagement", "component": "() => import('@/views/hegui/monitor/intelligentReporting/reportingPolicyManagement/index.vue')", "meta": {"title": "举报政策管理"}, "children": [{"name": "/monitor/intelligentReporting/reportingPolicyManagement/detail", "path": "/monitor/intelligentReporting/reportingPolicyManagement/detail", "component": "() => import('@/views/hegui/monitor/intelligentReporting/reportingPolicyManagement/detail.vue')", "meta": {"title": "举报政策管理详情", "sidebar": false, "breadcrumb": false}}, {"name": "/monitor/intelligentReporting/reportingPolicyManagement/addEdit", "path": "/monitor/intelligentReporting/reportingPolicyManagement/addEdit", "component": "() => import('@/views/hegui/monitor/intelligentReporting/reportingPolicyManagement/addEdit.vue')", "meta": {"title": "举报政策管理详情", "sidebar": false, "breadcrumb": false}}]}]}}, "one": {"database": {"name": "database", "path": "/database", "component": "Layout", "meta": {"title": "法规库", "icon": "i-heroicons-solid:menu-alt-cases"}, "children": [{"name": "database/laws", "path": "/database/laws", "component": "() => import('@/views/hegui/one/database/regulatoryDatabase.vue')", "meta": {"title": "法规库"}, "children": [{"name": "/database/laws/detail", "path": "/database/laws/detail", "component": "() => import('@/views/hegui/one/database/regulatoryDatabaseMode/detail.vue')", "meta": {"title": "法规详情", "sidebar": false, "breadcrumb": false}}, {"name": "/database/laws/addEdit", "path": "/database/laws/addEdit", "component": "() => import('@/views/hegui/one/database/regulatoryDatabaseMode/addEdit.vue')", "meta": {"title": "新增法规", "sidebar": false, "breadcrumb": false}}]}, {"name": "database/duty", "path": "/database/duty", "component": "() => import('@/views/hegui/one/database/complianceObligationLibrary.vue')", "meta": {"title": "合规义务库"}, "children": [{"name": "/database/duty/detail", "path": "/database/duty/detail", "component": "() => import('@/views/hegui/one/database/complianceObligationLibraryMode/detail.vue')", "meta": {"title": "义务详情", "sidebar": false, "breadcrumb": false}}, {"name": "/database/duty/addEdit", "path": "/database/duty/addEdit", "component": "() => import('@/views/hegui/one/database/complianceObligationLibraryMode/addEdit.vue')", "meta": {"title": "新增合规义务", "sidebar": false, "breadcrumb": false}}]}, {"name": "database/cases", "path": "/database/cases", "component": "() => import('@/views/hegui/one/database/complianceCaseLibrary.vue')", "meta": {"title": "合规案例库"}, "children": [{"name": "/database/cases/detail", "path": "/database/cases/detail", "component": "() => import('@/views/hegui/one/database/complianceCaseLibraryMode/detail.vue')", "meta": {"title": "案例详情", "sidebar": false, "breadcrumb": false}}, {"name": "/database/cases/addEdit", "path": "/database/cases/addEdit", "component": "() => import('@/views/hegui/one/database/complianceCaseLibraryMode/addEdit.vue')", "meta": {"title": "新增案例", "sidebar": false, "breadcrumb": false}}]}, {"name": "/system/role", "path": "/system/role", "component": "() => import('@/views/system/role/index.vue')", "meta": {"title": "权限树"}}]}, "system": {"name": "multilevelMenuExample", "path": "/one", "component": "Layout", "meta": {"title": "制度管理", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "/one/systemManagement/index", "path": "/one/systemManagement/index", "component": "() => import('@/views/hegui/one/system/systemManagement/index.vue')", "meta": {"title": "制度库管理"}, "children": [{"name": "/one/systemManagement/detail", "path": "/one/systemManagement/detail", "component": "() => import('@/views/hegui/one/system/systemManagement/detail.vue')", "meta": {"title": "制度详情", "sidebar": false, "breadcrumb": false}}, {"name": "/one/systemManagement/addEdit", "path": "/one/systemManagement/addEdit", "component": "() => import('@/views/hegui/one/system/systemManagement/addEdit.vue')", "meta": {"title": "新增制度", "sidebar": false, "breadcrumb": false}}]}, {"name": "/one/regulatoryConversion/index", "path": "/one/regulatoryConversion/index", "component": "() => import('@/views/hegui/one/system/regulatoryConversion/index.vue')", "meta": {"title": "法规转化"}, "children": [{"name": "/one/regulatoryConversion/detail", "path": "/one/regulatoryConversion/detail", "component": "() => import('@/views/hegui/one/system/regulatoryConversion/detail.vue')", "meta": {"title": "制度详情", "sidebar": false, "breadcrumb": false}}, {"name": "/one/regulatoryConversion/addEdit", "path": "/one/regulatoryConversion/addEdit", "component": "() => import('@/views/hegui/one/system/regulatoryConversion/addEdit.vue')", "meta": {"title": "新增制度", "sidebar": false, "breadcrumb": false}}]}, {"name": "system/review", "path": "/system/review", "component": "() => import('@/views/hegui/one/system/systemAdditionAndReview.vue')", "meta": {"title": "制度新增与审查"}, "children": [{"name": "/system/review/detail", "path": "/system/review/detail", "component": "() => import('@/views/hegui/one/system/systemAdditionAndReview/detail.vue')", "meta": {"title": "制度审查详情", "sidebar": false, "breadcrumb": false}}, {"name": "/system/review/report", "path": "/system/review/report", "component": "() => import('@/views/hegui/one/system/systemAdditionAndReview/report.vue')", "meta": {"title": "制度审查报告", "sidebar": false, "breadcrumb": false}}, {"name": "/system/review/addEdit", "path": "/system/review/addEdit", "component": "() => import('@/views/hegui/one/system/systemAdditionAndReview/addEdit.vue')", "meta": {"title": "新增审核记录", "sidebar": false, "breadcrumb": false}}]}]}}, "personalCenter": {"messageNotification": {"name": "messageNotification", "path": "/messageNotification", "component": "Layout", "meta": {"title": "消息通知", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "messageNotification/index", "path": "/messageNotification/index", "component": "() => import('@/views/hegui/personalCenter/messageNotification/index.vue')", "meta": {"title": "消息通知"}}]}, "personalInformation": {"name": "personalInformation", "path": "/personalInformation", "component": "Layout", "meta": {"title": "个人信息", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "personalInformation/index", "path": "/personalInformation/index", "component": "() => import('@/views/hegui/personalCenter/personalInformation/index.vue')", "meta": {"title": "个人信息"}}]}, "toDoList": {"name": "toDoList", "path": "/toDoList", "component": "Layout", "meta": {"title": "待办事项", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "toDoList/index", "path": "/toDoList/index", "component": "() => import('@/views/hegui/personalCenter/toDoList/index.vue')", "meta": {"title": "待办事项"}}]}}, "prevention": {"organizationManagement": {"name": "organizationManagement", "path": "/organizationManagement", "component": "Layout", "meta": {"title": "合规组织管理", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "organizationManagement/index", "path": "/organizationManagement/index", "component": "() => import('@/views/hegui/prevention/organizationManagement/index.vue')", "meta": {"title": "合规组织管理"}, "children": [{"name": "/organizationManagement/detail", "path": "/organizationManagement/detail", "component": "() => import('@/views/hegui/prevention/organizationManagement/detail.vue')", "meta": {"title": "合规组织详情", "sidebar": false, "breadcrumb": false}}, {"name": "/organizationManagement/addEdit", "path": "/organizationManagement/addEdit", "component": "() => import('@/views/hegui/prevention/organizationManagement/addEdit.vue')", "meta": {"title": "新增合规组织", "sidebar": false, "breadcrumb": false}}]}]}, "threeListManagement": {"name": "threeListManagement", "path": "/threeListManagement", "component": "Layout", "meta": {"title": "三张清单管理", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "threeListManagement/riskList", "path": "/threeListManagement/riskList", "component": "() => import('@/views/hegui/prevention/threeListManagement/riskList/index.vue')", "meta": {"title": "风险清单"}, "children": [{"name": "/threeListManagement/riskList/detail", "path": "/threeListManagement/riskList/detail", "component": "() => import('@/views/hegui/prevention/threeListManagement/riskList/detail.vue')", "meta": {"title": "风险清单详情", "sidebar": false, "breadcrumb": false}}, {"name": "/threeListManagement/riskList/addEdit", "path": "/threeListManagement/riskList/addEdit", "component": "() => import('@/views/hegui/prevention/threeListManagement/riskList/addEdit.vue')", "meta": {"title": "新增风险清单", "sidebar": false, "breadcrumb": false}}]}, {"name": "threeListManagement/obligationList", "path": "/threeListManagement/obligationList", "component": "() => import('@/views/hegui/prevention/threeListManagement/obligationList/index.vue')", "meta": {"title": "义务清单"}, "children": [{"name": "/threeListManagement/obligationList/detail", "path": "/threeListManagement/obligationList/detail", "component": "() => import('@/views/hegui/prevention/threeListManagement/obligationList/detail.vue')", "meta": {"title": "义务清单详情", "sidebar": false, "breadcrumb": false}}, {"name": "/threeListManagement/obligationList/addEdit", "path": "/threeListManagement/obligationList/addEdit", "component": "() => import('@/views/hegui/prevention/threeListManagement/obligationList/addEdit.vue')", "meta": {"title": "新增义务清单", "sidebar": false, "breadcrumb": false}}]}, {"name": "threeListManagement/measuresList", "path": "/threeListManagement/measuresList", "component": "() => import('@/views/hegui/prevention/threeListManagement/measuresList/index.vue')", "meta": {"title": "措施清单"}, "children": [{"name": "/threeListManagement/measuresList/detail", "path": "/threeListManagement/measuresList/detail", "component": "() => import('@/views/hegui/prevention/threeListManagement/measuresList/detail.vue')", "meta": {"title": "措施清单详情", "sidebar": false, "breadcrumb": false}}, {"name": "/threeListManagement/measuresList/addEdit", "path": "/threeListManagement/measuresList/addEdit", "component": "() => import('@/views/hegui/prevention/threeListManagement/measuresList/addEdit.vue')", "meta": {"title": "新增措施清单", "sidebar": false, "breadcrumb": false}}]}]}, "training": {"name": "training", "path": "/training", "component": "Layout", "meta": {"title": "合规培训", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "training/trainingPlan", "path": "/training/trainingPlan", "component": "() => import('@/views/hegui/prevention/training/trainingPlan/index.vue')", "meta": {"title": "培训计划"}, "children": [{"name": "/training/trainingPlan/detail", "path": "/training/trainingPlan/detail", "component": "() => import('@/views/hegui/prevention/training/trainingPlan/detail.vue')", "meta": {"title": "培训计划详情", "sidebar": false, "breadcrumb": false}}, {"name": "/training/trainingPlan/addEdit", "path": "/training/trainingPlan/addEdit", "component": "() => import('@/views/hegui/prevention/training/trainingPlan/addEdit.vue')", "meta": {"title": "新增培训计划", "sidebar": false, "breadcrumb": false}}]}, {"name": "training/trainingImplementation", "path": "/training/trainingImplementation", "component": "() => import('@/views/hegui/prevention/training/trainingImplementation/index.vue')", "meta": {"title": "培训实施"}, "children": [{"name": "/training/trainingImplementation/detail", "path": "/training/trainingImplementation/detail", "component": "() => import('@/views/hegui/prevention/training/trainingImplementation/detail.vue')", "meta": {"title": "培训实施详情", "sidebar": false, "breadcrumb": false}}, {"name": "/training/trainingImplementation/addEdit", "path": "/training/trainingImplementation/addEdit", "component": "() => import('@/views/hegui/prevention/training/trainingImplementation/addEdit.vue')", "meta": {"title": "新增培训实施", "sidebar": false, "breadcrumb": false}}]}, {"name": "training/trainingEvaluation", "path": "/training/trainingEvaluation", "component": "() => import('@/views/hegui/prevention/training/trainingEvaluation/index.vue')", "meta": {"title": "培训评估"}, "children": [{"name": "/training/trainingEvaluation/detail", "path": "/training/trainingEvaluation/detail", "component": "() => import('@/views/hegui/prevention/training/trainingEvaluation/detail.vue')", "meta": {"title": "培训评估详情", "sidebar": false, "breadcrumb": false}}, {"name": "/training/trainingEvaluation/addEdit", "path": "/training/trainingEvaluation/addEdit", "component": "() => import('@/views/hegui/prevention/training/trainingEvaluation/addEdit.vue')", "meta": {"title": "新增培训评估", "sidebar": false, "breadcrumb": false}}]}]}}, "respond": {"accountability": {"name": "accountability", "path": "/accountability", "component": "Layout", "meta": {"title": "责任追究处理", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "accountability/handlingMeasures", "path": "/accountability/handlingMeasures", "component": "() => import('@/views/hegui/respond/accountability/handlingMeasures/index.vue')", "meta": {"title": "处理措施"}, "children": [{"name": "/accountability/handlingMeasures/detail", "path": "/accountability/handlingMeasures/detail", "component": "() => import('@/views/hegui/respond/accountability/handlingMeasures/detail.vue')", "meta": {"title": "处理措施详情", "sidebar": false, "breadcrumb": false}}, {"name": "/accountability/handlingMeasures/addEdit", "path": "/accountability/handlingMeasures/addEdit", "component": "() => import('@/views/hegui/respond/accountability/handlingMeasures/addEdit.vue')", "meta": {"title": "新增处理措施", "sidebar": false, "breadcrumb": false}}]}, {"name": "accountability/processingResults", "path": "/accountability/processingResults", "component": "() => import('@/views/hegui/respond/accountability/processingResults/index.vue')", "meta": {"title": "处理结果"}, "children": [{"name": "/accountability/processingResults/detail", "path": "/accountability/processingResults/detail", "component": "() => import('@/views/hegui/respond/accountability/processingResults/detail.vue')", "meta": {"title": "处理结果详情", "sidebar": false, "breadcrumb": false}}]}, {"name": "accountability/rectificationTracking", "path": "/accountability/rectificationTracking", "component": "() => import('@/views/hegui/respond/accountability/rectificationTracking/index.vue')", "meta": {"title": "整改跟踪"}, "children": [{"name": "/accountability/rectificationTracking/detail", "path": "/accountability/rectificationTracking/detail", "component": "() => import('@/views/hegui/respond/accountability/rectificationTracking/detail.vue')", "meta": {"title": "整改跟踪详情", "sidebar": false, "breadcrumb": false}}]}]}, "improveAndOptimize": {"name": "improveAndOptimize", "path": "/improveAndOptimize", "component": "Layout", "meta": {"title": "持续改进优化", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "improveAndOptimize/experienceAndLesson", "path": "/improveAndOptimize/experienceAndLesson", "component": "() => import('@/views/hegui/respond/improveAndOptimize/experienceAndLesson/index.vue')", "meta": {"title": "经验教训"}, "children": [{"name": "/improveAndOptimize/experienceAndLesson/detail", "path": "/improveAndOptimize/experienceAndLesson/detail", "component": "() => import('@/views/hegui/respond/improveAndOptimize/experienceAndLesson/detail.vue')", "meta": {"title": "经验教训详情", "sidebar": false, "breadcrumb": false}}, {"name": "/improveAndOptimize/experienceAndLesson/addEdit", "path": "/improveAndOptimize/experienceAndLesson/addEdit", "component": "() => import('@/views/hegui/respond/improveAndOptimize/experienceAndLesson/addEdit.vue')", "meta": {"title": "新增经验教训", "sidebar": false, "breadcrumb": false}}]}, {"name": "improveAndOptimize/improvementMeasures", "path": "/improveAndOptimize/improvementMeasures", "component": "() => import('@/views/hegui/respond/improveAndOptimize/improvementMeasures/index.vue')", "meta": {"title": "改进措施"}, "children": [{"name": "/improveAndOptimize/improvementMeasures/detail", "path": "/improveAndOptimize/improvementMeasures/detail", "component": "() => import('@/views/hegui/respond/improveAndOptimize/improvementMeasures/detail.vue')", "meta": {"title": "改进措施详情", "sidebar": false, "breadcrumb": false}}, {"name": "/improveAndOptimize/improvementMeasures/addEdit", "path": "/improveAndOptimize/improvementMeasures/addEdit", "component": "() => import('@/views/hegui/respond/improveAndOptimize/improvementMeasures/addEdit.vue')", "meta": {"title": "新增改进措施", "sidebar": false, "breadcrumb": false}}]}, {"name": "improveAndOptimize/effectEvaluation", "path": "/improveAndOptimize/effectEvaluation", "component": "() => import('@/views/hegui/respond/improveAndOptimize/effectEvaluation/index.vue')", "meta": {"title": "效果评估"}, "children": [{"name": "/improveAndOptimize/effectEvaluation/detail", "path": "/improveAndOptimize/effectEvaluation/detail", "component": "() => import('@/views/hegui/respond/improveAndOptimize/effectEvaluation/detail.vue')", "meta": {"title": "效果评估详情", "sidebar": false, "breadcrumb": false}}, {"name": "/improveAndOptimize/effectEvaluation/addEdit", "path": "/improveAndOptimize/effectEvaluation/addEdit", "component": "() => import('@/views/hegui/respond/improveAndOptimize/effectEvaluation/addEdit.vue')", "meta": {"title": "新增效果评估", "sidebar": false, "breadcrumb": false}}]}, {"name": "improveAndOptimize/optimizationReport", "path": "/improveAndOptimize/optimizationReport", "component": "() => import('@/views/hegui/respond/improveAndOptimize/optimizationReport/index.vue')", "meta": {"title": "优化报告"}, "children": [{"name": "/improveAndOptimize/optimizationReport/detail", "path": "/improveAndOptimize/optimizationReport/detail", "component": "() => import('@/views/hegui/respond/improveAndOptimize/optimizationReport/detail.vue')", "meta": {"title": "优化报告详情", "sidebar": false, "breadcrumb": false}}, {"name": "/improveAndOptimize/optimizationReport/addEdit", "path": "/improveAndOptimize/optimizationReport/addEdit", "component": "() => import('@/views/hegui/respond/improveAndOptimize/optimizationReport/addEdit.vue')", "meta": {"title": "新增优化报告", "sidebar": false, "breadcrumb": false}}]}]}, "violationIssues": {"name": "violationIssues", "path": "/violationIssues", "component": "Layout", "meta": {"title": "违规问题调查", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "violationIssues/taskManagement", "path": "/violationIssues/taskManagement", "component": "() => import('@/views/hegui/respond/violationIssues/taskManagement/index.vue')", "meta": {"title": "任务管理"}, "children": [{"name": "/violationIssues/taskManagement/detail", "path": "/violationIssues/taskManagement/detail", "component": "() => import('@/views/hegui/respond/violationIssues/taskManagement/detail.vue')", "meta": {"title": "任务详情", "sidebar": false, "breadcrumb": false}}, {"name": "/violationIssues/taskManagement/addEdit", "path": "/violationIssues/taskManagement/addEdit", "component": "() => import('@/views/hegui/respond/violationIssues/taskManagement/addEdit.vue')", "meta": {"title": "新增任务", "sidebar": false, "breadcrumb": false}}]}, {"name": "violationIssues/reportManagement", "path": "/violationIssues/reportManagement", "component": "() => import('@/views/hegui/respond/violationIssues/reportManagement/index.vue')", "meta": {"title": "报告管理"}, "children": [{"name": "/violationIssues/reportManagement/detail", "path": "/violationIssues/reportManagement/detail", "component": "() => import('@/views/hegui/respond/violationIssues/reportManagement/detail.vue')", "meta": {"title": "报告详情", "sidebar": false, "breadcrumb": false}}, {"name": "/violationIssues/reportManagement/addEdit", "path": "/violationIssues/reportManagement/addEdit", "component": "() => import('@/views/hegui/respond/violationIssues/reportManagement/addEdit.vue')", "meta": {"title": "新增报告", "sidebar": false, "breadcrumb": false}}]}]}}, "systemSettings": {"enterpriseInformation": {"name": "enterpriseInformation", "path": "/enterpriseInformation", "component": "Layout", "meta": {"title": "企业信息", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "enterpriseInformation/basicInformation", "path": "/enterpriseInformation/basicInformation", "component": "() => import('@/views/hegui/systemSettings/enterpriseInformation/basicInformation/index.vue')", "meta": {"title": "基本信息"}}, {"name": "enterpriseInformation/brandSettings", "path": "/enterpriseInformation/brandSettings", "component": "() => import('@/views/hegui/systemSettings/enterpriseInformation/brandSettings/index.vue')", "meta": {"title": "品牌设置"}}]}, "integratedConfiguration": {"name": "integratedConfiguration", "path": "/integratedConfiguration", "component": "Layout", "meta": {"title": "集成配置", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "integratedConfiguration/thirdPartySystems", "path": "/integratedConfiguration/thirdPartySystems", "component": "() => import('@/views/hegui/systemSettings/integratedConfiguration/thirdPartySystems/index.vue')", "meta": {"title": "第三方系统"}}, {"name": "integratedConfiguration/dataImportAndExport", "path": "/integratedConfiguration/dataImportAndExport", "component": "() => import('@/views/hegui/systemSettings/integratedConfiguration/dataImportAndExport/index.vue')", "meta": {"title": "数据导入导出"}}]}, "logQuery": {"name": "log<PERSON><PERSON>y", "path": "/logQuery", "component": "Layout", "meta": {"title": "日志查询", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "logQuery/index", "path": "/logQuery/index", "component": "() => import('@/views/hegui/systemSettings/logQuery/index.vue')", "meta": {"title": "日志查询"}}]}, "organizationalStructure": {"name": "organizationalStructure", "path": "/organizationalStructure", "component": "Layout", "meta": {"title": "组织架构", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "organizationalStructure/settings", "path": "/organizationalStructure/settings", "component": "() => import('@/views/hegui/systemSettings/organizationalStructure/settings/index.vue')", "meta": {"title": "设置"}}, {"name": "organizationalStructure/department", "path": "/organizationalStructure/department", "component": "() => import('@/views/hegui/systemSettings/organizationalStructure/department/index.vue')", "meta": {"title": "部门"}}, {"name": "organizationalStructure/post", "path": "/organizationalStructure/post", "component": "() => import('@/views/hegui/systemSettings/organizationalStructure/post/index.vue')", "meta": {"title": "岗位"}}, {"name": "organizationalStructure/menuManagement", "path": "/organizationalStructure/menuManagement", "component": "() => import('@/views/hegui/systemSettings/organizationalStructure/menuManagement/index.vue')", "meta": {"title": "菜单管理"}}]}, "systemParameter": {"name": "systemParameter", "path": "/systemParameter", "component": "Layout", "meta": {"title": "系统参数", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "systemParameter/basicSettings", "path": "/systemParameter/basicSettings", "component": "() => import('@/views/hegui/systemSettings/systemParameter/basicSettings/index.vue')", "meta": {"title": "基本设置"}}, {"name": "systemParameter/notificationSettings", "path": "/systemParameter/notificationSettings", "component": "() => import('@/views/hegui/systemSettings/systemParameter/notificationSettings/index.vue')", "meta": {"title": "通知设置"}}, {"name": "systemParameter/securitySettings", "path": "/systemParameter/securitySettings", "component": "() => import('@/views/hegui/systemSettings/systemParameter/securitySettings/index.vue')", "meta": {"title": "安全设置"}}]}, "usersAndPermissions": {"name": "usersAndPermissions", "path": "/usersAndPermissions", "component": "Layout", "meta": {"title": "用户与权限", "icon": "i-heroicons-solid:menu-alt-3"}, "children": [{"name": "usersAndPermissions/userManagement", "path": "/usersAndPermissions/userManagement", "component": "() => import('@/views/hegui/systemSettings/usersAndPermissions/userManagement/index.vue')", "meta": {"title": "用户管理"}}, {"name": "usersAndPermissions/roleManagement", "path": "/usersAndPermissions/roleManagement", "component": "() => import('@/views/hegui/systemSettings/usersAndPermissions/roleManagement/index.vue')", "meta": {"title": "角色管理"}}, {"name": "usersAndPermissions/permissionManagement", "path": "/usersAndPermissions/permissionManagement", "component": "() => import('@/views/hegui/systemSettings/usersAndPermissions/permissionManagement/index.vue')", "meta": {"title": "权限管理"}}]}}}}