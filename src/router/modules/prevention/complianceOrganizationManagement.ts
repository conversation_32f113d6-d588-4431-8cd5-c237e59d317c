import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/threeListManagement',
  component: Layout,
  // redirect: '/one/1',
  name: 'threeListManagement',
  meta: {
    title: '合规组织管理',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/threeListManagement/1',
      name: 'system/1',
      component: () => import('@/views/hegui/one/system/systemManagement.vue'),
      meta: {
        title: '合规风险识别清单',
      },
      // redirect: '/one/1',
    },
    {
      path: '/threeListManagement/2',
      name: 'system/2',
      component: () => import('@/views/hegui/one/system/regulatoryConversion.vue'),
      meta: {
        title: '重点岗位合规职责清单',
      },
    },
    {
      path: '/threeListManagement/3',
      name: 'system/3',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/one/system/systemAdditionAndReview.vue'),
      meta: {
        title: '关键业务流程管控清单',
      },
    },
  ],
}

export default routes
