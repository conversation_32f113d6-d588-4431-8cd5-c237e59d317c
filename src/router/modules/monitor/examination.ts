import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/monitor/examination',
  component: Layout,
  // redirect: '/one/1',
  name: '/monitor/examination',
  meta: {
    title: '合规审查',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/monitor/examination/contractReview',
      name: '/monitor/examination/contractReview',
      component: () => import('@/views/hegui/monitor/examination/contractReview/index.vue'),
      meta: {
        title: '合同审查',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/monitor/examination/contractReview/detail',
          name: '/monitor/examination/contractReview/detail',
          component: () => import('@/views/hegui/monitor/examination/contractReview/detail.vue'),
          meta: {
            title: '合同审查详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/monitor/examination/contractReview/addEdit',
          name: '/monitor/examination/contractReview/addEdit',
          component: () => import('@/views/hegui/monitor/examination/contractReview/addEdit.vue'),
          meta: {
            title: '合同审查新增修改',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/monitor/examination/contractReview/reviewResult',
          name: '/monitor/examination/contractReview/reviewResult',
          component: () => import('@/views/hegui/monitor/examination/contractReview/reviewResult.vue'),
          meta: {
            title: '审查详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/monitor/examination/decisionReview',
      name: '/monitor/examination/decisionReview',
      component: () => import('@/views/hegui/monitor/examination/decisionReview/index.vue'),
      meta: {
        title: '重大决策审查',
      },
      children: [
        {
          path: '/monitor/examination/decisionReview/detail',
          name: '/monitor/examination/decisionReview/detail',
          component: () => import('@/views/hegui/monitor/examination/decisionReview/detail.vue'),
          meta: {
            title: '重大决策审查详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/monitor/examination/decisionReview/addEdit',
          name: '/monitor/examination/decisionReview/addEdit',
          component: () => import('@/views/hegui/monitor/examination/decisionReview/addEdit.vue'),
          meta: {
            title: '重大决策审查新增修改',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/monitor/examination/ohter',
      name: '/monitor/examination/ohter',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/monitor/examination/otherReviews/index.vue'),
      meta: {
        title: '其他审查',
      },
      children: [
        {
          path: '/monitor/examination/ohter/detail',
          name: '/monitor/examination/ohter/detail',
          component: () => import('@/views/hegui/monitor/examination/otherReviews/detail.vue'),
          meta: {
            title: '其他审查详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/monitor/examination/ohter/addEdit',
          name: '/monitor/examination/ohter/addEdit',
          component: () => import('@/views/hegui/monitor/examination/otherReviews/addEdit.vue'),
          meta: {
            title: '其他审查新增修改',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
  ],
}

export default routes
