import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/monitor/cockpit',
  component: Layout,
  // redirect: '/one/1',
  name: '/monitor/cockpit',
  meta: {
    title: '合规驾驶舱',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/monitor/cockpit/1',
      name: '/monitor/cockpit/1',
      component: () => import('@/views/hegui/monitor/cockpit/realTimeMonitoring/index.vue'),
      meta: {
        title: '实时监控',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/monitor/cockpit/1/detail',
          name: '/monitor/cockpit/1/detail',
          component: () => import('@/views/hegui/monitor/cockpit/realTimeMonitoring/detail.vue'),
          meta: {
            title: '实时监控详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/monitor/cockpit/2',
      name: '/monitor/cockpit/2',
      component: () => import('@/views/hegui/monitor/cockpit/analysisReport/index.vue'),
      meta: {
        title: '风险分析报告',
      },
    },
    {
      path: '/monitor/cockpit/3',
      name: '/monitor/cockpit/3',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/monitor/cockpit/earlyWarning/index.vue'),
      meta: {
        title: '预警管理',
      },
      children: [
        {
          path: '/monitor/cockpit/3/detail',
          name: '/monitor/cockpit/3/detail',
          component: () => import('@/views/hegui/monitor/cockpit/earlyWarning/detail.vue'),
          meta: {
            title: '实时监控详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
  ],
}

export default routes
