import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/monitor/intelligentReporting',
  component: Layout,
  // redirect: '/one/1',
  name: '/monitor/intelligentReporting',
  meta: {
    title: '智能举报管理',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/monitor/intelligentReporting/reportAcceptance',
      name: '/monitor/intelligentReporting/reportAcceptance',
      component: () => import('@/views/hegui/monitor/intelligentReporting/reportAcceptance/index.vue'),
      meta: {
        title: '举报受理',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/monitor/intelligentReporting/reportAcceptance/detail',
          name: '/monitor/intelligentReporting/reportAcceptance/detail',
          component: () => import('@/views/hegui/monitor/intelligentReporting/reportAcceptance/detail.vue'),
          meta: {
            title: '举报详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        // {
        //   path: '/monitor/examination/contractReview/addEdit',
        //   name: '/monitor/examination/contractReview/addEdit',
        //   component: () => import('@/views/hegui/monitor/examination/contractReview/addEdit.vue'),
        //   meta: {
        //     title: '合同审查新增修改',
        //     sidebar: false,
        //     breadcrumb: false,
        //   },
        // },
      ],
    },
    // {
    //   path: '/monitor/intelligentReporting/reportHandling',
    //   name: '/monitor/intelligentReporting/reportHandling',
    //   component: () => import('@/views/hegui/monitor/intelligentReporting/reportHandling/index.vue'),
    //   meta: {
    //     title: '举报处理',
    //   },
    // },
    {
      path: '/monitor/intelligentReporting/3',
      name: '/monitor/intelligentReporting/3',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/monitor/intelligentReporting/reportStatisticsAndAnalysis/index.vue'),
      meta: {
        title: '举报统计分析',
      },
    },
    {
      path: '/monitor/intelligentReporting/reportingPolicyManagement',
      name: '/monitor/intelligentReporting/reportingPolicyManagement',
      // redirect: '/one/3',
      component: () => import('@/views/hegui/monitor/intelligentReporting/reportingPolicyManagement/index.vue'),
      meta: {
        title: '举报政策管理',
      },
      children: [
        {
          path: '/monitor/intelligentReporting/reportingPolicyManagement/detail',
          name: '/monitor/intelligentReporting/reportingPolicyManagement/detail',
          component: () => import('@/views/hegui/monitor/intelligentReporting/reportingPolicyManagement/detail.vue'),
          meta: {
            title: '举报政策管理详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/monitor/intelligentReporting/reportingPolicyManagement/addEdit',
          name: '/monitor/intelligentReporting/reportingPolicyManagement/addEdit',
          component: () => import('@/views/hegui/monitor/intelligentReporting/reportingPolicyManagement/addEdit.vue'),
          meta: {
            title: '举报政策管理详情',
            sidebar: false,
            breadcrumb: false,
          },
        },

      ],
    },
  ],
}

export default routes
