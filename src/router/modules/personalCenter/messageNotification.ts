import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/personalCenter/messageNotification',
  component: Layout,
  // redirect: '/one/1',
  name: '/personalCenter/messageNotification',
  meta: {
    title: '消息通知',
    icon: 'i-heroicons-solid:menu-alt-3',
    // sidebar: false,
  },
  children: [
    {
      path: '/personalCenter/messageNotification/allMessages',
      name: '/personalCenter/messageNotification/allMessages',
      component: () => import('@/views/hegui/personalCenter/messageNotification/allMessages/index.vue'),
      meta: {
        title: '全部消息',
        // sidebar: false,
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/personalCenter/messageNotification/allMessages/detail',
          name: '/personalCenter/messageNotification/allMessages/detail',
          component: () => import('@/views/hegui/personalCenter/messageNotification/allMessages/detail.vue'),
          meta: {
            title: '实时监控详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/personalCenter/messageNotification/systemNotification',
      name: '/personalCenter/messageNotification/systemNotification',
      component: () => import('@/views/hegui/personalCenter/messageNotification/systemNotification/index.vue'),
      meta: {
        title: '系统通知',
      },
    },
    {
      path: '/personalCenter/messageNotification/toDoNotification',
      name: '/personalCenter/messageNotification/toDoNotification',
      component: () => import('@/views/hegui/personalCenter/messageNotification/toDoNotification/index.vue'),
      meta: {
        title: '待办通知',
      },
    },
    {
      path: '/personalCenter/messageNotification/notificationsSettings',
      name: '/personalCenter/messageNotification/notificationsSettings',
      component: () => import('@/views/hegui/personalCenter/messageNotification/notificationsSettings/index.vue'),
      meta: {
        title: '通知设置',
      },

    },
  ],
}

export default routes
