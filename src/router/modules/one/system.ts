import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/one/systemManagement',
  component: Layout,
  // redirect: '/one/1',
  name: 'multilevelMenuExample',
  meta: {
    title: '制度管理',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/one/systemManagement/index',
      name: '/one/systemManagement/index',
      component: () => import('@/views/hegui/one/system/systemManagement/index.vue'),
      meta: {
        title: '制度库管理',
      },
      children: [
        {
          path: '/one/systemManagement/detail',
          name: '/one/systemManagement/detail',
          component: () => import('@/views/hegui/one/system/systemManagement/detail.vue'),
          meta: {
            title: '制度详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/one/systemManagement/addEdit',
          name: '/one/systemManagement/addEdit',
          component: () => import('@/views/hegui/one/system/systemManagement/addEdit.vue'),
          meta: {
            title: '新增制度',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/one/regulatoryConversion/index',
      name: '/one/regulatoryConversion/index',
      component: () => import('@/views/hegui/one/system/regulatoryConversion/index.vue'),
      meta: {
        title: '法规转化',
      },
      children: [
        {
          path: '/one/regulatoryConversion/detail',
          name: '/one/regulatoryConversion/detail',
          component: () => import('@/views/hegui/one/system/regulatoryConversion/detail.vue'),
          meta: {
            title: '制度详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/one/regulatoryConversion/addEdit',
          name: '/one/regulatoryConversion/addEdit',
          component: () => import('@/views/hegui/one/system/regulatoryConversion/addEdit.vue'),
          meta: {
            title: '新增制度',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/system/review',
      name: 'system/review',
      // redirect: '/system/review',
      component: () => import('@/views/hegui/one/system/systemAdditionAndReview.vue'),
      meta: {
        title: '审查记录',
      },
      children: [
        {
          path: '/system/review/detail',
          name: '/system/review/detail',
          component: () => import('@/views/hegui/one/system/systemAdditionAndReview/detail.vue'),
          meta: {
            title: '制度审查详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/system/review/report',
          name: '/system/review/report',
          component: () => import('@/views/hegui/one/system/systemAdditionAndReview/report.vue'),
          meta: {
            title: '制度审查报告',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/system/review/addEdit',
          name: '/system/review/addEdit',
          component: () => import('@/views/hegui/one/system/systemAdditionAndReview/addEdit.vue'),
          meta: {
            title: '新增审核记录',
            sidebar: false,
            breadcrumb: false,
          },
        },
        //   {
        //     path: 'page',
        //     name: 'multilevelMenuExample2-1',
        //     component: () => import('@/views/multilevel_menu_example/level2/page.vue'),
        //     meta: {
        //       title: '导航2-1',
        //     },
        //   },
        //   {
        //     path: 'level3',
        //     name: 'multilevelMenuExample2-2',
        //     redirect: '/multilevel_menu_example/level2/level3/page1',
        //     meta: {
        //       title: '导航2-2',
        //     },
        //     children: [
        //       {
        //         path: 'page1',
        //         name: 'multilevelMenuExample2-2-1',
        //         component: () => import('@/views/multilevel_menu_example/level2/level3/page1.vue'),
        //         meta: {
        //           title: '导航2-2-1',
        //         },
        //       },
        //       {
        //         path: 'page2',
        //         name: 'multilevelMenuExample2-2-2',
        //         component: () => import('@/views/multilevel_menu_example/level2/level3/page2.vue'),
        //         meta: {
        //           title: '导航2-2-2',
        //         },
        //       },
        //     ],
        //   },
      ],
    },
  ],
}

export default routes
