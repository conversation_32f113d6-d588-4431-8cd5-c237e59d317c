import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/systemSettings/usersAndPermissions',
  component: Layout,
  name: '/systemSettings/usersAndPermissions',
  meta: {
    title: '用户与权限',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/systemSettings/usersAndPermissions/userManagement',
      name: '/systemSettings/usersAndPermissions/userManagement',
      component: () => import('@/views/hegui/systemSettings/usersAndPermissions/userManagement/index.vue'),
      meta: {
        title: '用户管理',
      },
      children: [
        {
          path: '/systemSettings/usersAndPermissions/userManagement/addEdit',
          name: '/systemSettings/usersAndPermissions/userManagement/addEdit',
          component: () => import('@/views/hegui/systemSettings/usersAndPermissions/userManagement/addEdit.vue'),
          meta: {
            title: '新增编辑用户',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/systemSettings/usersAndPermissions/roleManagement',
      name: '/systemSettings/usersAndPermissions/roleManagement',
      component: () => import('@/views/hegui/systemSettings/usersAndPermissions/roleManagement/index.vue'),
      meta: {
        title: '角色管理',
      },
    },
    {
      path: '/systemSettings/usersAndPermissions/permissionSetting',
      name: '/systemSettings/usersAndPermissions/permissionSetting',
      component: () => import('@/views/hegui/systemSettings/usersAndPermissions/permissionSetting/index.vue'),
      meta: {
        title: '权限设置',
      },
    },

  ],
}

export default routes
