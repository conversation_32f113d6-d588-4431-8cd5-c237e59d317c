import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/systemSettings/systemParameter',
  component: Layout,
  name: '/systemSettings/systemParameter',
  meta: {
    title: '系统参数',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/systemSettings/systemParameter/basicParameters',
      name: '/systemSettings/systemParameter/basicParameters',
      component: () => import('@/views/hegui/systemSettings/systemParameter/basicParameters/index.vue'),
      meta: {
        title: '基础参数',
        // sidebar: false,
      },
      // children: [
      //   {
      //     path: '/systemSettings/systemParameter/toDoTasks/detail',
      //     name: '/systemSettings/systemParameter/toDoTasks/detail',
      //     component: () => import('@/views/hegui/systemSettings/systemParameter/toDoTasks/detail.vue'),
      //     meta: {
      //       title: '实时监控详情',
      //       sidebar: false,
      //       breadcrumb: false,
      //     },
      //   },
      // ]
    },
    {
      path: '/systemSettings/systemParameter/notificationsSettings',
      name: '/systemSettings/systemParameter/notificationsSettings',
      component: () => import('@/views/hegui/systemSettings/systemParameter/notificationsSettings/index.vue'),
      meta: {
        title: '通知设置',
      },
    },
    {
      path: '/systemSettings/systemParameter/securitySetting',
      name: '/systemSettings/systemParameter/securitySetting',
      component: () => import('@/views/hegui/systemSettings/systemParameter/securitySetting/index.vue'),
      meta: {
        title: '安全设置',
      },
    },
  ],
}

export default routes
