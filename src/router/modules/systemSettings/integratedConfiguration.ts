import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/systemSettings/integratedConfiguration',
  component: Layout,
  name: '/systemSettings/integratedConfiguration',
  meta: {
    title: '集成配置',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/systemSettings/integratedConfiguration/thirdPartySystem',
      name: '/systemSettings/integratedConfiguration/thirdPartySystem',
      component: () => import('@/views/hegui/systemSettings/integratedConfiguration/thirdPartySystem/index.vue'),
      meta: {
        title: '第三方系统',
        // sidebar: false,
      },
      // children: [
      //   {
      //     path: '/systemSettings/integratedConfiguration/toDoTasks/detail',
      //     name: '/systemSettings/integratedConfiguration/toDoTasks/detail',
      //     component: () => import('@/views/hegui/systemSettings/integratedConfiguration/toDoTasks/detail.vue'),
      //     meta: {
      //       title: '实时监控详情',
      //       sidebar: false,
      //       breadcrumb: false,
      //     },
      //   },
      // ]
    },
    {
      path: '/systemSettings/integratedConfiguration/dataImportAndExport',
      name: '/systemSettings/integratedConfiguration/dataImportAndExport',
      component: () => import('@/views/hegui/systemSettings/integratedConfiguration/dataImportAndExport/index.vue'),
      meta: {
        title: '数据导入导出',
      },
    },
  ],
}

export default routes
