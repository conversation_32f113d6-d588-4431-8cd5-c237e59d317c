// 导出所有 store
export { default as useUserStore } from './modules/user'
export { default as useSettingsStore } from './modules/settings'
export { default as useTabbarStore } from './modules/tabbar'
export { default as useMenuStore } from './modules/menu'
export { default as useRouteStore } from './modules/route'
export { default as useKeepAliveStore } from './modules/keepAlive'
export { default as useNotificationStore } from './modules/notification'
export { default as useFavoritesStore } from './modules/favorites'
export { default as useIframeStore } from './modules/iframe'
export { default as useWatermarkStore } from './modules/watermark'
