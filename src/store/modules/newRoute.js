export default [
  {
    id: 1,
    parentId: 0,
    path: '/one',
    redirect: '/one/systemManagement',
    component: '',
    name: '/one',
    realName: '一体',
    isMenu: true,
    sort: 1,
    mark: 1,
    meta: '{"title":"一体","icon":"i-heroicons-solid:home","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
    children: [
      {
        id: 2,
        parentId: 1,
        path: '/one/systemManagement',
        redirect: '/one/systemManagement/index',
        component: 'Layout',
        name: '/one/systemManagement',
        realName: '制度管理',
        isMenu: true,
        sort: 1,
        mark: 1,
        meta: '{"title":"制度管理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 3,
            parentId: 2,
            path: '/one/systemManagement/index',
            redirect: null,
            component: 'hegui/one/systemManagement/index.vue',
            name: '/one/systemManagement/index',
            realName: '制度库管理',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"制度库管理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 4,
                parentId: 3,
                path: '/one/systemManagement/detail',
                redirect: null,
                component: 'hegui/one/systemManagement/detail.vue',
                name: '/one/systemManagement/detail',
                realName: '制度详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"制度详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/one/systemManagement/index","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
          {
            id: 5,
            parentId: 2,
            path: '/one/systemManagement/regulationTransformation',
            redirect: null,
            component: 'hegui/one/systemManagement/regulationTransformation.vue',
            name: '/one/systemManagement/regulationTransformation',
            realName: '法规转化',
            isMenu: true,
            sort: 2,
            mark: 1,
            meta: '{"title":"法规转化","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [],
            auths: [],
            isAvailable: null,
          },
          {
            id: 6,
            parentId: 2,
            path: '/one/systemManagement/systemAdditionAndReview',
            redirect: null,
            component: 'hegui/one/systemManagement/systemAdditionAndReview.vue',
            name: '/one/systemManagement/systemAdditionAndReview',
            realName: '制度新增与审查',
            isMenu: true,
            sort: 3,
            mark: 1,
            meta: '{"title":"制度新增与审查","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 7,
                parentId: 6,
                path: '/one/systemManagement/systemAdditionAndReview/detail',
                redirect: null,
                component: 'hegui/one/systemManagement/systemAdditionAndReview_detail.vue',
                name: '/one/systemManagement/systemAdditionAndReview/detail',
                realName: '制度新增与审查详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"制度新增与审查详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/one/systemManagement/systemAdditionAndReview","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 8,
                parentId: 6,
                path: '/one/systemManagement/systemAdditionAndReview/addEdit',
                redirect: null,
                component: 'hegui/one/systemManagement/systemAdditionAndReview_addEdit.vue',
                name: '/one/systemManagement/systemAdditionAndReview/addEdit',
                realName: '制度新增与审查新增/编辑',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"制度新增与审查新增/编辑","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/one/systemManagement/systemAdditionAndReview","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
      {
        id: 9,
        parentId: 1,
        path: '/one/complianceDatabase',
        redirect: '/one/complianceDatabase/regulationLibrary',
        component: 'Layout',
        name: '/one/complianceDatabase',
        realName: '合规数据库',
        isMenu: true,
        sort: 2,
        mark: 1,
        meta: '{"title":"合规数据库","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 10,
            parentId: 9,
            path: '/one/complianceDatabase/regulationLibrary',
            redirect: null,
            component: 'hegui/one/complianceDatabase/regulationLibrary.vue',
            name: '/one/complianceDatabase/regulationLibrary',
            realName: '法规库',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"法规库","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 11,
                parentId: 10,
                path: '/one/complianceDatabase/regulationLibrary/detail',
                redirect: null,
                component: 'hegui/one/complianceDatabase/regulationLibrary_detail.vue',
                name: '/one/complianceDatabase/regulationLibrary/detail',
                realName: '法规库详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"法规库详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/one/complianceDatabase/regulationLibrary","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 92,
                parentId: 10,
                path: '/one/complianceDatabase/regulationLibrary/addEdit',
                redirect: null,
                component: 'hegui/one/complianceDatabase/regulationLibrary/addEdit.vue',
                name: '/one/complianceDatabase/regulationLibrary/addEdit',
                realName: '法规库新增修改',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"法规库新增修改","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/one/complianceDatabase/regulationLibrary","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
          {
            id: 93,
            parentId: 9,
            path: '/one/complianceDatabase/complianceObligationLibrary',
            redirect: null,
            component: 'hegui/one/complianceDatabase/complianceObligationLibrary/index.vue',
            name: '/one/complianceDatabase/complianceObligationLibrary',
            realName: '合规义务库',
            isMenu: true,
            sort: 2,
            mark: 1,
            meta: '{"title":"合规义务库","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 94,
                parentId: 93,
                path: '/one/complianceDatabase/complianceObligationLibrary/detail',
                redirect: null,
                component: 'hegui/one/complianceDatabase/complianceObligationLibrary/detail.vue',
                name: '/one/complianceDatabase/complianceObligationLibrary/detail',
                realName: '合规义务库详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"合规义务库详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/one/complianceDatabase/complianceObligationLibrary","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 95,
                parentId: 93,
                path: '/one/complianceDatabase/complianceObligationLibrary/addEdit',
                redirect: null,
                component: 'hegui/one/complianceDatabase/complianceObligationLibrary/addEdit.vue',
                name: '/one/complianceDatabase/complianceObligationLibrary/addEdit',
                realName: '合规义务库新增修改',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"合规义务库新增修改","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/one/complianceDatabase/complianceObligationLibrary","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
          {
            id: 96,
            parentId: 9,
            path: '/one/complianceDatabase/complianceCaseLibrary',
            redirect: null,
            component: 'hegui/one/complianceDatabase/complianceCaseLibrary/index.vue',
            name: '/one/complianceDatabase/complianceCaseLibrary',
            realName: '合规案例库',
            isMenu: true,
            sort: 3,
            mark: 1,
            meta: '{"title":"合规案例库","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 97,
                parentId: 96,
                path: '/one/complianceDatabase/complianceCaseLibrary/detail',
                redirect: null,
                component: 'hegui/one/complianceDatabase/complianceCaseLibrary/detail.vue',
                name: '/one/complianceDatabase/complianceCaseLibrary/detail',
                realName: '合规案例库详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"合规案例库详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/one/complianceDatabase/complianceCaseLibrary","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 98,
                parentId: 96,
                path: '/one/complianceDatabase/complianceCaseLibrary/addEdit',
                redirect: null,
                component: 'hegui/one/complianceDatabase/complianceCaseLibrary/addEdit.vue',
                name: '/one/complianceDatabase/complianceCaseLibrary/addEdit',
                realName: '合规案例库新增修改',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"合规案例库新增修改","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/one/complianceDatabase/complianceCaseLibrary","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
    ],
    auths: [],
    isAvailable: null,
  },
  {
    id: 26,
    parentId: 0,
    path: '/prevention',
    redirect: '/threeListManagement/complianceRisk',
    component: '',
    name: '/prevention',
    realName: '预防之翼',
    isMenu: true,
    sort: 2,
    mark: 1,
    meta: '{"title":"预防之翼","icon":"i-heroicons-solid:shield-check","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
    children: [
      {
        id: 27,
        parentId: 26,
        path: '/threeListManagement',
        redirect: '/threeListManagement/complianceRisk',
        component: 'Layout',
        name: '/threeListManagement',
        realName: '三单管理',
        isMenu: true,
        sort: 1,
        mark: 1,
        meta: '{"title":"三单管理","icon":"i-heroicons-solid:menu-alt-3","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 28,
            parentId: 27,
            path: '/threeListManagement/complianceRisk',
            redirect: null,
            component: 'hegui/prevention/threeListManagement/complianceRisk.vue',
            name: '/threeListManagement/complianceRisk',
            realName: '合规风险识别清单',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"合规风险识别清单","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 29,
                parentId: 28,
                path: '/threeListManagement/complianceRisk/edit',
                redirect: null,
                component: 'hegui/prevention/threeListManagement/complianceRisk_edit.vue',
                name: '/threeListManagement/complianceRisk/edit',
                realName: '新增合规清单',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"新增合规清单","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/threeListManagement/complianceRisk","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
      {
        id: 37,
        parentId: 26,
        path: '/organizationManagement',
        redirect: '/organizationManagement/1',
        component: 'Layout',
        name: '/organizationManagement',
        realName: '合规组织管理',
        isMenu: true,
        sort: 2,
        mark: 1,
        meta: '{"title":"合规组织管理","icon":"i-heroicons-solid:menu-alt-3","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 38,
            parentId: 37,
            path: '/organizationManagement/1',
            redirect: null,
            component: 'hegui/prevention/organizationManagement/g_view.vue',
            name: '/organizationManagement/1',
            realName: '组织结构视图',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"组织结构视图","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [],
            auths: [],
            isAvailable: null,
          },
          {
            id: 39,
            parentId: 37,
            path: '/organizationManagement/commissioner',
            redirect: null,
            component: 'hegui/prevention/organizationManagement/commissioner/index.vue',
            name: '/organizationManagement/commissioner',
            realName: '合规专员管理',
            isMenu: true,
            sort: 2,
            mark: 1,
            meta: '{"title":"合规专员管理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 40,
                parentId: 39,
                path: '/organizationManagement/commissioner/addEdit',
                redirect: null,
                component: 'hegui/prevention/organizationManagement/commissioner/addEdit.vue',
                name: '/organizationManagement/commissioner/addEdit',
                realName: '合规专员新增/编辑',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"合规专员新增/编辑","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/organizationManagement/commissioner","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
      {
        id: 41,
        parentId: 26,
        path: '/training',
        redirect: '/training/curriculum',
        component: 'Layout',
        name: '/training',
        realName: '合规培训体系',
        isMenu: true,
        sort: 3,
        mark: 1,
        meta: '{"title":"合规培训体系","icon":"i-heroicons-solid:menu-alt-3","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 42,
            parentId: 41,
            path: '/training/curriculum',
            redirect: null,
            component: 'hegui/prevention/training/curriculum.vue',
            name: '/training/curriculum',
            realName: '培训课程库',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"培训课程库","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 43,
                parentId: 42,
                path: '/training/curriculum/edit',
                redirect: null,
                component: 'hegui/prevention/training/curriculum_edit.vue',
                name: '/training/curriculum/edit',
                realName: '新增课程',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"新增课程","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/training/curriculum","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 44,
                parentId: 42,
                path: '/training/curriculum/detail',
                redirect: null,
                component: 'hegui/prevention/training/curriculum_detail.vue',
                name: '/training/curriculum/detail',
                realName: '培训课程库详情',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"培训课程库详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/training/curriculum","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
          {
            id: 45,
            parentId: 41,
            path: '/training/plan',
            redirect: null,
            component: 'hegui/prevention/training/plan.vue',
            name: '/training/plan',
            realName: '培训计划',
            isMenu: true,
            sort: 2,
            mark: 1,
            meta: '{"title":"培训计划","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 46,
                parentId: 45,
                path: '/training/plan/edit',
                redirect: null,
                component: 'hegui/prevention/training/plan_edit.vue',
                name: '/training/plan/edit',
                realName: '新增培训计划',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"新增培训计划","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/training/plan","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 47,
                parentId: 45,
                path: '/training/plan/detail',
                redirect: null,
                component: 'hegui/prevention/training/plan_detail.vue',
                name: '/training/plan/detail',
                realName: '培训计划详情',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"培训计划详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/training/plan","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
    ],
    auths: [],
    isAvailable: null,
  },
  {
    id: 99,
    parentId: 0,
    path: '/monitor',
    redirect: '/monitor/cockpit',
    component: '',
    name: '/monitor',
    realName: '监测之眼',
    isMenu: true,
    sort: 3,
    mark: 1,
    meta: '{"title":"监测之眼","icon":"i-heroicons-solid:eye","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
    children: [
      {
        id: 68,
        parentId: 67,
        path: '/monitor/cockpit',
        redirect: null,
        component: 'hegui/monitor/cockpit.vue',
        name: '/monitor/cockpit',
        realName: '合规驾驶舱',
        isMenu: true,
        sort: 1,
        mark: 1,
        meta: '{"title":"合规驾驶舱","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [],
        auths: [],
        isAvailable: null,
      },
      {
        id: 69,
        parentId: 67,
        path: '/monitor/intelligentReporting',
        redirect: '/monitor/intelligentReporting/reportAcceptance',
        component: 'Layout',
        name: '/monitor/intelligentReporting',
        realName: '智能举报管理',
        isMenu: true,
        sort: 2,
        mark: 1,
        meta: '{"title":"智能举报管理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 70,
            parentId: 69,
            path: '/monitor/intelligentReporting/reportAcceptance',
            redirect: null,
            component: 'hegui/monitor/intelligentReporting/reportAcceptance.vue',
            name: '/monitor/intelligentReporting/reportAcceptance',
            realName: '举报受理',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"举报受理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 71,
                parentId: 70,
                path: '/monitor/intelligentReporting/reportAcceptance/detail',
                redirect: null,
                component: 'hegui/monitor/intelligentReporting/reportAcceptance_detail.vue',
                name: '/monitor/intelligentReporting/reportAcceptance/detail',
                realName: '举报受理详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"举报受理详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/monitor/intelligentReporting/reportAcceptance","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
      {
        id: 72,
        parentId: 67,
        path: '/monitor/examination',
        redirect: '/monitor/examination/contractReview',
        component: 'Layout',
        name: '/monitor/examination',
        realName: '合规审查',
        isMenu: true,
        sort: 3,
        mark: 1,
        meta: '{"title":"合规审查","icon":"i-heroicons-solid:menu-alt-3","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 73,
            parentId: 72,
            path: '/monitor/examination/contractReview',
            redirect: null,
            component: 'hegui/monitor/examination/contractReview/index.vue',
            name: '/monitor/examination/contractReview',
            realName: '合同审查',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"合同审查","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 74,
                parentId: 73,
                path: '/monitor/examination/contractReview/detail',
                redirect: null,
                component: 'hegui/monitor/examination/contractReview/detail.vue',
                name: '/monitor/examination/contractReview/detail',
                realName: '合同审查详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"合同审查详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/monitor/examination/contractReview","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 75,
                parentId: 73,
                path: '/monitor/examination/contractReview/addEdit',
                redirect: null,
                component: 'hegui/monitor/examination/contractReview/addEdit.vue',
                name: '/monitor/examination/contractReview/addEdit',
                realName: '合同审查新增修改',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"合同审查新增修改","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/monitor/examination/contractReview","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
          {
            id: 76,
            parentId: 72,
            path: '/monitor/examination/majorDecisionReview',
            redirect: null,
            component: 'hegui/monitor/examination/majorDecisionReview/index.vue',
            name: '/monitor/examination/majorDecisionReview',
            realName: '重大决策审查',
            isMenu: true,
            sort: 2,
            mark: 1,
            meta: '{"title":"重大决策审查","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 77,
                parentId: 76,
                path: '/monitor/examination/majorDecisionReview/detail',
                redirect: null,
                component: 'hegui/monitor/examination/majorDecisionReview/detail.vue',
                name: '/monitor/examination/majorDecisionReview/detail',
                realName: '重大决策审查详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"重大决策审查详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/monitor/examination/majorDecisionReview","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 78,
                parentId: 76,
                path: '/monitor/examination/majorDecisionReview/addEdit',
                redirect: null,
                component: 'hegui/monitor/examination/majorDecisionReview/addEdit.vue',
                name: '/monitor/examination/majorDecisionReview/addEdit',
                realName: '重大决策审查新增修改',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"重大决策审查新增修改","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/monitor/examination/majorDecisionReview","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
          {
            id: 89,
            parentId: 72,
            path: '/monitor/examination/otherReview',
            redirect: null,
            component: 'hegui/monitor/examination/otherReview/index.vue',
            name: '/monitor/examination/otherReview',
            realName: '其他审查',
            isMenu: true,
            sort: 3,
            mark: 1,
            meta: '{"title":"其他审查","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 90,
                parentId: 89,
                path: '/monitor/examination/otherReview/detail',
                redirect: null,
                component: 'hegui/monitor/examination/otherReview/detail.vue',
                name: '/monitor/examination/otherReview/detail',
                realName: '其他审查详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"其他审查详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/monitor/examination/otherReview","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 91,
                parentId: 89,
                path: '/monitor/examination/otherReview/addEdit',
                redirect: null,
                component: 'hegui/monitor/examination/otherReview/addEdit.vue',
                name: '/monitor/examination/otherReview/addEdit',
                realName: '其他审查新增修改',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"其他审查新增修改","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/monitor/examination/otherReview","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
    ],
    auths: [],
    isAvailable: null,
  },
  {
    id: 840,
    parentId: 0,
    path: '/respond',
    redirect: '/respond/violationIssues',
    component: '',
    name: '/respond',
    realName: '应对之剑',
    isMenu: true,
    sort: 4,
    mark: 1,
    meta: '{"title":"应对之剑","icon":"i-heroicons-solid:shield-check","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
    children: [
      {
        id: 77,
        parentId: 76,
        path: '/respond/violationIssues',
        redirect: '/respond/violationIssues/investigationTask',
        component: 'Layout',
        name: '/respond/violationIssues',
        realName: '违规问题调查',
        isMenu: true,
        sort: 1,
        mark: 1,
        meta: '{"title":"违规问题调查","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 78,
            parentId: 77,
            path: '/respond/violationIssues/investigationTask',
            redirect: null,
            component: 'hegui/respond/violationIssues/investigationTask/index.vue',
            name: '/respond/violationIssues/investigationTask',
            realName: '调查任务管理',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"调查任务管理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 79,
                parentId: 78,
                path: '/respond/violationIssues/investigationTask/detail',
                redirect: null,
                component: 'hegui/respond/violationIssues/investigationTask/detail.vue',
                name: '/respond/violationIssues/investigationTask/detail',
                realName: '调查任务详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"调查任务详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/respond/violationIssues/investigationTask","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
          {
            id: 80,
            parentId: 77,
            path: '/respond/violationIssues/investigationReport',
            redirect: null,
            component: 'hegui/respond/violationIssues/investigationReport/index.vue',
            name: '/respond/violationIssues/investigationReport',
            realName: '调查报告管理',
            isMenu: true,
            sort: 2,
            mark: 1,
            meta: '{"title":"调查报告管理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 81,
                parentId: 80,
                path: '/respond/violationIssues/investigationReport/addEdit',
                redirect: null,
                component: 'hegui/respond/violationIssues/investigationReport/addEdit.vue',
                name: '/respond/violationIssues/investigationReport/addEdit',
                realName: '调查报告新增修改',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"调查报告新增修改","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/respond/violationIssues/investigationReport","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
      {
        id: 82,
        parentId: 76,
        path: '/respond/accountability',
        redirect: '/respond/accountability/processingMeasures',
        component: 'Layout',
        name: '/respond/accountability',
        realName: '责任追究处理',
        isMenu: true,
        sort: 2,
        mark: 1,
        meta: '{"title":"责任追究处理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 83,
            parentId: 82,
            path: '/respond/accountability/processingMeasures',
            redirect: null,
            component: 'hegui/respond/accountability/processingMeasures/index.vue',
            name: '/respond/accountability/processingMeasures',
            realName: '处理措施管理',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"处理措施管理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 84,
                parentId: 83,
                path: '/respond/accountability/processingMeasures/detail',
                redirect: null,
                component: 'hegui/respond/accountability/processingMeasures/detail.vue',
                name: '/respond/accountability/processingMeasures/detail',
                realName: '处理措施详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"处理措施详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/respond/accountability/processingMeasures","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
              {
                id: 85,
                parentId: 83,
                path: '/respond/accountability/processingMeasures/addEdit',
                redirect: null,
                component: 'hegui/respond/accountability/processingMeasures/addEdit.vue',
                name: '/respond/accountability/processingMeasures/addEdit',
                realName: '处理措施新增修改',
                isMenu: true,
                sort: 2,
                mark: 1,
                meta: '{"title":"处理措施新增修改","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/respond/accountability/processingMeasures","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
    ],
    auths: [],
    isAvailable: null,
  },
  {
    id: 78,
    parentId: 0,
    path: '/systemSettings',
    redirect: '/systemSettings/enterpriseInformation',
    component: '',
    name: '/systemSettings',
    realName: '系统设置',
    isMenu: true,
    sort: 5,
    mark: 1,
    meta: '{"title":"系统设置","icon":"i-heroicons-solid:cog","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
    children: [
      {
        id: 79,
        parentId: 78,
        path: '/systemSettings/enterpriseInformation',
        redirect: '/systemSettings/enterpriseInformation/basicInformation',
        component: 'Layout',
        name: '/systemSettings/enterpriseInformation',
        realName: '企业信息',
        isMenu: true,
        sort: 1,
        mark: 1,
        meta: '{"title":"企业信息","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 80,
            parentId: 79,
            path: '/systemSettings/enterpriseInformation/basicInformation',
            redirect: null,
            component: 'hegui/systemSettings/enterpriseInformation/basicInformation.vue',
            name: '/systemSettings/enterpriseInformation/basicInformation',
            realName: '基本信息',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"基本信息","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [],
            auths: [],
            isAvailable: null,
          },
          {
            id: 81,
            parentId: 79,
            path: '/systemSettings/enterpriseInformation/brandSettings',
            redirect: null,
            component: 'hegui/systemSettings/enterpriseInformation/brandSettings.vue',
            name: '/systemSettings/enterpriseInformation/brandSettings',
            realName: '品牌设置',
            isMenu: true,
            sort: 2,
            mark: 1,
            meta: '{"title":"品牌设置","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
      {
        id: 82,
        parentId: 78,
        path: '/systemSettings/usersAndPermissions',
        redirect: '/systemSettings/usersAndPermissions/userManagement',
        component: 'Layout',
        name: '/systemSettings/usersAndPermissions',
        realName: '用户与权限',
        isMenu: true,
        sort: 2,
        mark: 1,
        meta: '{"title":"用户与权限","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 83,
            parentId: 82,
            path: '/systemSettings/usersAndPermissions/userManagement',
            redirect: null,
            component: 'hegui/systemSettings/usersAndPermissions/userManagement.vue',
            name: '/systemSettings/usersAndPermissions/userManagement',
            realName: '用户管理',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"用户管理","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 84,
                parentId: 83,
                path: '/systemSettings/usersAndPermissions/userManagement/addEdit',
                redirect: null,
                component: 'hegui/systemSettings/usersAndPermissions/userManagement_addEdit.vue',
                name: '/systemSettings/usersAndPermissions/userManagement/addEdit',
                realName: '用户管理新增/编辑',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"用户管理新增/编辑","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/systemSettings/usersAndPermissions/userManagement","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
      {
        id: 85,
        parentId: 78,
        path: '/systemSettings/logQuery',
        redirect: '/systemSettings/logQuery/logQuery',
        component: 'Layout',
        name: '/systemSettings/logQuery',
        realName: '操作日志',
        isMenu: true,
        sort: 3,
        mark: 1,
        meta: '{"title":"操作日志","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 86,
            parentId: 85,
            path: '/systemSettings/logQuery/logQuery',
            redirect: null,
            component: 'hegui/systemSettings/logQuery/logQuery.vue',
            name: '/systemSettings/logQuery/logQuery',
            realName: '日志查询',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"日志查询","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
    ],
    auths: [],
    isAvailable: null,
  },
  {
    id: 87,
    parentId: 0,
    path: '/personalCenter',
    redirect: '/personalCenter/personalInformation',
    component: '',
    name: '/personalCenter',
    realName: '个人中心',
    isMenu: true,
    sort: 6,
    mark: 1,
    meta: '{"title":"个人中心","icon":"i-heroicons-solid:user","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
    children: [
      {
        id: 88,
        parentId: 87,
        path: '/personalCenter/personalInformation',
        redirect: '/personalCenter/personalInformation/personalInformation',
        component: 'Layout',
        name: '/personalCenter/personalInformation',
        realName: '个人信息',
        isMenu: true,
        sort: 1,
        mark: 1,
        meta: '{"title":"个人信息","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 89,
            parentId: 88,
            path: '/personalCenter/personalInformation/personalInformation',
            redirect: null,
            component: 'hegui/personalCenter/personalInformation/personalInformation.vue',
            name: '/personalCenter/personalInformation/personalInformation',
            realName: '个人信息',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"个人信息","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [],
            auths: [],
            isAvailable: null,
          },
          {
            id: 90,
            parentId: 88,
            path: '/personalCenter/personalInformation/basicData',
            redirect: null,
            component: 'hegui/personalCenter/personalInformation/basicData.vue',
            name: '/personalCenter/personalInformation/basicData',
            realName: '基本资料',
            isMenu: true,
            sort: 2,
            mark: 1,
            meta: '{"title":"基本资料","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
      {
        id: 91,
        parentId: 87,
        path: '/personalCenter/messageNotification',
        redirect: '/personalCenter/messageNotification/allMessages',
        component: 'Layout',
        name: '/personalCenter/messageNotification',
        realName: '消息通知',
        isMenu: true,
        sort: 2,
        mark: 1,
        meta: '{"title":"消息通知","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 92,
            parentId: 91,
            path: '/personalCenter/messageNotification/allMessages',
            redirect: null,
            component: 'hegui/personalCenter/messageNotification/allMessages.vue',
            name: '/personalCenter/messageNotification/allMessages',
            realName: '全部消息',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"全部消息","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 93,
                parentId: 92,
                path: '/personalCenter/messageNotification/allMessages/detail',
                redirect: null,
                component: 'hegui/personalCenter/messageNotification/allMessages_detail.vue',
                name: '/personalCenter/messageNotification/allMessages/detail',
                realName: '全部消息详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"全部消息详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/personalCenter/messageNotification/allMessages","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
      {
        id: 94,
        parentId: 87,
        path: '/personalCenter/toDoList',
        redirect: '/personalCenter/toDoList/toDoTasks',
        component: 'Layout',
        name: '/personalCenter/toDoList',
        realName: '待办事项',
        isMenu: true,
        sort: 3,
        mark: 1,
        meta: '{"title":"待办事项","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
        children: [
          {
            id: 95,
            parentId: 94,
            path: '/personalCenter/toDoList/toDoTasks',
            redirect: null,
            component: 'hegui/personalCenter/toDoList/toDoTasks.vue',
            name: '/personalCenter/toDoList/toDoTasks',
            realName: '待办任务',
            isMenu: true,
            sort: 1,
            mark: 1,
            meta: '{"title":"待办任务","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [
              {
                id: 96,
                parentId: 95,
                path: '/personalCenter/toDoList/toDoTasks/detail',
                redirect: null,
                component: 'hegui/personalCenter/toDoList/toDoTasks_detail.vue',
                name: '/personalCenter/toDoList/toDoTasks/detail',
                realName: '待办任务详情',
                isMenu: true,
                sort: 1,
                mark: 1,
                meta: '{"title":"待办任务详情","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":false,"breadcrumb":false,"activeMenu":"/personalCenter/toDoList/toDoTasks","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":false}',
                children: [],
                auths: [],
                isAvailable: null,
              },
            ],
            auths: [],
            isAvailable: null,
          },
          {
            id: 97,
            parentId: 94,
            path: '/personalCenter/toDoList/doneTasks',
            redirect: null,
            component: 'hegui/personalCenter/toDoList/doneTasks.vue',
            name: '/personalCenter/toDoList/doneTasks',
            realName: '已办任务',
            isMenu: true,
            sort: 2,
            mark: 1,
            meta: '{"title":"已办任务","icon":"","activeIcon":"","defaultOpened":false,"permanent":false,"auth":[],"menu":true,"breadcrumb":true,"activeMenu":"","cache":[],"noCache":[],"badge":"","link":"","iframe":"","copyright":false,"paddingBottom":"0px","sidebar":true}',
            children: [],
            auths: [],
            isAvailable: null,
          },
        ],
        auths: [],
        isAvailable: null,
      },
    ],
    auths: [],
    isAvailable: null,
  },
]
