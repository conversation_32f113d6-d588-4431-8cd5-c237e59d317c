import { cloneDeep } from 'lodash-es'
import { createDefu } from 'defu'
import { ElMessage } from 'element-plus'
import useSettingsStore from './settings'
import useTabbarStore from './tabbar'
import useRouteStore from './route'
import router from '@/router'
import type { Settings } from '#/global'
import apiUser from '@/api/modules/user'
import roleApi from '@/api/permissions/role'
import userApi from '@/api/modules/system/user'
import storage from '@/utils/storage'
import settingsDefault from '@/settings'

// 新增的获取权限地址 因为后端没有提供，所以在这里获取之后，进行循环取出

const useUserStore = defineStore(
  // 唯一ID
  'user',
  () => {
    const settingsStore = useSettingsStore()
    const tabbarStore = useTabbarStore()
    const routeStore = useRouteStore()
    // const menuStore = useMenuStore()
    // console.log(menuStore, 'menuStoremenuStoremenuStore')
    const account = ref(storage.local.get('account') ?? '')
    const token = ref(storage.local.get('token') ?? '')
    const tenantId = ref(storage.local.get('tenantId') ?? 1)
    const userId = ref(storage.local.get('userId') ? JSON.parse(storage.local.get('userId') as string) : '')
    const avatar = ref(storage.local.get('avatar') ?? '')
    const permissions = ref<string[]>([])
    const lawyer = ref(false)
    const orgTree = ref<any[]>([])
    const isLogin = computed(() => {
      if (token.value) {
        return true
      }
      return false
    })

    // 登录
    // async function login(data: {
    //   account: string
    //   password: string
    // }) {
    //   const res = await apiUser.login(data)
    //   storage.local.set('account', res.data.account)
    //   storage.local.set('token', res.data.token)
    //   storage.local.set('avatar', res.data.avatar)
    //   account.value = res.data.account
    //   token.value = res.data.token
    //   avatar.value = res.data.avatar
    // }
    // 登录
    async function login(data: any) {
      // console.log(data, '登录')
      // 远程路由
      const res: any = await apiUser.login(data)

      // 检查token是否存在
      if (!res || !res.token) {
        throw new Error('登录失败：未获取到有效的token')
      }

      storage.local.set('token', res.token)
      storage.local.set('tenantId', res.tenantId)
      storage.local.set('avatar', 'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg')
      storage.local.set('refreshToken', JSON.stringify(res.refreshToken))
      storage.local.set('userId', JSON.stringify(res.userId))
      account.value = 'admin'
      token.value = res.token
      tenantId.value = res.tenantId
      userId.value = res.userId
      avatar.value = 'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg'
      storage.local.set('userinfo', JSON.stringify(res))

      // 登录成功后获取组织树数据
      try {
        const orgTreeRes = await roleApi.unitTree()
        const positionRes = await roleApi.getAllPosition()
        const userDetail = await userApi.getEmpById(res.userId)
        storage.local.set('userDetail', JSON.stringify(userDetail))
        // 检查roleList中是否包含律师角色
        if (userDetail && userDetail.roleList) {
          const isLawyer = userDetail.roleList.some(role => role
            .name && role.name.includes('律师'))
          lawyer.value = isLawyer
        }
        else {
          lawyer.value = false
        }
        const departments = filterDepartments(orgTreeRes)
        storage.local.set('orgTree', JSON.stringify(orgTreeRes))
        storage.local.set('departments', JSON.stringify(departments))
        storage.local.set('positionRes', JSON.stringify(positionRes))
        // 更新响应式状态
        orgTree.value = orgTreeRes || []
      }
      catch (error) {
        console.error('获取组织树数据失败:', error)
        // 不阻断登录流程，只记录错误
      }

      // data.username = data.account
      // const res : any = await apiUser.login(data)
      // console.log(res, data.account, '登录打印')
      // storage.local.set('account', data.account ? data.account : '')
      // storage.local.set('token', res.access_token)
      // storage.local.set('avatar', res.avatar)
      // account.value = res.account || 'admin'
      // token.value = res.access_token
      // avatar.value = res.avatar

      // 本地路由
      // storage.local.set('account', 'admin')
      // storage.local.set('token', '*********')
      // storage.local.set('avatar', 'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg')
      // account.value = 'admin'
      // token.value = '123456'
      // avatar.value = 'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg'
    }
    /**
     * 递归筛选出type为DEPARTMENT的数据
     */
    function filterDepartments(data: any) {
      const departments: any = []
      const traverse = (nodes: any) => {
        if (!Array.isArray(nodes)) { return [] }

        nodes.forEach((node) => {
          if (node.type === 'DEPARTMENT' || node.type === 'TEAM') {
            departments.push(node)
          }

          // 如果有子节点，继续递归
          if (node.children && Array.isArray(node.children)) {
            traverse(node.children)
          }
        })
      }

      traverse(Array.isArray(data) ? data : [data])
      return departments
    }

    // 登出
    async function logout(_redirect = router.currentRoute.value.fullPath) {
      storage.local.remove('account')
      storage.local.remove('token')
      storage.local.remove('avatar')
      storage.local.remove('userinfo')
      account.value = ''
      token.value = ''
      avatar.value = ''
      permissions.value = []
      tabbarStore.clean()
      routeStore.removeRoutes()
      // menuStore.setActived(0)
      router.push({
        name: 'login',
        // query: {
        // ...(router.currentRoute.value.path !== settingsStore.settings.home.fullPath && router.currentRoute.value.name !== 'login' && { redirect }),
        // },
      })
    }
    // 提取所有auths中的value值
    function extractAuthsValues(routeData: any[]): string[] {
      const authsValues: string[] = []

      function traverse(routes: any[]) {
        routes.forEach((route) => {
          // 如果当前路由有auths数组，提取其中的value值
          if (route.auths && Array.isArray(route.auths)) {
            route.auths.forEach((auth: any) => {
              if (auth.value) {
                authsValues.push(auth.value)
              }
            })
          }

          // 递归处理子路由
          if (route.children && Array.isArray(route.children)) {
            traverse(route.children)
          }
        })
      }

      traverse(routeData)
      return authsValues
    }

    // 获取权限
    async function getPermissions() {
      // 检查userId是否存在
      if (!userId.value) {
        // 清除本地存储
        storage.local.remove('account')
        storage.local.remove('token')
        storage.local.remove('avatar')
        storage.local.remove('userinfo')
        storage.local.remove('userId')
        storage.local.remove('tenantId')
        storage.local.remove('refreshToken')

        // 重置状态
        account.value = ''
        token.value = ''
        avatar.value = ''
        permissions.value = []

        // 提示用户登录超时
        ElMessage.warning('登录已超时，请重新登录')

        // 跳转到登录页
        router.push({
          name: 'login',
        })

        return []
      }

      // console.log('获取当前登录用户信息')
      // const permissionBtn = await apiApp.routeList({ id: userId.value })
      // const authsValues = extractAuthsValues(permissionBtn || [])

      // console.log(press, 'pppppppppp')

      // 提取所有auths中的value值
      // console.log('提取的auths值:', authsValues)

      // const res : any = await apiUser.permission()
      const res: any = {
        permissionList: {
          // auth: [],
          auths: [],
          auth: ['/home'],
          // auths: authsValues, // 使用提取的auths值
        },
      }
      storage.local.set('userinfo', JSON.stringify(res))
      // console.log(res, '获取权限')
      permissions.value = [...res.permissionList.auth, ...res.permissionList.auths]
      // permissions.value = [] // 测试环境权限制空
      // console.log(permissions.value, '所有权限-权限列表')
      return permissions.value
    }

    // 修改密码
    async function editPassword(data: {
      password: string
      newpassword: string
    }) {
      await apiUser.passwordEdit(data)
    }

    // 框架已将可提供给用户配置的选项提取出来，请勿新增其他选项，不需要的选项可以在这里注释掉
    const preferences = ref<Settings.all>({
      app: {
        colorScheme: settingsDefault.app.colorScheme,
        lightTheme: settingsDefault.app.lightTheme,
        darkTheme: settingsDefault.app.darkTheme,
        enableProgress: settingsDefault.app.enableProgress,
        defaultLang: settingsDefault.app.defaultLang,
      },
      menu: {
        menuMode: settingsDefault.menu.menuMode,
        isRounded: settingsDefault.menu.isRounded,
        menuActiveStyle: settingsDefault.menu.menuActiveStyle,
        switchMainMenuAndPageJump: settingsDefault.menu.switchMainMenuAndPageJump,
        subMenuOnlyOneHide: settingsDefault.menu.subMenuOnlyOneHide,
        subMenuUniqueOpened: settingsDefault.menu.subMenuUniqueOpened,
        subMenuCollapse: settingsDefault.menu.subMenuCollapse,
        subMenuAutoCollapse: settingsDefault.menu.subMenuAutoCollapse,
        enableSubMenuCollapseButton: settingsDefault.menu.enableSubMenuCollapseButton,
      },
      layout: {
        widthMode: settingsDefault.layout.widthMode,
      },
      mainPage: {
        enableTransition: settingsDefault.mainPage.enableTransition,
        transitionMode: settingsDefault.mainPage.transitionMode,
      },
      topbar: {
        mode: settingsDefault.topbar.mode,
        switchTabbarAndToolbar: settingsDefault.topbar.switchTabbarAndToolbar,
      },
      tabbar: {
        style: settingsDefault.tabbar.style,
        enableIcon: settingsDefault.tabbar.enableIcon,
        enableMemory: settingsDefault.tabbar.enableMemory,
      },
      toolbar: {
        breadcrumb: settingsDefault.toolbar.breadcrumb,
        navSearch: settingsDefault.toolbar.navSearch,
        fullscreen: settingsDefault.toolbar.fullscreen,
        pageReload: settingsDefault.toolbar.pageReload,
        colorScheme: settingsDefault.toolbar.colorScheme,
        layout: settingsDefault.toolbar.layout,
      },
      breadcrumb: {
        style: settingsDefault.breadcrumb.style,
        enableMainMenu: settingsDefault.breadcrumb.enableMainMenu,
      },
    })
    // 此处没有使用 lodash 的 defaultsDeep 函数，而是基于 defu 库自定义了一个函数，只合并 settings 中有的属性，而不是全部合并，这样做的目的是为了排除用户历史遗留的偏好配置
    const customDefaultsDeep = createDefu((obj, key, value) => {
      if (obj[key] === undefined) {
        delete obj[key]
        return true
      }
      if (Array.isArray(obj[key]) && Array.isArray(value)) {
        obj[key] = value
        return true
      }
    })
    // isPreferencesUpdating 用于防止循环更新
    let isPreferencesUpdating = false
    watch(preferences, (val) => {
      if (!settingsStore.settings.userPreferences.enable) {
        return
      }
      if (!isPreferencesUpdating) {
        isPreferencesUpdating = true
        settingsStore.updateSettings(cloneDeep(val))
      }
      else {
        isPreferencesUpdating = false
      }
      updatePreferences(cloneDeep(val))
    }, {
      deep: true,
    })
    watch(() => settingsStore.settings, (val) => {
      if (!settingsStore.settings.userPreferences.enable) {
        return
      }
      if (!isPreferencesUpdating) {
        isPreferencesUpdating = true
        preferences.value = customDefaultsDeep(val, preferences.value)
      }
      else {
        isPreferencesUpdating = false
      }
    }, {
      deep: true,
    })
    // isPreferencesInited 用于防止初始化时触发更新
    let isPreferencesInited = false
    // 获取偏好设置
    async function getPreferences() {
      let data: Settings.all = {}
      if (settingsStore.settings.userPreferences.storageTo === 'local') {
        if (storage.local.has('userPreferences')) {
          data = JSON.parse(storage.local.get('userPreferences') as string)[account.value] || {}
        }
      }
      else if (settingsStore.settings.userPreferences.storageTo === 'server') {
        const res = await apiUser.preferences()
        data = JSON.parse(res.data.preferences || '{}') as Settings.all
      }
      preferences.value = customDefaultsDeep(data, preferences.value)
    }
    // 更新偏好设置
    async function updatePreferences(data: Settings.all = {}) {
      if (!isPreferencesInited) {
        isPreferencesInited = true
        return
      }
      if (settingsStore.settings.userPreferences.storageTo === 'local') {
        const userPreferencesData = storage.local.has('userPreferences') ? JSON.parse(storage.local.get('userPreferences') as string) : {}
        userPreferencesData[account.value] = data
        storage.local.set('userPreferences', JSON.stringify(userPreferencesData))
      }
      else if (settingsStore.settings.userPreferences.storageTo === 'server') {
        await apiUser.preferencesEdit(JSON.stringify(data))
      }
    }

    // 获取部门列表
    function getDepartments() {
      try {
        const departmentsData = storage.local.get('departments')
        return departmentsData ? JSON.parse(departmentsData) : []
      }
      catch (error) {
        console.error('获取部门数据失败:', error)
        return []
      }
    }

    // 获取岗位列表
    function getPostList() {
      try {
        const positionData = storage.local.get('positionRes')
        if (positionData) {
          const positions = JSON.parse(positionData)
          // 如果是分页数据结构，提取content数组
          return positions.content || positions || []
        }
        return []
      }
      catch (error) {
        console.error('获取岗位数据失败:', error)
        return []
      }
    }

    // 获取组织树数据
    function getOrgTree() {
      try {
        // 如果响应式状态中有数据，直接返回
        if (orgTree.value && orgTree.value.length > 0) {
          return orgTree.value
        }
        // 否则从localStorage中获取
        const orgTreeData = storage.local.get('orgTree')
        if (orgTreeData) {
          const parsedData = JSON.parse(orgTreeData)
          orgTree.value = parsedData || []
          return orgTree.value
        }
        return []
      }
      catch (error) {
        console.error('获取组织树数据失败:', error)
        return []
      }
    }

    return {
      account,
      token,
      tenantId,
      userId,
      avatar,
      permissions,
      orgTree,
      isLogin,
      login,
      lawyer,
      logout,
      getPermissions,
      extractAuthsValues,
      editPassword,
      preferences,
      getPreferences,
      updatePreferences,
      getDepartments,
      getPostList,
      getOrgTree,
    }
  },
)

export default useUserStore
