export const menuList = [

  {
    id: 1,
    name: '一体',
    path: '/one',
    name: '/one',
    children: [

      {
        name: '制度管理',
        path: '/one/systemManagement',
        name: '/one/systemManagement',
        component: `hegui/one/system/systemManagement/index.vue`,
        children: [{
            name: '制度库管理',
            path: '/one/systemManagement/index',
            name: '/one/systemManagement/index',
            children: [

              {
                path: '/one/systemManagement/detail',
                name: '/one/systemManagement/detail',
                component: `hegui/one/system/systemManagement/detail.vue`,
                children: false,
                meta: {
                  title: '详情页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
              {
                path: '/one/systemManagement/addEdit',
                name: '/one/systemManagement/addEdit',
                component: `hegui/one/system/systemManagement/addEdit.vue`,
                children: false,
                meta: {
                  title: '新增/修改页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
            ],
          },
          {
            name: '法规转化',
            path: '/one/regulatoryConversion/index',
            name: '/one/regulatoryConversion/index',
            children: [{
                path: '/one/regulatoryConversion/detail',
                name: '/one/regulatoryConversion/detail',
                component: `hegui/one/system/regulatoryConversion/detail.vue`,
                children: false,
                meta: {
                  title: '详情页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
              {
                path: '/one/regulatoryConversion/addEdit',
                name: '/one/regulatoryConversion/addEdit',
                component: `hegui/one/system/regulatoryConversion/addEdit.vue`,
                children: false,
                meta: {
                  title: '新增/修改页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
            ],
          },
          {
            name: '制度新增与审查',
            path: '/one/systemAdditionAndReview/index',
            name: '/one/systemAdditionAndReview/index',
            children: [{
                path: '/one/systemAdditionAndReview/detail',
                name: '/one/systemAdditionAndReview/detail',
                component: `hegui/one/system/systemAdditionAndReview/detail.vue`,
                children: false,
                meta: {
                  title: '详情页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
              {
                path: '/one/systemAdditionAndReview/report',
                name: '/one/systemAdditionAndReview/report',
                component: `hegui/one/system/systemAdditionAndReview/report.vue`,
                children: false,
                meta: {
                  title: '新增/修改页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
            ],
          },
        ]
      },
      {
        name: '合规数据库',
        children: [

          {
            name: '法规库',
            path: '/database/complianceObligationLibrary/index',
            name: '/database/complianceObligationLibrary/index',
            component: `hegui/database/system/complianceObligationLibrary/index.vue`,
            // children: [

            //   {
            //     path: '/database/complianceObligationLibrary/detail',
            //     name: '/database/complianceObligationLibrary/detail',
            //     component: `hegui/database/system/complianceObligationLibrary/detail.vue`,
            //     meta: {
            //       title: '详情页面',
            //       sidebar: false,
            //       breadcrumb: false,
            //     },
            //   },
            //   {
            //     path: '/database/complianceObligationLibrary/addEdit',
            //     name: '/database/complianceObligationLibrary/addEdit',
            //     component: `hegui/database/system/complianceObligationLibrary/addEdit.vue`,
            //     meta: {
            //       title: '新增/修改页面',
            //       sidebar: false,
            //       breadcrumb: false,
            //     },
            //   },
            // ],
          },
          {
            name: '合规义务库',
            path: '/database/complianceObligationLibraryMode/index',
            name: '/database/complianceObligationLibraryMode/index',
            component: `hegui/database/system/complianceObligationLibraryMode/index.vue`,
            children: [

              {
                path: '/database/complianceObligationLibraryMode/detail',
                name: '/database/complianceObligationLibraryMode/detail',
                component: `hegui/database/system/complianceObligationLibraryMode/detail.vue`,
                meta: {
                  title: '详情页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
              {
                path: '/database/complianceObligationLibraryMode/addEdit',
                name: '/database/complianceObligationLibraryMode/addEdit',
                component: `hegui/database/system/complianceObligationLibraryMode/addEdit.vue`,
                meta: {
                  title: '新增/修改页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
            ],
          },
          {
            name: '合规案例库',
            path: '/database/complianceCaseLibrary/index',
            name: '/database/complianceCaseLibrary/index',
            component: `hegui/database/system/complianceCaseLibrary/index.vue`,
            children: [

              {
                path: '/database/complianceCaseLibrary/detail',
                name: '/database/complianceCaseLibrary/detail',
                component: `hegui/database/system/complianceCaseLibrary/detail.vue`,
                meta: {
                  title: '详情页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
              {
                path: '/database/complianceCaseLibrary/addEdit',
                name: '/database/complianceCaseLibrary/addEdit',
                component: `hegui/database/system/complianceCaseLibrary/addEdit.vue`,
                meta: {
                  title: '新增/修改页面',
                  sidebar: false,
                  breadcrumb: false,
                },
              },
            ],
          },
        ]
      }
    ]
  }
]
