[{"id": 672, "name": "中合数联（北京）科技有限公司", "code": "COMPANY_001", "type": "COMPANY", "level": 1, "status": 1, "sortOrder": 1, "description": "总公司", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:05:00", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:05:00", "isDeleted": false, "parentId": null, "children": [{"id": 673, "name": "中合数联（上海）科技有限公司", "code": "COMPANY_002", "type": "SUBSIDIARY", "level": 2, "status": 1, "sortOrder": 2, "description": "分公司", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:10:04", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:10:04", "isDeleted": false, "parentId": 672, "children": [{"id": 661, "name": "总经理办公室", "code": "CEO_OFFICE", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "企业最高管理层，负责战略决策和整体运营管理", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}, {"id": 662, "name": "综合管理部", "code": "ADMINISTRATION_DEPT", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "负责行政事务、人力资源和后勤保障", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}, {"id": 663, "name": "财务部", "code": "FINANCE_DEPT", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "负责财务核算、资金管理、成本控制和财务合规", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}, {"id": 664, "name": "法务合规部", "code": "LEGAL_COMPLIANCE_DEPT", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "负责法律事务处理、合同管理和合规风险控制", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}, {"id": 665, "name": "市场部", "code": "MARKETING_DEPT", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "负责市场调研、品牌推广和营销策略制定", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}, {"id": 666, "name": "销售部", "code": "SALES_DEPT", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "负责产品销售、客户关系维护和销售目标达成", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}, {"id": 667, "name": "客户成功部", "code": "CUSTOMER_SUCCESS_DEPT", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "负责客户服务、项目支持和客户满意度管理", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}, {"id": 668, "name": "产品研发部", "code": "PRODUCT_RD_DEPT", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "负责产品需求分析、方案设计及原型开发", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}, {"id": 669, "name": "技术部", "code": "TECH_DEPT", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "负责信息系统开发、技术支持和网络安全管理", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}, {"id": 670, "name": "测试与运维部", "code": "QA_OPS_DEPT", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 0, "description": "负责系统测试、性能优化及运维保障", "metadata": null, "version": 1, "createdBy": "中合数联（test）苏州有限公司351", "createdAt": "2025-06-30 14:24:24", "updatedBy": "中合数联（test）苏州有限公司351", "updatedAt": "2025-06-30 14:24:24", "isDeleted": false, "parentId": 673, "children": []}]}, {"id": 674, "name": "中合数联（深圳）科技有限公司", "code": "COMPANY_003", "type": "SUBSIDIARY", "level": 2, "status": 1, "sortOrder": 2, "description": "分公司", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:10:04", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:10:04", "isDeleted": false, "parentId": 672, "children": [{"id": 675, "name": "总经理办公室", "code": "CEO_OFFICE1", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "企业最高管理层，负责战略决策和整体运营管理", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}, {"id": 676, "name": "综合管理部", "code": "ADMINISTRATION_DEPT2", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "负责行政事务、人力资源和后勤保障", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}, {"id": 677, "name": "财务部", "code": "FINANCE_DEPT3", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "负责财务核算、资金管理、成本控制和财务合规", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}, {"id": 678, "name": "法务合规部", "code": "LEGAL_COMPLIANCE_DEPT4", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "负责法律事务处理、合同管理和合规风险控制", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}, {"id": 679, "name": "市场部", "code": "MARKETING_DEPT5", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "负责市场调研、品牌推广和营销策略制定", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}, {"id": 680, "name": "销售部", "code": "SALES_DEPT6", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "负责产品销售、客户关系维护和销售目标达成", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}, {"id": 681, "name": "客户成功部", "code": "CUSTOMER_SUCCESS_DEPT7", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "负责客户服务、项目支持和客户满意度管理", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}, {"id": 682, "name": "产品研发部", "code": "PRODUCT_RD_DEPT8", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "负责产品需求分析、方案设计及原型开发", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}, {"id": 683, "name": "技术部", "code": "TECH_DEPT9", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "负责信息系统开发、技术支持和网络安全管理", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}, {"id": 684, "name": "测试与运维部", "code": "QA_OPS_DEPT0", "type": "DEPARTMENT", "level": 3, "status": 1, "sortOrder": 3, "description": "负责系统测试、性能优化及运维保障", "metadata": null, "version": 1, "createdBy": "中合数联（北京）科技有限公司", "createdAt": "2025-06-30 15:23:47", "updatedBy": "中合数联（北京）科技有限公司", "updatedAt": "2025-06-30 15:23:47", "isDeleted": false, "parentId": 674, "children": []}]}]}]