import { cloneDeep } from 'lodash-es'
import type { RouteMeta, RouteRecordRaw } from 'vue-router'
import useSettingsStore from './settings'
import useTabbarStore from './tabbar'
import useUserStore from './user'
import { menuList } from './menuTest'
import { resolveRoutePath } from '@/utils'
import { systemRoutes } from '@/router/routes'
import apiApp from '@/api/modules/app'
import type { Route } from '#/global'

console.log('menuList', menuList)

const useRouteStore = defineStore(
  // 唯一ID
  'route',
  () => {
    const settingsStore = useSettingsStore()
    const tabbarStore = useTabbarStore()
    const userStore = useUserStore()

    const isGenerate = ref(false)
    const routesRaw = ref<Route.recordMainRaw[]>([])
    const filesystemRoutesRaw = ref<RouteRecordRaw[]>([])
    const currentRemoveRoutes = ref<(() => void)[]>([])

    // 将多层嵌套路由处理成两层，保留顶层和最子层路由，中间层级将被拍平
    function flatAsyncRoutes<T extends RouteRecordRaw>(route: T): T {
      if (route.children) {
        route.children = flatAsyncRoutesRecursive(
          route.children,
          [
            {
              path: route.path,
              title: route.meta?.title,
              i18n: route.meta?.i18n,
              icon: route.meta?.icon,
              activeIcon: route.meta?.activeIcon,
              hide: !route.meta?.breadcrumb && route.meta?.breadcrumb === false,
            },
          ],
          route.path,
          route.meta?.auth,
        )
      }
      return route
    }
    function flatAsyncRoutesRecursive(routes: RouteRecordRaw[], breadcrumb: Route.breadcrumb[] = [], baseUrl = '', baseAuth: RouteMeta['auth']): RouteRecordRaw[] {
      const res: RouteRecordRaw[] = []
      routes.forEach((route) => {
        if (route.children) {
          const childrenBaseUrl = resolveRoutePath(baseUrl, route.path)
          const childrenBaseAuth = !baseAuth || baseAuth === '' || baseAuth?.length === 0 ? route.meta?.auth : baseAuth
          const tmpBreadcrumb = cloneDeep(breadcrumb)
          tmpBreadcrumb.push({
            path: childrenBaseUrl,
            title: route.meta?.title,
            i18n: route.meta?.i18n,
            icon: route.meta?.icon,
            activeIcon: route.meta?.activeIcon,
            hide: !route.meta?.breadcrumb && route.meta?.breadcrumb === false,
          })
          const tmpRoute = cloneDeep(route)
          tmpRoute.path = childrenBaseUrl
          if (!tmpRoute.meta) {
            tmpRoute.meta = {}
          }
          tmpRoute.meta.auth = childrenBaseAuth
          tmpRoute.meta.breadcrumbNeste = tmpBreadcrumb
          delete tmpRoute.children
          res.push(tmpRoute)
          const childrenRoutes = flatAsyncRoutesRecursive(route.children, tmpBreadcrumb, childrenBaseUrl, childrenBaseAuth)
          childrenRoutes.forEach((item) => {
            // 如果 path 一样则覆盖，因为子路由的 path 可能设置为空，导致和父路由一样，直接注册会提示路由重复
            if (res.some(v => v.path === item.path)) {
              res.forEach((v, i) => {
                if (v.path === item.path) {
                  res[i] = item
                }
              })
            }
            else {
              res.push(item)
            }
          })
        }
        else {
          const tmpRoute = cloneDeep(route)
          tmpRoute.path = resolveRoutePath(baseUrl, tmpRoute.path)
          // 处理面包屑导航
          const tmpBreadcrumb = cloneDeep(breadcrumb)
          tmpBreadcrumb.push({
            path: tmpRoute.path,
            title: tmpRoute.meta?.title,
            i18n: tmpRoute.meta?.i18n,
            icon: tmpRoute.meta?.icon,
            activeIcon: tmpRoute.meta?.activeIcon,
            hide: !tmpRoute.meta?.breadcrumb && tmpRoute.meta?.breadcrumb === false,
          })
          if (!tmpRoute.meta) {
            tmpRoute.meta = {}
          }
          tmpRoute.meta.auth = !baseAuth || baseAuth === '' || baseAuth?.length === 0 ? tmpRoute.meta?.auth : baseAuth
          tmpRoute.meta.breadcrumbNeste = tmpBreadcrumb
          res.push(tmpRoute)
        }
      })
      return res
    }
    // 扁平化路由（将三级及以上路由数据拍平成二级）
    const flatRoutes = computed(() => {
      const returnRoutes: RouteRecordRaw[] = []
      if (settingsStore.settings.app.routeBaseOn !== 'filesystem') {
        if (routesRaw.value) {
          routesRaw.value.forEach((item) => {
            const tmpRoutes = cloneDeep(item.children) as RouteRecordRaw[]
            tmpRoutes.map((v) => {
              if (!v.meta) {
                v.meta = {}
              }
              v.meta.auth = item.meta?.auth ?? v.meta?.auth
              return v
            })
            returnRoutes.push(...tmpRoutes)
          })
          returnRoutes.forEach(item => flatAsyncRoutes(item))
        }
      }
      else {
        returnRoutes.push(...(cloneDeep(filesystemRoutesRaw.value) as RouteRecordRaw[]))
      }
      return returnRoutes
    })
    const flatSystemRoutes = computed(() => {
      const routes = [...systemRoutes]
      routes.forEach(item => flatAsyncRoutes(item))
      return routes
    })

    // 将设置 meta.singleMenu 的一级路由转换成二级路由
    function convertSingleRoutes<T extends Route.recordMainRaw[]>(routes: T): T {
      routes.map((route) => {
        if (route.children) {
          route.children.forEach((item, index, arr) => {
            if (item.meta?.singleMenu) {
              arr[index] = {
                ...item,
                component: () => import('@/layouts/index.vue'),
                children: [
                  {
                    path: '',
                    component: item.component,
                    meta: {
                      title: item.meta.title,
                      i18n: item.meta.i18n,
                      menu: false,
                      breadcrumb: false,
                    },
                  },
                ],
              } as RouteRecordRaw
              delete arr[index].meta!.singleMenu
            }
          })
        }
        return route
      })
      return routes
    }

    // TODO 将设置 meta.sidebar 的属性转换成 meta.menu ，过渡处理，未来将被弃用
    let isUsedDeprecatedAttribute = false
    function converDeprecatedAttribute<T extends Route.recordMainRaw[]>(routes: T): T {
      routes.forEach((route) => {
        route.children = converDeprecatedAttributeRecursive(route.children)
      })
      if (isUsedDeprecatedAttribute) {
        // turbo-console-disable-next-line
        console.warn('[Fantastic-admin] 路由配置中的 "sidebar" 属性即将被弃用, 请尽快替换为 "menu" 属性')
      }
      return routes
    }
    function converDeprecatedAttributeRecursive(routes: RouteRecordRaw[]) {
      if (routes) {
        routes.forEach((route) => {
          if (typeof route.meta?.sidebar === 'boolean') {
            isUsedDeprecatedAttribute = true
            route.meta.menu = route.meta.sidebar
            delete route.meta.sidebar
          }
          if (route.children) {
            converDeprecatedAttributeRecursive(route.children)
          }
        })
      }
      return routes
    }

    // 生成路由（前端生成）
    function generateRoutesAtFront(asyncRoutes: Route.recordMainRaw[]) {
      // 设置 routes 数据
      routesRaw.value = converDeprecatedAttribute(convertSingleRoutes(cloneDeep(asyncRoutes) as any))
      isGenerate.value = true
      // 加载常驻标签页
      if (settingsStore.settings.tabbar.enable) {
        tabbarStore.initPermanentTab()
      }
    }
    // 格式化后端路由数据
    function formatBackRoutes(routes: any, views = import.meta.glob('../../views/**/*.vue')): Route.recordMainRaw[] {
      return routes.map((route: any) => {
        // 解析 meta 数据（如果是 JSON 字符串）
        let meta = {}
        if (route.meta) {
          try {
            meta = typeof route.meta === 'string' ? JSON.parse(route.meta) : route.meta
          }
          catch (error) {
            console.warn('解析路由 meta 数据失败:', error)
            meta = {}
          }
        }

        // 处理权限数据
        if (route.auths && route.auths.length > 0) {
          meta.auth = route.auths.map((auth: any) => auth.value || auth.name).filter(Boolean)
        }

        // 设置菜单显示控制
        if (typeof route.isMenu === 'boolean') {
          meta.menu = route.isMenu
        }

        // 设置标题（如果 meta 中没有 title 且 route 有 name）
        if (!meta.title && route.name && route.name !== route.path) {
          meta.title = route.name
        }

        // 处理组件路径
        switch (route.component) {
          case 'Layout':
            route.component = () => import('@/layouts/index.vue')
            break
          case '':
          case null:
          case undefined:
            // 如果有子路由且没有组件，设置为 Layout
            if (route.children && route.children.length > 0) {
              route.component = () => import('@/layouts/index.vue')
            }
            else {
              delete route.component
            }
            break
          default:
            if (route.component) {
              // 处理组件路径，确保以 / 开头的路径被正确处理
              const componentPath = route.component.startsWith('/')
                ? route.component.substring(1)
                : route.component
              route.component = views[`../../views/${componentPath}`]
            }
        }

        // 设置处理后的 meta
        route.meta = meta

        // 递归处理子路由
        if (route.children) {
          route.children = formatBackRoutes(route.children, views)
        }

        // 清理不需要的字段
        delete route.auths
        delete route.isMenu
        delete route.parentId
        delete route.id
        delete route.sort
        delete route.mark

        return route
      })
    }
    // 生成路由（后端获取）
    async function generateRoutesAtBack() {
      await apiApp
        .routeList({ id: userStore.userId })
        .then((res: any) => {
          // const arrID: any = [2, 298, 293, 284, 279, 272, 263, 307,301]
          // const filteredArray: any = res.data.filter((obj: any) => arrID.includes(obj.id))
          // console.log('filteredArray', filteredArray)
          // filteredArray.forEach((i:any)=>{
          //  if(i.children && i.children.length){
          //   i.children.forEach((a:any)=>{
          // 	  if(a.id === 268){
          // 		  a.meta.badge = 2
          // 	  }
          //   })
          //  }
          // })
          console.log('res.data', res)
          // routesRaw.value = []
          // isGenerate.value = true
          // return
          const rssarr = res
          console.log('rssarr', rssarr)
          // 处理路由数据：第一层name和path置为空字符串，最后一层children置为null
          const processRouteData = (data: any[]): any[] => {
            return data.map((item) => {
              const processedItem = { ...item }

              // 第一层：将name和path置为空字符串
              if (item.parentId === 0) {
                processedItem.path = ''
              }

              // 递归处理children
              if (item.children && item.children.length > 0) {
                processedItem.children = processRouteData(item.children)
              }
              else {
                // 最后一层：将children置为null
                processedItem.children = null
              }

              return processedItem
            })
          }

          // 使用处理方法
          const processedData = processRouteData(rssarr)

          console.log('processedData', processedData)
          routesRaw.value = []
          isGenerate.value = true
          return
          const filteredArray: any = processedData
          if (import.meta.env.MODE !== 'development') {
            filteredArray.forEach((i: any) => {
              if (i.children && i.children.length) {
                i.children.forEach((a: any, b: any) => {
                  if (a.id === 284) {
                    i.children.splice(b, 1)
                  }
                  else if (a.id === 33) {
                    i.children.splice(b, 1)
                  }
                })
              }
            })
          }

          // 设置 routes 数据
          routesRaw.value = converDeprecatedAttribute(convertSingleRoutes(formatBackRoutes(filteredArray) as any))
          isGenerate.value = true
          // 初始化常驻标签页
          if (settingsStore.settings.tabbar.enable) {
            tabbarStore.initPermanentTab()
          }
        })
        .catch(() => { })
    }
    // 生成路由（文件系统生成）
    function generateRoutesAtFilesystem(asyncRoutes: RouteRecordRaw[]) {
      // 设置 routes 数据
      filesystemRoutesRaw.value = convertSingleRoutes(cloneDeep(asyncRoutes) as any)
      isGenerate.value = true
      // 加载常驻标签页
      if (settingsStore.settings.tabbar.enable) {
        tabbarStore.initPermanentTab()
      }
    }
    // 记录 accessRoutes 路由，用于登出时删除路由
    function setCurrentRemoveRoutes(routes: (() => void)[]) {
      currentRemoveRoutes.value = routes
    }
    // 清空动态路由
    function removeRoutes() {
      isGenerate.value = false
      routesRaw.value = []
      filesystemRoutesRaw.value = []
      currentRemoveRoutes.value.forEach((removeRoute) => {
        removeRoute()
      })
      currentRemoveRoutes.value = []
    }

    return {
      isGenerate,
      routesRaw,
      currentRemoveRoutes,
      flatRoutes,
      flatSystemRoutes,
      generateRoutesAtFront,
      generateRoutesAtBack,
      generateRoutesAtFilesystem,
      setCurrentRemoveRoutes,
      removeRoutes,
    }
  },
)

export default useRouteStore
