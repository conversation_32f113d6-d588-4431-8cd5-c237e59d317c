// 定义关于counter的store
import { defineStore } from 'pinia'

/* defineStore 是需要传参数的，其中第一个参数是id，就是一个唯一的值，
简单点说就可以理解成是一个命名空间.
第二个参数就是一个对象，里面有三个模块需要处理，第一个是 state，
第二个是 getters，第三个是 actions。
*/
const setModule = defineStore('ccp-settings', {
  state: () => ({
    count: 66,
    brand: [], // 品牌
    bard: [{
      id: 6,
      name: '小车品牌',
      code: 'smallVm',
      dict_id: 3,
      status: 1,
      note: '小车型',
      sort: 1,
      create_user: 1,
      create_time: '2023-11-09 17:20:37',
      update_user: 1,
      update_time: 1700309982,
      mark: 1,
      create_user_name: '管理员',
      update_user_name: '管理员',
      num: 10,
    }, {
      id: 5,
      name: '大车品牌',
      code: 'bigVm',
      dict_id: 3,
      status: 1,
      note: '大车标',
      sort: 2,
      create_user: 1,
      create_time: '2023-11-09 17:19:53',
      update_user: 1,
      update_time: 1700309990,
      mark: 1,
      create_user_name: '管理员',
      update_user_name: '管理员',
      num: 10,
    }],
    // 会员用户列表
    bardList: [
      {
        id: 1,
        sort: 1,
        note: '',
        create_user: 1,
        create_time: '2023-12-23 09:52:07',
        update_user: 0,
        update_time: 0,
        mark: 1,
        type: {
          id: 6,
          name: '小车品牌',
          status: 1,
          mark: 1,
        },
        car_image: '/set/vehiclemodel/60715982809523972d4dd069eb6a5374.png',
        name: '奥迪',
        initial: 'A',
        status: 1,
        create_user_name: '管理员',
        children: [
          {
            id: 1,
            name: '奥迪1',
          },
          {
            id: 2,
            name: '奥迪2',
          },
          {
            id: 3,
            name: '奥迪3',
          },
        ],

      },
      {
        id: 3,
        sort: 1,
        note: '',
        create_user: 1,
        create_time: '2023-12-23 09:53:07',
        update_user: 0,
        update_time: 0,
        mark: 1,
        type: {
          id: 6,
          name: '小车品牌',
          status: 1,
          mark: 1,
        },
        car_image: '/set/vehiclemodel/18b92ee675d2cc82244ef1f7b5264cd1.png',
        name: '奔驰',
        initial: 'B',
        status: 1,
        create_user_name: '管理员',
      },
      {
        id: 4,
        sort: 1,
        note: '',
        create_user: 1,
        create_time: '2023-12-23 09:53:25',
        update_user: 0,
        update_time: 0,
        mark: 1,
        type: {
          id: 6,
          name: '小车品牌',
          status: 1,
          mark: 1,
        },
        car_image: '/set/vehiclemodel/a6e0e01eccd24c8cc09b695fbbc4cd1b.png',
        name: '保时捷',
        initial: 'B',
        status: 1,
        create_user_name: '管理员',
      },
    ],
    // 类目
    categoryList: [
      {
        id: 1,
        name: '单项易损',
        children: [
          {
            id: 1,
            name: '易损系列',
            children: [
              {
                id: 1,
                name: '常用1',
              },
              {
                id: 2,
                name: '常用2',
              },

            ],
          },
          {
            id: 2,
            name: '轮胎/轮毂',
            children: [
              {
                id: 1,
                name: '常用1',
              },
              {
                id: 2,
                name: '常用2',
              },
            ],
          },

        ],
      },
      {
        id: 2,
        name: '装潢用品',
      },
      {
        id: 3,
        name: '工具设备',
      },
      {
        id: 4,
        name: '机械专用',
      },
    ],
  }),

  getters: {

  },

  actions: {

  },
})

// 暴露这个useCounter模块
export default setModule
