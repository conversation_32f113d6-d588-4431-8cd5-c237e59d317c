import { defaultsDeep } from 'lodash-es'
import type { RecursiveRequired, Settings } from '#/global'
import settingsDefault from '@/settings.default'

// const globalSettings: Settings.all = {
//   // 请在此处编写或粘贴配置代码
//   app: {
//     lightTheme: 'naive',
//     enablePermission: true,
//     enableDynamicTitle: true,
//     enableErrorLog: true,
//     // routeBaseOn: 'frontend', // 前端路由
//     routeBaseOn: 'backend', // 后端路由
//   },
//   menu: {
//     // menuMode: 'only-head',
//     // isRounded: true,
//     // menuActiveStyle: 'dot',
//     // enableSubMenuCollapseButton: true,
//     // enableHotkeys: true,
//     // subMenuCollapse: true,
//     // menuFillStyle: 'radius',
//     isRounded: true,
//     menuMode: 'only-head',
//     menuActiveStyle: 'dot',
//     enableSubMenuCollapseButton: true,
//     enableHotkeys: true,
//     baseOn: 'frontend',
//     switchMainMenuAndPageJump: true,
//     subMenuUniqueOpened: true,
//     subMenuOnlyOneHide: true,
//     subMenuCollapse: false,
//   },
//   layout: {
//     enableMobileAdaptation: true,
//   },
//   mainPage: {
//     transitionMode: 'slide-right',
//     iframeCacheMax: 9,
//   },
//   tabbar: {
//     style: 'fashion',
//     enableIcon: true,
//     enableMemory: true,
//     enableHotkeys: true,
//   },
//   toolbar: {
//     fullscreen: true,
//     pageReload: true,
//     layout: [
//       'favorites',
//       'breadcrumb',
//       'navSearch',
//       '->',
//       'notification',
//       'i18n',
//       'fullscreen',
//       'pageReload',
//       'colorScheme',
//     ],
//     favorites: true,
//     notification: true,
//   },
//   breadcrumb: {
//     style: 'modern',
//     enableMainMenu: true,
//   },
//   userPreferences: {
//     enable: true,
//   },
//   // copyright: {
//   //   enable: true,
//   //   dates: '2020-present',
//   //   company: 'Fantastic-admin',
//   //   website: 'https://fantastic-admin.gitee.io',
//   // },
// }

const globalSettings: Settings.all = {

  app: {
    lightTheme: 'classic',
    enablePermission: true,
    enableDynamicTitle: true,
    enableErrorLog: true,
    routeBaseOn: 'backend',
  },
  menu: {
    menuMode: 'head',
    isRounded: true,
    menuActiveStyle: 'dot',
    subMenuOnlyOneHide: true,
    enableSubMenuCollapseButton: true,
    enableHotkeys: true,
  },
  layout: {
    enableMobileAdaptation: true,
  },
  mainPage: {
    transitionMode: 'slide-right',
    iframeCacheMax: 9,
  },
  tabbar: {
    style: 'fashion',
    enableIcon: true,
    enableMemory: true,
    enableHotkeys: true,
  },
  toolbar: {
    fullscreen: true,
    pageReload: true,
    layout: [
      'favorites',
      'breadcrumb',
      'navSearch',
      '->',
      'notification',
      'i18n',
      'fullscreen',
      'pageReload',
      'colorScheme',
    ],
    favorites: true,
  },
  breadcrumb: {
    style: 'modern',
    enableMainMenu: true,
  },
  userPreferences: {
    enable: true,
  },

}

export default defaultsDeep(globalSettings, settingsDefault) as RecursiveRequired<Settings.all>
