<script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import eventBus from '@/utils/eventBus'
  import useSettingsStore from '@/store/modules/settings'
  import useUser from '@/api/modules/user'
  import useUserStore from '@/store/modules/user'
  import storage from '@/utils/storage'
  const userinfo : any = ref(JSON.parse(storage.local.get('userinfo') || '') ?? '')

  defineOptions({
    name: 'Respwd',
  })
  const userStore = useUserStore()
  const isShow = ref(false)

  const settingsStore = useSettingsStore()
  const formRef = ref<FormInstance>()
  const form : any = ref({
    oldPassword: '',
    newPassword: '',
    rePassword: '',
  })

  const rules = ref<FormRules>({
    oldPassword: [
      { required: true, message: '请输入原密码', trigger: 'blur' },
    ],
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 6, max: 18, trigger: 'blur', message: '密码长度为6到18位' },
    ],
    rePassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      {
        validator: (rule : any, value : any, callback : any) => {
          if (value !== form.value.newPassword) {
            callback(new Error('请确认新密码'))
          }
          else {
            callback()
          }
        },
      },
    ],
  })
  onMounted(() => {
    eventBus.on('reswpd', () => {
      isShow.value = !isShow.value
    })
  })
  function onSubmit() {
    formRef.value && formRef.value.validate((valid : any) => {
      if (valid) {
        ElMessageBox.confirm(
          '确认提交?',
          '提示',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          },
        )
          .then(() => {
            console.log(form.value)
            let params = {
              id: userinfo.value.id,
              "currentPassword": form.value.oldPassword,
              "newPassword": form.value.newPassword,
            }
            useUser.updatePwd(params).then((res : any) => {
              // if (res.code === 0) {
              //   ElMessage({
              //     type: 'success',
              //     message: '修改成功，请重新登录',
              //     duration: 2000,
              //   })
              //   isShow.value = false
              //   userStore.logout()
              // }
            })
          })
          .catch(() => {
            ElMessage({
              type: 'info',
              message: '已取消',
            })
          })
      }
    })
  }
</script>

<template>
  <HDialog v-model="isShow" title="修改密码">
    <div class="px-4">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="原密码" prop="oldPassword">
          <el-input v-model="form.oldPassword" type="password" placeholder="请输入原密码" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="form.newPassword" type="password" placeholder="请输入原密码" show-password />
        </el-form-item>
        <el-form-item label="确认新密码" prop="rePassword">
          <el-input v-model="form.rePassword" type="password" placeholder="请输入原密码" show-password />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="fotterbtn">
        <el-button class="cancel" @click="isShow = false, form = {} ">
          取消
        </el-button>
        <el-button type="primary" @click="onSubmit">
          保存
        </el-button>
      </div>
    </template>
  </HDialog>
</template>
