<script setup lang="ts">
import type {
  FormInstance,
  FormRules,
} from 'element-plus'
import {
  ElMessage,
} from 'element-plus'
import {
  useI18n,
} from 'vue-i18n'
import { Lock, User, View } from '@element-plus/icons-vue'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import storage from '@/utils/storage'
import apiUser from '@/api/modules/user'

defineOptions({
  name: 'Login',
})
const captcha: any = ref('')
const innerWidth: any = ref(window.innerWidth)
const innerHeight: any = ref(window.innerHeight)

onMounted(() => {
  // getcaptcha()
  const screenWidth = window.screen.width
  const screenHeight = window.screen.height
  console.log('Screen Width:', screenWidth, 'Screen Height:', screenHeight)
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  console.log('Viewport Width:', viewportWidth, 'Viewport Height:', viewportHeight)
})

function getcaptcha() {
  apiUser.captcha({}).then((res: any) => {
    captcha.value = res.captcha
    loginForm.value.key = res.key
  })
}

const route = useRoute()
const router = useRouter()

const settingsStore = useSettingsStore()
const userStore = useUserStore()

const {
  t,
} = useI18n()

const logo = new URL('@/assets/images/logo.png', import.meta.url).href
const phone = new URL('@/assets/images/phone.png', import.meta.url).href
const loginbgs = new URL('@/assets/images/loginbgs.png', import.meta.url).href
const banner = new URL('@/assets/images/login-banner.png', import.meta.url).href

const title
    = import.meta.env.VITE_APP_TITLE
const isqrcode: any = ref(false)
const checked = ref(false)
const captchaImg: any = ref('')

// 表单类型，login 登录，register 注册，reset 重置密码
const formType = ref('login')
const loading = ref(false)
const redirect = ref(route.query.redirect?.toString() ?? settingsStore.settings.home.fullPath)

// 登录
const loginFormRef = ref<FormInstance>()
const loginForm: any = ref({
  account: storage.local.get('login_account') || '',
  password: '',
  remember: storage.local.has('login_account'),
  key: '',
  captcha: 520,
})
const loginRules = ref<FormRules>({
  account: [{
    required: true,
    trigger: 'blur',
    message: '请输入用户名',
  }],
  password: [{
    required: true,
    trigger: 'blur',
    message: '请输入密码',
  },
  {
    min: 6,
    max: 18,
    trigger: 'blur',
    message: '密码长度为6到18位',
  },
  ],
})

function handleLogin() {
  console.log(redirect.value, userStore, 'redirect.value')
  loading.value = true
  userStore.login(loginForm.value).then((res: any) => {
    console.log(res, 'resresres12')
    loading.value = false
    if (loginForm.value.remember) {
      storage.local.set('login_account', loginForm.value.account)
    }
    else {
      storage.local.remove('login_account')
    }
    router.push(redirect.value)
  }).catch(() => {
    loading.value = false
  })
}
// 注册
const registerFormRef = ref<FormInstance>()
const registerForm = ref({
  account: '',
  captcha: '',
  password: '',
  checkPassword: '',
})
const registerRules = ref<FormRules>({
  account: [{
    required: true,
    trigger: 'blur',
    message: '请输入用户名',
  }],
  captcha: [{
    required: true,
    trigger: 'blur',
    message: '请输入验证码',
  }],
  password: [{
    required: true,
    trigger: 'blur',
    message: '请输入密码',
  },
  {
    min: 6,
    max: 18,
    trigger: 'blur',
    message: '密码长度为6到18位',
  },
  ],
  checkPassword: [{
    required: true,
    trigger: 'blur',
    message: '请再次输入密码',
  },
  {
    validator: (rule, value, callback) => {
      if (value !== registerForm.value.password) {
        callback(new Error('两次输入的密码不一致'))
      }
      else {
        callback()
      }
    },
  },
  ],
})

function handleRegister() {
  ElMessage({
    message: '注册模块仅提供界面演示，无实际功能，需开发者自行扩展',
    type: 'warning',
  })
  registerFormRef.value && registerFormRef.value.validate((valid) => {
    if (valid) {
      // 这里编写业务代码
    }
  })
}

// 重置密码
const resetFormRef = ref<FormInstance>()
const resetForm = ref({
  account: storage.local.get('login_account'),
  captcha: '',
  newPassword: '',
})
const resetRules = ref<FormRules>({
  account: [{
    required: true,
    trigger: 'blur',
    message: '请输入用户名',
  }],
  captcha: [{
    required: true,
    trigger: 'blur',
    message: '请输入验证码',
  }],
  newPassword: [{
    required: true,
    trigger: 'blur',
    message: '请输入新密码',
  },
  {
    min: 6,
    max: 18,
    trigger: 'blur',
    message: '密码长度为6到18位',
  },
  ],
})

function handleReset() {
  ElMessage({
    message: '重置密码仅提供界面演示，无实际功能，需开发者自行扩展',
    type: 'info',
  })
  resetFormRef.value && resetFormRef.value.validate((valid) => {
    if (valid) {
      // 这里编写业务代码
    }
  })
}

function testAccount() {
  const params: any = loginForm.value
  if (!params.captcha) {
    ElMessage.error('请输入验证码')
    return false
  }
  if (!params.account || !params.password) {
    ElMessage.error('账号密码不能为空')
    return false
  }
  userStore.login(params).then((res: any) => {
    console.log(res, '登录成功')
    loading.value = false
    if (loginForm.value.remember) {
      storage.local.set('login_account', loginForm.value.account)
    }
    else {
      storage.local.remove('login_account')
    }
    console.log(redirect.value, 'redirect.value')
    // router.push(redirect.value)
    router.push('/')
  }).catch(() => {
    loading.value = false
  })
}

function checkType(e: any) {
  console.log(e)
  if (e === 1) {
    isqrcode.value = true
  }
  else {
    isqrcode.value = false
  }
}
</script>

<template>
  <div>
    <div class="flex">
      <div class="leftBox">
        <div class="aic fdc flex">
          <img :src="logo" class="logo" alt="logo">
          <div class="title">
            猫伯伯合规管家
          </div>
          <div class="desc">
            为企业构建全方位合规防护网
          </div>
        </div>
      </div>
      <div class="rightBox">
        <div class="conBox">
          <div class="text_dl">
            欢迎登录
          </div>
          <div class="text_desc">
            请输入您的账号信息
          </div>
          <div v-if="!isqrcode" class="mt-8 w-full">
            <div class="w-full">
              <div class="f-14">
                用户名
              </div>
              <div class="br6 mt-2 w-full flex p6">
                <el-input
                  v-model="loginForm.account" :prefix-icon="User" border="none" class="" type="text"
                  auto-complete="off" :input-style="{ '--el-input-border-color': '#fff' }" placeholder="请输入账号/手机号"
                />
              </div>
            </div>
            <div class="f-14 mt-4">
              密码
            </div>
            <div class="br6 mt-2 w-full flex p6">
              <el-input
                v-model="loginForm.password" :prefix-icon="Lock" :suffix-icon="View" border="none" class=""
                type="password" auto-complete="off" placeholder="请输入密码"
              />
            </div>
            <div class="f-14 mb-2 mt-4">
              验证码
            </div>
            <!-- 验证码 -->
            <el-form-item prop="captcha">
              <div class="aic jcsb flex">
                <div style="width: 208px;">
                  <el-input
                    v-model="loginForm.captcha" clearable prefix-icon="el-icon-_vercode"
                    :placeholder="$t('login.captcha')"
                  />
                </div>
                <img v-if="captcha" alt="" :src="captcha" class="login-captcha ml-4" @click="getcaptcha">
              </div>
            </el-form-item>
            <div
              v-if="!isqrcode" class="f-12 cur-poi col-595959 mt-4 w-full flex items-center justify-between"
              style="font-size: 12px;"
            >
              <div class="flex items-center">
                <el-checkbox v-model="checked" />
                <span class="ml10">记住密码</span>
              </div>
              <div class="aic cur-poi jcc flex" style="font-size: 12px;">
                <span class="c-[#1E88E5]">忘记密码？</span>
              </div>
            </div>
          </div>
          <div
            v-if="!isqrcode" class="jcc aic cur-poi loginbt mt-6 flex" style="margin-top: 20px;"
            @click="testAccount()"
          >
            登录
          </div>
        </div>
      </div>
    </div>
    <!-- <particles />
		<div class="bg-banner" />
		<I18nSelector placement="bottom-end" class="i18n-selector">
			<SvgIcon name="i-ri:translate" />
		</I18nSelector>
		<div id="login-box">
			<div class="login-banner flex items-center justify-center">
				<div class="d-c flex items-center justify-center">
					<img :src="banner" style="width: 420px; height: auto;" class="mx-10">
				</div>
			</div>
			<ElForm v-show="formType === 'login'" ref="loginFormRef" :model="loginForm" :rules="loginRules"
				class="login-form">
				<div class="d-c w-full flex items-center justify-center">
					<div class="title-container w-full flex items-center">
						<img style="width: 28px; height: 28px;" :src="logo" alt="">
						<div class="font-wight600 jcc aic my-4 ml10 flex" style="font-size: 22px;">
							{{ title }}
						</div>
					</div>
					<div class="w-full">
						<div class="formbg w-full">
							<div class="p-0-30 jcc aic d-c flex">
								<div v-if="isqrcode" class="jcc aic d-c flex">
									<div class="form-title mt-8">
										扫码登录
									</div>
									<div class="form-text mt-6">
										请使用微信扫码二维码登录
									</div>
								</div>
								<div v-if="!isqrcode" class="jcc aic d-c flex">
									<div class="form-title mt-8">
										账号登录
									</div>
									<div class="form-text mt-6">
										请使用密码登录
									</div>
								</div>
								<div v-if="!isqrcode" class="mt-8 w-full">
									<div class="w-full">
										<div class="br6 w-full flex p6" style="background-color: #f5f5f5;">
											<el-input v-model="loginForm.account" style="width: 100%;"
												class="input-border-style" type="text" auto-complete="off"
												placeholder="请输入账号/手机号" />
										</div>
									</div>
									<div class="br6 mt-6 w-full flex p6" style="background-color: #f5f5f5;">
										<el-input v-model="loginForm.password" class="input-border-style"
											type="password" auto-complete="off" placeholder="请输入密码" />
									</div>
								</div>
								<div v-if="!isqrcode" class="loginbt jcc aic cur-poi mt-6 flex" @click="testAccount()">
									登录
								</div>
								<div v-if="!isqrcode"
									class="f-12 col-595959 cur-poi mt-4 w-full flex items-center justify-between"
									style="font-size: 12px;">
									<div class="jcc aic cur-poi flex" style="font-size: 12px;">
										<span>忘记密码？</span> <span class="c-[#18A058]">联系管理员

										</span>
									</div>
									<div class="flex items-center">
										<el-checkbox v-model="checked" />
										<span class="ml10">记住我</span>
									</div>
								</div>
							</div>
							<div v-if="isqrcode" class="logintype" @click.stop="checkType(2)">
								<div class="checkType cur-poi" />
								<img style=" position: absolute;top: 0; right: 5px;z-index: 999;width: 15px; height: 18px;margin-top: 7px; margin-left: 5px;cursor: pointer;"
									:src="phone" alt="">
							</div>
						</div>
					</div>
				</div>
			</ElForm>
			<ElForm v-show="formType === 'register'" ref="registerFormRef" :model="registerForm" :rules="registerRules"
				class="login-form" auto-complete="on">
				<div class="title-container">
					<h3 class="title">
						探索从这里开始! 🚀
					</h3>
				</div>
				<div>
					<ElFormItem prop="account">
						<ElInput v-model="registerForm.account" placeholder="用户名" tabindex="1">
							<template #prefix>
								<SvgIcon name="i-ri:user-3-fill" />
							</template>
						</ElInput>
					</ElFormItem>
					<ElFormItem prop="captcha">
						<ElInput v-model="registerForm.captcha" placeholder="验证码" tabindex="2">
							<template #prefix>
								<SvgIcon name="i-ic:baseline-verified-user" />
							</template>
							<template #append>
								<ElButton>发送验证码</ElButton>
							</template>
						</ElInput>
					</ElFormItem>
					<ElFormItem prop="password">
						<ElInput v-model="registerForm.password" type="password" placeholder="密码" tabindex="3"
							show-password>
							<template #prefix>
								<SvgIcon name="i-ri:lock-2-fill" />
							</template>
						</ElInput>
					</ElFormItem>
					<ElFormItem prop="checkPassword">
						<ElInput v-model="registerForm.checkPassword" type="password" placeholder="确认密码" tabindex="4"
							show-password>
							<template #prefix>
								<SvgIcon name="i-ri:lock-2-fill" />
							</template>
						</ElInput>
					</ElFormItem>
				</div>
				<ElButton :loading="loading" type="primary" size="large" style="width: 100%; margin-top: 20px;"
					@click.prevent="handleRegister">
					注册
				</ElButton>
				<div class="sub-link">
					<span class="text">已经有帐号?</span>
					<ElLink type="primary" :underline="false" @click="formType = 'login'">
						去登录
					</ElLink>
				</div>
			</ElForm>
			<ElForm v-show="formType === 'reset'" ref="resetFormRef" :model="resetForm" :rules="resetRules"
				class="login-form">
				<div class="title-container">
					<h3 class="title">
						忘记密码了? 🔒
					</h3>
				</div>
				<div>
					<ElFormItem prop="account">
						<ElInput v-model="resetForm.account" :placeholder="t('app.account')" type="text" tabindex="1">
							<template #prefix>
								<SvgIcon name="i-ri:user-3-fill" />
							</template>
						</ElInput>
					</ElFormItem>
					<ElFormItem prop="captcha">
						<ElInput v-model="resetForm.captcha" :placeholder="t('app.captcha')" type="text" tabindex="2">
							<template #prefix>
								<SvgIcon name="i-ic:baseline-verified-user" />
							</template>
							<template #append>
								<ElButton>{{ t('app.sendCaptcha') }}</ElButton>
							</template>
						</ElInput>
					</ElFormItem>
					<ElFormItem prop="newPassword">
						<ElInput v-model="resetForm.newPassword" type="password" :placeholder="t('app.newPassword')"
							tabindex="3" show-password>
							<template #prefix>
								<SvgIcon name="i-ri:lock-2-fill" />
							</template>
						</ElInput>
					</ElFormItem>
				</div>
				<ElButton :loading="loading" type="primary" size="large" style="width: 100%; margin-top: 20px;"
					@click.prevent="handleReset">
					{{ t('app.check') }}
				</ElButton>
				<div class="sub-link">
					<ElLink type="primary" :underline="false" @click="formType = 'login'">
						{{ t('app.goLogin') }}
					</ElLink>
				</div>
			</ElForm>
		</div>
		<Copyright /> -->
  </div>
</template>

<style lang="scss" scoped>
  [data-mode="mobile"] {
    #login-box {
      position: relative;
      top: inherit;
      left: inherit;
      flex-direction: column;
      justify-content: start;
      width: 100%;
      height: 100%;
      border-radius: 0;
      box-shadow: none;
      transform: translateX(0) translateY(0);

      .login-banner {
        width: 100%;
        padding: 20px 0;

        .banner {
          position: relative;
          top: inherit;
          right: inherit;
          display: inherit;
          width: 100%;
          max-width: 375px;
          margin: 0 auto;
          transform: translateY(0);
        }
      }

      .login-form {
        width: 100%;
        min-height: auto;
        padding: 30px;
      }
    }

    .copyright {
      position: relative;
    }

    .login-switcher {
      display: none;
    }
  }

  :deep(input[type="password"]::-ms-reveal) {
    display: none;
  }

  :deep(.i18n-selector) {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1;
    font-size: 18px;
    color: var(--el-text-color-primary);
    cursor: pointer;
  }

  .bg-banner {
    position: fixed;
    z-index: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, var(--g-container-bg), var(--g-bg));
  }

  #login-box {
    position: absolute;
    top: 50%;
    left: 50%;
    // flex-direction: column;
    z-index: 9999;
    display: flex;
    justify-content: space-between;
    width: 880px;
    height: 560px;
    overflow: hidden;
    // background-color: #f5f5f5;
    background-color: #f2f3f5;
    border-radius: 10px;
    box-shadow: var(--el-box-shadow);
    transform: translateX(-50%) translateY(-50%);

    .login-banner {
      position: relative;
      width: 500px;
      overflow: hidden;
      // background-color: var(--g-bg);
      background-color: #fff;

      .banner {
        width: 100%;

        @include position-center(y);
      }

      .logo {
        position: absolute;
        top: 20px;
        left: 20px;
        height: 30px;
        border-radius: 4px;
        box-shadow: var(--el-box-shadow-light);
      }
    }

    .login-form {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 448px;
      min-height: 448px;
      padding: 44px;
      overflow: hidden;

      .title-container {
        position: relative;

        .title {
          margin: 0 auto 30px;
          font-size: 1.3em;
          font-weight: bold;
          color: var(--el-text-color-primary);
        }
      }
    }

    .el-form-item {
      margin-bottom: 24px;

      :deep(.el-input) {
        width: 100%;
        height: 48px;
        line-height: inherit;

        input {
          height: 48px;
        }

        .el-input__prefix,
        .el-input__suffix {
          display: flex;
          align-items: center;
        }

        .el-input__prefix {
          left: 10px;
        }

        .el-input__suffix {
          right: 10px;
        }
      }
    }

    :deep(.el-divider__text) {
      background-color: var(--g-container-bg);
    }

    .flex-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .sub-link {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
      font-size: 14px;
      color: var(--el-text-color-secondary);

      .text {
        margin-right: 10px;
      }
    }
  }

  .copyright {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 20px;
    margin: 0;
  }

  .login-switcher {
    position: absolute;
    right: 20px;
    bottom: 20px;
  }

  :deep(.el-input__wrapper) {
    height: 48px;
    // background-color: #f5f5f5 !important;
    border: 1px solid #fff;
  }

  :deep(--el-input-border-color) {
    // background-color: #f5f5f5 !important;
    border: 1px solid #fff;
  }

  .input-border-style {
    width: 100%;

    :deep(.el-input__wrapper) {
      cursor: default;
      box-shadow: 0 0 0 0 var(--el-input-border-color, var(--el-border-color)) inset;

      .el-input__inner {
        // cursor: default !important;
      }
    }
  }

  .login {
    width: 100%;
    height: 100%;
    background-color: #eeeff3;
  }

  .tishititle {
    font-size: 22px;
    font-weight: 600;
    line-height: 24px;
    color: #141414;
    text-align: justify;
  }

  .form-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 20px;
    color: #141414;
    text-align: justify;
    letter-spacing: 0;
  }

  .form-text {
    font-size: 16px;
    font-weight: 400;
    line-height: 16px;
    color: #595959;
    text-align: justify;
    letter-spacing: 0;
  }

  .tishitext {
    max-width: 380px;
    font-size: 14px;
    line-height: 18px;
    color: #757575;
    text-align: justify;
  }

  .formbg {
    position: relative;
    // max-width: 360px;
    height: 384px;
    background: #fff;
    border-radius: 10px;
    opacity: 1;
  }

  .sendcode {
    height: 32px;
    padding: 0 8px;
    font-size: 12px;
    color: #fff;
    background: #1759ff;
    border-radius: 4px;
    opacity: 1;
  }

  .loginbt {
    width: 100%;
    height: 40px;
    color: #fff;
    background: #1e88e5;
    border-radius: 6px;
    opacity: 1;
  }

  .logintype {
    position: absolute;
    top: 4px;
    right: 4px;
  }

  .checkType {
    position: relative;
    z-index: 999;
    width: 50px;
    height: 50px;
    background-color: #18a058;
    border-radius: 10px 0 0;
    transform: rotate(90deg);
  }

  .checkType::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    width: 0;
    height: 0;
    content: "";
    border-top: 50px solid transparent;
    border-right: 50px solid white;
  }

  .flex {
    display: flex;
  }

  .d-c {
    flex-direction: column;
  }

  .jcc {
    justify-content: center;
  }

  .aic {
    align-items: center;
  }

  .jcsb {
    justify-content: space-between;
  }

  .jcsa {
    justify-content: space-around;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .flex-start {
    justify-content: flex-start;
  }

  .flex-end {
    justify-content: flex-end;
  }

  .ml6 {
    margin-left: 6px;
  }

  .ml10 {
    margin-left: 10px;
  }

  .ml92 {
    margin-left: 92px;
  }

  .mt6 {
    margin-top: 6px;
  }

  .mt12 {
    margin-top: 12px;
  }

  .mt20 {
    margin-top: 20px;
  }

  .mt32 {
    margin-top: 32px;
  }

  .mt46 {
    margin-top: 46px;
  }

  .br6 {
    border-radius: 6px;
  }

  .p6 {
    padding: 6px;
  }

  .p10 {
    padding: 10px;
  }

  .p32 {
    padding: 32px;
  }

  .pt32-pl32 {
    padding-top: 32px;
    padding-left: 32px;
  }

  .p-0-30 {
    padding: 0 30px;
  }

  .p24-16 {
    padding: 24px 16px;
  }

  .p24-0 {
    padding: 24px 0;
  }

  .p16-0 {
    padding: 16px 0;
  }

  .font-wight600 {
    font-weight: 600;
  }

  .col-fff {
    color: #fff;
  }

  .col-blue {
    color: #2254f4;
  }

  .col-1759FF {
    color: #1759ff;
  }

  .col-595959 {
    color: #595959;
  }

  .cur-poi {
    cursor: pointer;
  }

  .login-captcha {
    width: 120px;
    height: 40px;
  }

  // new
  .leftBox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50vw;
    height: 100vh;
    background: linear-gradient(180deg, #338fdf 0%, #075fb3 100%), rgb(0 0 0 / 0%);

    .logo {
      width: 64px;
      height: 64px;
    }

    .title {
      /* 1.111 */
      margin-top: 16px;
      font-family: Inter;
      font-size: 36px;
      font-weight: 400;
      line-height: 40px;
      color: #fff;
    }

    .desc {
      /* 1.4 */
      margin-top: 8px;
      font-size: 20px;
      font-weight: 400;
      line-height: 28px;
      color: #fff;
    }
  }

  .rightBox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50vw;
    height: 100vh;

    .conBox {
      // width: 440px;
      // height: 638px;
      width: 27.5rem;
      height: 39.84rem;
      padding: 48px 48px 0;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 4px 24px 0 rgb(0 0 0 / 8%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);

      .text_dl {
        font-family: Inter;
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 42px;
        color: #1a1a1a;

        /* 1.5 */
      }

      .text_desc {
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        color: #666;

        /* 1.5 */
      }

      .inputBox {
        background: #fff !important;
        border: 1px solid #e5e5e5;
        border-radius: 8px;
      }
    }
  }
</style>
