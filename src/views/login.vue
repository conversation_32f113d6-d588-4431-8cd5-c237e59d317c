<script setup lang="ts">
import type {
  FormInstance,
  FormRules,
} from 'element-plus'
import {
  ElMessage,
} from 'element-plus'
import {
  useI18n,
} from 'vue-i18n'
import { Lock, Search, User, View } from '@element-plus/icons-vue'
import Copyright from '@/layouts/components/Copyright/index.vue'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import storage from '@/utils/storage'
import apiUser from '@/api/modules/user'
import apiLogon from '@/api/modules/system/logon'
import { asyncRoutes } from '@/router/routes.ts'

defineOptions({
  name: 'Login',
})
const _captcha: any = ref('')

// 合规承诺书弹窗相关
const showCommitmentDialog = ref(false)
const commitmentContent = ref('')
const commitmentLoading = ref(false)

onMounted(() => {
  loadCommitmentContent()
})

// 加载承诺书内容
function loadCommitmentContent() {
  commitmentContent.value = `
    <div class="commitment">
      <h1 style="text-align: center;">猫伯伯合规管家系统使用合规承诺书</h1>
      <p><strong>尊敬的用户：</strong></p>
      <p>欢迎使用猫伯伯合规管家系统！为确保所有使用者（包括所在企业租户的普通员工、管理人员及其他授权人员）能够合法、合规、安全地使用本系统，特制定本合规承诺书。请您在首次登录系统前认真阅读并确认同意本承诺书全部条款。</p>
      <h2>一、承诺范围</h2>
      <p>本人（以下亦称"用户"）明确知晓并承诺严格遵守中华人民共和国现行的法律、行政法规、部门规章及其他规范性文件，以及本人所属企业（租户）的内部规章制度，并严格遵守猫伯伯合规管家系统的各项规则和使用要求。</p>
      <h2>二、信息真实性承诺</h2>
      <p>本人保证在猫伯伯合规管家系统内填写、上传或生成的所有信息均真实、准确、完整，不存在虚假、误导或重大遗漏，并将在信息变更时及时更新。若因信息不真实、不完整或未及时更新造成任何损失或法律后果，由本人承担全部责任。</p>
      <h2>三、合法合规使用承诺</h2>
      <p>本人承诺在使用猫伯伯合规管家系统过程中，不会从事或协助他人从事以下任何行为：</p>
      <ul>
        <li>违反宪法及法律、行政法规、部门规章等规范性文件的行为；</li>
        <li>侵犯他人知识产权、商业秘密、个人信息或其他合法权益的行为；</li>
        <li>利用系统散布虚假信息、实施商业欺诈、传输违禁内容或扰乱社会秩序的行为；</li>
        <li>破坏、攻击、入侵或以其他方式干扰系统或相关网络、软件、硬件、数据的安全与完整性；</li>
        <li>违反所属企业（租户）规章制度或猫伯伯合规管家系统使用规则的行为；</li>
        <li>其他法律、法规、规章或政策明令禁止的行为。</li>
      </ul>
      <h2>四、数据安全与保密承诺</h2>
      <p>本人承诺妥善保管系统账号、密码及多因素认证信息，不向任何未经授权的第三方透露、出租、出借或转让。如因账号管理不善导致账号被盗用、数据泄露或造成其他损失，本人将承担全部责任。本人同时承诺严格遵守企业及系统的数据分类分级、信息安全及保密制度，不得擅自导出、披露或用于超出授权范围的目的。</p>
      <h2>五、责任追究</h2>
      <p>如本人违反本承诺书任一条款，愿意自行承担全部法律责任，并同意猫伯伯合规管家系统及所属企业（租户）视情节轻重采取包括但不限于以下措施：暂停或终止账户使用、删除违规信息、内部行政处分、追究民事赔偿责任、移交司法机关处理等。</p>
      <h2>六、确认与生效</h2>
      <p>点击 "我已阅读并同意" 按钮，即视为本人已充分阅读、理解并接受本承诺书全部内容，并将其作为电子签署予以确认。本承诺书自本人点击确认之时起即时生效，对本人具有法律效力。系统将自动记录以下信息作为电子签署凭证：</p>
      <ul>
        <li>用户账号及唯一标识；</li>
        <li>真实姓名或实名信息（如适用）；</li>
        <li>IP 地址及设备信息；</li>
        <li>系统服务器记录的时间戳。</li>
      </ul>
      <p>根据《中华人民共和国民法典》《中华人民共和国电子签名法》等相关法律法规，上述电子确认与手写签名具有同等法律效力。</p>
      <h2>七、联系我们</h2>
      <p>如您对本承诺书内容有任何疑问，请联系所属企业管理员或猫伯伯合规管家客服热线：400-166-5291（服务时间：每天 09:00-21:00）。</p>
      <p style="text-align:right;">感谢您的配合！</p>
    </div>
  `
}

const route = useRoute()
const router = useRouter()

const settingsStore = useSettingsStore()
const userStore = useUserStore()

const logo = new URL('@/assets/images/logo.png', import.meta.url).href

const isqrcode: any = ref(false)
const checked = ref(false)

// 表单类型，login 登录，register 注册，reset 重置密码
const _formType = ref('login')
const loading = ref(false)
const _redirect = ref(route.query.redirect?.toString() ?? settingsStore.settings.home.fullPath)

// 登录

const loginForm: any = ref({
  account: storage.local.get('login_account') || '',
  password: '',
  remember: storage.local.has('login_account'),
  key: '',
  captcha: 520,
})

function testAccount() {
  const params: any = {
    username: loginForm.value.account,
    password: loginForm.value.password,
    loginType: 'PASSWORD',
    deviceId: '12345600p',
  }

  userStore.login(params).then((_res: any) => {
    loading.value = false

    // 登录成功后检查用户是否已签约承诺书
    checkCommitmentStatus()
  }).catch(() => {
    loading.value = false
  })
}

// 检查用户是否已签约承诺书
function checkCommitmentStatus() {
  apiLogon.checkEmployeeCommitment().then((res: any) => {
    if (!res || res === false) {
      showCommitmentDialog.value = true
    }
    else {
      router.push('/')
    }
  }).catch(() => {
    showCommitmentDialog.value = true
  })
}

// 确认承诺书
function confirmCommitment() {
  commitmentLoading.value = true

  const commitmentData = {
    type: 1, // 类别：1、合规承诺
    filePath: 'https://dev.static.mbbhg.com/whiskerguard-front/%E7%8C%AB%E4%BC%AF%E4%BC%AF%E5%90%88%E8%A7%84%E7%AE%A1%E5%AE%B6%E7%B3%BB%E7%BB%9F%E4%BD%BF%E7%94%A8%E5%90%88%E8%A7%84%E6%89%BF%E8%AF%BA%E4%B9%A6.doc', // 文件地址，可以为空
    isSigned: true, // 已签名
  }

  apiLogon.createCommitment(commitmentData).then(() => {
    commitmentLoading.value = false
    showCommitmentDialog.value = false
    ElMessage.success('承诺书签署成功')
    router.push('/')
  }).catch(() => {
    commitmentLoading.value = false
    ElMessage.error('承诺书签署失败，请重试')
  })
}
</script>

<template>
  <div>
    <div class="flex">
      <div class="h-screen w-1/2 flex items-center justify-center from-blue-400 to-blue-800 bg-gradient-to-b">
        <div class="flex flex-col items-center">
          <img :src="logo" class="mb-4 h-16 w-16" alt="logo">
          <div class="mt-4 text-4xl text-white font-normal leading-10">
            猫伯伯合规管家
          </div>
          <div class="mt-2 text-xl text-white font-normal leading-7">
            为企业构建全方位合规防护网
          </div>
        </div>
      </div>
      <div class="h-screen w-1/2 flex items-center justify-center">
        <div class="h-auto w-96 rounded-2xl bg-white p-12 shadow-lg">
          <div class="text-3xl text-gray-900 font-normal leading-10">
            欢迎登录
          </div>
          <div class="text-base text-gray-600 font-normal leading-6">
            请输入您的账号信息
          </div>
          <div v-if="!isqrcode" class="mt-8 w-full">
            <div class="w-full">
              <div class="text-sm text-gray-700 font-medium">
                用户名
              </div>
              <div class="mt-2 w-full flex border border-gray-200 rounded-md bg-gray-50 focus-within:border-blue-500 focus-within:bg-white">
                <el-input
                  v-model="loginForm.account" :prefix-icon="User" border="none" class="" type="text"
                  auto-complete="off" :input-style="{ '--el-input-border-color': '#fff' }" placeholder="请输入账号/手机号"
                />
              </div>
            </div>
            <div class="mt-4 text-sm text-gray-700 font-medium">
              密码
            </div>
            <div class="mt-2 w-full flex border border-gray-200 rounded-md bg-gray-50 focus-within:border-blue-500 focus-within:bg-white">
              <el-input
                v-model="loginForm.password" :prefix-icon="Lock" :suffix-icon="View" border="none" class=""
                type="password" auto-complete="off" placeholder="请输入密码"
              />
            </div>
            <div class="mb-2 mt-4 text-sm text-gray-700 font-medium">
              验证码
            </div>
            <div
              v-if="!isqrcode" class="mt-4 w-full flex cursor-pointer items-center justify-between text-xs text-gray-600"
            >
              <div class="flex items-center">
                <el-checkbox v-model="checked" />
                <span class="ml-2">记住密码</span>
              </div>
              <div class="flex cursor-pointer items-center justify-center">
                <span class="text-blue-500">忘记密码？</span>
              </div>
            </div>
          </div>
          <div
            v-if="!isqrcode" class="mt-5 h-10 w-full flex cursor-pointer items-center justify-center rounded-md bg-blue-500 text-white transition-all duration-300 hover:bg-blue-600 hover:shadow-lg hover:-translate-y-0.5"
            @click="testAccount()"
          >
            登录
          </div>
        </div>
      </div>
    </div>

    <!-- 合规承诺书弹窗 -->
    <el-dialog
      v-model="showCommitmentDialog"
      title="合规承诺书"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      center
    >
      <div class="max-h-96 overflow-y-auto">
        <div class="p-5 leading-relaxed" v-html="commitmentContent" />
      </div>
      <template #footer>
        <div class="py-5 text-center">
          <el-button
            type="primary"
            :loading="commitmentLoading"
            size="large"
            class="w-48"
            @click="confirmCommitment"
          >
            我已阅读并同意
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
/* 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 */

/* 承诺书弹窗内容样式 */
:deep(.p-5.leading-relaxed) {
  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: bold;
  }

  h2 {
    color: #333;
    margin: 20px 0 10px 0;
    font-size: 18px;
    font-weight: bold;
  }

  p {
    margin: 10px 0;
    color: #666;
    text-align: justify;
  }

  ul {
    margin: 10px 0;
    padding-left: 20px;

    li {
      margin: 5px 0;
      color: #666;
    }
  }

  strong {
    color: #333;
  }
}

/* Element Plus 弹窗样式覆盖 */
:deep(.el-dialog) {
  border-radius: 10px;
}

:deep(.el-dialog__header) {
  background: none !important;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-dialog__title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin: 0;
  }
}

:deep(.el-input__wrapper) {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  height: 48px;
}
</style>
