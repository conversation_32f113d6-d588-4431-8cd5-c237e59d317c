<script setup lang="ts">
import { onActivated, ref } from 'vue'
import moment from 'moment'
import type {
  FormInstance,
  FormRules,
} from 'element-plus'
import { ElMessage } from 'element-plus'
import Api from '@/api/modules/system/department'
import useUserStore from '@/store/modules/user'

const userStore: any = useUserStore()

const tableData: any = ref([])
const dialogVisible: any = ref(false)

// 请求参数
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
  sizes: [10, 20, 50, 100],
  layout: 'total, sizes, ->, prev, pager, next, jumper',
})
// 请求列表
function getList() {
  Api.list({
    page: pagination.value.page,
    limit: pagination.value.size,
    name: pagination.value.name,
  }).then((res: any) => {
    // console.log(res.data, '列表打印')
    tableData.value = res.data
    pagination.value.total = res.count
  })
}
// 请求列表
getList()
onActivated(() => {
  getList()
})

function removeBatch(e: any) {
  Api.pdelete({
    id: e,
  }).then((res: any) => {
    ElMessage({ message: res.msg, type: 'success' })
    getList()
  })
}
// 分页方法
function sizeChange(e: any) {
  pagination.value.size = e
  getList()
}
function currentChange(e: any) {
  pagination.value.page = e
  getList()
}
// 新增修改
const form: any = ref({})
function handleAdd(e: any) {
  // console.log(e, '新增修改')
  if (e.id) {
    form.value = JSON.parse(JSON.stringify(e))
  }
  else {
    form.value = {
      sort: 1,
      status: 1,
    }
  }
  dialogVisible.value = true
}
// 定义提交按钮函数
const formRef = ref<FormInstance>()
const formRules = ref<FormRules>({
  name: [{
    required: true,
    message: '请输入岗位名称',
    trigger: 'blur',
  }],
  sort: [{
    required: true,
    message: '请输入排序',
    trigger: 'blur',
  }],
})
function submitButton() {
  formRef.value && formRef.value.validate((valid) => {
    if (valid) {
      Api.edit(form.value).then((res: any) => {
        ElMessage({ message: res.msg, type: 'success' })
        dialogVisible.value = false
        getList()
      })
    }
  })
}
</script>

<template>
  <div>
    <page-main style="position: relative;">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="岗位名称:">
          <el-input v-model="pagination.name" placeholder="岗位名称" clearable />
        </el-form-item>
        <el-form-item label="状态" style="width: 200px;">
          <el-select v-model="pagination.status" clearable placeholder="请选择状态" @clear="pagination.status = null">
            <el-option label="正常" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList()">
            <template #icon>
              <svg-icon name="ep:search" />
            </template>
            查询
          </el-button>
          <el-button
            @click="pagination = {
              page: 1,
              size: 10,
              total: 0,
              sizes: [10, 20, 50, 100],
              layout: 'total, sizes, ->, prev, pager, next, jumper',
            }, getList()"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <div v-if="userStore.permissions.includes('departmentadd')" class="btnbox">
        <el-button type="primary" size="default" @click="handleAdd">
          <template #icon>
            <svg-icon name="ep:plus" />
          </template>
          添加
        </el-button>
      </div>
      <el-table :data="tableData" border height="60vh">
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column prop="id" label="ID" width="60" align="center" />
        <el-table-column prop="name" label="岗位名称" min-width="200" align="center" />
        <el-table-column prop="status" label="岗位状态" width="100" align="center">
          <template #default="{ row: i }">
            <div v-if="i.status == 1" class="css-new-status">
              <div class="left-box" />
              <div>正常</div>
            </div>
            <div v-else class="css-new-status" :style="{ '--color': '#f56C6C' }">
              <div class="left-box" />
              <div>停用</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序号" width="100" align="center" />
        <el-table-column prop="roles" label="创建时间" width="180" align="center">
          <template #default="scope">
            {{ moment(scope.row.create_time * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="update_time" label="更新时间" width="180" align="center">
          <template #default="scope">
            {{ scope.row.update_time > 0 ? moment(scope.row.update_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180" align="center">
          <template #default="scope">
            <el-button
              v-if="userStore.permissions.includes('departmentedit')" type="primary" text
              @click="handleAdd(scope.row)"
            >
              <template #icon>
                <svg-icon name="ep:edit" />
              </template>修改
            </el-button>
            <el-popconfirm
              v-if="userStore.permissions.includes('departmentdelete')" title="确定删除吗？"
              @confirm="removeBatch(scope.row.id)"
            >
              <template #reference>
                <el-button type="danger" text>
                  <template #icon>
                    <svg-icon name="ep:delete" />
                  </template>删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
        :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination mt-4"
        background @size-change="sizeChange" @current-change="currentChange"
      />
    </page-main>
    <!-- 添加修改 -->
    <el-dialog v-model="dialogVisible" :title="form.id ? '修改岗位' : '新增岗位' " width="680px">
      <el-form ref="formRef" :rules="formRules" :model="form" label-width="90px">
        <el-form-item label="岗位名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">
              在用
            </el-radio>
            <el-radio :label="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序号" prop="sort">
          <el-input-number v-model="form.sort" :min="1" />
        </el-form-item>
        <div style="display: flex;justify-content: center;">
          <el-button type="primary" @click="submitButton()">
            确定
          </el-button>
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  .btnbox {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }
</style>
