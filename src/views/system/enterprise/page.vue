<script setup lang="ts">
import { ref } from 'vue'
import type {
  FormInstance,
} from 'element-plus'

const activeName: any = ref('1')

const formRef = ref<FormInstance>()
const formRef1 = ref<FormInstance>()

const form: any = ref({
  logo: '',
  type: '地接社',
  name: '西安盈讯电子科技有限责任公司',
  address: '西安市高新区唐兴数码国际',
  webaddress: '无',
  business: '无',
  city: '陕西省 > 西安市',
})
const form1: any = ref({
  name: '西安盈讯电子科技有限责任公司',
  taxpayernum: '916101136631663946',
  email: '<EMAIL>',
  desc: '无',
})
const businesslist: any = ref([{
  id: 1,
  name: '小程序',
}, {
  id: 2,
  name: 'App',
}, {
  id: 3,
  name: '官网',
}])
const edittype: any = ref(1)
const showEdit = ref(false)
const showEditer = ref(false)
const data1: any = ref({})
const data2: any = ref({})

const logo = new URL('../../../assets/images/logo.png', import.meta.url).href
onMounted(() => {
  getList()// 请求数据
})
const formRules = ref({
  name: [{
    required: true,
    message: '请输入企业名称',
    trigger: 'blur',
  }],
  address: [{
    required: true,
    message: '请输入企业地址',
    trigger: 'blur',
  }],

})
function getList() {

}

function handleClick() {

}
function handleSuccess(row: any) {
  console.log(row, 'row')
}
function showEditfn() {
  data1.value = {}
  data1.value = JSON.parse(JSON.stringify(form.value))
  data1.value.city = []
  showEdit.value = true
}
function showEditfner() {
  data2.value = Object.assign({}, form1.value)
  console.log(data2.value, 'data2.value')
  showEditer.value = true
}
function submitForm() {
  form.value = data1.value
  showEdit.value = false
}
function submitFormer() {
  form1.value = data2.value
  showEditer.value = false
}
</script>

<template>
  <div style="overflow-y: auto;">
    <PageHeader>
      <template #title>
        <div class="flex items-center gap-4">
          账户一览
        </div>
      </template>
      <!-- <template #content>
        <div class="text-sm/6">
          <div>
            这是一款<b class="text-emphasis">开箱即用</b>的中后台框架，同时它也经历过数十个真实项目的技术沉淀，确保框架在开发中可落地、可使用、可维护
          </div>
        </div>
      </template> -->
      <!-- <HButton outline @click="open('https://fantastic-admin.gitee.io')">
        <SvgIcon name="i-ri:file-text-line" />
        开发文档
      </HButton> -->
      <!-- <HDropdownMenu
        :items="[
          [
            { label: 'Gitee', handle: () => open('https://gitee.com/fantastic-admin/basic') },
            { label: 'Github', handle: () => open('https://github.com/fantastic-admin/basic') },
          ],
        ]"
      >
        <HButton class="ml-2">
          <SvgIcon name="i-ri:code-s-slash-line" />
          代码仓库
          <SvgIcon name="i-ep:arrow-down" />
        </HButton>
      </HDropdownMenu> -->
    </PageHeader>
    <PageMain
      class="rounded-lg text-white"
      style="background: linear-gradient(50deg, rgb(14 76 253), rgb(106 142 255));"
    >
      <div class="d-c-s-b">
        <div class="flex">
          <img :src="logo">
          <div class="d-c d-c-s-a ml10">
            <div style="font-size: 20px;font-weight: 600;">
              忆佰智能有限公司
            </div>
            <div style="font-size: 14px;">
              组织ID：8025738915633327119
            </div>
          </div>
        </div>
        <div class="aic d-f" style="font-size: 14px;">
          基础版 2027-02-03 到期
        </div>
      </div>
    </PageMain>
    <div class="w-full flex flex-col gap-4 px-4 xl:flex-row">
      <PageMain class="ecology">
        <template #title>
          <div class="title-info">
            <!-- <img :src="logo"> -->
            <div>
              <h1 class="c-[#41b883]">
                企业信息
              </h1>
              <!-- <h2>一款简单好用的 Vue3 项目启动套件</h2> -->
            </div>
          </div>
          <div class="ml-auto">
            <HButton outline @click="showEditfn()">
              编辑
            </HButton>
          </div>
        </template>
        <div class="boxaa pb16">
          <ul class="f16 m-0 pr-8 text-size-sm leading-6">
            <!-- <li>
              <span class="wid100">类型</span>  <span class="ml80">{{ form.type }}</span>
            </li> -->
            <li>
              <span class="wid100">企业名称</span> <span class="ml80">{{ form.name }}</span>
            </li>
            <li>
              <span class="wid100">所在城市</span> <span class="ml80">{{ form.city }}</span>
            </li>
            <li>
              <span class="wid100">经营地址</span> <span class="ml80">{{ form.address }}</span>
            </li>
            <!-- <li>
              <span class="wid100">网站地址</span> <span class="ml80">{{ form.webaddress }}</span>
            </li> -->
            <li>
              <span class="wid100">经营业务</span> <span class="ml80">{{ form.business }}</span>
            </li>
          </ul>
        </div>
      </PageMain>
      <PageMain class="ecology">
        <template #title>
          <div class="title-info">
            <!-- <ImagePreview :src="logo" :width="54" :height="54" /> -->
            <!-- <img :src="logo"> -->
            <div>
              <h1 class="c-[#e60000]">
                开票信息
              </h1>
              <!-- <h2>一款开箱即用的 Vue 中后台管理系统框架</h2> -->
            </div>
          </div>
          <div class="ml-auto">
            <HButton class="mr10">
              一键复制
            </HButton>
            <HButton outline @click="showEditfner()">
              编辑
            </HButton>
          </div>
        </template>
        <div class="boxaa pb16">
          <ul class="f16 m-0 pr-8 text-size-sm leading-6">
            <!-- <li>
              <span class="wid100">
                发票类型
              </span>   <span class="ml80">{{ form.invoicetype }}</span>
            </li> -->
            <li>
              <span class="wid100">公司名称</span> <span class="ml80">{{ form1.name }}</span>
            </li>
            <li>
              <span class="wid100">纳税人识别号</span> <span class="ml80">{{ form1.taxpayernum }}</span>
            </li>
            <li>
              <span class="wid100">邮箱信息</span> <span class="ml80">{{ form1.email }}</span>
            </li>
            <li>
              <span class="wid100">备注</span> <span class="ml80">{{ form1.desc }}</span>
            </li>
          </ul>
        </div>
      </PageMain>
    </div>
    <!-- <page-main style="position: relative;height: calc(100vh); background-color: transparent;">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <div>
            <el-card shadow="hover" class="grid-content">
              <div class="d-b-c mb16 mt10">
                <div class="title">
                  企业信息
                </div>
                <el-button @click="showEditfn">
                  配置
                </el-button>
              </div>
              <el-card shadow="hover" class="item">
                <div class="head d-b-c">
                  <div class="p15">
                    <div class="f16">
                      企业Logo
                    </div>
                    <div class="f24 mt10">
                      <ImagePreview :src="form.logo" :width="100" :height="100" />
                    </div>
                  </div>
                </div>
                <div class="bot-info f14 gray9 p15">
                  (建议尺寸 100-*100 / 200-*200格式jpg/png，大小不超过 1M)
                </div>
              </el-card>
              <el-card shadow="hover" class="item mt16">
                <div class="head d-b-c">
                  <div class="p15">
                    <div class="f16">
                      企业名称
                    </div>
                    <div class="f24 mt10">
                      {{ form.name || '未配置' }}
                    </div>
                  </div>
                </div>
                <div class="bot-info f14 gray9 p15">
                  资料整理中
                </div>
              </el-card>
              <el-card shadow="hover" class="item mt16">
                <div class="head d-b-c">
                  <div class="p15">
                    <div class="f16">
                      信用代码
                    </div>
                    <div class="f24 mt10">
                      {{ form.code || '未配置' }}
                    </div>
                  </div>
                </div>
                <div class="bot-info f14 gray9 p15">
                  资料整理中
                </div>
              </el-card>
              <el-card shadow="hover" class="item mt16">
                <div class="head d-b-c">
                  <div class="p15">
                    <div class="f16">
                      经营地址
                    </div>
                    <div class="f24 mt10">
                      {{ form.address || '未配置' }}
                    </div>
                  </div>
                </div>
                <div class="bot-info f14 gray9 p15">
                  资料整理中
                </div>
              </el-card>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </page-main> -->
    <HDialog v-model="showEdit" title="编辑企业信息">
      <div style="padding: 0 18px;">
        <el-form ref="formRef" :model="data1" :rules="formRules" label-position="top" label-width="100px">
          <el-form-item label="企业logo" prop="logo">
            <ImageUpload v-model:url="data1.logo" :width="1" :height="1" @on-success="handleSuccess($event)" />
          </el-form-item>

          <el-form-item label="企业名称" prop="name">
            <el-input v-model="data1.name" size="large" placeholder="请输入企业名称" clearable />
          </el-form-item>
          <el-form-item label="所在城市" prop="city">
            <pcas-cascader v-model="data1.city" type="pc" style="width: 100%;" size="large" />
          </el-form-item>

          <el-form-item label="经营地址" prop="address">
            <el-input v-model="data1.address" size="large" placeholder="请输入经营地址" clearable />
          </el-form-item>
          <el-form-item label="经营业务" prop="business">
            <el-select
              v-model="data1.business" class="" multiple placeholder="请选择" size="large"
              :popper-append-to-body="false" style="width: 100%;"
            >
              <el-option v-for="item in businesslist" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="showEdit = false">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
    <HDialog v-model="showEditer" title="编辑开票信息">
      <div style="padding: 0 18px;">
        <el-form ref="formRef1" :model="data2" :rules="formRules" label-position="top" label-width="100px">
          <el-form-item label="公司名称" prop="name">
            <el-input v-model="data2.name" size="large" placeholder="请输入企业名称" clearable />
          </el-form-item>
          <el-form-item label="纳税人识别号" prop="taxpayernum">
            <el-input v-model="data2.taxpayernum" size="large" placeholder="请输入企业名称" clearable />
          </el-form-item>

          <el-form-item label="邮箱信息" prop="email">
            <el-input v-model="data2.email" size="large" placeholder="请输入邮箱信息" clearable />
          </el-form-item>
          <el-form-item label="备注" prop="desc">
            <el-input v-model="data2.desc" size="large" placeholder="请输入备注" clearable />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="showEditer = false">
            取消
          </el-button>
          <el-button type="primary" @click="submitFormer">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
    <!-- <el-dialog v-model="showEdit" title="修改资料" width="480px">
      <el-form ref="formRef" :model="data1" :rules="formRules" label-position="top" label-width="100px">
        <el-form-item label="企业logo" prop="logo" class="mt24">
          <ImageUpload
            v-model:url="data1.logo" :width="1" :height="1"
            @on-success="handleSuccess($event)"
          />
        </el-form-item>

        <el-form-item label="企业名称" prop="name">
          <el-input v-model="data1.name" placeholder="请输入公司名称" clearable />
        </el-form-item>
        <el-form-item label="信用代码" prop="address">
          <el-input v-model="data1.code" placeholder="请输入公司地址" clearable />
        </el-form-item>
        <el-form-item label="企业地址" prop="address">
          <el-input v-model="data1.address" placeholder="请输入公司地址" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEdit = false">取消</el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog> -->
  </div>
</template>

<style lang="scss" scoped>
  .container {
    :deep(.tox-tinymce) {
      height: calc(100% - 40px - 32px) !important;
    }
  }

  .fotterbtn {

    // padding: 8px 32px;
    .cancel {
      color: #409eff;
      border: 1px solid #409eff;
    }
  }

  .boxaa {
    ul {
      list-style: none;

      li {
        display: flex;
        line-height: 64px;
        border-bottom: 1px solid #eee;
      }
    }
  }

  .text-emphasis {
    text-emphasis-style: "❤";
  }

  .ecology {
    --at-apply: flex-1 m-0;

    :deep(.title-container) {
      --at-apply: flex items-center justify-between flex-wrap gap-4;

      .title-info {
        --at-apply: flex items-center gap-4;

        img {
          --at-apply: block w-12 h-12;
        }

        h1 {
          --at-apply: m-0 text-2xl;
        }

        h2 {
          --at-apply: m-0 text-base text-stone-5 font-normal;
        }
      }
    }
  }

  .el-card.grid-content {
    width: 50%;
    // height: 75vh;
    padding-bottom: 10px;
    margin: auto;
    // overflow-y: auto;
    background-color: #fff;
    border: none;
    transition: var(--el-transition-duration);
  }

  .btnbox {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .font-16 {
    font-size: 16px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .its {
    border-bottom: 1px solid var(--el-card-border-color);
  }

  .its:last-child {
    border-bottom: none;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }

  .wid100 {
    width: 100px;
  }

  .title {
    position: relative;
    height: 40px;
    padding-left: 16px;
    font-size: 20px;
    line-height: 40px;
  }

  .title::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 4px;
    height: 20px;
    content: "";
    background: var(--el-color-primary);
    border-radius: 4px;
    transform: translateY(-50%);
  }

  .el-card.item {
    margin-bottom: 16px;
    border-bottom: 1px solid var(--el-card-border-color);

    :deep(.el-card__body) {
      padding: 0;
    }
  }

  .p15 {
    padding: 15px;
  }

  .bot-info {
    border-top: 1px solid var(--el-card-border-color);

    .link {
      color: #3370ff;
    }
  }
</style>
