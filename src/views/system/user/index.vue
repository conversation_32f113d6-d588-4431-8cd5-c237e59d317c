<script setup lang="ts">
import { reactive, ref } from 'vue'
import moment from 'moment'
import { ElMessage } from 'element-plus'
import Edit from './edit.vue'
import Api from '@/api/modules/system/user'
import useUserStore from '@/store/modules/user'

const userStore: any = useUserStore()

console.log('Api-打印', Api)
const formInline = reactive({
  user: '',
  region: '',
  date: '',
})
function onSubmit() {
  console.log('submit!')
}

function reset() {

}
const tableData = ref([
  // {
  //   id: 1,
  //   username: 'admin',
  //   realname: '管理',
  //   nickname: '管理',
  //   avatar: '',
  //   gender: 1,
  //   mobile: '17758986988',
  //   roles: '超级管理员',
  //   level_name: '市场总监',
  //   position_name: '技术经理',
  //   dept_name: '南京研发中心',
  //   status: 1,
  //   create_time: '2021-05-26 11:14:24',
  //   update_time: '2021-05-26 11:14:24',
  // },
])
const baseurl: any = ref(`${import.meta.env.VITE_APP_API_BASEURLIMG}`)
const form: any = ref({
  id: '',
  password: '',
  role_ids: [],
  username: '',
  city: [],
})
const data = ref({
  page: 1,
  limit: 10,
})
const value = ref(true)
const total: any = ref(0)
const paging: any = ref({
  page: data.value.page,
  limit: data.value.limit,
  username: '',
  gender: null,
})
// const childRef = ref(null)
const currentPage4 = ref(1)
const pageSize4 = ref(10)
const small = ref(false)
const background = ref(true)
const disabled = ref(false)

const dialogVisible = ref(false)
const dialogFormVisible = ref(false)
function handleClose(done: () => void) {
  dialogVisible.value = false
}
function submit() {
  console.log('提交')
  setTimeout(() => {
    dialogVisible.value = false

    getList()
  }, 10)
}
function getList() {
  Api.list(paging.value).then((res: any) => {
    // res.data.forEach((item: any) => {
    //   if (item.avatar && !item.avatar.includes(baseurl.value)) {
    //     item.avatar = baseurl.value + item.avatar
    //   }
    // })
    // console.log(res, '列表打印')
    tableData.value = res.data
    total.value = res.count
    // console.log(total.value, '列表打印')
  }).catch(() => {
    // console.log(error, '列表打印')
  })
}
getList()// 请求数据

// 修改
const modifyID: any = ref(null)
function modify(val: any) {
  modifyID.value = val
  dialogVisible.value = !dialogVisible.value
  // console.log(val, '列表打印', modifyID.value)
}
// 重置密码
// function resetPwd(data: any) {
//   Api.resetPwd({
//     id: data,
//   }).then((res: any) => {
//     ElMessage({ message: res.msg, type: 'success' })
//     getList()
//   })
// }
function resetPwd(e: any) {
  dialogFormVisible.value = true
  form.value.id = e.id
  form.value.username = e.username
  form.value.password = ''
  const rolesid: any = []
  e.roles.forEach((item: any) => {
    rolesid.push(item.id)
  })
  form.value.role_ids = rolesid
  form.value.city = e.city
}

function removeBatch(data: any) {
  Api.userdelete({
    id: data,
  }).then((res: any) => {
    ElMessage({ message: res.msg, type: 'success' })
    getList()
  })
}
const childRef = ref<InstanceType<typeof Edit>>()
function submitForm() {
  // console.log(childRef, 'childRef')
  if (dialogVisible.value && childRef.value) {
    childRef.value.submitButton()
  }
}
function canForm() {
  // console.log(childRef, 'childRef')
  if (dialogVisible.value && childRef.value) {
    childRef.value.cancelForm()
  }
}

function openEdit() {
  modifyID.value = null
  dialogVisible.value = !dialogVisible.value
}
// 分页
function sizeChange(val: any) {
  console.log(val, '55555')
}
// 分页
function handleCurrentChange(val: any) {
  paging.value.page = val
  getList()
}
function submitPass() {
  if (form.value.password === '') {
    ElMessage({ message: '请输入密码', type: 'error' })
  }
  Api.resetPwd({ id: form.value.id, password: form.value.password }).then((res: any) => {
    dialogFormVisible.value = false
    ElMessage({ message: '修改成功', type: 'success' })
    getList()
  })

  // apiUser.editpassword(form.value).then((res : any) => {
  // 	// if (res.code === 0) {
  // 	dialogFormVisible.value = false
  // 	ElMessage({ message: '修改成功', type: 'success' })
  // 	getList()
  // 	// }
  // })
}
</script>

<template>
  <div>
    <page-main style="position: relative;">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="用户账号:">
          <el-input v-model="paging.username" placeholder="请输入用户账号" clearable />
        </el-form-item>
        <!-- <el-form-item label="性别:">
					<el-select v-model="paging.gender" placeholder="请选择性别" clearable class="ele-fluid">
						<el-option label="男" value="1" />
						<el-option label="女" value="2" />
						<el-option label="保密" value="3" />
					</el-select>
				</el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="getList()">
            <template #icon>
              <svg-icon name="ep:search" />
            </template>
            查询
          </el-button>
          <el-button
            @click="paging = {
              page: 1,
              limit: 10,
              username: null,
              gender: null,
            }, getList()"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- <el-button
        type="primary" icon="el-icon-plus" class="ele-btn-icon"
        size="small" @click="openEdit()"
      >
        添加
      </el-button> -->
      <div class="btnbox">
        <el-button type="primary" size="default" @click="openEdit">
          <template #icon>
            <svg-icon name="ep:plus" />
          </template>
          添加
        </el-button>
      </div>
      <el-table :data="tableData" highlight-current-row border height="calc(100vh - 315px)">
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column prop="id" label="ID" width="60" align="center" />
        <el-table-column prop="username" label="用户账号" min-width="120" align="center" />
        <el-table-column prop="realname" label="用户姓名" width="100" align="center" />
        <!-- <el-table-column label="头像" width="100" align="center">
					<template #default="scope">
						<image-preview :src="scope.row.avatar" :width="34" :height="34" />
					</template>
				</el-table-column> -->
        <el-table-column prop="gender" label="性别" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.gender === 1" class="ml-2" type="success">
              男
            </el-tag>
            <el-tag v-if="scope.row.gender === 2" class="ml-2" type="danger">
              女
            </el-tag>
            <el-tag v-if="scope.row.gender === 3" class="ml-2" type="danger">
              保密
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="mobile" label="手机号" width="120" align="center" />
        <el-table-column prop="roles" label="角色" width="120" align="center">
          <template #default="scope">
            <el-tag v-for="i, j in scope.row.roles" :key="j">
              {{ i.name }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="shop_id.name" label="所属门店" width="120" align="center" /> -->

        <!-- <el-table-column prop="level_name" label="职级" width="120" align="center" />
        <el-table-column prop="position_name" label="岗位" width="120" align="center" />
        <el-table-column prop="dept_name" label="部门" width="120" align="center" />
        <el-table-column prop="status" label="职级状态" width="90" align="center">
          <el-switch v-model="value" active-color="#1890ff" inactive-color="#ff4949" />
        </el-table-column> -->
        <el-table-column prop="roles" label="创建时间" width="180" align="center">
          <template #default="scope">
            {{ moment(scope.row.create_time * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="roles" label="更新时间" width="180" align="center">
          <template #default="scope">
            {{ scope.row.update_time > 0 ? moment(scope.row.update_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="230" align="center">
          <template #default="scope">
            <el-button type="primary" text @click="modify(scope.row.id)">
              <template #icon>
                <svg-icon name="ep:edit" />
              </template>修改
            </el-button>
            <el-popconfirm title="确定删除吗？" @confirm="removeBatch(scope.row.id)">
              <template #reference>
                <el-button type="danger" text>
                  <template #icon>
                    <svg-icon name="ep:delete" />
                  </template>删除
                </el-button>
              </template>
            </el-popconfirm>

            <!-- @click="resetPwd(scope.row.id)" -->
            <el-button type="success" text @click="resetPwd(scope.row)">
              <template #icon>
                <svg-icon name="ep:copy-document" />
              </template>重置密码
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- <div style="display: flex;justify-content: space-between;padding: 0 20px;">
        <el-pagination background layout="prev, pager, next" :total="total" @size-change="sizeChange" @current-change="handleCurrentChange" />
      </div> -->
      <div class="mt-4 flex items-center justify-center">
        <el-pagination
          v-model:current-page="currentPage4" v-model:page-size="pageSize4" :page-sizes="[10, 20, 50, 100]"
          :background="background" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="sizeChange" @current-change="handleCurrentChange"
        />
      </div>
    </page-main>

    <el-dialog v-model="dialogVisible" :title="modifyID ? '修改用户' : '添加用户'" width="680px" :before-close="handleClose">
      <Edit v-if="dialogVisible" ref="childRef" :modify-i-d="modifyID" @submit="submit" />

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="canForm">取消</el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重置密码 -->
    <el-dialog v-model="dialogFormVisible" title="重置密码" width="440px">
      <el-form :model="form">
        <el-form-item label="新密码">
          <el-input v-model="form.password" autocomplete="off" placeholder="请输入新密码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPass">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  .btnbox {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }
</style>
