<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import type { FormInstance } from 'element-plus'
import Api from '@/api/modules/system/dict'

const props = defineProps<{
  obj: any
}>()
const emits = defineEmits(['showeditfn'])
const formRef = ref<FormInstance>()
const title = ref('添加字典')
const form: any = ref({
  name: '',
  code: '',
  sort: 1,
  note: '',
  pid: 0,
})
const route = useRoute()
const formRules = ref({
  name: [
    { required: true, message: '请输入字典名称', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入字典值', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序号', trigger: 'blur' },
  ],
})
const data: any = ref({
  dataList: [],
})
interface FlatItem {
  value: string
  parentId: string | null
  label: string
}

interface TreeNode {
  value: string
  label: string
  children?: TreeNode[]
}
onMounted(() => {
  console.log(props.obj, 'props.obj')
  getList()
  if (props.obj.id) {
    form.value = JSON.parse(JSON.stringify(props.obj))
    form.value.pid = form.value.pid === 0 ? null : form.value.pid
    title.value = '编辑字典'
  }
})
function onCancel() {
  // myVisible.value = false
  emits('showeditfn', false)
}
function getList() {
  Api.list({}).then((res: any) => {
    res.data.forEach((item: any) => {
      item.label = item.name
      item.value = item.id
      item.parentId = item.pid === 0 ? null : item.pid
    })
    data.value.dataList = toTreeData(res.data)
    console.log(data.value.dataList, 'data.value.dataList')
  })
}
function changepid() {

}
function toTreeData(flatData: FlatItem[]): TreeNode[] {
  const map: Map<string, TreeNode> = new Map()

  flatData.forEach((item) => {
    map.set(item.value, { value: item.value, label: item.label })
  })
  console.log(map, flatData, 'map')
  const tree: TreeNode[] = []

  flatData.forEach((item) => {
    const node = map.get(item.value)
    console.log(item, 'node')
    if (node) {
      if (item.parentId === null) {
        tree.push(node)
      }
      else {
        const parent = map.get(item.parentId)
        if (parent && !parent.children) {
          parent.children = []
        }
        parent?.children?.push(node)
      }
    }
  })
  console.log(tree, 'eee')
  return tree
}
function onSubmit() {
  formRef.value && formRef.value.validate((valid) => {
    if (valid) {
      ElMessageBox.confirm(
        '确认提交?',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        },
      )
        .then(() => {
          if (!form.value.pid) {
            form.value.pid = 0
          }
          Api.edit(form.value).then((res: any) => {
            ElMessage({ message: '操作成功', type: 'success' })
            onCancel()
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消',
          })
        })
    }
  })
}
</script>

<template>
  <el-dialog :title="title" width="440px" :close-on-click-modal="false" append-to-body destroy-on-close>
    <el-form ref="formRef" :model="form" :rules="formRules" label-width="84px">
      <el-form-item label="字典名称:" prop="name">
        <el-input v-model="form.name" placeholder="请输入字典名称" clearable />
      </el-form-item>
      <!-- <el-form-item label="字典值:" prop="code">
        <el-input v-model="form.code" placeholder="请输入字典值" clearable />
      </el-form-item>
      <el-form-item label="字典分类:" prop="pid">
        <el-tree-select
          v-model="form.pid"
          :data="data.dataList"
          :render-after-expand="false"
          clearable
          check-strictly="true"
          style="width: 100%;"
          @change="changepid()"
        />
      </el-form-item>
      <el-form-item label="排序号:" prop="sort">
        <el-input-number
          v-model="form.sort"
          style="width: 100%;"
          :min="0"
          placeholder="请输入排序号"
          controls-position="right"
          class="ele-fluid ele-text-left"
        />
      </el-form-item>
      <el-form-item label="备注:">
        <el-input
          v-model="form.note"
          :maxlength="200"
          placeholder="请输入备注"
          :rows="4"
          type="textarea"
        />
      </el-form-item> -->
    </el-form>

    <template #footer>
      <el-button @click="onCancel">
        取消
      </el-button>
      <el-button type="primary" @click="onSubmit">
        保存
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
  .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
