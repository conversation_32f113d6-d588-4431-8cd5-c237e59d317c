<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import DictData from './dict-data.vue'
import Edit from './edit.vue'
import Api from '@/api/modules/system/dict'

const isShow: any = ref(null)
const open: any = ref(true)
const isdev: any = ref(import.meta.env.MODE === 'development')
const showEdit = ref(false)
const data: any = ref({
  dataList: [],
  obj: {},
  id: '',
  allData: [],
})
interface FlatItem {
  id: string
  parentId: string | null
  label: string
}

interface TreeNode {
  id: string
  label: string
  children?: TreeNode[]
}
onMounted(() => {
  getList()
})
function toTreeData(flatData: FlatItem[]): TreeNode[] {
  const map: Map<string, TreeNode> = new Map()

  flatData.forEach((item) => {
    map.set(item.id, { id: item.id, label: item.label })
  })
  console.log(map, flatData, 'map')
  const tree: TreeNode[] = []

  flatData.forEach((item) => {
    const node = map.get(item.id)
    console.log(item, 'node')
    if (node) {
      if (!item.parentId) {
        tree.push(node)
      }
      else {
        const parent = map.get(item.parentId)
        if (parent && !parent.children) {
          parent.children = []
        }
        parent?.children?.push(node)
      }
    }
  })
  console.log(tree, 'eee')
  return tree
}

function getList() {
  Api.list({}).then((res: any) => {
    data.value.allData = res.data
    res.data.forEach((item: any) => {
      item.label = item.name
      item.value = item.id
      item.parentId = item.pid === 0 ? null : item.pid
    })
    data.value.dataList = toTreeData(res.data)
    console.log(data.value.dataList, ' res.data res.data')
  })
}
function add() {
  data.value.obj = {}
  showEdit.value = true
  isShow.value = true
}

function onEdit(e: any) {
  console.log(data.value.obj, 'data.value.obj')
  if (!data.value.id) {
    ElMessage.info('请选择修改项')
    return
  }
  showEdit.value = true
  isShow.value = true
}

function remove() {
  if (!data.value.id) {
    ElMessage.info('请选择修改项')
    return
  }
  ElMessageBox.confirm(
    '确定要删除选中的字典吗?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      Api.delete({ id: data.value.id }).then((res: any) => {
        ElMessage({
          type: 'success',
          message: '删除成功',
        })
        getList()
        data.value.id = ''
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消删除',
      })
    })
}
function handleEvent(data: any) {
  console.log(data, '')
}
function showEditFn() {
  showEdit.value = false
  getList()
  isShow.value = false
  // data.value.obj = {}
}
function rowClick(row: any, column: any, event: any) {
  data.value.allData.forEach((item: any) => {
    console.log(row.id, item.id)
    if (row.id === item.id) {
      data.value.obj = item
    }
  })
  console.log(row)
  data.value.id = row.id
  open.value = false
  console.log
  setTimeout(() => {
    open.value = true
  }, 10)
  // console.log(row, column, event)
}
</script>

<template>
  <div class="absolute-container">
    <div class="page-main">
      <LayoutContainer>
        <template #leftSide>
          <div v-if="isdev" class="btnbox d-s-c">
            <el-button type="primary" class="add" style="width: auto;" @click="add">
              <template #icon>
                <svg-icon name="ep:plus" />添加
              </template>
            </el-button>
            <el-button type="warning" class="add" style="width: auto;" @click="onEdit">
              <template #icon>
                <svg-icon name="ep:edit" />修改
              </template>
            </el-button>
            <el-button type="danger" class="add" style="width: auto;" @click="remove">
              <template #icon>
                <svg-icon name="ep:delete" />删除
              </template>
            </el-button>
          </div>
          <el-table
            :data="data.dataList" row-key="id" default-expand-all highlight-current-row border height="100%"
            @row-click="rowClick"
          >
            <el-table-column prop="id" label="ID" width="45" align="center" fixed="left" />
            <el-table-column prop="label" label="字典名称" />
          </el-table>
        </template>
        <div class="container">
          <DictData v-if="open" :id="data.id" />
        </div>
      </LayoutContainer>
    </div>

    <!-- 修改添加组件 -->
    <Edit v-if="isShow" v-model="showEdit" :obj="data.obj" @showeditfn="showEditFn" />
  </div>
</template>

<style lang="scss" scoped>
  .absolute-container {
    position: absolute;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .page-header {
      margin-bottom: 0;
    }
  }

  .flex-container {
    :deep(.left-side) {
      display: flex;
      flex-direction: column;
      height: 100%;

      i {
        font-style: normal;
      }

      .btnbox {
        margin-bottom: 10px;
      }

      .btnbox .el-button {
        padding: 0 26px;
        font-size: 12px;
      }

      .search {
        margin: 15px 0;
      }

      .tree {
        flex: 1;
        overflow-y: auto;

        .el-tree {
          .el-tree-node__content {
            height: 60px;
          }

          .is-current>.el-tree-node__content {
            background-color: var(--el-color-primary-light-9);
          }

          .custom-tree-node {
            position: relative;
            display: flex;
            flex: 1;
            flex-direction: column;
            justify-content: center;
            width: 0;
            height: 100%;

            .label {
              width: calc(100% - 10px);
              color: var(--el-text-color-primary);

              @include text-overflow;
            }

            .code {
              width: calc(100% - 10px);
              color: var(--el-text-color-placeholder);

              @include text-overflow;
            }

            &:hover {
              .actions {
                display: block;
              }
            }

            .actions {
              position: absolute;
              top: 50%;
              right: 10px;
              display: none;
              transform: translateY(-50%);

              .el-button {
                padding: 5px 8px;
              }
            }
          }
        }
      }
    }

    :deep(.main) {
      display: flex;
      justify-content: center;
    }

    .container {
      // display: flex;
      // flex-direction: column;
      // justify-content: center;
      width: 100%;
      height: 100%;

      .empty {
        font-size: 32px;
        color: var(--el-text-color-placeholder);
        text-align: center;
      }

      .el-table {
        margin: 15px 0;
      }
    }
  }
</style>
