<script setup lang="ts">
import { onActivated, ref } from 'vue'
import moment from 'moment'
import type {
  FormInstance,
  FormRules,
} from 'element-plus'
import { ElMessage } from 'element-plus'
import Api from '@/api/modules/system/dept'

const options: any = ref([])
const tableData: any = ref([])
const dialogVisible: any = ref(false)
const props1 = {
  checkStrictly: true,
}
// 请求参数
const paging = ref({
  page: 1,
  limit: 1000,
})
const total: any = ref(0)
getallList()
function getallList() {
  Api.list({
    page: 1,
    limit: 1000,
    pid: 0,
  }).then((res: any) => {
    res.data.forEach((i: any, j: any) => {
      i.value = i.id
      i.label = i.name
    })
    options.value = listToTree(res.data)
  })
}

// 请求列表
function getList() {
  tableData.value = []
  Api.list(paging.value).then((res: any) => {
    // console.log(buildTree(res.data), '列表打印')
    // res.data.forEach((i : any, j : any) => {
    // 	if (i.pid > 0 && i.pid === res.data[j].id) {
    // 		res.data[j].children = res.data[j].children ? res.data[j].children : [],
    // 			res.data[j].children.push(i)
    // 		res.data.splice(j, 1)
    // 	}
    // })

    tableData.value = listToTree(res.data)
    total.value = res.count
    console.log(tableData.value, '列表打印')
  })
}

function listToTree(list: any) {
  const map = {}; const roots = []
  for (const item of list) {
    map[item.id] = { ...item, children: [] }
  }
  for (const item of list) {
    if (item.pid === 0) {
      roots.push(map[item.id])
    }
    else {
      if (map[item.pid]) {
        map[item.pid].children.push(map[item.id])
      }
    }
  }
  return roots
}
// 请求列表
getList()
onActivated(() => {
  getList()
})

function removeBatch(e: any) {
  Api.pdelete({
    id: e,
  }).then((res: any) => {
    ElMessage({ message: res.msg, type: 'success' })
    getList()
  })
}
// 分页方法
// function sizeChange(e : any) {
// 	paging.value.size = e
// 	getList()
// }
// function currentChange(e : any) {
// 	paging.value.page = e
// 	getList()
// }
// 新增修改
const form: any = ref({})
function handleAdd(e: any) {
  // console.log(e, '新增修改')
  if (e.id) {
    form.value = JSON.parse(JSON.stringify(e))
    if (form.value.pid === 0) {
      form.value.pid = ''
    }
    // form.value.pid = [form.value.pid]
  }
  else {
    form.value = {
      sort: 1,
      status: 1,
    }
  }
  dialogVisible.value = true
}
// 定义提交按钮函数
const formRef = ref<FormInstance>()
const formRules = ref<FormRules>({
  name: [{
    required: true,
    message: '请输入部门名称',
    trigger: 'blur',
  }],
  sort: [{
    required: true,
    message: '请输入排序',
    trigger: 'blur',
  }],
})
function submitButton() {
  if (form.value.id && form.value.pid === form.value.id) {
    ElMessage({ message: '不可选择自身为上级部门', type: 'warming' })
    return
  }
  formRef.value && formRef.value.validate((valid) => {
    if (valid) {
      // if (form.value.pid) {
      // 	form.value.pid = form.value.pid[0]
      // }
      Api.edit(form.value).then((res: any) => {
        ElMessage({ message: res.msg, type: 'success' })
        dialogVisible.value = false
        getList()
      })
    }
  })
}
function handleChange(e: any) {
  // console.log(e, '酷酷酷')

}
// 分页代码
function pagChange(e: any) {
  paging.value = Object.assign(paging.value, e)
  getList()
}
</script>

<template>
  <div>
    <page-main style="position: relative;">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="部门名称:">
          <el-input v-model="paging.name" placeholder="部门名称" clearable />
        </el-form-item>
        <el-form-item label="状态" style="width: 200px;">
          <el-select v-model="paging.status" clearable placeholder="请选择状态" @clear="paging.status = null">
            <el-option label="正常" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList()">
            <template #icon>
              <svg-icon name="ep:search" />
            </template>
            查询
          </el-button>
          <el-button
            @click="paging = {
              page: 1,
              limit: 1000,
            }, getList()"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <div class="btnbox">
        <el-button type="primary" size="default" @click="handleAdd">
          <template #icon>
            <svg-icon name="ep:plus" />
          </template>
          添加
        </el-button>
      </div>
      <el-table :data="tableData" row-key="id" default-expand-all border height="60vh">
        <!-- <el-table-column type="selection" width="55" /> -->
        <!-- <el-table-column prop="id" label="ID" width="60" align="center" /> -->
        <el-table-column type="index" width="65" align="center" fixed="left" />
        <el-table-column prop="name" label="部门名称" min-width="200" />
        <el-table-column prop="status" label="部门状态" width="100" align="center">
          <template #default="{ row: i }">
            <div v-if="i.status == 1" class="css-new-status">
              <div class="left-box" />
              <div>正常</div>
            </div>
            <div v-else class="css-new-status" :style="{ '--color': '#f56C6C' }">
              <div class="left-box" />
              <div>停用</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序号" width="100" align="center" />
        <el-table-column prop="roles" label="创建时间" width="180" align="center">
          <template #default="scope">
            {{ moment(scope.row.create_time * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="update_time" label="更新时间" width="180" align="center">
          <template #default="scope">
            {{ scope.row.update_time > 0 ? moment(scope.row.update_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180" align="center">
          <template #default="scope">
            <el-button type="primary" text @click="handleAdd(scope.row)">
              <template #icon>
                <svg-icon name="ep:edit" />
              </template>修改
            </el-button>
            <el-popconfirm title="确定删除吗？" @confirm="removeBatch(scope.row.id)">
              <template #reference>
                <el-button type="danger" text>
                  <template #icon>
                    <svg-icon name="ep:delete" />
                  </template>删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-paging :current-page="paging.page" :total="paging.total" :page-size="paging.size"
				:page-sizes="paging.sizes" :layout="paging.layout" :hide-on-single-page="false"
				class="paging mt-4" background @size-change="sizeChange" @current-change="currentChange" /> -->
      <!-- <div class="h-30">
				<page-compon :page="paging.page" :size="paging.limit" :total="total" @pag-change="pagChange" />
			</div> -->
    </page-main>
    <!-- 添加修改 -->
    <el-dialog v-model="dialogVisible" :title="form.id ? '修改部门' : '新增部门' " width="680px">
      <el-form ref="formRef" :rules="formRules" :model="form" label-width="90px">
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="上级部门" prop="pid">
          <!-- <el-cascader v-model="form.pid" :props="props1" class="w-full" :options="options"
						@change="handleChange" /> -->
          <el-select v-model="form.pid" placeholder="请选择上级部门" class="w-full">
            <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">
              在用
            </el-radio>
            <el-radio :label="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序号" prop="sort">
          <el-input-number v-model="form.sort" :min="1" />
        </el-form-item>
        <div style="display: flex;justify-content: center;">
          <el-button type="primary" @click="submitButton()">
            确定
          </el-button>
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  .btnbox {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }
</style>
