<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
// import type Edit from './edit.vue'
// import Getuser from './getuser.vue'
// import Handover from './handover.vue'

// import systemApi from '@/api/modules/system/systemApi'
const headerCellStyle = ref({
  backgroundColor: '#e5e4e4',
  color: '#333',
  padding: '8px 0',
  fontSize: '16px',
})
const activeleft: any = ref([0, 0])
const switch1 = ref(1)
const switch2 = ref(0)

const dialogVisibleauto = ref(false)
const roleautoid = ref({})
const autotitle = ref('权限配置')
const checkList = ref([])
const tableData: any = ref([
  {
    id: 1,
    name: '等级1',
    code: 'name1',
    sort: 1,
    desc: '备注',
    create_time: '2024-2-23 14:00:00',
    update_time: '2024-2-23 16:00:00',
  },
  {
    id: 1,
    name: '等级2',
    code: '',
    sort: 1,
    desc: '备注',
    create_time: '2024-2-23 14:00:00',
    update_time: '2024-2-23 16:00:00',
  },
  {
    id: 1,
    name: '等级3',
    code: 'name3',
    sort: 1,
    desc: '',
    create_time: '2024-2-23 14:00:00',
    update_time: '2024-2-23 16:00:00',
  },
])
const leftData: any = ref([
  {
    code: '1',
    create_user: 1,
    create_user_name: '超管',
    id: 1,
    name: '用户等级',
    pid: 52,
    sort: 1,
  },
  {
    code: '1',
    create_user: 1,
    create_user_name: '超管',
    id: 2,
    name: '操作类型',
    pid: 52,
    sort: 1,
  },
  {
    code: '1',
    create_user: 1,
    create_user_name: '超管',
    id: 3,
    name: '创建方式',
    pid: 52,
    sort: 1,
  },
])
const loadingRef: any = ref(null)
const form: any = ref({})
const data = ref({
  page: 1,
  limit: 10,
})
const total: any = ref(0)
const paging: any = ref({
  page: data.value.page,
  limit: data.value.limit,
})

const currentPage4 = ref(1)
const pageSize4 = ref(10)
const background = ref(true)

const dialogVisible = ref(false)
onMounted(() => {
  // loadingRef.value.open(1500)
  // getList()// 请求数据
})
function getList() {
  loadingRef.value.open(1500)

  // systemApi.Ranklist(paging.value).then((res: any) => {
  //   tableData.value = res.data
  //   total.value = res.count
  // })
}
// 修改
const isedit: any = ref(null)
function modify(val: any) {
  isedit.value = true
  if (!val) {
    form.value = {}
    dialogVisible.value = !dialogVisible.value
  }
  else {
    form.value = JSON.parse(JSON.stringify(val))
    dialogVisible.value = !dialogVisible.value
  }
}

function openEdit() {
  isedit.value = false
  dialogVisible.value = !dialogVisible.value
  form.value = {}
}
// 分页
function sizeChange(val: any) {
  // console.log(val, '55555')
}
// 分页
function handleCurrentChange(val: any) {
  paging.value.page = val
  getList()
}
function submitForm() {
  console.log(form.value, ' form.value')

  if (!form.value.name) {
    ElMessage({ message: '请输入角色名称', type: 'error' })
    return false
  }
  // systemApi.Rank_edit(form.value).then((res: any) => {
  //   ElMessage({ message: res.msg, type: 'success' })
  //   dialogVisible.value = false
  //   getList()
  // })
  if (form.value.id) {
    tableData.value.forEach((element: any, index: any) => {
      if (element.id === form.value.id) {
        tableData.value[index] = form.value
      }
    })
  }
  else {
    form.value.is_edit = true
    form.value.id = tableData.value.length + 1
    tableData.value.push(form.value)
  }
  dialogVisible.value = false
}
// // 状态修改
// function onChangeStatus(row: any) {
//   console.log(row, 'rowrow')

//   ElMessageBox.confirm(
//       `确认${row.status === 1 ? '启用当前职位？' : '关闭当前职位？'}`,
//       '提示',
//       {
//         confirmButtonText: '确认',
//         cancelButtonText: '取消',
//         type: 'warning',
//       },
//   )
//     .then(() => {
//       systemApi.Rank_edit({
//         id: row.id,
//         status: row.status,
//       }).then(() => {
//         getList()
//         ElMessage({
//           type: 'success',
//           message: '修改成功',
//         })
//       })
//     })
//     .catch(() => {
//       // getList()
//       ElMessage({
//         type: 'info',
//         message: '已取消',
//       })
//     })
// }
function removeBatch(row: any) {
  tableData.value.forEach((element: any, index: any) => {
    if (element.id === row) {
      tableData.value.splice(index, 1)
    }
  })
  // systemApi.Rank_delete({
  //   id: row,
  // }).then(() => {
  //   getList()
  //   ElMessage({
  //     type: 'success',
  //     message: '操作成功',
  //   })
  // })
}
// 修改权限
function changerolepage(row: any) {
  dialogVisibleauto.value = true
  roleautoid.value = row
  autotitle.value = `权限配置` + ` - ${row.name}`
  // router.push({
  //   path: '/autopege',
  //   params: {
  //     id: row,
  //   },
  // })
}
function changeleft(i: any, j: any) {
  activeleft.value = [0, j]
}
</script>

<template>
  <div>
    <PageHeader>
      <template #title>
        <div class="flex items-center gap-4">
          字典数据
        </div>
      </template>
    </PageHeader>
    <page-main style="position: relative;">
      <div class="w-full flex">
        <div class="bor-r min-w-60 p5" style="width: 13%;">
          <div style="margin-top: -17px;">
            <div class="flex justify-between">
              <div class="f18 pb5 pt5" style="font-weight: 600;">
                字典名称
              </div>
              <!-- <el-button class="mt4">
                管理
              </el-button> -->
            </div>
            <div
              v-for="(it, jt) in leftData" :key="jt" style="border-radius: 4px;"
              :style="{ backgroundColor: (activeleft[1] === jt) ? '#edf6ff' : '', color: (activeleft[1] === jt) ? '#409eff' : '' }"
              class="min-w-40 flex cursor-pointer justify-between p2" @click="changeleft(it, jt)"
            >
              <div>{{ it.name }}</div>
              <div style="color: #999;" :style="{ color: (activeleft[1] === jt) ? '#409eff' : '#999' }">
                {{ it.num }}
              </div>
            </div>
          </div>
        </div>

        <div class="p5" style="position: relative;width: 87%;">
          <Loading ref="loadingRef" />
          <div class="d-c flex">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="字典名称:">
                <el-input v-model="paging.name" placeholder="请输入字典名称" clearable @clear="paging.name = null" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="getList()">
                  <template #icon>
                    <svg-icon name="ep:search" />
                  </template>
                  查询
                </el-button>
                <el-button
                  @click="paging = {
                    page: 1,
                    limit: 10,
                    name: null,
                  }, getList()"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-form>
            <div class="btnbox">
              <el-button type="primary" size="default" @click="openEdit">
                <template #icon>
                  <svg-icon name="ep:plus" />
                </template>
                新建字典
              </el-button>
            </div>
          </div>

          <el-table :data="tableData" highlight-current-row border height="calc(100vh - 300px)">
            <!-- <el-table-column type="selection" width="55" /> -->
            <el-table-column prop="id" label="ID" width="60" align="center" />

            <el-table-column prop="name" label="字典名称" width="100" align="center" />
            <el-table-column prop="code" label="字典项值" width="100" align="center" />
            <el-table-column prop="sort" label="排序" width="60" align="center" />
            <el-table-column prop="desc" label="备注" />

            <el-table-column prop="create_time" label="创建时间" width="180" align="center">
              <template #default="{ row }">
                {{ row.create_time }}
              </template>
            </el-table-column>
            <!-- <el-table-column prop="update_time" label="更新时间" width="180" align="center">
              <template #default="{ row }">
                {{ row.update_time }}
              </template>
            </el-table-column> -->
            <el-table-column fixed="right" label="操作" width="220" align="center">
              <template #default="scope">
                <el-button type="primary" :disabled="!scope.row.is_edit" text @click="modify(scope.row)">
                  <template #icon>
                    <svg-icon name="ep:edit" />
                  </template>修改
                </el-button>
                <el-popconfirm title="确定删除吗？" @confirm="removeBatch(scope.row.id)">
                  <template #reference>
                    <el-button type="danger" text>
                      <template #icon>
                        <svg-icon name="ep:delete" />
                      </template>删除
                    </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <!--
        <el-pagination
          v-model:current-page="currentPage4"
          v-model:page-size="pageSize4"
          :page-sizes="[10, 20, 50, 100]"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="sizeChange"
          @current-change="handleCurrentChange"
        /> -->
        </div>
      </div>
    </page-main>
    <HDialog v-model="dialogVisible" :title="form.id ? '修改角色' : '添加角色'">
      <!-- <HInput v-model="data1.name" class="w-full!" /> -->
      <div style="padding: 0 18px;">
        <el-form :model="form" label-width="86px">
          <el-form-item label="角色名称：" prop="name">
            <el-input v-model="form.name" size="large" placeholder="请输入角色名称" />
          </el-form-item>
          <el-form-item label="角色描述：" prop="remark">
            <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入角色描述" />
          </el-form-item>
          <!-- <el-form-item label="成员数量：" prop="num">
            <el-input-number
              v-model="form.num"
              size="large"
              style="width: 100%;"
              placeholder="请输入成员数量"
              controls-position="right"
              class="ele-fluid ele-text-left"
            />
          </el-form-item> -->
          <!-- <el-form-item label="排序：" prop="sort">
          <el-input-number
            v-model="form.sort"
            style="width: 100%;"
            :min="0"
            placeholder="请输入排序号"
            controls-position="right"
            class="ele-fluid ele-text-left"
          />
        </el-form-item> -->
          <!-- <el-form-item label="状态：" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">
              启用
            </el-radio>
            <el-radio :label="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item> -->
        </el-form>
      </div>
      <template #footer>
        <div class="fotterbtn">
          <!-- <HButton outline class="mr-2" @click="showEdit = false">
            取消
          </HButton>
          <HButton type="primary" @click="submitForm">
            确定
          </HButton> -->
          <el-button class="cancel" @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
  </div>
</template>

<style lang="scss" scoped>
  :deep(.main-container) {
    padding: 0 15px;
  }

  .hdialogwidth {
    padding: 120px;
  }

  .fotterbtn {

    // padding: 8px 32px;
    .cancel {
      color: #409eff;
      border: 1px solid #409eff;
    }
  }

  .custom-tooltip {
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s, color 0.3s;
  }

  .btnbox {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }

  :deep(.el-checkbox__label) {
    font-size: 16px;
  }

  :deep(.el-checkbox) {
    width: 120px;
    margin-right: 0;
  }

  .border-2-999 {
    border: 1px solid #e9e8e8;
  }

  .bg-ccc {
    background: #f5f5f5;
  }

  .fontW-500 {
    font-weight: 500;
  }

  .fontW-600 {
    font-weight: 600;
  }

  .d-c {
    flex-direction: column;
  }

  .bor-r {
    border-right: 1px solid #e9e8e8;
  }

  .bor-t {
    border-top: 1px solid #e9e8e8;
  }

  .bor-l {
    border-left: 1px solid #e9e8e8;
  }

  .bor-b {
    border-bottom: 1px solid #e9e8e8;
  }
</style>
