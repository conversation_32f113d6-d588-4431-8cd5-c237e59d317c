<template>
  <div class="department-tree-example">
    <PageHeader title="部门树形组件示例" />
    
    <PageMain>
      <!-- 基本用法 -->
      <el-card class="example-card" header="基本用法">
        <p class="example-description">展示组织架构的树形结构，支持公司、分公司、部门的层级展示</p>
        <DepartmentTree
          :default-expand-all="true"
          :show-type="true"
          @node-click="handleNodeClick"
        />
      </el-card>

      <!-- 带复选框 -->
      <el-card class="example-card" header="带复选框">
        <p class="example-description">支持多选功能，可以选择多个部门</p>
        <div class="example-controls">
          <el-button @click="getCheckedData">获取选中数据</el-button>
          <el-button @click="clearChecked">清空选中</el-button>
          <el-button @click="setDefaultChecked">设置默认选中</el-button>
        </div>
        <DepartmentTree
          ref="checkboxTreeRef"
          :show-checkbox="true"
          :default-expand-all="true"
          :show-type="true"
          :show-code="true"
          @check-change="handleCheckChange"
        />
        <div v-if="checkedInfo" class="checked-info">
          <h4>选中的节点信息：</h4>
          <pre>{{ checkedInfo }}</pre>
        </div>
      </el-card>

      <!-- 带搜索过滤 -->
      <el-card class="example-card" header="带搜索过滤">
        <p class="example-description">支持按名称或编码搜索过滤节点</p>
        <div class="example-controls">
          <el-input
            v-model="filterText"
            placeholder="请输入部门名称或编码进行搜索"
            style="width: 300px;"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button @click="refreshTree">刷新数据</el-button>
        </div>
        <DepartmentTree
          ref="filterTreeRef"
          :filter-text="filterText"
          :highlight-current="true"
          :show-type="true"
          :show-code="true"
          @current-change="handleCurrentChange"
        />
        <div v-if="currentNodeInfo" class="current-info">
          <h4>当前选中节点：</h4>
          <pre>{{ currentNodeInfo }}</pre>
        </div>
      </el-card>

      <!-- 紧凑模式 -->
      <el-card class="example-card" header="紧凑模式">
        <p class="example-description">不显示类型标签和编码的紧凑展示模式</p>
        <DepartmentTree
          :show-type="false"
          :show-code="false"
          :expand-on-click-node="false"
        />
      </el-card>
    </PageMain>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import PageHeader from '@/components/PageHeader/index.vue'
import PageMain from '@/components/PageMain/index.vue'
import DepartmentTree from '@/components/DepartmentTree/index.vue'

// 组件引用
const checkboxTreeRef = ref()
const filterTreeRef = ref()

// 过滤文本
const filterText = ref('')

// 选中信息
const checkedInfo = ref('')
const currentNodeInfo = ref('')

// 节点点击事件
function handleNodeClick(data: any, node: any, component: any) {
  console.log('节点点击:', data)
  ElMessage.success(`点击了：${data.name}`)
}

// 复选框状态改变事件
function handleCheckChange(data: any, checked: boolean, indeterminate: boolean) {
  console.log('复选框状态改变:', { data, checked, indeterminate })
}

// 当前选中节点改变事件
function handleCurrentChange(data: any, node: any) {
  console.log('当前选中节点改变:', data)
  currentNodeInfo.value = JSON.stringify({
    id: data.id,
    name: data.name,
    code: data.code,
    type: data.type,
    level: data.level,
    description: data.description
  }, null, 2)
}

// 获取选中数据
function getCheckedData() {
  if (!checkboxTreeRef.value) {
    ElMessage.warning('组件未加载完成')
    return
  }
  
  const checkedNodes = checkboxTreeRef.value.getCheckedNodes()
  const checkedKeys = checkboxTreeRef.value.getCheckedKeys()
  
  checkedInfo.value = JSON.stringify({
    checkedKeys,
    checkedNodes: checkedNodes.map((node: any) => ({
      id: node.id,
      name: node.name,
      code: node.code,
      type: node.type
    }))
  }, null, 2)
  
  ElMessage.success(`已选中 ${checkedNodes.length} 个节点`)
}

// 清空选中
function clearChecked() {
  if (!checkboxTreeRef.value) {
    ElMessage.warning('组件未加载完成')
    return
  }
  
  checkboxTreeRef.value.setCheckedKeys([])
  checkedInfo.value = ''
  ElMessage.success('已清空选中')
}

// 设置默认选中
function setDefaultChecked() {
  if (!checkboxTreeRef.value) {
    ElMessage.warning('组件未加载完成')
    return
  }
  
  // 设置一些默认选中的节点ID
  checkboxTreeRef.value.setCheckedKeys([661, 662, 663])
  ElMessage.success('已设置默认选中')
}

// 刷新树数据
function refreshTree() {
  if (!filterTreeRef.value) {
    ElMessage.warning('组件未加载完成')
    return
  }
  
  filterTreeRef.value.refresh()
  ElMessage.success('数据已刷新')
}
</script>

<style lang="scss" scoped>
.department-tree-example {
  .example-card {
    margin-bottom: 20px;
    
    .example-description {
      margin-bottom: 16px;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
    
    .example-controls {
      margin-bottom: 16px;
      display: flex;
      gap: 12px;
      align-items: center;
    }
    
    .checked-info,
    .current-info {
      margin-top: 16px;
      padding: 12px;
      background-color: var(--el-fill-color-lighter);
      border-radius: 4px;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: var(--el-text-color-primary);
      }
      
      pre {
        margin: 0;
        font-size: 12px;
        color: var(--el-text-color-regular);
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}
</style>