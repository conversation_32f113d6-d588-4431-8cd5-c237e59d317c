<script setup lang="ts">
import { ref } from 'vue'

const activeName = ref(1)

function handleClick(tab: TabsPaneContext, event: Event) {
  console.log(tab, event)
}

const tabsList = ref([
  {
    id: 1,
    name: '基本信息',
  },
  {
    id: 2,
    name: '关联信息',
  },
  {
    id: 3,
    name: '风险分析',
  },
  {
    id: 4,
    name: '映射关系',
  },
])
</script>

<template>
  <div style="">
    <div class="aic jcsb flex">
      <div class="f-20 fw-500">
        法规列表
      </div>
      <div>
        <svg-icon name="ep:setting" />
      </div>
    </div>
    <div class="topBox mb-20">
      <div style="width: 50%;">
        <div class="f-14 c-[#6B7280] fw-400">
          义务类型
        </div>
        <div class="f-16 mt-16 fw-500">
          法律法规要求
        </div>
      </div>
      <div style="width: 50%;">
        <div class="f-14 c-[#6B7280] fw-400">
          责任部门
        </div>
        <div class="f-16 mt-16 fw-500">
          合规部
        </div>
      </div>
      <div class="mt-20" style="width: 50%;">
        <div class="f-14 c-[#6B7280] fw-400">
          生效状态
        </div>
        <div class="f-16 aic mt-16 flex fw-500">
          <div style="width: 8px;height: 8px;background-color: #22c55e;border-radius: 50%;" />
          <span class="ml-6">已生效</span>
        </div>
      </div>
      <div class="mt-20" style="width: 50%;">
        <div class="f-14 c-[#6B7280] fw-400">
          更新时间
        </div>
        <div class="f-16 mt-16 fw-500">
          2024-02-20
        </div>
      </div>
    </div>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane v-for="i, j in tabsList" :key="j" :label="i.name" :name="i.id">
        <div v-if="activeName == 1">
          <div class="f-16 fw-500">
            义务描述
          </div>
          <div class="f-14 mt-12 fw-400">
            金融机构应当按照规定建立客户身份识别制度，遵循"了解你的客户"的原则，针对不同客户、业务关系或交易，采取相应的措施，了解客户及其交易目的和交易性质，收集相关信息并建立相应的管理机制。
          </div>
          <div class="f-16 mt-24 fw-500">
            义务描述
          </div>
          <div class="br-4 mt-16 bg-[#F9FAFB] p-6" style="">
            <div class="f-14 p-16 fw-500">
              《中华人民共和国反洗钱法》第十六条
            </div>
            <div class="f-14 mt-8">
              金融机构应当按照规定建立客户身份识别制度、客户身份资料和交易记录保存制度、大额交易和可疑交易报告制度，履行反洗钱义务。
            </div>
          </div>
          <div class="f-16 mt-24 fw-500">
            执行要求
          </div>
          <div>
            <div v-for="i, j in 4" :key="j" class="aic mt-12 flex">
              <div>
                <svg-icon name="ep:circle-check" style="color: #22c55e;" />
              </div>
              <div class="f-14 ml-8">
                建立健全的客户身份识别制度
              </div>
            </div>
          </div>
        </div>
        <div v-if="activeName == 2" />
        <div v-if="activeName == 3" />
        <div v-if="activeName == 4" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .topBox {
    display: flex;
    flex-wrap: wrap;
    padding: 24px 0;
    border-bottom: 1px solid #f3f4f6;
  }
</style>
