<script lang="ts" setup>
import { watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import systemApi from '@/api/complianceApi/one/systemManagement'

// 定义props
interface Props {
  searchParams?: Record<string, any>
  refreshTrigger?: number
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({}),
  refreshTrigger: 0
})

// 定义emits
const emit = defineEmits<{
  'update:loading': [loading: boolean]
  'update:total': [total: number]
  'data-loaded': [data: any[]]
}>()

const router = useRouter()

// 表格数据
const tableData = ref([])

// 分页数据
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0,
})

// 加载状态
const loading = ref(false)

// 获取列表数据
async function getList() {
  try {
    loading.value = true
    emit('update:loading', true)
    
    const params = {
      ...props.searchParams,
      page: pagination.value.page,
      limit: pagination.value.limit,
    }

    const response = await systemApi.lawsSystem(params)

    if (response && response.content) {
      tableData.value = response.content
      pagination.value.total = response.totalElements || 0
      emit('update:total', pagination.value.total)
      emit('data-loaded', response.content)
    }
  }
  catch (error) {
    console.error('获取法规列表失败:', error)
    ElMessage.error('获取法规列表失败')
  }
  finally {
    loading.value = false
    emit('update:loading', false)
  }
}

// 查看详情
function viewDetail(row) {
  router.push({
    path: '/database/laws/detail',
    query: { id: row.id },
  })
}

// 编辑
const editRegulation = (row: any) => {
  router.push({
    path: '/database/laws/addEdit',
    query: { id: row.id }
  })
}

// 订阅
function subscribe(row) {
  ElMessage.success(`已订阅：${row.title}`)
  // 这里后续添加订阅逻辑
}

// 删除
async function deleteRegulation(row) {
  try {
    await ElMessageBox.confirm(
      `确定要删除「${row.title}」吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await systemApi.lawsSystem({ id: row.id }, 'delete')
    ElMessage.success('删除成功')
    getList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 更多操作
function moreActions(row: any, action: string) {
  switch (action) {
    case 'edit':
      editRegulation(row)
      break
    case 'delete':
      deleteRegulation(row)
      break
    case 'download':
      ElMessage.info(`下载：${row.title}`)
      // 这里后续添加下载逻辑
      break
  }
}

// 分页改变
function handlePageChange(page: number) {
  pagination.value.page = page
  getList()
}

function handleSizeChange(size: number) {
  pagination.value.limit = size
  pagination.value.page = 1
  getList()
}

// 格式化状态
function formatStatus(status: string) {
  return status === '现行有效' ? 'success' : 'danger'
}

// 格式化级别
function formatLevel(level: string) {
  const levelMap: Record<string, string> = {
    法律: 'danger',
    行政法规: 'warning',
    部门规章: 'primary',
    规范性文件: 'info',
  }
  return levelMap[level] || 'info'
}

// 用于防止重复请求的标记
let isSearchParamsChanging = false

// 监听搜索参数变化
watch(
  () => props.searchParams,
  () => {
    isSearchParamsChanging = true
    pagination.value.page = 1
    getList()
    // 延迟重置标记，确保refreshTrigger的watch不会在同一时刻触发
    nextTick(() => {
      setTimeout(() => {
        isSearchParamsChanging = false
      }, 100)
    })
  },
  { deep: true, immediate: true }
)

// 监听刷新触发器（仅在searchParams未变化时触发）
watch(
  () => props.refreshTrigger,
  (newVal, oldVal) => {
    // 只有当refreshTrigger变化且不是初始化且searchParams没有在变化时才执行
    if (newVal !== oldVal && oldVal !== undefined && !isSearchParamsChanging) {
      getList()
    }
  }
)

// 暴露方法给父组件
defineExpose({
  getList,
  pagination
})
</script>

<template>
  <div class="rounded-lg bg-white p-6 shadow-sm">
    <!-- 列表头部 -->
    <div class="mb-6 flex items-center justify-between border-b border-gray-200 pb-4">
      <div>
        <h3 class="text-lg text-gray-800 font-semibold">
          法规列表
        </h3>
        <div class="mt-1 text-sm text-gray-500">
          共 {{ pagination.total }} 条记录
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      class="w-full"
      :header-cell-style="{ backgroundColor: '#f8fafc', color: '#374151', fontWeight: '500' }"
      :row-style="{ transition: 'all 0.2s' }"
    >
      <el-table-column prop="title" label="法规标题" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div
            class="cursor-pointer text-blue-600 font-medium transition-colors duration-200 hover:text-blue-800"
            @click="viewDetail(row)"
          >
            {{ row.title }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="department" label="发布部门" width="120">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.department }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="province" label="发布省份" width="100">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.province }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="code" label="文件号" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="text-sm text-gray-600 font-mono">{{ row.code }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="pubDate" label="发布日期" width="110">
        <template #default="{ row }">
          <span class="text-sm text-gray-600">{{ row.pubDate }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="useDate" label="使用日期" width="110">
        <template #default="{ row }">
          <span class="text-sm text-gray-600">{{ row.useDate }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="isTime" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="formatStatus(row.isTime)" size="small" class="font-medium">
            {{ row.isTime }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="level" label="级别" width="120">
        <template #default="{ row }">
          <el-tag :type="formatLevel(row.level)" size="small" class="font-medium">
            {{ row.level }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="220" fixed="right">
        <template #default="{ row }">
          <div class="flex flex-wrap items-center gap-2">
            <el-button
              type="primary"
              size="small"
              class="mb-1"
              @click="viewDetail(row)"
            >
              查看详情
            </el-button>
            <el-button
              type="success"
              size="small"
              plain
              class="mb-1"
              @click="subscribe(row)"
            >
              订阅
            </el-button>
            <el-dropdown
              class="mb-1"
              @command="(command) => moreActions(row, command)"
            >
              <el-button size="small" plain class="flex items-center">
                更多
                <el-icon class="ml-1">
                  <svg-icon name="ep:arrow-down" />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit" class="flex items-center">
                    <svg-icon name="ep:edit" class="mr-2 text-blue-500" />
                    <span>编辑</span>
                  </el-dropdown-item>
                  <el-dropdown-item command="download" class="flex items-center">
                    <svg-icon name="ep:download" class="mr-2 text-green-500" />
                    <span>下载</span>
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided class="flex items-center">
                    <svg-icon name="ep:delete" class="mr-2 text-red-500" />
                    <span>删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="mt-6 flex justify-center border-t border-gray-100 pt-4">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination-custom"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 使用 Tailwind CSS 原子化样式，保留必要的深度选择器样式 */
:deep(.el-table__row) {
  &:hover {
    background-color: #f8fafc !important;
  }
}

:deep(.el-table__header-wrapper) {
  .el-table__header {
    th {
      border-bottom: 1px solid #e5e7eb;
    }
  }
}

:deep(.el-table__body-wrapper) {
  .el-table__row {
    td {
      border-bottom: 1px solid #f3f4f6;
    }
  }
}

/* 操作按钮间距优化 */
:deep(.el-button) {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* 下拉菜单样式优化 */
:deep(.el-dropdown-menu) {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  .el-dropdown-menu__item {
    padding: 8px 16px;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: #f8fafc;
      color: #374151;
    }
  }
}
</style>
