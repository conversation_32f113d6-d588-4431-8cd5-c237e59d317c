<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElLoading, ElMessage } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

const route = useRoute()
const router = useRouter()
const loading = ref(false)

// 法规详情数据
const regulationDetail = ref({
  id: 0,
  title: '',
  summary: '',
  detailUrl: '',
  content: '',
  province: '',
  department: '',
  code: '',
  pubDate: '',
  useDate: '',
  isTime: '',
  level: '',
  lawType: '',
  noUse: '',
  modifyAccording: '',
  changeReference: '',
  status: '',
  createdBy: '',
  createdAt: null,
  updatedBy: '',
  updatedAt: null,
  categoryName: '',
  attachments: [],
} as any)

// 获取法规详情
function getRegulationDetail() {
  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少法规ID参数')
    return
  }

  return new Promise<void>((resolve, reject) => {
    loading.value = true
    systemApi.lawsSystem({ id }, 'info')
      .then((response) => {
        // 根据API拦截器逻辑，response就是实际数据
        if (response && typeof response === 'object') {
          // 安全地赋值，只赋值存在的字段
          const fieldsToUpdate = [
            'id', 'title', 'summary', 'detailUrl', 'content', 'province',
            'department', 'code', 'pubDate', 'useDate', 'isTime', 'level',
            'lawType', 'noUse', 'modifyAccording', 'changeReference',
            'status', 'createdBy', 'createdAt', 'updatedBy', 'updatedAt',
            'categoryName', 'attachments',
          ]

          fieldsToUpdate.forEach((field) => {
            if (response[field] !== undefined && response[field] !== null) {
              regulationDetail.value[field] = response[field]
            }
          })
        }
        resolve()
      })
      .catch((error) => {
        ElMessage.error('获取法规详情失败')
        reject(error)
      })
      .finally(() => {
        loading.value = false
      })
  })
}

// 返回列表
function goBack() {
  router.push('/database/laws')
}

// 编辑法规
function editRegulation() {
  router.push({
    path: '/database/laws/addEdit',
    query: { id: regulationDetail.value.id, mode: 'edit' },
  })
}

// 立即转化
function transformRegulation() {
  if (!regulationDetail.value.id) {
    ElMessage.error('缺少法规ID')
    return
  }

  loading.value = true
  systemApi.transform({ regulationId: regulationDetail.value.id })
    .then(() => {
      ElMessage.success('转化成功')
    })
    .catch(() => {
      ElMessage.error('转化失败，请重试')
    })
    .finally(() => {
      loading.value = false
    })
}

// 格式化状态
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    DRAFT: '草稿',
    PUBLISHED: '已发布',
    ARCHIVED: '已归档',
  }
  return statusMap[status] || status
}

// 获取状态标签类型
function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    DRAFT: 'info',
    PUBLISHED: 'success',
    ARCHIVED: 'warning',
  }
  return typeMap[status] || 'primary'
}

onMounted(() => {
  getRegulationDetail()
})
</script>

<template>
  <div class="rounded-lg bg-white p-6 shadow-sm">
    <!-- 页面头部 -->
    <div class="mb-6 flex items-center justify-between border-b border-gray-200 pb-4">
      <div class="flex items-center space-x-4">
        <el-button class="flex items-center" @click="goBack">
          <svg-icon name="ep:arrow-left" class="mr-1" />
          返回列表
        </el-button>
        <h1 class="text-xl text-gray-800 font-semibold">
          法规详情
        </h1>
      </div>
      <div class="flex space-x-2">
        <el-button type="primary" @click="editRegulation">
          <svg-icon name="ep:edit" class="mr-1" />
          编辑
        </el-button>
        <el-button type="success" :loading="loading" @click="transformRegulation">
          <svg-icon name="ep:refresh" class="mr-1" />
          立即转化
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-20">
      <el-icon class="is-loading text-2xl text-blue-500">
        <svg-icon name="ep:loading" />
      </el-icon>
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 详情内容 -->
    <div v-else class="space-y-6">
      <!-- 基本信息 -->
      <div class="rounded-lg bg-gray-50 p-6">
        <h2 class="mb-4 flex items-center text-lg text-gray-800 font-semibold">
          <svg-icon name="ep:document" class="mr-2" />
          基本信息
        </h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div class="space-y-4">
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">法规标题</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.title || '-' }}
              </p>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">发布省份</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.province || '-' }}
              </p>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">发布部门</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.department || '-' }}
              </p>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">文件号</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.code || '-' }}
              </p>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">级别</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.level || '-' }}
              </p>
            </div>
          </div>
          <div class="space-y-4">
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">发布日期</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.pubDate || '-' }}
              </p>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">生效日期</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.useDate || '-' }}
              </p>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">状态</label>
              <div class="border rounded bg-white p-3">
                <el-tag :type="getStatusType(regulationDetail.status)">
                  {{ getStatusText(regulationDetail.status) }}
                </el-tag>
              </div>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">时效性</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.isTime || '-' }}
              </p>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">分类</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.categoryName || '-' }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 摘要信息 -->
      <div v-if="regulationDetail.summary" class="rounded-lg bg-gray-50 p-6">
        <h2 class="mb-4 flex items-center text-lg text-gray-800 font-semibold">
          <svg-icon name="ep:document-copy" class="mr-2" />
          法规摘要
        </h2>
        <div class="border rounded bg-white p-4">
          <p class="text-gray-900 leading-relaxed">
            {{ regulationDetail.summary }}
          </p>
        </div>
      </div>

      <!-- 详细内容 -->
      <div v-if="regulationDetail.content" class="rounded-lg bg-gray-50 p-6">
        <h2 class="mb-4 flex items-center text-lg text-gray-800 font-semibold">
          <svg-icon name="ep:document-checked" class="mr-2" />
          法规内容
        </h2>
        <div class="border rounded bg-white p-4">
          <div class="whitespace-pre-wrap text-gray-900 leading-relaxed" v-html="regulationDetail.content" />
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="rounded-lg bg-gray-50 p-6">
        <h2 class="mb-4 flex items-center text-lg text-gray-800 font-semibold">
          <svg-icon name="ep:info-filled" class="mr-2" />
          其他信息
        </h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div class="space-y-4">
            <div v-if="regulationDetail.lawType">
              <label class="mb-1 block text-sm text-gray-600 font-medium">法规类型</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.lawType }}
              </p>
            </div>
            <div v-if="regulationDetail.modifyAccording">
              <label class="mb-1 block text-sm text-gray-600 font-medium">修改依据</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.modifyAccording }}
              </p>
            </div>
            <div v-if="regulationDetail.detailUrl">
              <label class="mb-1 block text-sm text-gray-600 font-medium">详情链接</label>
              <div class="border rounded bg-white p-3">
                <a :href="regulationDetail.detailUrl" target="_blank" class="text-blue-600 underline hover:text-blue-800">
                  {{ regulationDetail.detailUrl }}
                </a>
              </div>
            </div>
          </div>
          <div class="space-y-4">
            <div v-if="regulationDetail.changeReference">
              <label class="mb-1 block text-sm text-gray-600 font-medium">变更参考</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.changeReference }}
              </p>
            </div>
            <div v-if="regulationDetail.noUse">
              <label class="mb-1 block text-sm text-gray-600 font-medium">废止信息</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.noUse }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 创建和更新信息 -->
      <div class="rounded-lg bg-gray-50 p-6">
        <h2 class="mb-4 flex items-center text-lg text-gray-800 font-semibold">
          <svg-icon name="ep:clock" class="mr-2" />
          记录信息
        </h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div class="space-y-4">
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">创建人</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.createdBy || '-' }}
              </p>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">创建时间</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.createdAt }}
              </p>
            </div>
          </div>
          <div class="space-y-4">
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">更新人</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.updatedBy || '-' }}
              </p>
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-600 font-medium">更新时间</label>
              <p class="border rounded bg-white p-3 text-gray-900">
                {{ regulationDetail.updatedAt }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.el-tag {
  @apply px-2 py-1 text-xs font-medium rounded;
}

/* 深度选择器用于修改Element Plus组件样式 */
:deep(.el-button) {
  @apply transition-all duration-200;
}

:deep(.el-button:hover) {
  @apply transform scale-105;
}
</style>
