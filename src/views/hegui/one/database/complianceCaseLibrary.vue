<script setup lang="ts">
import { onMounted, ref } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

const homeimg = new URL('@/assets/images/homeimg4.jpg', import.meta.url).href

// 义务库

const activeName = ref(1)

function handleClick(tab: TabsPaneContext, event: Event) {
  console.log(tab, event)
}
const tabsList: Array = ref([
  {
    id: 1,
    name: '全部制度 (127)',
  },
  {
    id: 2,
    name: '我创建的 (45)',
  },
  {
    id: 3,
    name: '待我审批 (12)',
  },
  {
    id: 4,
    name: '近期更新 (23)',
  },
])

// 左侧树
interface Tree {
  label: string
  children?: Tree[]
}

function handleNodeClick(data: Tree) {
  console.log(data)
}

const data: Tree[] = [
  {
    label: '发布机构',
    children: [
      {
        label: '银保监会',
      },
      {
        label: '商务部',
      },
      {
        label: '证监会',
      },
    ],
  },
  {
    label: '业务领域',
    children: [
      {
        label: '销售管理',
      },
      {
        label: '财务管理',
      },
      {
        label: '运营管理',
      },
    ],
  },
  {
    label: '制度类型',
    children: [
      {
        label: '规章制度',
      },
      {
        label: '操作规程',
      },
      {
        label: '工作指引',
      },
    ],
  },
]

const defaultProps = {
  children: 'children',
  label: 'label',
}
// 搜索参数
const searchParams = ref({
  caseName: '',
  areaType: '',
  caseSource: '',
  level: '',
  occurDateStart: '',
  occurDateEnd: '',
})

const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})

// 表格数据
const tableData = ref([])

// 风险等级映射
const riskLevelMap: any = {
  GENERAL: '一般风险',
  MAJOR: '重大风险',
  TYPICAL: '典型风险',
  SAFE: '安全',
}

// 案例来源映射
const caseSourceMap: any = {
  REGULATORY_PENALTIES: '监管处罚',
  JUDICIAL_PRECEDENTS: '司法判例',
  INDUSTRY: '行业案例',
  INTERNAL: '内部案例',
}

// 领域类型映射
const areaTypeMap: any = {
  INDUSTRY_REGULATION: '行业监管',
  CORPORATE_GOVERNANCE: '公司治理',
  BUSINESS_OPERATIONS: '业务运营',
  FINANCE_TAXATION: '财务税务',
}

const sliderValue: any = ref(40)

const yxfwImg = new URL('@/assets/images/icons/yxfw.png', import.meta.url).href
const gjyqImg = new URL('@/assets/images/icons/gjyq.png', import.meta.url).href
const dqjdImg = new URL('@/assets/images/icons/dqjd.png', import.meta.url).href
const router = useRouter()

// 获取案例列表
async function getList() {
  try {
    const params = {
      // ...searchParams.value,
      page: paging.value.page,
      limit: paging.value.limit,
    }
    const response = await systemApi.caseSystem(params, 'list')
    if (response && response.content) {
      tableData.value = response.content.map((item: any) => ({
        ...item,
        riskLevel: riskLevelMap[item.level] || item.level,
        category: areaTypeMap[item.areaType] || item.areaType,
        createTime: item.occurDate || item.createdAt?.seconds ? new Date(item.createdAt.seconds * 1000).toLocaleDateString() : '',
        viewCount: Math.floor(Math.random() * 200) + 50, // 模拟浏览量
        commentCount: Math.floor(Math.random() * 20) + 1, // 模拟评论数
        isStarred: false,
      }))
      paging.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取案例列表失败:', error)
    ElMessage.error('获取案例列表失败')
  }
}

// 分页变化
function pagChange(params: { page: number, limit: number }) {
  paging.value.page = params.page
  paging.value.limit = params.limit
  getList()
}

// 搜索
function handleSearch() {
  paging.value.page = 1
  getList()
}

// 重置搜索
function handleReset() {
  searchParams.value = {
    caseName: '',
    areaType: '',
    caseSource: '',
    level: '',
    occurDateStart: '',
    occurDateEnd: '',
  }
  paging.value.page = 1
  getList()
}

function goDetail(item: any) {
  router.push({
    name: '/database/cases/detail',
    query: { id: item.id },
  })
}

function goAddEdit(item: any) {
  router.push({
    name: '/database/cases/addEdit',
    query: item ? { id: item.id } : {},
  })
}

// 分享案例
function shareCase(item: any) {
  console.log('分享案例:', item)
  ElMessage.success('分享功能开发中')
}

// 切换收藏状态
function toggleStar(item: any) {
  item.isStarred = !item.isStarred
  ElMessage.success(item.isStarred ? '已收藏' : '已取消收藏')
}

// 获取树形数据
async function getTreeData() {
  try {
    const response = await systemApi.caseTree({})
    // 处理树形数据
    console.log('树形数据:', response)
  }
  catch (error) {
    console.error('获取树形数据失败:', error)
  }
}

// 页面初始化
onMounted(() => {
  getList()
  getTreeData()
})
</script>

<template>
  <div class="absolute-container">
    <PageHeader>
      <template #content>
        <div class="card flex p-16">
          <div>
            <el-button type="primary" @click="goAddEdit(null)">
              <svg-icon name="ep:plus" />
              <span class="ml-4">新增案例</span>
            </el-button>
          </div>
          <div class="ml-14">
            <el-button type="primary" plain>
              <svg-icon name="ep:download" />
              <span class="ml-4">批量导出</span>
            </el-button>
          </div>
          <div class="ml-32">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="案例名称">
                <el-input v-model="searchParams.caseName" placeholder="请输入案例名称" clearable />
              </el-form-item>
              <el-form-item label="领域类型">
                <el-select v-model="searchParams.areaType" placeholder="请选择领域类型" clearable>
                  <el-option label="行业监管" value="INDUSTRY_REGULATION" />
                  <el-option label="公司治理" value="CORPORATE_GOVERNANCE" />
                  <el-option label="业务运营" value="BUSINESS_OPERATIONS" />
                  <el-option label="财务税务" value="FINANCE_TAXATION" />
                </el-select>
              </el-form-item>
              <el-form-item label="案例来源">
                <el-select v-model="searchParams.caseSource" placeholder="请选择案例来源" clearable>
                  <el-option label="监管处罚" value="REGULATORY_PENALTIES" />
                  <el-option label="司法判例" value="JUDICIAL_PRECEDENTS" />
                  <el-option label="行业案例" value="INDUSTRY" />
                  <el-option label="内部案例" value="INTERNAL" />
                </el-select>
              </el-form-item>
              <el-form-item label="风险等级">
                <el-select v-model="searchParams.level" placeholder="请选择风险等级" clearable>
                  <el-option label="一般风险" value="GENERAL" />
                  <el-option label="重大风险" value="MAJOR" />
                  <el-option label="典型风险" value="TYPICAL" />
                  <el-option label="安全" value="SAFE" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">
                  <template #icon>
                    <svg-icon name="ep:search" />
                  </template>
                  搜索
                </el-button>
                <el-button @click="handleReset">
                  重置
                </el-button>
                <el-button>
                  <svg-icon name="ep:filter" />
                  <span class="ml-4">高级筛选</span>
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
    </PageHeader>
    <!-- 合规案例库 -->
    <PageMain style="background-color: transparent;">
      <div class="mt-20 pr-20">
        <LayoutContainer
          style="padding: 0;" :enable-left-side="true" :enable-right-side="true" :left-side-width="230"
          :right-side-width="476"
        >
          <template #leftSide>
            <div class="aic jcsb flex">
              <div class="f-16 f-500">
                筛选条件
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree :data="data" :props="defaultProps" :default-expand-all="true" @node-click="handleNodeClick" />
            </div>
          </template>
          <div>
            <!-- 列表视图 -->
            <el-table :data="tableData" style="width: 100%" @row-click="goDetail">
              <el-table-column prop="caseName" label="案例名称" min-width="200">
                <template #default="{ row }">
                  <div class="f-16 cursor-pointer fw-600 hover:text-blue-600">
                    {{ row.caseName }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="summary" label="案例描述" min-width="300">
                <template #default="{ row }">
                  <div class="f-14 line-clamp-2 c-[#666]">
                    {{ row.summary }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="riskLevel" label="风险等级" width="120">
                <template #default="{ row }">
                  <el-tag
                    :type="row.riskLevel === '重大风险' ? 'danger' : row.riskLevel === '典型风险' ? 'warning' : 'info'"
                    size="small"
                  >
                    {{ row.riskLevel }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="category" label="业务领域" width="120">
                <template #default="{ row }">
                  <el-tag type="primary" size="small" plain>
                    {{ row.category }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="createTime" label="创建时间" width="120">
                <template #default="{ row }">
                  <div class="f-14 c-[#666]">
                    {{ row.updatedAt }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="统计" width="120">
                <template #default="{ row }">
                  <div class="flex items-center c-[#999] space-x-4">
                    <div class="flex items-center">
                      <svg-icon name="ep:view" class="text-sm" />
                      <span class="f-12 ml-1">{{ row.viewCount }}</span>
                    </div>
                    <div class="flex items-center">
                      <svg-icon name="ep:comment" class="text-sm" />
                      <span class="f-12 ml-1">{{ row.commentCount }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <div class="flex items-center space-x-2">
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click.stop="goAddEdit(row)"
                    >
                      <svg-icon name="ep:edit" />
                    </el-button>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click.stop="shareCase(row)"
                    >
                      <svg-icon name="ep:share" />
                    </el-button>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click.stop="toggleStar(row)"
                    >
                      <svg-icon name="ep:star" />
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <page-compon :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;" @pag-change="pagChange" />
            <!-- <centerContent></centerContent> -->
          </div>
        </LayoutContainer>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  // .card {
  //   background: #fff;
  //   border-radius: 4px;
  //   box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  // }

  // .conBox {
  //   :deep(.main) {
  //     background-color: transparent;

  //     .main-container {
  //       padding: 0 !important;
  //     }

  //     .el-slider__button-wrapper {
  //       display: none;
  //     }

  //     .el-slider__runway.is-disabled .el-slider__bar {
  //       height: 8px;
  //       background: #4caf50;
  //       border-radius: 9999px;
  //     }
  //   }

  //   :deep(.flex-container) {
  //     padding-right: 40px !important;
  //   }
  // }
</style>
