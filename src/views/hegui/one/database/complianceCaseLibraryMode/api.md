---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 05-法律法规管理服务/合规案例管理

## POST 创建合规案例

POST /whiskerguardregulatoryservice/api/compliance/cases/create

描述：创建合规案例

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "caseName": "string",
  "caseCode": "string",
  "areaType": "INDUSTRY_REGULATION",
  "caseSource": "REGULATORY_PENALTIES",
  "level": "GENERAL",
  "summary": "string",
  "keyword": "string",
  "backgroundDesc": "string",
  "occurDate": "string",
  "mediaUrl": "string",
  "violation": "string",
  "riskAnalysis": "string",
  "preventionControl": "string",
  "learningPoints": "string",
  "trainingUrl": "string",
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|object| 否 |none|
|» id|body|integer(int64)| 否 |none|
|» tenantId|body|integer(int64)| 否 |租户ID，标识不同公司的数据隔离|
|» caseName|body|string| 是 |案例名称|
|» caseCode|body|string| 是 |案例编号，用于唯一标识|
|» areaType|body|string| 否 |领域类型：行业监管、公司治理、业务运营、财务税务|
|» caseSource|body|string| 否 |案例来源：监管处罚、司法判例、行业案例、内部案例|
|» level|body|string| 否 |风险等级：一般风险、重大风险、典型风险、安全|
|» summary|body|string| 否 |概述|
|» keyword|body|string| 否 |关键词，以逗号分隔|
|» backgroundDesc|body|string| 否 |背景描述|
|» occurDate|body|string| 否 |发生时间|
|» mediaUrl|body|string| 否 |多媒体资料|
|» violation|body|string| 否 |违规点分析|
|» riskAnalysis|body|string| 否 |风险分析|
|» preventionControl|body|string| 否 |防控建议|
|» learningPoints|body|string| 否 |学习要点|
|» trainingUrl|body|string| 否 |培训资料|
|» createdBy|body|string| 是 |创建者账号或姓名|
|» createdAt|body|[Instant](#schemainstant)| 否 |上传时间，默认当前时间|
|»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|» updatedBy|body|string| 否 |最后修改者|
|» updatedAt|body|[Instant](#schemainstant)| 否 |上传时间，默认当前时间|
|»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|» isDeleted|body|integer| 否 |是否删除：0 表示正常 1 表示已删除|

#### 详细说明

**»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

**»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

#### 枚举值

|属性|值|
|---|---|
|» areaType|INDUSTRY_REGULATION|
|» areaType|CORPORATE_GOVERNANCE|
|» areaType|BUSINESS_OPERATIONS|
|» areaType|FINANCE_TAXATION|
|» caseSource|REGULATORY_PENALTIES|
|» caseSource|JUDICIAL_PRECEDENTS|
|» caseSource|INDUSTRY|
|» caseSource|INTERNAL|
|» level|GENERAL|
|» level|MAJOR|
|» level|SAFE|
|» level|TYPICAL|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "caseName": "",
  "caseCode": "",
  "areaType": "",
  "caseSource": "",
  "level": "",
  "summary": "",
  "keyword": "",
  "backgroundDesc": "",
  "occurDate": "",
  "mediaUrl": "",
  "violation": "",
  "riskAnalysis": "",
  "preventionControl": "",
  "learningPoints": "",
  "trainingUrl": "",
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityComplianceCaseDTO](#schemaresponseentitycompliancecasedto)|

## POST 部分更新合规案例

POST /whiskerguardregulatoryservice/api/compliance/cases/update/{id}

描述：部分更新合规案例

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "caseName": "string",
  "caseCode": "string",
  "areaType": "INDUSTRY_REGULATION",
  "caseSource": "REGULATORY_PENALTIES",
  "level": "GENERAL",
  "summary": "string",
  "keyword": "string",
  "backgroundDesc": "string",
  "occurDate": "string",
  "mediaUrl": "string",
  "violation": "string",
  "riskAnalysis": "string",
  "preventionControl": "string",
  "learningPoints": "string",
  "trainingUrl": "string",
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "status": "DRAFT",
  "attachments": [
    {}
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要更新的案例ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[ComplianceCaseDTO](#schemacompliancecasedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "caseName": "",
  "caseCode": "",
  "areaType": "",
  "caseSource": "",
  "level": "",
  "summary": "",
  "keyword": "",
  "backgroundDesc": "",
  "occurDate": "",
  "mediaUrl": "",
  "violation": "",
  "riskAnalysis": "",
  "preventionControl": "",
  "learningPoints": "",
  "trainingUrl": "",
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityComplianceCaseDTO](#schemaresponseentitycompliancecasedto)|

## POST 获取所有合规案例（分页）

POST /whiskerguardregulatoryservice/api/compliance/cases/page

描述：获取所有合规案例（分页）

> Body 请求参数

```json
{
  "tenantId": 0,
  "caseName": "string",
  "caseCode": "string",
  "areaType": "INDUSTRY_REGULATION",
  "caseSource": "REGULATORY_PENALTIES",
  "level": "GENERAL",
  "occurDateStart": "string",
  "occurDateEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |none|
|size|query|integer| 否 |none|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[ComplianceCasesReq](#schemacompliancecasesreq)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "caseName": "",
      "caseCode": "",
      "areaType": "",
      "caseSource": "",
      "level": "",
      "summary": "",
      "keyword": "",
      "backgroundDesc": "",
      "occurDate": "",
      "mediaUrl": "",
      "violation": "",
      "riskAnalysis": "",
      "preventionControl": "",
      "learningPoints": "",
      "trainingUrl": "",
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": 0
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "total": 0,
  "empty": false,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "totalPages": 0,
  "totalElements": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageComplianceCaseDTO](#schemaresponseentitypagecompliancecasedto)|

## GET 根据ID获取合规案例

GET /whiskerguardregulatoryservice/api/compliance/cases/{id}

描述：根据ID获取合规案例。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |案例ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "caseName": "",
  "caseCode": "",
  "areaType": "",
  "caseSource": "",
  "level": "",
  "summary": "",
  "keyword": "",
  "backgroundDesc": "",
  "occurDate": "",
  "mediaUrl": "",
  "violation": "",
  "riskAnalysis": "",
  "preventionControl": "",
  "learningPoints": "",
  "trainingUrl": "",
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityComplianceCaseDTO](#schemaresponseentitycompliancecasedto)|

## DELETE 删除合规案例

DELETE /whiskerguardregulatoryservice/api/compliance/cases/{id}

描述：删除合规案例。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要删除的案例ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_ComplianceCasesAttachmentDTO">ComplianceCasesAttachmentDTO</h2>

<a id="schemacompliancecasesattachmentdto"></a>
<a id="schema_ComplianceCasesAttachmentDTO"></a>
<a id="tocScompliancecasesattachmentdto"></a>
<a id="tocscompliancecasesattachmentdto"></a>

```json
{
  "id": 0,
  "caseId": 0,
  "fileName": "string",
  "filePath": "string",
  "fileType": "string",
  "fileSize": "string",
  "fileDesc": "string",
  "uploadedBy": "string",
  "uploadedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||附件ID|
|caseId|integer(int64)|true|none||关联合规案例ID|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|fileType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|fileDesc|string|false|none||附件描述|
|uploadedBy|string|true|none||上传者|
|uploadedAt|[Instant](#schemainstant)|false|none||上传时间，默认当前时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常，1 表示已删除|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ResponseEntityComplianceCaseDTO">ResponseEntityComplianceCaseDTO</h2>

<a id="schemaresponseentitycompliancecasedto"></a>
<a id="schema_ResponseEntityComplianceCaseDTO"></a>
<a id="tocSresponseentitycompliancecasedto"></a>
<a id="tocsresponseentitycompliancecasedto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "caseName": "string",
  "caseCode": "string",
  "areaType": "INDUSTRY_REGULATION",
  "caseSource": "REGULATORY_PENALTIES",
  "level": "GENERAL",
  "summary": "string",
  "keyword": "string",
  "backgroundDesc": "string",
  "occurDate": "string",
  "mediaUrl": "string",
  "violation": "string",
  "riskAnalysis": "string",
  "preventionControl": "string",
  "learningPoints": "string",
  "trainingUrl": "string",
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "status": "DRAFT",
  "attachments": [
    {}
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|false|none||租户ID，标识不同公司的数据隔离|
|caseName|string|true|none||案例名称|
|caseCode|string|true|none||案例编号，用于唯一标识|
|areaType|string|false|none||领域类型：行业监管、公司治理、业务运营、财务税务|
|caseSource|string|false|none||案例来源：监管处罚、司法判例、行业案例、内部案例|
|level|string|false|none||风险等级：一般风险、重大风险、典型风险、安全|
|summary|string|false|none||概述|
|keyword|string|false|none||关键词，以逗号分隔|
|backgroundDesc|string|false|none||背景描述|
|occurDate|string|false|none||发生时间|
|mediaUrl|string|false|none||多媒体资料|
|violation|string|false|none||违规点分析|
|riskAnalysis|string|false|none||风险分析|
|preventionControl|string|false|none||防控建议|
|learningPoints|string|false|none||学习要点|
|trainingUrl|string|false|none||培训资料|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|object|false|none||上传时间，默认当前时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|object|false|none||上传时间，默认当前时间|
|status|string|false|none||状态：草稿、已发布、已下架|
|attachments|[object]|false|none||附件列表|

#### 枚举值

|属性|值|
|---|---|
|areaType|INDUSTRY_REGULATION|
|areaType|CORPORATE_GOVERNANCE|
|areaType|BUSINESS_OPERATIONS|
|areaType|FINANCE_TAXATION|
|caseSource|REGULATORY_PENALTIES|
|caseSource|JUDICIAL_PRECEDENTS|
|caseSource|INDUSTRY|
|caseSource|INTERNAL|
|level|GENERAL|
|level|MAJOR|
|level|SAFE|
|level|TYPICAL|
|status|DRAFT|
|status|PUBLISHED|
|status|EFFECTIVE|
|status|EXPIRED|
|status|REVIEWING|

<h2 id="tocS_ComplianceCaseDTO">ComplianceCaseDTO</h2>

<a id="schemacompliancecasedto"></a>
<a id="schema_ComplianceCaseDTO"></a>
<a id="tocScompliancecasedto"></a>
<a id="tocscompliancecasedto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "caseName": "string",
  "caseCode": "string",
  "areaType": "INDUSTRY_REGULATION",
  "caseSource": "REGULATORY_PENALTIES",
  "level": "GENERAL",
  "summary": "string",
  "keyword": "string",
  "backgroundDesc": "string",
  "occurDate": "string",
  "mediaUrl": "string",
  "violation": "string",
  "riskAnalysis": "string",
  "preventionControl": "string",
  "learningPoints": "string",
  "trainingUrl": "string",
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "status": "DRAFT",
  "attachments": [
    {}
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|false|none||租户ID，标识不同公司的数据隔离|
|caseName|string|true|none||案例名称|
|caseCode|string|true|none||案例编号，用于唯一标识|
|areaType|string|false|none||领域类型：行业监管、公司治理、业务运营、财务税务|
|caseSource|string|false|none||案例来源：监管处罚、司法判例、行业案例、内部案例|
|level|string|false|none||风险等级：一般风险、重大风险、典型风险、安全|
|summary|string|false|none||概述|
|keyword|string|false|none||关键词，以逗号分隔|
|backgroundDesc|string|false|none||背景描述|
|occurDate|string|false|none||发生时间|
|mediaUrl|string|false|none||多媒体资料|
|violation|string|false|none||违规点分析|
|riskAnalysis|string|false|none||风险分析|
|preventionControl|string|false|none||防控建议|
|learningPoints|string|false|none||学习要点|
|trainingUrl|string|false|none||培训资料|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|object|false|none||上传时间，默认当前时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|object|false|none||上传时间，默认当前时间|
|status|string|false|none||状态：草稿、已发布、已下架|
|attachments|[object]|false|none||附件列表|

#### 枚举值

|属性|值|
|---|---|
|areaType|INDUSTRY_REGULATION|
|areaType|CORPORATE_GOVERNANCE|
|areaType|BUSINESS_OPERATIONS|
|areaType|FINANCE_TAXATION|
|caseSource|REGULATORY_PENALTIES|
|caseSource|JUDICIAL_PRECEDENTS|
|caseSource|INDUSTRY|
|caseSource|INTERNAL|
|level|GENERAL|
|level|MAJOR|
|level|SAFE|
|level|TYPICAL|
|status|DRAFT|
|status|PUBLISHED|
|status|EFFECTIVE|
|status|EXPIRED|
|status|REVIEWING|

<h2 id="tocS_ResponseEntityPageComplianceCaseDTO">ResponseEntityPageComplianceCaseDTO</h2>

<a id="schemaresponseentitypagecompliancecasedto"></a>
<a id="schema_ResponseEntityPageComplianceCaseDTO"></a>
<a id="tocSresponseentitypagecompliancecasedto"></a>
<a id="tocsresponseentitypagecompliancecasedto"></a>

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "caseName": "string",
      "caseCode": "string",
      "areaType": "INDUSTRY_REGULATION",
      "caseSource": "REGULATORY_PENALTIES",
      "level": "GENERAL",
      "summary": "string",
      "keyword": "string",
      "backgroundDesc": "string",
      "occurDate": "string",
      "mediaUrl": "string",
      "violation": "string",
      "riskAnalysis": "string",
      "preventionControl": "string",
      "learningPoints": "string",
      "trainingUrl": "string",
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "status": "DRAFT",
      "attachments": [
        {}
      ]
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "total": 0,
  "empty": true,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "totalPages": 0,
  "totalElements": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|content|[[ComplianceCaseDTO](#schemacompliancecasedto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|total|integer(int64)|false|none||none|
|empty|boolean|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE",
  "ascending": true,
  "descending": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|
|ascending|boolean|false|none||none|
|descending|boolean|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

<h2 id="tocS_ComplianceCasesReq">ComplianceCasesReq</h2>

<a id="schemacompliancecasesreq"></a>
<a id="schema_ComplianceCasesReq"></a>
<a id="tocScompliancecasesreq"></a>
<a id="tocscompliancecasesreq"></a>

```json
{
  "tenantId": 0,
  "caseName": "string",
  "caseCode": "string",
  "areaType": "INDUSTRY_REGULATION",
  "caseSource": "REGULATORY_PENALTIES",
  "level": "GENERAL",
  "occurDateStart": "string",
  "occurDateEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|integer(int64)|false|none||租户ID，标识不同公司的数据隔离|
|caseName|string|false|none||案例名称|
|caseCode|string|false|none||案例编号，用于唯一标识|
|areaType|string|false|none||领域类型：行业监管、公司治理、业务运营、财务税务|
|caseSource|string|false|none||案例来源：监管处罚、司法判例、行业案例、内部案例|
|level|string|false|none||风险等级：一般风险、重大风险、典型风险、安全|
|occurDateStart|string|false|none||发生时间开始|
|occurDateEnd|string|false|none||发生时间结束|

#### 枚举值

|属性|值|
|---|---|
|areaType|INDUSTRY_REGULATION|
|areaType|CORPORATE_GOVERNANCE|
|areaType|BUSINESS_OPERATIONS|
|areaType|FINANCE_TAXATION|
|caseSource|REGULATORY_PENALTIES|
|caseSource|JUDICIAL_PRECEDENTS|
|caseSource|INDUSTRY|
|caseSource|INTERNAL|
|level|GENERAL|
|level|MAJOR|
|level|SAFE|
|level|TYPICAL|

