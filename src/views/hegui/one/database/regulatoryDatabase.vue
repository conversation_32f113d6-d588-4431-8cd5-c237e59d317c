<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import type { TabsPaneContext } from 'element-plus'
import { ElMessage } from 'element-plus'

import centerContent from './regulatoryDatabaseMode/center.vue'
import rightContent from './regulatoryDatabaseMode/right.vue'

const router = useRouter()
const homeimg = new URL('@/assets/images/homeimg4.jpg', import.meta.url).href

// 法规库
const activeName = ref(1)

// 搜索参数
const searchParams = ref({
  title: '',
  group_id: null,
  status: null,
})

// 刷新触发器
const refreshTrigger = ref(0)

// 加载状态和总数
const loading = ref(false)
const total = ref(0)

function handleClick(tab: TabsPaneContext, event: Event) {
  console.log(tab, event)
}

const tabsList: Array = ref([
  {
    id: 1,
    name: '全部制度 (127)',
  },
  {
    id: 2,
    name: '我创建的 (45)',
  },
  {
    id: 3,
    name: '待我审批 (12)',
  },
  {
    id: 4,
    name: '近期更新 (23)',
  },
])

// 左侧树
interface Tree {
  label: string
  children?: Tree[]
}

function handleNodeClick(data: Tree) {
  console.log(data)
}

const data: Tree[] = [
  {
    label: '发布机构',
    children: [
      {
        label: '银保监会',
      },
      {
        label: '商务部',
      },
      {
        label: '证监会',
      },
    ],
  },
  {
    label: '业务领域',
    children: [
      {
        label: '销售管理',
      },
      {
        label: '财务管理',
      },
      {
        label: '运营管理',
      },
    ],
  },
  {
    label: '制度类型',
    children: [
      {
        label: '规章制度',
      },
      {
        label: '操作规程',
      },
      {
        label: '工作指引',
      },
    ],
  },
]

const defaultProps = {
  children: 'children',
  label: 'label',
}

// 组织架构数据（示例）
const groupList = ref([
  {
    value: '1',
    label: '银保监会',
    children: [
      { value: '1-1', label: '银行监管部' },
      { value: '1-2', label: '保险监管部' },
    ],
  },
  {
    value: '2',
    label: '商务部',
    children: [
      { value: '2-1', label: '市场建设司' },
      { value: '2-2', label: '外贸司' },
    ],
  },
  {
    value: '3',
    label: '证监会',
    children: [
      { value: '3-1', label: '市场监管部' },
      { value: '3-2', label: '发行监管部' },
    ],
  },
])

const props1 = {
  expandTrigger: 'hover' as const,
  value: 'value',
  label: 'label',
  children: 'children',
}

// 搜索功能
function getList() {
  // 点击查询按钮时，通过refreshTrigger触发搜索
  // 这样即使searchParams没有变化也能重新搜索
  refreshTrigger.value++
}

// 重置搜索
function resetSearch() {
  // 重置搜索参数，子组件会通过watch自动响应
  searchParams.value = {
    title: '',
    group_id: null,
    status: null,
  }
  // 不需要再增加refreshTrigger，因为searchParams的变化已经会触发重新加载
}

// 新增法规
function addRegulation() {
  router.push({
    path: '/database/laws/addEdit',
  })
}

// 批量导出
function batchExport() {
  ElMessage.info('批量导出功能开发中...')
}

// 高级筛选
function advancedFilter() {
  ElMessage.info('高级筛选功能开发中...')
}

// 组织架构变化
function changegroup(value: any) {
  console.log('选择的组织架构:', value)
}

// 处理子组件事件
function handleLoadingUpdate(loadingState: boolean) {
  loading.value = loadingState
}

function handleTotalUpdate(totalCount: number) {
  total.value = totalCount
}

function handleDataLoaded(data: any[]) {
  console.log('数据加载完成:', data)
}
</script>

<template>
  <div class="absolute-container">
    <PageHeader>
      <template #content>
        <div class="card flex p-16">
          <div>
            <el-button type="primary" @click="addRegulation">
              <svg-icon name="ep:plus" />
              <span class="ml-4">新增法规</span>
            </el-button>
          </div>
          <div class="ml-14">
            <el-button type="primary" plain @click="batchExport">
              <svg-icon name="ep:download" />
              <span class="ml-4">批量导出</span>
            </el-button>
          </div>
          <div class="ml-32">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="名称:">
                <el-input v-model="searchParams.title" clearable placeholder="搜索法规名称..." @clear="searchParams.title = ''" />
              </el-form-item>
              <el-form-item label="发布机构:">
                <el-cascader
                  v-model="searchParams.group_id" :props="props1" clearable class="w-full" :options="groupList"
                  @change="changegroup" @clear="searchParams.group_id = null"
                />
              </el-form-item>
              <el-form-item label="全部状态" style="width: 200px;">
                <el-select v-model="searchParams.status" clearable placeholder="请选择状态" @clear="searchParams.status = null">
                  <el-option label="现行有效" value="现行有效" />
                  <el-option label="失效" value="失效" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" :loading="loading" @click="getList">
                  <template #icon>
                    <svg-icon name="ep:search" />
                  </template>
                  查询
                </el-button>
                <el-button @click="resetSearch">
                  重置
                </el-button>
                <el-button @click="advancedFilter">
                  <svg-icon name="ep:filter" />
                  <span class="ml-4">高级筛选</span>
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
    </PageHeader>
    <PageMain style="background-color: transparent;">
      <div class="conBox mt-20 pr-20">
        <!-- <template #leftSide>
            <div class="aic jcsb flex">
              <div class="f-16 f-500">
                筛选条件
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree :data="data" :props="defaultProps" :default-expand-all="true" @node-click="handleNodeClick" />
            </div>
          </template> -->
        <!-- <template #rightSide>
            <rightContent />
          </template> -->
        <div>
          <centerContent
            :search-params="searchParams"
            :refresh-trigger="refreshTrigger"
            @update:loading="handleLoadingUpdate"
            @update:total="handleTotalUpdate"
            @data-loaded="handleDataLoaded"
          />
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }

  .conBox {
    :deep(.main) {
      background-color: transparent;

      .main-container {
        padding: 0 !important;
      }

      .el-slider__button-wrapper {
        display: none;
      }

      .el-slider__runway.is-disabled .el-slider__bar {
        height: 8px;
        background: #4caf50;
        border-radius: 9999px;
      }
    }

    :deep(.flex-container) {
      padding-right: 40px !important;
    }
  }
</style>
