---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 05-法律法规管理服务/企业内部法规制度审核内容

## GET 根据审核记录ID获取审核内容信息

GET /whiskerguardregulatoryservice/api/enterprise/regulation/audit/contents/review/{reviewId}

描述：根据指定审核记录ID获取企业内部法规制度审核内容信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|reviewId|path|integer| 是 |审核记录的ID，不能为空|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "regulationId": 0,
  "content": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "updatedBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityEnterpriseRegulationAuditContentDTO](#schemaresponseentityenterpriseregulationauditcontentdto)|

# 数据模型

<h2 id="tocS_ResponseEntityEnterpriseRegulationAuditContentDTO">ResponseEntityEnterpriseRegulationAuditContentDTO</h2>

<a id="schemaresponseentityenterpriseregulationauditcontentdto"></a>
<a id="schema_ResponseEntityEnterpriseRegulationAuditContentDTO"></a>
<a id="tocSresponseentityenterpriseregulationauditcontentdto"></a>
<a id="tocsresponseentityenterpriseregulationauditcontentdto"></a>

```json
{
  "id": 0,
  "regulationId": 0,
  "content": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "updatedBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|regulationId|integer(int64)|true|none||内部法规ID|
|content|string|false|none||报告内容|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|updatedBy|string|false|none||最后修改者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：false 表示正常 true 表示已删除|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

