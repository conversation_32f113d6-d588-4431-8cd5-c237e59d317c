<script setup lang="ts">
import { ref } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import systemApi from '@/api/complianceApi/one/systemManagement.ts'

console.log('制度api', systemApi)

const router = useRouter()
const activeName = ref(1)

function handleClick(tab: TabsPaneContext, event: Event) {
  console.log(tab, event)
}
const tabsList: Array = ref([
  {
    id: 1,
    name: '全部制度 (127)',
  },
  {
    id: 2,
    name: '我创建的 (45)',
  },
  {
    id: 3,
    name: '待我审批 (12)',
  },
  {
    id: 4,
    name: '近期更新 (23)',
  },
])

// 左侧树
interface Tree {
  label: string
  children?: Tree[]
}

function handleNodeClick(data: Tree) {
  console.log(data)
}

const data: Tree[] = [
  // {
  //   label: '发布机构',
  //   children: [
  //     {
  //       label: '银保监会',
  //     },
  //     {
  //       label: '商务部',
  //     },
  //     {
  //       label: '证监会',
  //     },
  //   ],
  // },
  {
    id: 1,
    categoryName: '发布机构',
    parentId: null,
    description: '发布机构',
    applyRange: null,
    rules: null,
    status: null,
    createdBy: null,
    createdAt: '2025-04-22T18:20:50Z',
    updatedAt: '2025-04-22T18:20:50Z',
    children: [
      {
        id: 2,
        categoryName: '商务部',
        parentId: 1,
        description: '商务部',
        applyRange: null,
        rules: null,
        status: null,
        createdBy: null,
        createdAt: '2025-04-22T18:21:02Z',
        updatedAt: '2025-04-22T18:21:02Z',
        children: [],
        lawsCount: null,
      },
    ],
    lawsCount: null,
  },
  {
    id: 3,
    categoryName: '行业分类',
    parentId: null,
    description: '行业分类',
    applyRange: null,
    rules: null,
    status: null,
    createdBy: null,
    createdAt: '2025-04-22T18:21:13Z',
    updatedAt: '2025-04-22T18:21:13Z',
    children: [
      {
        id: 4,
        categoryName: '金融业务',
        parentId: 3,
        description: '金融业务',
        applyRange: null,
        rules: null,
        status: null,
        createdBy: null,
        createdAt: '2025-04-22T18:21:25Z',
        updatedAt: '2025-04-22T18:21:25Z',
        children: [],
        lawsCount: null,
      },
    ],
    lawsCount: null,
  },
  {
    id: 6,
    categoryName: '新分类1',
    parentId: null,
    description: '新分类1',
    applyRange: null,
    rules: null,
    status: null,
    createdBy: null,
    createdAt: '2025-04-29T07:24:38Z',
    updatedAt: '2025-04-29T07:25:07Z',
    children: [],
    lawsCount: null,
  },
  {
    id: 7,
    categoryName: '测试法律法规分类',
    parentId: null,
    description: '用于测试的法律法规分类',
    applyRange: null,
    rules: null,
    status: null,
    createdBy: null,
    createdAt: '2025-04-29T12:36:28Z',
    updatedAt: '2025-04-29T12:36:28Z',
    children: [],
    lawsCount: null,
  },
]

const defaultProps = {
  children: 'children',
  label: 'categoryName',
}
const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})
const dataList: any = ref([{
  regulationName: '国家卫生健康委关于发布推荐性卫生行业标准《输血医学术语》的通告',
  taskName: '第一个转化任务',
  enterpriseRegulation: '卫生行业标准',
  createdBy: 'xuk',
  status: 'PROGRESS',
  level: 'MIDDLE',
  startDateStart: '2025-04-29',
  startDateEnd: '2025-05-20',
  finishDateStart: '2025-04-29',
  finishDateEnd: '2025-05-20',
}])

const sliderValue: any = ref(40)

const yxfwImg = new URL('@/assets/images/icons/yxfw.png', import.meta.url).href
const gjyqImg = new URL('@/assets/images/icons/gjyq.png', import.meta.url).href
const dqjdImg = new URL('@/assets/images/icons/dqjd.png', import.meta.url).href

onMounted(() => {
  getList()
  getClassification()
})

function getList() {
  systemApi.regulatoryConversion({
    page: Number(paging.value.page) - 1,
    limit: Number(paging.value.limit),
  }).then((res: any) => {
    console.log(res, 'regulatoryConversion')

    paging.value.total = res.totalElements ? res.totalElements : 0
    dataList.value = res.content ? res.content : []
  })
}
const classificationList: any = ref([])
function getClassification() {
  systemApi.categories({}, 'tree').then((res: any) => {
    classificationList.value = res
    console.log(res, '分类树结构')
  })
}

// 新增转化任务
function goEdit(id: any) {
  if (id) {
    // 编辑模式，跳转到编辑页面
    router.push({
      name: '/one/regulatoryConversion/addEdit',
      query: { id },
    })
  }
  else {
    // 新增模式，跳转到新增页面
    router.push({
      name: '/one/regulatoryConversion/addEdit',
    })
  }
}

// 查看详情
function goDetail(id: any) {
  router.push({
    name: '/one/regulatoryConversion/detail',
    query: { id },
  })
}

// 处理表格选择变化
function handleSelectionChange(selection: any) {
  console.log('选择变化:', selection)
}

// 删除转化任务
function deleteTask(id: any) {
  ElMessageBox.confirm(
    '确定要删除这个转化任务吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    // 调用删除接口
    systemApi.regulatoryConversion({ id }, 'delete').then(() => {
      ElMessage.success('删除成功')
      getList() // 重新加载列表
    }).catch(() => {
      ElMessage.error('删除失败')
    })
  }).catch(() => {
    // 用户取消删除
  })
}

// 审核转化任务
function auditTask(id: any, isAudited: boolean) {
  const actionText = isAudited ? '通过' : '不通过'
  ElMessageBox.confirm(
    `确定要${actionText}这个转化任务吗？`,
    '审核确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    // 显示loading
    const loading = ElLoading.service({
      lock: true,
      text: `正在${actionText}审核...`,
      background: 'rgba(0, 0, 0, 0.7)',
    })

    // 调用审核接口
    systemApi.auditRegulatoryConversion({ id, isAudited }).then(() => {
      ElMessage.success(`审核${actionText}成功`)
      getList() // 重新加载列表
    }).catch(() => {
      ElMessage.error(`审核${actionText}失败`)
    }).finally(() => {
      // 隐藏loading
      loading.close()
    })
  }).catch(() => {
    // 用户取消审核
  })
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              法规转化
            </h1>
          </div>
          <!-- <div class="flex space-x-3">
            <el-button v-auth="['/regulatoryConversion/add']" type="primary" @click="goEdit(null)">
              <svg-icon name="ep:plus" />
              <span class="ml-4">新增转化任务</span>
            </el-button>
          </div> -->
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div class="card flex p-16">
        <div class="ml-32">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="名称:">
              <el-input v-model="paging.title" clearable placeholder="搜索法规名称..." @clear="paging.title = null" />
            </el-form-item>
            <el-form-item label="发布机构:">
              <el-cascader
                v-model="paging.group_id" :props="props1" clearable class="w-full" :options="groupList"
                @change="changegroup" @clear="paging.group_id = null"
              />
            </el-form-item>
            <el-form-item label="全部状态" style="width: 200px;">
              <el-select v-model="paging.status" clearable placeholder="请选择状态" @clear="paging.status = null">
                <el-option label="正常" value="1" />
                <el-option label="禁用" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">
                <template #icon>
                  <svg-icon name="ep:search" />
                </template>
                查询
              </el-button>
              <el-button
                @click="paging = {
                  page: 1,
                  limit: 10,
                }, getList()"
              >
                重置
              </el-button>
              <!-- <el-button>
                <svg-icon name="ep:filter" />
                <span class="ml-4">高级筛选</span>
              </el-button> -->
              <!-- <el-button type="danger" @click="removealldata()">
                <template #icon>
                  <svg-icon name="ep:search" />
                </template>
                批量删除
              </el-button> -->
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!--      <div>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="i,j in tabsList" :key="j" :label="i.name" :name="i.id"></el-tab-pane>
        </el-tabs>
      </div> -->
      <div class="conBox mt-20">
        <!-- <LayoutContainer style="padding: 0;" :enable-left-side="true" :enable-right-side="true" :left-side-width="280"> -->
        <!-- <template #leftSide>
            <div class="aic jcsb flex">
              <div class="f-16 f-500">
                法规分类
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree
                :data="classificationList" :props="defaultProps" :default-expand-all="true"
                @node-click="handleNodeClick"
              />
            </div>
          </template> -->
        <div>
          <el-card class="">
            <!-- <image-preview :src="i.image" :width="60" :height="60" /> -->
            <el-table :data="dataList" min-height="200" border @selection-change="handleSelectionChange">
              <el-table-column prop="taskName" label="任务名称" min-width="150" align="center" />
              <!-- <el-table-column prop="id" label="发布机构" width="100" align="center" /> -->
              <!-- <el-table-column prop="id" label="制度类型" width="100" align="center" /> -->
              <el-table-column prop="startDate" label="发布日期" width="180" align="center" />
              <el-table-column prop="finishDate" label="完成日期" width="180" align="center">
                <template #default="{ row: i }">
                  <!-- {{ i.send_time>0?dayjs(i.send_time * 1000).format('YYYY-MM-DD'):'' }} -->
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row: i }">
                  <el-tag v-if="i.status === 'PROGRESS'" type="primary">
                    进行中
                  </el-tag>
                  <el-tag v-else-if="i.status === 'FINISHED'" type="success">
                    已完成
                  </el-tag>
                  <el-tag v-else-if="i.status === 'OVERTIME'" type="warning">
                    已超时
                  </el-tag>
                  <el-tag v-else-if="i.status === 'CANCEL'" type="info">
                    已取消
                  </el-tag>
                  <el-tag v-else>
                    未知
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180">
                <template #default="{ row }">
                  <div class="flex">
                    <el-button type="text" size="small" @click="goDetail(row.id)">
                      详情
                    </el-button>
                    <el-dropdown>
                      <el-button size="small" type="text">
                        <span>更多</span>
                        <svg-icon name="ep:arrow-down" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="deleteTask(row.id)">
                            删除
                          </el-dropdown-item>
                          <el-dropdown-item divided @click="auditTask(row.id, true)">
                            通过
                          </el-dropdown-item>
                          <el-dropdown-item @click="auditTask(row.id, false)">
                            不通过
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <page-compon
              :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
              @pag-change="Object.assign(paging, $event), getList()"
            />
          </el-card>
        </div>
        <!-- </LayoutContainer> -->
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/toolsCss";

.card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
}

.conBox {
  :deep(.main) {
    background-color: transparent;

    .main-container {
      padding: 0 !important;
    }

    .el-slider__button-wrapper {
      display: none;
    }

    .el-slider__runway.is-disabled .el-slider__bar {
      height: 8px;
      background: #4caf50;
      border-radius: 9999px;
    }
  }
}
</style>
