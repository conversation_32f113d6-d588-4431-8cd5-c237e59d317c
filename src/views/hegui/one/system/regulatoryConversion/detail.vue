<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement.ts'

const route = useRoute()
const router = useRouter()

const transformDetail = ref({
  taskName: '',
  regulationName: '',
  enterpriseRegulation: '',
  level: '',
  startDate: '',
  finishDate: '',
  description: '',
  status: '',
  createdBy: '',
  createdAt: '',
  updatedAt: '',
})

const loading = ref(false)

onMounted(() => {
  const id = route.query.id || route.params.id
  if (id) {
    getTransformDetail(id)
  }
})

// 获取转化任务详情
function getTransformDetail(id: any) {
  loading.value = true
  systemApi.regulatoryConversion({ id }, 'info').then((res: any) => {
    const data = res.data || res
    transformDetail.value = {
      taskName: data.taskName || '',
      regulationName: data.regulationName || '',
      enterpriseRegulation: data.enterpriseRegulation || '',
      level: data.level || '',
      startDate: data.startDate || '',
      finishDate: data.finishDate || '',
      description: data.description || '',
      status: data.status || '',
      createdBy: data.createdBy || '',
      createdAt: data.createdAt || '',
      updatedAt: data.updatedAt || '',
    }
    loading.value = false
  }).catch((error: any) => {
    console.error('获取转化任务详情失败:', error)
    ElMessage.error('获取转化任务详情失败')
    loading.value = false
  })
}

// 返回列表
function goBack() {
  router.push('/one/regulatoryConversion/index')
}

// 编辑转化任务
function editTransform() {
  const id = route.query.id || route.params.id
  router.push({
    path: '/one/regulatoryConversion/addEdit',
    query: { id },
  })
}

// 格式化状态
function formatStatus(status: string) {
  const statusMap: any = {
    DRAFT: '草稿',
    PROGRESS: '进行中',
    FINISHED: '已完成',
    OVERTIME: '已超时',
    CANCEL: '已取消',
  }
  return statusMap[status] || '未知'
}

// 格式化优先级
function formatLevel(level: string) {
  const levelMap: any = {
    HIGH: '高',
    MIDDLE: '中',
    LOW: '低',
  }
  return levelMap[level] || '未知'
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              转化任务详情
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" plain @click="goBack">
              <svg-icon name="ep:back" />
              <span class="ml-4">返回</span>
            </el-button>
            <!-- <el-button type="primary" @click="editTransform">
              <svg-icon name="ep:edit" />
              <span class="ml-4">编辑</span>
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div v-loading="loading" class="space-y-6">
        <el-card class="card">
          <div class="space-y-6">
            <!-- 基本信息 -->
            <div class="mb-6">
              <h3 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
                基本信息
              </h3>
              <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">任务名称：</span>
                  <span class="flex-1">{{ transformDetail.taskName || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">法规名称：</span>
                  <span class="flex-1">{{ transformDetail.regulationName || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">企业制度：</span>
                  <span class="flex-1">{{ transformDetail.enterpriseRegulation || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">优先级：</span>
                  <span class="flex-1">
                    <el-tag v-if="transformDetail.level === 'HIGH'" type="danger">{{ formatLevel(transformDetail.level) }}</el-tag>
                    <el-tag v-else-if="transformDetail.level === 'MIDDLE'" type="warning">{{ formatLevel(transformDetail.level) }}</el-tag>
                    <el-tag v-else-if="transformDetail.level === 'LOW'" type="info">{{ formatLevel(transformDetail.level) }}</el-tag>
                    <span v-else>-</span>
                  </span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">状态：</span>
                  <span class="flex-1">
                    <el-tag v-if="transformDetail.status === 'PROGRESS'" type="primary">{{ formatStatus(transformDetail.status) }}</el-tag>
                    <el-tag v-else-if="transformDetail.status === 'FINISHED'" type="success">{{ formatStatus(transformDetail.status) }}</el-tag>
                    <el-tag v-else-if="transformDetail.status === 'OVERTIME'" type="warning">{{ formatStatus(transformDetail.status) }}</el-tag>
                    <el-tag v-else-if="transformDetail.status === 'CANCEL'" type="info">{{ formatStatus(transformDetail.status) }}</el-tag>
                    <el-tag v-else-if="transformDetail.status === 'DRAFT'" type="info">{{ formatStatus(transformDetail.status) }}</el-tag>
                    <span v-else>-</span>
                  </span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">创建人：</span>
                  <span class="flex-1">{{ transformDetail.createdBy || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 时间信息 -->
            <div class="mb-6">
              <h3 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
                时间信息
              </h3>
              <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">开始日期：</span>
                  <span class="flex-1">{{ transformDetail.startDate || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">完成日期：</span>
                  <span class="flex-1">{{ transformDetail.finishDate || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">创建时间：</span>
                  <span class="flex-1">{{ transformDetail.createdAt || '-' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 min-w-24 text-gray-600 font-medium">更新时间：</span>
                  <span class="flex-1">{{ transformDetail.updatedAt || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 描述信息 -->
            <div v-if="transformDetail.description" class="mb-6">
              <h3 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
                描述信息
              </h3>
              <div class="border-l-4 border-blue-500 rounded bg-gray-50 p-4">
                <p class="m-0 leading-relaxed">
                  {{ transformDetail.description }}
                </p>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!transformDetail.taskName && !loading" class="py-10 text-center">
              <p class="text-gray-500">
                暂无数据
              </p>
            </div>
          </div>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }
</style>
