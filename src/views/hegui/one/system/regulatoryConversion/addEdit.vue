<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import systemApi from '@/api/complianceApi/one/systemManagement.ts'

  const route = useRoute()
  const router = useRouter()
  
  const form = ref({
    taskName: '',
    regulationName: '',
    enterpriseRegulation: '',
    level: '',
    startDate: '',
    finishDate: '',
    description: ''
  })
  
  const isEdit = ref(false)
  const loading = ref(false)
  
  onMounted(() => {
    const id = route.query.id || route.params.id
    if (id) {
      isEdit.value = true
      getTransformDetail(id)
    }
  })
  
  // 获取转化任务详情
  function getTransformDetail(id: any) {
    loading.value = true
    systemApi.regulatoryConversion({ id }, 'info').then((res: any) => {
      const data = res.data || res
      form.value = {
        taskName: data.taskName || '',
        regulationName: data.regulationName || '',
        enterpriseRegulation: data.enterpriseRegulation || '',
        level: data.level || '',
        startDate: data.startDate || '',
        finishDate: data.finishDate || '',
        description: data.description || ''
      }
      loading.value = false
    }).catch((error: any) => {
      console.error('获取转化任务详情失败:', error)
      ElMessage.error('获取转化任务详情失败')
      loading.value = false
    })
  }
  
  // 保存草稿
  function saveDraft() {
    const params = {
      ...form.value,
      status: 'DRAFT'
    }
    
    const action = isEdit.value ? 'update' : 'create'
    if (isEdit.value) {
      params.id = route.query.id || route.params.id
    }
    
    systemApi.regulatoryConversion(params, action).then((res: any) => {
      ElMessage.success('保存草稿成功')
      router.push('/one/regulatoryConversion/index')
    }).catch((error: any) => {
      console.error('保存草稿失败:', error)
      ElMessage.error('保存草稿失败')
    })
  }
  
  // 提交发布
  function submitPublish() {
    ElMessageBox.confirm('确认提交发布该转化任务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const params = {
        ...form.value,
        status: 'PROGRESS'
      }
      
      const action = isEdit.value ? 'update' : 'create'
      if (isEdit.value) {
        params.id = route.query.id || route.params.id
      }
      
      systemApi.regulatoryConversion(params, action).then((res: any) => {
        ElMessage.success('提交成功')
        router.push('/one/regulatoryConversion/index')
      }).catch((error: any) => {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
      })
    })
  }
  
  // 取消
  function cancel() {
    ElMessageBox.confirm('确认取消编辑吗？未保存的内容将丢失', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '继续编辑',
      type: 'warning'
    }).then(() => {
      router.push('/one/regulatoryConversion/index')
    })
  }
  
  // 预览
  function preview() {
    ElMessage.info('预览功能开发中')
  }
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{isEdit?'修改转化任务':'新增转化任务'}}
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" plain @click="preview">
              <svg-icon name="ep:view" />
              <span class="ml-4">预览</span>
            </el-button>
            <el-button type="primary" plain @click="cancel">
              <svg-icon name="ep:close" />
              <span class="ml-4">取消</span>
            </el-button>
            <el-button type="primary" plain @click="saveDraft">
              <svg-icon name="ep:document" />
              <span class="ml-4">保存草稿</span>
            </el-button>
            <el-button type="primary" @click="submitPublish">
              <svg-icon name="ep:check" />
              <span class="ml-4">{{isEdit?'更新任务':'创建任务'}}</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>

      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";
</style>
