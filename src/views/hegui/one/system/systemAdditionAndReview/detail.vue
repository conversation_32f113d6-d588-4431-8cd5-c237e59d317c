<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { TabsPaneContext } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

const router = useRouter()
const route = useRoute()
const textarea = ref('')

// 审核记录详情数据
const reviewDetail = ref<any>({})

// 状态映射
const statusMap: Record<string, string> = {
  'PASS': '通过',
  'CONDITIONALPASS': '有条件通过',
  'NOPASS': '不通过',
  'REVIEWING': '待审查',
  'EXPIRED': '过期'
}

// 审查类型映射
const auditTypeMap: Record<string, string> = {
  'CONVENTION': '常规审查',
  'EMERGENCY': '紧急审查'
}

const tabsList = ref([
  {
    id: 1,
    name: '基本信息',
  },
  {
    id: 2,
    name: '关联信息',
  },
  {
    id: 3,
    name: '风险分析',
  },
])

const activeName = ref(1)
const activeName_center: any = ref(1)
function handleClick(tab: TabsPaneContext, event: Event) {
  console.log(tab, event)
}
const activeName_list: any = ref([
  {
    id: 1,
    name: '完整模式',
  },
  {
    id: 2,
    name: '对比模式',
  },
  {
    id: 3,
    name: '批注模式',
  },
])

// 获取审核记录详情
const getReviewDetail = async () => {
  try {
    const id = route.params.id || route.query.id
    if (!id) {
      ElMessage.error('缺少审核记录ID')
      return
    }
    const res = await systemApi.reviewSystem({ id }, 'info')
    if (res.code === 200) {
      reviewDetail.value = {
        ...res.data,
        statusText: statusMap[res.data.status] || res.data.status,
        auditTypeText: auditTypeMap[res.data.auditType] || res.data.auditType,
        createdTime: res.data.createdTime ? new Date(res.data.createdTime).toLocaleDateString() : '-',
        deadlineTime: res.data.deadlineTime ? new Date(res.data.deadlineTime).toLocaleDateString() : '-'
      }
    }
  } catch (error) {
    console.error('获取审核记录详情失败:', error)
    ElMessage.error('获取审核记录详情失败')
  }
}

// 提交审核决策
const submitDecision = async (decision: string) => {
  try {
    const params = {
      id: reviewDetail.value.id,
      decision,
      opinion: textarea.value
    }
    const res = await systemApi.reviewSystem(params, 'update')
    if (res.code === 200) {
      ElMessage.success('审核决策提交成功')
      await getReviewDetail() // 刷新详情
    }
  } catch (error) {
    console.error('提交审核决策失败:', error)
    ElMessage.error('提交审核决策失败')
  }
}

// 处理审核决策
const handleDecision = async (decision: string) => {
  const decisionText = {
    'PASS': '通过（无修改）',
    'CONDITIONAL_PASS': '有条件通过',
    'RETURN': '退回修改'
  }[decision] || decision
  
  try {
    await ElMessageBox.confirm(
      `确定要${decisionText}吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    await submitDecision(decision)
  } catch {
    // 用户取消操作
  }
}

// 返回列表
const goBack = () => {
  router.push('/system/review')
}

onMounted(() => {
  getReviewDetail()
})
</script>

<template>
  <div class="absolute-container">
    <PageMain style="background-color: transparent;">
      <el-row>
        <el-col :span="6" class="pr-10">
          <el-card shadow="hover">
            <template #header>
              <div class="aic jcsb flex">
                <div class="f-16 fw-600">
                  {{ reviewDetail.title || '制度审查详情' }}
                </div>
                <div>
                  <el-tag
                    :type="reviewDetail.status === 'PASS' ? 'success' : reviewDetail.status === 'REVIEWING' ? 'warning' : reviewDetail.status === 'NOPASS' ? 'danger' : 'info'"
                  >
                    {{ reviewDetail.statusText || '-' }}
                  </el-tag>
                </div>
              </div>
            </template>
            <div class="flex-1">
              <div class="aic jcsb flex">
                <span style="color: rgb(0 0 0 / 45%);">审核ID：</span>
                <span>{{ reviewDetail.id || '-' }}</span>
              </div>
              <div class="aic jcsb mt-16 flex">
                <span style="color: rgb(0 0 0 / 45%);">制度ID：</span>
                <span>{{ reviewDetail.regulationId || '-' }}</span>
              </div>
              <div class="aic jcsb mt-16 flex">
                <span style="color: rgb(0 0 0 / 45%);">提交人：</span>
                <span>{{ reviewDetail.commitBy || '-' }}</span>
              </div>
              <div class="aic jcsb mt-16 flex">
                <span style="color: rgb(0 0 0 / 45%);">审查类型：</span>
                <span>{{ reviewDetail.auditTypeText || '-' }}</span>
              </div>
              <div class="aic jcsb mt-16 flex">
                <span style="color: rgb(0 0 0 / 45%);">截止日期：</span>
                <span>{{ reviewDetail.deadlineTime || '-' }}</span>
              </div>
              <div class="aic jcsb mt-16 flex">
                <span style="color: rgb(0 0 0 / 45%);">审核人：</span>
                <span>{{ reviewDetail.auditBy || '-' }}</span>
              </div>
              <div class="aic jcsb mt-16 flex">
                <span style="color: rgb(0 0 0 / 45%);">负责人ID：</span>
                <span>{{ reviewDetail.director || '-' }}</span>
              </div>
              <div class="aic jcsb mt-16 flex">
                <span style="color: rgb(0 0 0 / 45%);">说明：</span>
                <span>{{ reviewDetail.explain || '-' }}</span>
              </div>
              <div class="aic jcsb mt-16 flex">
                <span style="color: rgb(0 0 0 / 45%);">审核意见：</span>
                <span>{{ reviewDetail.opinion || '-' }}</span>
              </div>
              <div class="fdc mt-24 flex">
                <el-button type="primary" style="width: 100%;">
                  开始审查
                </el-button>
                <div class="mt-10">
                  <el-button class="" style="width: 100%;">
                    返回修改
                  </el-button>
                </div>
                <div class="mt-10">
                  <el-button class="" style="width: 100%;">
                    生成报告
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="mt-24">
            <template #header>
              <div class="f-16 fw-600">
                合规检查结果
              </div>
            </template>
            <div>
              <div
                class="aic jcsb flex"
                style="width: 100%;height: 40px;padding: 0 16px;border-bottom: 1px solid rgb(5 5 5 / 6%);"
              >
                <div class="f-14">
                  发现问题
                </div>
                <div class="mt-4">
                  <el-badge :value="12" />
                </div>
              </div>
              <div
                class="aic jcsb flex"
                style="width: 100%;height: 40px;padding: 0 16px;border-bottom: 1px solid rgb(5 5 5 / 6%);"
              >
                <div class="f-14">
                  发现问题
                </div>
                <div class="mt-4">
                  <el-badge :value="3" />
                </div>
              </div>
              <div class="aic jcsb flex" style="width: 100%;height: 40px;padding: 0 16px;">
                <div class="f-14">
                  发现问题
                </div>
                <div class="mt-4">
                  <el-badge :value="8" />
                </div>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="mt-24">
            <template #header>
              <div class="f-16 fw-600">
                风险评估
              </div>
            </template>
            <div class="">
              <div class="aic jcsb flex">
                <div class="f-14 fw-400">
                  合规性评分
                </div>
                <div class="f-14 c-[#FAAD14] fw-500">
                  92%
                </div>
              </div>
              <div class="mt-6">
                <el-progress :percentage="92" status="success" :show-text="false" />
              </div>
            </div>
            <div class="mt-16">
              <div class="aic jcsb flex">
                <div class="f-14 fw-400">
                  完整性评分
                </div>
                <div class="f-14 fw-500">
                  85%
                </div>
              </div>
              <div class="mt-6">
                <el-progress :percentage="85" status="warning" :show-text="false" />
              </div>
            </div>
            <div class="mt-16">
              <div class="aic jcsb flex">
                <div class="f-14 fw-400">
                  可执行性评分
                </div>
                <div class="f-14 fw-500">
                  75%
                </div>
              </div>
              <div class="mt-6">
                <el-progress :percentage="75" status="format" :show-text="false" />
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12" class="pl-10 pr-10">
          <el-card shadow="hover" class="">
            <template #header>
              <div class="f-16 fw-600">
                <el-tabs v-model="activeName_center" class="demo-tabs" @tab-click="handleClick">
                  <el-tab-pane v-for="i, j in activeName_list" :key="j" :label="i.name" :name="i.id" />
                </el-tabs>
              </div>
            </template>
            <div>
              {{ activeName_center }}
            </div>
          </el-card>
        </el-col>
        <el-col :span="6" class="pl-10">
          <el-card shadow="hover" class="">
            <template #header>
              <div class="f-16 fw-600">
                审查工具
              </div>
            </template>
          </el-card>
          <el-card shadow="hover" class="mt-16">
            <template #header>
              <div class="f-16 fw-600">
                审查进度
              </div>
            </template>
            <div class="mt-6">
              <el-progress :percentage="75" />
            </div>
          </el-card>
          <el-card shadow="hover" class="mt-16">
            <template #header>
              <div class="f-16 fw-600">
                审查意见
              </div>
            </template>
            <div class="pb-8 pt-8" style="border-bottom: 1px solid rgb(5 5 5 / 6%);">
              <div class="f-14 fw-600">
                第一章 总则
              </div>
              <div class="br-4 mt-8 bg-[#F9F9F9] p-8">
                <div class="aic flex">
                  <img src="" alt="" style="width: 24px;height: 24px;border-radius: 50%;">
                  <span class="f-14 ml-10">张审查</span>
                </div>
                <div class="f-14">
                  建议补充适用范围说明
                </div>
              </div>
            </div>
            <div class="pb-8 pt-8">
              <div class="f-14 fw-600">
                第一章 总则
              </div>
              <div class="br-4 mt-8 bg-[#F9F9F9] p-8">
                <div class="aic flex">
                  <img src="" alt="" style="width: 24px;height: 24px;border-radius: 50%;">
                  <span class="f-14 ml-10">张审查</span>
                </div>
                <div class="f-14">
                  建议补充适用范围说明
                </div>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="mt-16">
            <template #header>
              <div class="f-16 fw-600">
                审查决策
              </div>
            </template>
            <div class="fdc mt-24 flex">
              <el-button type="primary" style="width: 100%;" @click="handleDecision('PASS')">
                通过（无修改）
              </el-button>
              <div class="mt-10">
                <el-button class="" style="width: 100%;" @click="handleDecision('CONDITIONAL_PASS')">
                  有条件通过
                </el-button>
              </div>
              <div class="mt-10">
                <el-button class="" style="width: 100%;" type="danger" plain @click="handleDecision('RETURN')">
                  退回修改
                </el-button>
              </div>
            </div>
            <div class="mt-16">
              <el-input v-model="textarea" :rows="4" type="textarea" placeholder="请输入审查意见..." />
            </div>
            <div class="mt-16">
              <el-button style="width: 100%;" @click="goBack">
                返回列表
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }
</style>
