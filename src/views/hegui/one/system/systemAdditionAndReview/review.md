---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 05-法律法规管理服务/企业监管审核管理

## POST 创建企业监管审核记录

POST /whiskerguardregulatoryservice/api/enterprise/regulation/audits/create

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "regulationId": 0,
  "auditType": "CONVENTION",
  "commitBy": "string",
  "auditBy": "string",
  "deadlineTime": {},
  "status": "PASS",
  "opinion": "string",
  "createdAt": {},
  "updatedAt": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[EnterpriseRegulationAuditDTO](#schemaenterpriseregulationauditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "regulationId": 0,
  "auditType": "",
  "commitBy": "",
  "auditBy": "",
  "deadlineTime": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "",
  "opinion": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityEnterpriseRegulationAuditDTO](#schemaresponseentityenterpriseregulationauditdto)|

## POST 更新企业监管审核记录

POST /whiskerguardregulatoryservice/api/enterprise/regulation/audits/process/{id}

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "regulationId": 0,
  "auditType": "CONVENTION",
  "commitBy": "string",
  "auditBy": "string",
  "deadlineTime": {},
  "status": "PASS",
  "opinion": "string",
  "createdAt": {},
  "updatedAt": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |审核记录ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[EnterpriseRegulationAuditDTO](#schemaenterpriseregulationauditdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "regulationId": 0,
  "auditType": "",
  "commitBy": "",
  "auditBy": "",
  "deadlineTime": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "",
  "opinion": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityEnterpriseRegulationAuditDTO](#schemaresponseentityenterpriseregulationauditdto)|

## GET 分页查询企业监管审核记录

GET /whiskerguardregulatoryservice/api/enterprise/regulation/audits/page

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |none|
|size|query|integer| 否 |none|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "tenantId": 0,
    "regulationId": 0,
    "auditType": "",
    "commitBy": "",
    "auditBy": "",
    "deadlineTime": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "opinion": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    }
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*分页审核记录列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListEnterpriseRegulationAuditDTO](#schemalistenterpriseregulationauditdto)]|false|none||分页审核记录列表|
|» id|integer(int64)|false|none||none|
|» tenantId|integer(int64)|true|none||租户ID|
|» regulationId|integer(int64)|true|none||内部法规ID|
|» auditType|string|true|none||审查类型：常规审查、紧急审查|
|» commitBy|string|false|none||提交人|
|» auditBy|string|false|none||审核人|
|» deadlineTime|object|true|none||截止时间|
|» status|string|true|none||状态：通过、有条件通过、不通过、待审查、过期|
|» opinion|string|false|none||审核意见|
|» createdAt|object|false|none||截止时间|
|» updatedAt|object|false|none||截止时间|

#### 枚举值

|属性|值|
|---|---|
|auditType|CONVENTION|
|auditType|EMERGENCY|
|status|PASS|
|status|CONDITIONALPASS|
|status|NOPASS|
|status|REVIEWING|
|status|EXPIRED|

## GET 获取指定ID的审核记录详情

GET /whiskerguardregulatoryservice/api/enterprise/regulation/audits/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |审核记录ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "regulationId": 0,
  "auditType": "",
  "commitBy": "",
  "auditBy": "",
  "deadlineTime": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "",
  "opinion": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityEnterpriseRegulationAuditDTO](#schemaresponseentityenterpriseregulationauditdto)|

## GET 删除企业监管审核记录

GET /whiskerguardregulatoryservice/api/enterprise/regulation/audits/delete/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要删除的审核记录ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ResponseEntityEnterpriseRegulationAuditDTO">ResponseEntityEnterpriseRegulationAuditDTO</h2>

<a id="schemaresponseentityenterpriseregulationauditdto"></a>
<a id="schema_ResponseEntityEnterpriseRegulationAuditDTO"></a>
<a id="tocSresponseentityenterpriseregulationauditdto"></a>
<a id="tocsresponseentityenterpriseregulationauditdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "regulationId": 0,
  "auditType": "CONVENTION",
  "commitBy": "string",
  "auditBy": "string",
  "deadlineTime": {},
  "status": "PASS",
  "opinion": "string",
  "createdAt": {},
  "updatedAt": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID|
|regulationId|integer(int64)|true|none||内部法规ID|
|auditType|string|true|none||审查类型：常规审查、紧急审查|
|commitBy|string|false|none||提交人|
|auditBy|string|false|none||审核人|
|deadlineTime|object|true|none||截止时间|
|status|string|true|none||状态：通过、有条件通过、不通过、待审查、过期|
|opinion|string|false|none||审核意见|
|createdAt|object|false|none||截止时间|
|updatedAt|object|false|none||截止时间|

#### 枚举值

|属性|值|
|---|---|
|auditType|CONVENTION|
|auditType|EMERGENCY|
|status|PASS|
|status|CONDITIONALPASS|
|status|NOPASS|
|status|REVIEWING|
|status|EXPIRED|

<h2 id="tocS_EnterpriseRegulationAuditDTO">EnterpriseRegulationAuditDTO</h2>

<a id="schemaenterpriseregulationauditdto"></a>
<a id="schema_EnterpriseRegulationAuditDTO"></a>
<a id="tocSenterpriseregulationauditdto"></a>
<a id="tocsenterpriseregulationauditdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "regulationId": 0,
  "auditType": "CONVENTION",
  "commitBy": "string",
  "auditBy": "string",
  "deadlineTime": {},
  "status": "PASS",
  "opinion": "string",
  "createdAt": {},
  "updatedAt": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID|
|regulationId|integer(int64)|true|none||内部法规ID|
|auditType|string|true|none||审查类型：常规审查、紧急审查|
|commitBy|string|false|none||提交人|
|auditBy|string|false|none||审核人|
|deadlineTime|object|true|none||截止时间|
|status|string|true|none||状态：通过、有条件通过、不通过、待审查、过期|
|opinion|string|false|none||审核意见|
|createdAt|object|false|none||截止时间|
|updatedAt|object|false|none||截止时间|

#### 枚举值

|属性|值|
|---|---|
|auditType|CONVENTION|
|auditType|EMERGENCY|
|status|PASS|
|status|CONDITIONALPASS|
|status|NOPASS|
|status|REVIEWING|
|status|EXPIRED|

<h2 id="tocS_ListEnterpriseRegulationAuditDTO">ListEnterpriseRegulationAuditDTO</h2>

<a id="schemalistenterpriseregulationauditdto"></a>
<a id="schema_ListEnterpriseRegulationAuditDTO"></a>
<a id="tocSlistenterpriseregulationauditdto"></a>
<a id="tocslistenterpriseregulationauditdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "regulationId": 0,
  "auditType": "CONVENTION",
  "commitBy": "string",
  "auditBy": "string",
  "deadlineTime": {},
  "status": "PASS",
  "opinion": "string",
  "createdAt": {},
  "updatedAt": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID|
|regulationId|integer(int64)|true|none||内部法规ID|
|auditType|string|true|none||审查类型：常规审查、紧急审查|
|commitBy|string|false|none||提交人|
|auditBy|string|false|none||审核人|
|deadlineTime|object|true|none||截止时间|
|status|string|true|none||状态：通过、有条件通过、不通过、待审查、过期|
|opinion|string|false|none||审核意见|
|createdAt|object|false|none||截止时间|
|updatedAt|object|false|none||截止时间|

#### 枚举值

|属性|值|
|---|---|
|auditType|CONVENTION|
|auditType|EMERGENCY|
|status|PASS|
|status|CONDITIONALPASS|
|status|NOPASS|
|status|REVIEWING|
|status|EXPIRED|

