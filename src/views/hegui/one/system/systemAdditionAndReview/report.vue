<script setup lang="ts">
import * as Echarts from 'echarts'
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

const router = useRouter()
const route = useRoute()

// 审核报告数据
const reportData = ref<any>({})

const tabsList = ref([
  {
    id: 1,
    name: '基本信息',
  },
  {
    id: 2,
    name: '关联信息',
  },
  {
    id: 3,
    name: '风险分析',
  },
])

const activeName = ref(1)

// 获取审核报告数据
const getReportData = async () => {
  try {
    const id = route.params.id || route.query.id
    if (!id) {
      ElMessage.error('缺少审核记录ID')
      return
    }
    const res = await systemApi.reviewSystem({ id }, 'report')
    if (res.code === 200) {
      reportData.value = res.data
      // 重新初始化图表
      initChart1()
      initChart2()
      initChart3()
    }
  } catch (error) {
    console.error('获取审核报告失败:', error)
    ElMessage.error('获取审核报告失败')
  }
}

// 返回列表
const goBack = () => {
  router.push('/system/review')
}
onMounted(() => {
  getReportData()
})

const chart1Ref = ref(null)
const chart2Ref = ref(null)
const chart3Ref = ref(null)
function initChart1() {
  const chart1 = Echarts.init(chart1Ref.value)
  // 配置数据
  const option = {
    title: {
      // text: 'Basic Radar Chart'
    },
    color: ['#1677FF'],
    legend: {
      data: ['评分'],
    },
    radar: {
      // shape: 'circle',
      indicator: [
        { name: '合规性', max: 100 },
        { name: '实用性', max: 100 },
        { name: '一致性', max: 100 },
        { name: '可执行性', max: 100 },
        { name: '完整性', max: 100 },
      ],
    },
    series: [
      {
        name: 'Budget vs spending',
        type: 'radar',
        data: [
          {
            value: [42, 30, 20, 35, 50, 18],
            name: 'Allocated Budget',
          },
        ],
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
function initChart2() {
  const chart1 = Echarts.init(chart2Ref.value)
  // 配置数据
  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: '5%',
      left: 'center',
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          // position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 1048, name: '高风险' },
          { value: 735, name: '中风险' },
          { value: 580, name: '低风险' },
        ],
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
function initChart3() {
  const chart1 = Echarts.init(chart3Ref.value)
  // 配置数据
  const option = {
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [150, 230, 224, 218, 135, 147, 260],
        type: 'line',
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
</script>

<template>
  <div class="">
    <PageMain style="background-color: transparent;">
      <el-card shadow="hover">
        <div class="aic jcsb flex">
          <div class="f-30 fw-600">
            《员工行为规范》制度审查报告
          </div>
          <div>
            <el-tag class="mr-24" style="background: #e6f4ff;border: 1px solid #91caff;border-radius: 4px;">
              审批中
            </el-tag>
          </div>
        </div>
        <div class="mt-24">
          <el-tag class="mr-24" style="background: #e6f4ff;border: 1px solid #91caff;border-radius: 4px;">
            监管处罚
          </el-tag>
          <el-tag class="mr-24" type="danger">
            高风险
          </el-tag>
        </div>
        <div class="f-14 mt-24">
          <el-row>
            <el-col :span="8">
              <span class="c-[#999]">报告编号：</span>
              <span>123456</span>
            </el-col>
            <el-col :span="8">
              <span class="c-[#999]">生成日期：</span>
              <span>2023-01-15</span>
            </el-col>
            <el-col :span="8">
              <span class="c-[#999]">审查人员：</span>
              <span>银行业</span>
            </el-col>
          </el-row>
        </div>
        <div class="f-14 mt-24">
          <el-row>
            <el-col :span="8">
              <span class="c-[#999]">制度名称：</span>
              <span>员工行为规范 V2.0</span>
            </el-col>
            <el-col :span="8">
              <span class="c-[#999]">审查类型：</span>
              <span>常规审查</span>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="mt-20">
        <el-row>
          <el-col :span="12" class="pr-10">
            <el-card shadow="hover">
              <template #header>
                <div class="f-16 fw-600">
                  评分摘要
                </div>
              </template>
              <div class="" style="height: 500px;">
                <div ref="chart1Ref" style="width: 100%; height: 80%;" />
                <div>
                  <div class="jcc flex" style="align-items: baseline;">
                    <span class="f-36 fw-700">85</span>
                    <span class="f-16">/100</span>
                  </div>
                  <div class="jcc mt-10 flex">
                    <span class="f-16 c-[#666]">总体评分</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12" class="pl-10">
            <el-card shadow="hover">
              <template #header>
                <div class="f-16 fw-600">
                  问题分布
                </div>
              </template>
              <div class="" style="height: 500px;">
                <div ref="chart2Ref" style="width: 100%; height: 350px;" />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div class="mt-20">
        <el-card shadow="hover">
          <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="合规性评估" name="first">
              <el-row>
                <el-col :span="8" class="pr-8">
                  <div style="width: 100%;height: 114px;border: 1px solid #d9d9d9;border-radius: 8px;">
                    <div class="aic jcc fdc flex" style="height: 100%;">
                      <span class="f-24 fw-600">92%</span>
                      <span class="f-16">法规符合度</span>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8" class="pl-8 pr-8">
                  <div style="width: 100%;height: 114px;border: 1px solid #d9d9d9;border-radius: 8px;">
                    <div class="aic jcc fdc flex" style="height: 100%;">
                      <span class="f-24 fw-600">92%</span>
                      <span class="f-16">内部规章符合度</span>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8" class="pl-8">
                  <div style="width: 100%;height: 114px;border: 1px solid #d9d9d9;border-radius: 8px;">
                    <div class="aic jcc fdc flex" style="height: 100%;">
                      <span class="f-24 fw-600">92%</span>
                      <span class="f-16">行业标准符合度</span>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="完整性评估" name="second">
              完整性评估
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>
      <div class="mt-20">
        <el-card shadow="hover">
          <template #header>
            <div class="f-16 fw-600">
              问题明细
            </div>
          </template>
          <el-table :data="dataList" min-height="200" border @selection-change="handleSelectionChange">
            <el-table-column prop="id" label="问题类型" width="200" align="center" />
            <el-table-column prop="id" label="严重程度" width="200" align="center" />
            <el-table-column prop="id" label="严重程度" align="left" />
            <el-table-column prop="sort" label="修改建议" align="left" />
          </el-table>
        </el-card>
      </div>
      <div class="mt-20">
        <el-card shadow="hover">
          <template #header>
            <div class="f-16 fw-600">
              改进建议
            </div>
          </template>
          <div>
            <div class="flex p-16" style="background: #f6ffed;border: 1px solid #b7eb8f;border-radius: 4px;">
              <div style="width: 16px;height: 16px;">
                <img src="" style="width: 14px;height: 14px;" alt="">
              </div>
              <div class="ml-8">
                <div>优化制度结构</div>
                <div class="mt-12 c-[#666]">
                  建议将现有的五章内容重新整合为三大部分：总则、行为规范、处罚措施，使结构更加清晰。
                </div>
              </div>
            </div>
            <div class="mt-16 flex p-16" style="background: #fff7e6;border: 1px solid #ffd591;border-radius: 4px;">
              <div style="width: 16px;height: 16px;">
                <img src="" style="width: 14px;height: 14px;" alt="">
              </div>
              <div class="ml-8">
                <div>优化制度结构</div>
                <div class="mt-12 c-[#666]">
                  建议将现有的五章内容重新整合为三大部分：总则、行为规范、处罚措施，使结构更加清晰。
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      <div class="mt-20">
        <el-card shadow="hover">
          <template #header>
            <div class="f-16 fw-600">
              审查评分趋势
            </div>
          </template>
          <div class="" style="height: 350px;">
            <div ref="chart3Ref" style="width: 100%; height: 100%;" />
          </div>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }
</style>
