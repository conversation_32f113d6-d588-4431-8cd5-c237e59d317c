<script setup lang="ts">
import { ref } from 'vue'
import type { TabsPaneContext } from 'element-plus'

const homeimg = new URL('@/assets/images/homeimg4.jpg', import.meta.url).href

const activeName = ref(1)

function handleClick(tab: TabsPaneContext, event: Event) {
  console.log(tab, event)
}
const tabsList: Array = ref([
  {
    id: 1,
    name: '全部制度 (127)',
  },
  {
    id: 2,
    name: '我创建的 (45)',
  },
  {
    id: 3,
    name: '待我审批 (12)',
  },
  {
    id: 4,
    name: '近期更新 (23)',
  },
])

// 左侧树
interface Tree {
  label: string
  children?: Tree[]
}

function handleNodeClick(data: Tree) {
  console.log(data)
}

const data: Tree[] = [
  {
    label: '发布机构',
    children: [
      {
        label: '银保监会',
      },
      {
        label: '商务部',
      },
      {
        label: '证监会',
      },
    ],
  },
  {
    label: '业务领域',
    children: [
      {
        label: '销售管理',
      },
      {
        label: '财务管理',
      },
      {
        label: '运营管理',
      },
    ],
  },
  {
    label: '制度类型',
    children: [
      {
        label: '规章制度',
      },
      {
        label: '操作规程',
      },
      {
        label: '工作指引',
      },
    ],
  },
]

const defaultProps = {
  children: 'children',
  label: 'label',
}
const paging: any = ref({
  page: 1,
  limit: 10,
  total: 4,
})
const dataList: any = ref([{}, {}, {}, {}])

const sliderValue: any = ref(40)

const yxfwImg = new URL('@/assets/images/icons/yxfw.png', import.meta.url).href
const gjyqImg = new URL('@/assets/images/icons/gjyq.png', import.meta.url).href
const dqjdImg = new URL('@/assets/images/icons/dqjd.png', import.meta.url).href
</script>

<template>
  <div class="absolute-container">
    <PageMain style="background-color: transparent;">
      <div class="card flex p-16">
        <div>
          <el-button type="primary">
            <svg-icon name="ep:plus" />
            <span class="ml-4">新建转化任务123</span>
          </el-button>
        </div>
        <div class="ml-14">
          <el-button type="primary" plain>
            <svg-icon name="ep:download" />
            <span class="ml-4">批量导出</span>
          </el-button>
        </div>
        <div class="ml-32">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="名称:">
              <el-input v-model="paging.title" clearable placeholder="搜索法规名称..." @clear="paging.title = null" />
            </el-form-item>
            <el-form-item label="发布机构:">
              <el-cascader
                v-model="paging.group_id" :props="props1" clearable class="w-full" :options="groupList"
                @change="changegroup" @clear="paging.group_id = null"
              />
            </el-form-item>
            <el-form-item label="全部状态" style="width: 200px;">
              <el-select v-model="paging.status" clearable placeholder="请选择状态" @clear="paging.status = null">
                <el-option label="正常" value="1" />
                <el-option label="禁用" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">
                <template #icon>
                  <svg-icon name="ep:search" />
                </template>
                查询
              </el-button>
              <el-button
                @click="paging = {
                  page: 1,
                  limit: 10,
                }, getList()"
              >
                重置
              </el-button>
              <el-button>
                <svg-icon name="ep:filter" />
                <span class="ml-4">高级筛选</span>
              </el-button>
              <!-- <el-button type="danger" @click="removealldata()">
                <template #icon>
                  <svg-icon name="ep:search" />
                </template>
                批量删除
              </el-button> -->
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!--      <div>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="i,j in tabsList" :key="j" :label="i.name" :name="i.id"></el-tab-pane>
        </el-tabs>
      </div> -->
      <div class="conBox mt-20">
        <LayoutContainer style="padding: 0;" :enable-left-side="true" :enable-right-side="true" :left-side-width="280">
          <template #leftSide>
            <div class="aic jcsb flex">
              <div class="f-16 f-500">
                法规分类
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree :data="data" :props="defaultProps" :default-expand-all="true" @node-click="handleNodeClick" />
            </div>
          </template>
          <div>
            <!-- <image-preview :src="i.image" :width="60" :height="60" /> -->
            <el-table :data="dataList" min-height="200" border @selection-change="handleSelectionChange">
              <el-table-column prop="id" label="法规名称" min-width="150" align="center" />
              <el-table-column prop="id" label="发布机构" width="100" align="center" />
              <el-table-column prop="id" label="制度类型" width="100" align="center" />
              <el-table-column prop="sort" label="发布日期" width="80" align="center" />
              <el-table-column prop="send_time" label="实施日期" width="180" align="center">
                <template #default="{ row: i }">
                  <!-- {{ i.send_time>0?dayjs(i.send_time * 1000).format('YYYY-MM-DD'):'' }} -->
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80" align="center">
                <template #default="{ row }">
                  <div v-if="row.status === 1" class="relative flex items-center justify-center">
                    <div data-v-7edfc4d9="" class="badge relative mr-2 inline-flex">
                      <span
                        class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                      >true</span>
                    </div>
                    正常
                  </div>
                  <div v-else class="relative flex items-center justify-center">
                    <div data-v-7edfc4d9="" class="badge downcol relative mr-2 inline-flex">
                      <span
                        class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                      >true</span>
                    </div>
                    暂停
                  </div>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="100" align="center">
                <template #default="scope">
                  <div class="aic jcse flex">
                    <div>
                      <svg-icon name="ep:view" class="c-[#1E88E5]" />
                    </div>
                    <div>
                      <svg-icon name="ep:right" class="c-[#1E88E5]" />
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!-- <page-compon :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
              @pag-change="pagChange" /> -->
            <div class="jcsb mt-24 flex">
              <div style="width: 49%;">
                <el-card class="box-card">
                  <template #header>
                    <div class="card-header">
                      <span>法规详情</span>
                    </div>
                  </template>
                  <div>
                    <div class="f-18 fw-500">
                      《金融机构反洗钱规定》
                    </div>
                    <div class="f-14 mt-16 flex">
                      <div style="width: 50%;">
                        <span class="c-[#999]">发布机构：</span>
                        <span class="c-[#666]">中国人民银行</span>
                      </div>
                      <div style="width: 50%;">
                        <span class="c-[#999]">发布日期：</span>
                        <span class="c-[#666]">2024-01-15</span>
                      </div>
                    </div>
                    <div class="f-14 mt-16 flex">
                      <div style="width: 50%;">
                        <span class="c-[#999]">文号：</span>
                        <span class="c-[#666]">2024-01-15</span>
                      </div>
                      <div style="width: 50%;">
                        <span class="c-[#999]">发布日期：</span>
                        <span class="c-[#666]">银发〔2024〕1号</span>
                      </div>
                    </div>
                    <div class="mb-20 mt-20" style="border-top: 1px solid #e0e0e0;" />
                    <div>
                      <div class="aic flex">
                        <div class="fcc br-4 flex bg-[#F5F7FA]" style="width: 52px;height: 28px;">
                          原文
                        </div>
                        <div class="fcc br-4 ml-28 flex bg-[#fff]" style="width: 100px;height: 28px;">
                          智能解读
                        </div>
                      </div>
                      <div class="fcc br-4 f-14 c-666 mt-14 flex bg-[#fff]">
                        第一条
                        为了预防洗钱活动，维护金融秩序，根据《中华人民共和国反洗钱法》等法律规定，制定本规定。
                      </div>
                      <div class="fcc br-4 c-666 f-14 mt-14 flex bg-[#fff]">
                        第一条
                        第二条 本规定适用于在中华人民共和国境内依法设立的金融机构。 金融机构包括：政策性银行、商业银行、农村合作银行、城市信用合作社、农村信用合作社等吸收公众存款的金融机构...
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
              <div style="width: 49%;">
                <el-card class="box-card">
                  <template #header>
                    <div class="card-header">
                      <span>AI 智能分析</span>
                    </div>
                  </template>
                  <div>
                    <div class="br-4 bg-[#F5F7FA]" style="padding: 14px 12px;">
                      <div class="aic flex">
                        <img :src="yxfwImg" class="wh-20" alt="">
                        <span class="ml-8">影响范围</span>
                      </div>
                      <div class="f-16 mt-10 c-[#4B5563]">
                        涉及业务部门：金融科技部、风控部、合规部
                      </div>
                    </div>
                    <div class="br-4 mt-16 bg-[#F5F7FA]" style="padding: 14px 12px;">
                      <div class="aic flex">
                        <img :src="gjyqImg" class="wh-20" alt="">
                        <span class="ml-8">关键要求</span>
                      </div>
                      <div class="f-16 mt-10 c-[#4B5563]">
                        需要建立专门的金融科技创新管理制度
                      </div>
                    </div>
                  </div>
                </el-card>
                <el-card class="box-card mt-16">
                  <template #header>
                    <div class="card-header">
                      <span>转化任务</span>
                    </div>
                  </template>
                  <div>
                    <div class="aic jcsb flex">
                      <div class="aic flex">
                        <img :src="dqjdImg" class="wh-20" alt="">
                        <span class="ml-8">当前进度</span>
                      </div>
                      <div class="f-14 c-[#4CAF50]">
                        40%
                      </div>
                    </div>
                    <div class="mt-8">
                      <el-slider v-model="sliderValue" disabled />
                    </div>
                    <div class="jcsb flex flex-wrap">
                      <div class="aic mt-16 flex" style="width: 50%;">
                        <img :src="gjyqImg" class="wh-20" alt="">
                        <span class="ml-8 c-[#666666]">法规解析</span>
                      </div>
                      <div class="aic mt-16 flex" style="width: 50%;">
                        <img :src="gjyqImg" class="wh-20" alt="">
                        <span class="ml-8 c-[#666666]">要求提取</span>
                      </div>
                      <div class="aic mt-16 flex" style="width: 50%;">
                        <img :src="gjyqImg" class="wh-20" alt="">
                        <span class="ml-8 c-[#666666]">框架生成</span>
                      </div>
                      <div class="aic mt-16 flex" style="width: 50%;">
                        <img :src="gjyqImg" class="wh-20" alt="">
                        <span class="ml-8 c-[#666666]">内容撰写</span>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </div>
        </LayoutContainer>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }

  .conBox {
    :deep(.main) {
      background-color: transparent;

      .main-container {
        padding: 0 !important;
      }

      .el-slider__button-wrapper {
        display: none;
      }

      .el-slider__runway.is-disabled .el-slider__bar {
        height: 8px;
        background: #4caf50;
        border-radius: 9999px;
      }
    }
  }
</style>
