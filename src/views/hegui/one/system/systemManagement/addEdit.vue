<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import useUserStore from '@/store/modules/user'

import systemApi from '@/api/complianceApi/one/systemManagement.ts'
import LawsPop from '@/components/lawsPop/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()
const userStore = useUserStore()
// 是否是编辑模式
const isEdit = computed(() => !!route.query.id)

const AttachmentType = ref('OTHER')
// 表单数据
const formData = reactive({
  id: '',
  title: '',
  regulationCode: '',
  version: '',
  regulationType: '',
  summary: '',
  content: '',
  department: '',
  effectiveDate: '',
  expireDate: '',
  categoryId: '',
  categoryName: '',
  changeLog: '',
  attachments: [],
  regulationIds: [],
})

// 表单验证规则
const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入制度名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' },
  ],
  regulationCode: [
    { required: true, message: '请输入制度编号', trigger: 'blur' },
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
  ],
  regulationCode: [
    { required: true, message: '请输入法规编码', trigger: 'blur' },
  ],
  regulationType: [
    { required: true, message: '请选择制度类型', trigger: 'change' },
  ],
  summary: [
    { required: true, message: '请输入制度摘要', trigger: 'blur' },
  ],
  content: [
    { required: true, message: '请输入制度内容', trigger: 'blur' },
  ],
  department: [
    { required: true, message: '请输入所属部门', trigger: 'blur' },
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' },
  ],
  categoryId: [
    { required: false, message: '请选择制度分类', trigger: 'change' },
  ],
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
  ],
  changeLog: [
    { required: true, message: '请输入变更说明', trigger: 'blur' },
  ],
})

// 制度类型选项
const regulationTypeOptions = [
  { label: '规章制度', value: 'REGULATION' },
  { label: '管理办法', value: 'MEASURES' },
  { label: '行为准则', value: 'CONDUCT' },
]

// 分类树数据
const categoryTree = ref([])
// 选中的分类
const selectedCategory = ref(null)

// 关联制度弹窗
const showLawsPop = ref(false)
// 选中的关联制度
const selectedRegulations = ref([])
// 选中的关联制度完整数据
const selectedRegulationItems = ref([])

// 初始化
onMounted(() => {
  getCategoryTree()

  // 如果是编辑模式，获取制度详情
  if (isEdit.value) {
    getRegulationDetail()
  }
})

// 获取制度详情
function getRegulationDetail() {
  const id = route.query.id
  if (!id) { return }

  systemApi.system({ id }, 'info').then((res: any) => {
    if (res) {
      const data = res.data || res
      // 填充表单数据
      Object.keys(formData).forEach((key) => {
        if (data[key] !== undefined) {
          formData[key] = data[key]
        }
      })

      // 处理附件
      if (data.attachments && data.attachments.length > 0) {
        formData.attachments = data.attachments
      }

      // 处理关联制度
      if (data.regulationIds && data.regulationIds.length > 0) {
        selectedRegulations.value = data.regulationIds
        formData.regulationIds = data.regulationIds

        // 获取关联制度的详细信息用于显示
        if (data.relatedRegulations && data.relatedRegulations.length > 0) {
          selectedRegulationItems.value = data.relatedRegulations
        }
        else {
          // 如果没有详细信息，创建简单的显示对象
          selectedRegulationItems.value = data.regulationIds.map((id: string) => ({
            id,
            title: `制度ID: ${id}`, // 临时显示，实际使用中可能需要调用接口获取详细信息
          }))
        }
      }

      // 处理分类
      if (data.categoryId) {
        selectedCategory.value = data.categoryId
        formData.categoryId = data.categoryId
      }
    }
  }).catch((error: any) => {
    console.error('获取制度详情失败:', error)
    ElMessage.error('获取制度详情失败')
  })
}

// 获取分类树
function getCategoryTree() {
  systemApi.systemCategories({ }).then((res: any) => {
    if (res) {
      categoryTree.value = res.data || res
    }
  }).catch((error: any) => {
    console.error('获取分类树失败:', error)
    ElMessage.error('获取分类树失败')
  })
}

// 打开关联制度选择弹窗
function openLawsPop() {
  showLawsPop.value = true
}

// 处理关联制度选择确认
function handleLawsConfirm(selectedIds: string[], selectedItems: any[]) {
  selectedRegulations.value = selectedIds
  selectedRegulationItems.value = selectedItems
  formData.regulationIds = selectedIds
}

// 移除关联制度
function removeRegulation(id: string) {
  const index = selectedRegulations.value.indexOf(id)
  if (index > -1) {
    selectedRegulations.value.splice(index, 1)
    selectedRegulationItems.value.splice(index, 1)
    formData.regulationIds = selectedRegulations.value
  }
}

// 选择分类
function handleCategoryChange(value: any) {
  formData.categoryId = value
}

// 附件上传成功回调
function handleAttachmentUploadSuccess(files: any[]) {
  console.log('附件上传成功:', files)
  // formData.attachments 已经通过 v-model 自动更新
}

// 附件上传失败回调
function handleAttachmentUploadError(error: any) {
  console.error('附件上传失败:', error)
  ElMessage.error('附件上传失败')
}

// 保存草稿
function saveDraft() {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.error('请填写必填项')
      return
    }

    const params = { ...formData, status: 'DRAFT' }

    if (isEdit.value) {
      // 更新 - 保留id
      systemApi.system(params, 'update').then(() => {
        ElMessage.success('保存草稿成功')
        router.push('/one/systemManagement/index')
      }).catch((error: any) => {
        console.error('保存草稿失败:', error)
        ElMessage.error('保存草稿失败')
      })
    }
    else {
      // 新增 - 不传id
      const { id, ...createParams } = params
      systemApi.system({ ...createParams }, 'create').then(() => {
        ElMessage.success('保存草稿成功')
        router.push('/one/systemManagement/index')
      }).catch((error: any) => {
        console.error('保存草稿失败:', error)
        ElMessage.error(error?.response?.data?.title || '保存草稿失败')
      })
    }
  })
}

// 提交发布
function submitPublish() {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.error('请填写必填项')
      return
    }

    ElMessageBox.confirm('确定要发布该制度吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const params = { ...formData, status: 'PUBLISHED' }

      if (isEdit.value) {
        // 更新并发布 - 保留id
        systemApi.system(params, 'update').then(() => {
          ElMessage.success('更新成功')
          // systemApi.system({ id: formData.id }, 'publish').then(() => {
          //   ElMessage.success('发布成功')
          //   router.push('/one/systemManagement/index')
          // })
        }).catch((error: any) => {
          console.error('更新失败:', error)
          ElMessage.error('更新失败')
        })
      }
      else {
        // 新增并发布 - 不传id，直接创建为已发布状态
        const { id, ...createParams } = params
        systemApi.system({ ...createParams }, 'create').then(() => {
          ElMessage.success('发布成功')
          router.push('/one/systemManagement/index')
        }).catch((error: any) => {
          console.error('发布失败:', error)
          ElMessage.error('发布失败')
        })
      }
    }).catch(() => {})
  })
}

// 预览
function previewRegulation() {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.error('请填写必填项')
      return
    }

    // 这里可以实现预览功能，例如打开一个预览对话框
    ElMessage.info('预览功能待实现')
  })
}

// 取消
function cancel() {
  ElMessageBox.confirm('确定要取消编辑吗？未保存的内容将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.push('/one/systemManagement/index')
  }).catch(() => {})
}

const form: any = ref({})
</script>

<template>
  <div class="system-management">
    <page-header>
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ isEdit ? '修改制度' : '新增制度' }}
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button @click="cancel">
              取消
            </el-button>
            <el-button type="primary" plain @click="saveDraft">
              保存草稿
            </el-button>
            <el-button type="primary" @click="submitPublish">
              {{ isEdit ? '更新' : '新建制度' }}
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <page-main>
      <div class="card p-16">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
          status-icon
        >
          <el-form-item label="制度名称" prop="title">
            <el-input v-model="formData.title" placeholder="请输入制度名称" />
          </el-form-item>

          <el-form-item label="制度编号" prop="regulationCode">
            <el-input v-model="formData.regulationCode" placeholder="请输入制度编号" />
          </el-form-item>

          <el-form-item label="版本号" prop="version">
            <el-input v-model="formData.version" placeholder="请输入版本号" />
          </el-form-item>

          <el-form-item label="制度类型" prop="regulationType">
            <el-select v-model="formData.regulationType" placeholder="请选择制度类型" style="width: 100%">
              <el-option
                v-for="item in regulationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="制度摘要" prop="summary">
            <el-input v-model="formData.summary" type="textarea" :rows="3" placeholder="请输入制度摘要" />
          </el-form-item>

          <el-form-item label="制度内容" prop="content">
            <el-input v-model="formData.content" type="textarea" :rows="6" placeholder="请输入制度内容" />
          </el-form-item>

          <el-form-item label="所属部门" prop="department">
            <!-- <DepartmentTreeSelect
              v-model="formData.department"
              placeholder="请选择部门"
              clearable
            /> -->
            <el-input v-model="formData.department" placeholder="请输入所属部门" />
          </el-form-item>

          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker
              v-model="formData.effectiveDate"
              type="date"
              placeholder="选择生效日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>

          <el-form-item label="失效日期">
            <el-date-picker
              v-model="formData.expireDate"
              type="date"
              placeholder="选择失效日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>

          <el-form-item label="制度分类" prop="categoryId">
            <el-cascader
              v-model="selectedCategory"
              :options="categoryTree"
              :props="{
                checkStrictly: true,
                label: 'categoryName',
                value: 'id',
                children: 'children',
                emitPath: false,
              }"
              placeholder="请选择制度分类"
              style="width: 100%"
              clearable
              @change="handleCategoryChange"
            />
          </el-form-item>

          <el-form-item label="分类名称" prop="categoryName">
            <el-input v-model="formData.categoryName" placeholder="请输入分类名称" />
          </el-form-item>

          <el-form-item label="变更说明" prop="changeLog">
            <el-input v-model="formData.changeLog" type="textarea" :rows="3" placeholder="请输入变更说明" />
          </el-form-item>

          <el-form-item label="附件">
            <!-- 加一个下拉筛选的附件类型 -->
            <!-- <el-select v-model="attachmentType" placeholder="请选择附件类型">
              <el-option label="PDF" value="pdf" />
              <el-option label="Word" value="word" />
              <el-option label="Excel" value="excel" />
              <el-option label="PPT" value="ppt" />
              <el-option label="图片" value="image" />
            </el-select> -->
            <UploadMbb
              v-model="formData.attachments"
              :max="5"
              :size="10"
              accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif"
              tip-text="支持PDF、Word、Excel、PPT、图片等格式，单个文件大小不超过10MB，最多上传5个文件"
              service-name="whiskerguardregulatoryservice"
              category-name="regulation"
              :use-file-path="true"
              :attachment-type="AttachmentType"
              @upload-success="handleAttachmentUploadSuccess"
              @upload-error="handleAttachmentUploadError"
            />
          </el-form-item>

          <el-form-item label="关联制度">
            <div class="regulation-selector">
              <!-- 已选择的制度标签 -->
              <div v-if="selectedRegulationItems.length > 0" class="selected-regulations mb-2">
                <el-tag
                  v-for="item in selectedRegulationItems"
                  :key="item.id"
                  closable
                  type="info"
                  class="mb-2 mr-2"
                  @close="removeRegulation(item.id)"
                >
                  {{ item.title }}
                </el-tag>
              </div>

              <!-- 选择按钮 -->
              <el-button type="primary" plain @click="openLawsPop">
                <el-icon><Plus /></el-icon>
                选择关联制度
              </el-button>

              <div v-if="selectedRegulationItems.length === 0" class="mt-2 text-sm text-gray-500">
                暂未选择关联制度
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </page-main>

    <!-- 关联制度选择弹窗 -->
    <LawsPop
      v-model="showLawsPop"
      :selected-values="selectedRegulations"
      @confirm="handleLawsConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";
</style>
