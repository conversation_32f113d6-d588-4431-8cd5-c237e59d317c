<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/modules/user'
import ImportComponent from '@/components/import/index.vue'
import dictApi from '@/api/modules/system/dict'

import systemApi from '@/api/complianceApi/one/systemManagement.ts'

const userStore = useUserStore()

// 路由
const router = useRouter()

// 加载状态
const tableLoading = ref(false)
const treeLoading = ref(false)

// 导入弹窗状态
const importDialogVisible = ref(false)

// 当前激活的标签页
const activeName = ref(1)

// 标签页列表
const tabsList = ref([
  { id: 1, name: '全部制度 (0)', count: 0 },
  { id: 2, name: '我创建的 (0)', count: 0 },
  { id: 3, name: '待我审批 (0)', count: 0 },
  { id: 4, name: '近期更新 (0)', count: 0 },
])

// 分页参数
const paging = reactive({
  page: 1,
  limit: 10,
  total: 0,
  title: '', // 制度名称搜索
  status: '', // 状态筛选
  categoryId: '', // 分类筛选
  regulationType: '', // 制度类型筛选
  tabType: 1, // 当前标签页类型
})

// 表格数据
const dataList = ref([])

// 表格选中项
const multipleSelection = ref([])

// 状态选项
const statusOptions = ref([] as { value: string, name: string, description?: string }[])

// 制度类型选项
const regulationTypeOptions = [
  { label: '规章制度', value: 'REGULATION' },
  { label: '管理办法', value: 'MEASURES' },
  { label: '行为准则', value: 'CONDUCT' },
]

// 格式化状态显示
function formatStatus(status: string) {
  const statusItem = statusOptions.value.find(item => item.value === status)
  return statusItem?.name || status
}

// 格式化制度类型显示
function formatRegulationType(type: string) {
  const typeMap = {
    REGULATION: '规章制度',
    MEASURES: '管理办法',
    CONDUCT: '行为准则',
  }
  return typeMap[type] || type
}

// 左侧树结构接口
interface TreeItem {
  id: number | string
  name: string
  children?: TreeItem[]
}

// 树形数据
const categoryTree = ref<TreeItem[]>([])

// 树形配置
const defaultProps = {
  children: 'children',
  label: 'categoryName',
}

// 获取基础数据
async function loadBasicData() {
  try {
    // 通过字典API获取状态数据
    const response = await dictApi.dictAll(4)
    statusOptions.value = response
  }
  catch (error) {
    console.error('加载基础数据失败:', error)
    ElMessage.error('获取状态数据失败')
  }
}

// 初始化
onMounted(() => {
  loadBasicData()
  getCategoryTree()
  getTabCounts()
  getList()
})

// 监听标签页变化
watch(activeName, (newVal) => {
  paging.tabType = newVal
  paging.page = 1
  getList()
})

// 获取分类树
async function getCategoryTree() {
  treeLoading.value = true
  try {
    const res = await systemApi.enterpriseCategories({
    }, 'tree')
    categoryTree.value = res
  }
  catch (error) {
    console.error('获取分类树失败:', error)
    ElMessage.error('获取分类树失败')
  }
  finally {
    treeLoading.value = false
  }
}

// 获取各标签页数量
async function getTabCounts() {
  try {
    // 全部制度
    const allRes = await systemApi.system({ countOnly: true }, 'count')
    if (allRes && allRes.data) {
      tabsList.value[0].count = allRes.data
      tabsList.value[0].name = `全部制度 (${allRes.data})`
    }

    // 我创建的
    const creatorRes = await systemApi.system({ countOnly: true, creator: true }, 'count')
    if (creatorRes && creatorRes.data) {
      tabsList.value[1].count = creatorRes.data
      tabsList.value[1].name = `我创建的 (${creatorRes.data})`
    }

    // 待我审批
    const pendingRes = await systemApi.system({ countOnly: true, pendingApproval: true }, 'count')
    if (pendingRes && pendingRes.data) {
      tabsList.value[2].count = pendingRes.data
      tabsList.value[2].name = `待我审批 (${pendingRes.data})`
    }

    // 近期更新
    const recentRes = await systemApi.system({ countOnly: true, recentlyUpdated: true }, 'count')
    if (recentRes && recentRes.data) {
      tabsList.value[3].count = recentRes.data
      tabsList.value[3].name = `近期更新 (${recentRes.data})`
    }
  }
  catch (error) {
    console.error('获取标签页数量失败:', error)
  }
}

// 获取列表数据
async function getList() {
  tableLoading.value = true

  try {
    // 构建查询参数
    const params: any = {
      page: paging.page,
      limit: paging.limit,
    }

    // 添加筛选条件
    if (paging.title) { params.title = paging.title }
    if (paging.status) { params.status = paging.status }
    if (paging.categoryId) { params.categoryId = paging.categoryId }
    if (paging.regulationType) { params.regulationType = paging.regulationType }

    // 根据标签页类型添加不同参数
    switch (paging.tabType) {
      case 2: // 我创建的
        params.creator = true
        break
      case 3: // 待我审批
        params.pendingApproval = true
        break
      case 4: // 近期更新
        params.recentlyUpdated = true
        break
    }

    const res = await systemApi.system(params)
    if (res) {
      if (res.content) {
        dataList.value = res.content
        paging.total = res.totalElements || 0
      }
      else {
        dataList.value = res.data
        paging.total = res.length || 0
      }
    }
    else {
      dataList.value = []
      paging.total = 0
    }
  }
  catch (error) {
    console.error('获取制度列表失败:', error)
    ElMessage.error('获取制度列表失败')
    dataList.value = []
  }
  finally {
    tableLoading.value = false
  }
}

// 处理分页变化
function pagChange(val: any) {
  paging.page = val.page
  paging.limit = val.limit
  getList()
}

// 处理树节点点击
function handleNodeClick(data: TreeItem) {
  paging.categoryId = data.id
  paging.page = 1
  getList()
}

// 处理标签页点击
function handleClick(tab: TabsPaneContext) {
  // 标签页切换逻辑在watch中处理
}

// 处理表格选择变化
function handleSelectionChange(selection: any[]) {
  multipleSelection.value = selection
}

// 新增制度
function goAdd() {
  router.push({
    path: '/one/systemManagement/addEdit',
  })
}

// 编辑制度
function goEdit(item: any) {
  router.push({
    path: '/one/systemManagement/addEdit',
    query: { id: item.id },
  })
}

// 查看详情
function goDetail(item: any) {
  router.push({
    path: '/one/systemManagement/detail',
    query: { id: item.id },
  })
}

// 删除制度
function deleteRegulation(item: any) {
  ElMessageBox.confirm('确定要删除该制度吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await systemApi.system({ id: item.id }, 'delete')
      ElMessage.success('删除成功')
      getList()
      getTabCounts()
    }
    catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 批量发布
function batchPublish() {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要发布的制度')
    return
  }

  // 检查是否有已发布的制度
  const publishedItems = multipleSelection.value.filter(item => item.status === 'PUBLISHED')
  if (publishedItems.length > 0) {
    ElMessage.warning('已发布的制度不能重复发布')
    return
  }

  const ids = multipleSelection.value.map(item => item.id)
  ElMessageBox.confirm(`确定要发布选中的 ${ids.length} 个制度吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await systemApi.system(ids, 'publish')
      ElMessage.success('批量发布成功')
      getList()
      getTabCounts()
    }
    catch (error) {
      console.error('批量发布失败:', error)
      ElMessage.error('批量发布失败')
    }
  }).catch(() => {})
}

// 发布制度
function publishRegulation(row: any) {
  ElMessageBox.confirm('确定要发布该制度吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await systemApi.system({ id: row.id }, 'publish')
      ElMessage.success('发布成功')
      getList()
      getTabCounts()
    }
    catch (error) {
      console.error('发布失败:', error)
      ElMessage.error('发布失败')
    }
  }).catch(() => {})
}

// 重置筛选条件
function resetFilter() {
  paging.title = ''
  paging.status = ''
  paging.categoryId = ''
  paging.regulationType = ''
  paging.page = 1
  getList()
}

// 打开导入弹窗
function openImportDialog() {
  importDialogVisible.value = true
}

// 导入成功回调
function handleImportSuccess() {
  importDialogVisible.value = false
  getList()
  getTabCounts()
  ElMessage.success('导入成功')
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              制度库管理
            </h1>
          </div>
          <div class="flex space-x-3">
            <!-- <FaAuth v-auth="['/one/systemManagement/add']"> -->
            <el-button type="primary" @click="goAdd">
              <svg-icon name="ep:plus" />
              <span class="ml-4">新建制度</span>
            </el-button>
            <!-- </FaAuth> -->
            <el-button type="primary" plain @click="openImportDialog">
              <svg-icon name="ep:download" />
              <span class="ml-4">批量导入</span>
            </el-button>
            <!-- <el-button type="primary" plain>
              <svg-icon name="ep:download" />
              <span class="ml-4">导出</span>
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <!-- <div class="card p-16"> -->
      <div class="card flex p-16">
        <div class="ml-32">
          <el-form :inline="true">
            <el-form-item label="名称:">
              <el-input v-model="paging.title" clearable placeholder="搜索制度名称..." @clear="paging.title = null" />
            </el-form-item>
            <el-form-item label="状态:">
              <el-select
                v-model="paging.status" style="width: 192px;" clearable placeholder="请选择状态"
                @clear="paging.status = null"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="制度类型:">
              <el-select
                v-model="paging.regulationType" style="width: 192px;" clearable placeholder="请选择制度类型"
                @clear="paging.regulationType = null"
              >
                <el-option
                  v-for="item in regulationTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">
                <template #icon>
                  <svg-icon name="ep:search" />
                </template>
                查询
              </el-button>
              <el-button @click="resetFilter">
                重置
              </el-button>
              <el-button>
                <svg-icon name="ep:filter" />
                <span class="ml-4">高级筛选</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div v-if="false">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="i, j in tabsList" :key="j" :label="i.name" :name="i.id" />
        </el-tabs>
      </div>
      <div class="mt-10">
        <div>
          <div class="mb-4">
            <el-button type="primary" @click="batchPublish">
              批量发布
            </el-button>
          </div>
          <el-table
            v-loading="tableLoading"
            :data="dataList"
            highlight-current-row
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column prop="title" label="制度名称" min-width="150" align="center">
              <template #default="{ row }">
                <el-link type="primary" @click="goDetail(row)">
                  {{ row.title }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="regulationCode" label="制度编号" width="100" align="center" />
            <el-table-column label="制度类型" width="100" align="center">
              <template #default="{ row }">
                {{ formatRegulationType(row.regulationType) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="{ row }">
                <div v-if="row.status === 'PUBLISHED'" class="relative flex items-center justify-center">
                  <div data-v-7edfc4d9="" class="badge relative mr-2 inline-flex">
                    <span
                      class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                    >true</span>
                  </div>
                  已发布
                </div>
                <div v-else class="relative flex items-center justify-center">
                  <div data-v-7edfc4d9="" class="badge downcol relative mr-2 inline-flex">
                    <span
                      class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                    >true</span>
                  </div>
                  草稿
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="发布日期" width="120" align="center" />
            <el-table-column prop="updatedAt" label="最后更新" width="120" align="center" />
            <el-table-column prop="relatedRegulations" label="关联法规" width="100" align="center" />
            <el-table-column label="操作" width="180">
              <template #default="{ row }">
                <div class="flex">
                  <el-button type="text" size="small" @click="goEdit(row)">
                    修改
                  </el-button>
                  <el-button type="text" size="small" @click="deleteRegulation(row)">
                    删除
                  </el-button>
                  <el-dropdown>
                    <el-button size="small" type="text">
                      <span>更多</span>
                      <svg-icon name="ep:arrow-down" />
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="goDetail(row)">
                          查看详情
                        </el-dropdown-item>
                        <el-dropdown-item v-if="row.status !== 'PUBLISHED'">
                          发布
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <page-compon
            :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
            @pag-change="pagChange"
          />
        </div>
        <!-- <LayoutContainer v-if="false" style="padding: 0;" :enable-left-side="false" :enable-right-side="true" :left-side-width="280">
          <template v-if="false" #leftSide>
            <div class="aic jcsb flex">
              <div class="f-16 f-500">
                制度分类
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree
                v-loading="treeLoading"
                :data="categoryTree"
                :props="defaultProps"
                :default-expand-all="true"
                @node-click="handleNodeClick"
              />
            </div>
          </template>
          <div>
            <div class="mb-4">
              <el-button type="primary" @click="batchPublish">
                批量发布
              </el-button>
            </div>
            <el-table
              v-loading="tableLoading"
              :data="dataList"
              highlight-current-row
              border
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column prop="title" label="制度名称" min-width="150" align="center">
                <template #default="{ row }">
                  <el-link type="primary" @click="goDetail(row)">
                    {{ row.title }}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="regulationCode" label="制度编号" width="100" align="center" />
              <el-table-column label="制度类型" width="100" align="center">
                <template #default="{ row }">
                  {{ formatRegulationType(row.regulationType) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80" align="center">
                <template #default="{ row }">
                  <div v-if="row.status === 'PUBLISHED'" class="relative flex items-center justify-center">
                    <div data-v-7edfc4d9="" class="badge relative mr-2 inline-flex">
                      <span
                        class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                      >true</span>
                    </div>
                    已发布
                  </div>
                  <div v-else class="relative flex items-center justify-center">
                    <div data-v-7edfc4d9="" class="badge downcol relative mr-2 inline-flex">
                      <span
                        class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                      >true</span>
                    </div>
                    草稿
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="发布日期" width="120" align="center" />
              <el-table-column prop="updatedAt" label="最后更新" width="120" align="center" />
              <el-table-column prop="relatedRegulations" label="关联法规" width="100" align="center" />
              <el-table-column label="操作" width="180">
                <template #default="{ row }">
                  <div class="flex">
                    <el-button type="text" size="small" @click="goEdit(row)">
                      修改
                    </el-button>
                    <el-button type="text" size="small" @click="deleteRegulation(row)">
                      删除
                    </el-button>
                    <el-dropdown>
                      <el-button size="small" type="text">
                        <span>更多</span>
                        <svg-icon name="ep:arrow-down" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="goDetail(row)">
                            查看详情
                          </el-dropdown-item>
                          <el-dropdown-item v-if="row.status !== 'PUBLISHED'">
                            发布
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <page-compon
              :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
              @pag-change="pagChange"
            />
          </div>
        </LayoutContainer> -->
      </div>
    </PageMain>

    <!-- 导入组件 -->
    <ImportComponent
      v-model:visible="importDialogVisible"
      title="制度库导入"
      :import-data-api="() => systemApi.system({}, 'import')"
      template-file-name="制度库导入模板.xlsx"
      @success="handleImportSuccess"
    />
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }
</style>
