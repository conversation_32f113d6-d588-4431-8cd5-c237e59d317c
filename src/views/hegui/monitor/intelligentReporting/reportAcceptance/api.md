---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 21-违规举报服务/违规举报详情管理

## GET 根据ID查询指定的举报详情信息

GET /whiskerguardviolationservice/api/violation/details/{id}

描述：根据ID查询指定的举报详情信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |举报详情的唯一标识ID，不能为空|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "violationCode": "",
  "violationType": "",
  "title": "",
  "detail": "",
  "isAnonymous": false,
  "contactWay": "",
  "status": "",
  "formId": 0,
  "objectMsg": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "attachmentList": [
    {
      "id": 0,
      "violationId": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "uploadedBy": "",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "violationDealDTO": {
    "id": 0,
    "tenantId": 0,
    "detailId": 0,
    "level": "",
    "problemInvestigateId": "",
    "responsibilityInvestigateId": "",
    "continuousImprovementId": {
      "seconds": 0,
      "nanos": 0
    },
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityViolationDetailDTO](#schemaresponseentityviolationdetaildto)|

# 数据模型

<h2 id="tocS_ViolationDealDTO">ViolationDealDTO</h2>

<a id="schemaviolationdealdto"></a>
<a id="schema_ViolationDealDTO"></a>
<a id="tocSviolationdealdto"></a>
<a id="tocsviolationdealdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "detailId": 0,
  "level": "LOW",
  "problemInvestigateId": "string",
  "responsibilityInvestigateId": "string",
  "continuousImprovementId": {
    "seconds": 0,
    "nanos": 0
  },
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID|
|detailId|integer(int64)|true|none||举报详情ID|
|level|string|false|none||风险等级|
|problemInvestigateId|string|false|none||违规问题调查id|
|responsibilityInvestigateId|string|false|none||责任追究处理id|
|continuousImprovementId|[Instant](#schemainstant)|false|none||持续改进优化id|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||持续改进优化id|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||持续改进优化id|
|isDeleted|boolean|false|none||是否删除|

#### 枚举值

|属性|值|
|---|---|
|level|LOW|
|level|MIDDLE|
|level|HIGH|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ViolationAttachmentDTO">ViolationAttachmentDTO</h2>

<a id="schemaviolationattachmentdto"></a>
<a id="schema_ViolationAttachmentDTO"></a>
<a id="tocSviolationattachmentdto"></a>
<a id="tocsviolationattachmentdto"></a>

```json
{
  "id": 0,
  "violationId": 0,
  "fileName": "string",
  "filePath": "string",
  "fileType": "string",
  "fileSize": "string",
  "fileDesc": "string",
  "metadata": "string",
  "version": 0,
  "uploadedBy": "string",
  "uploadedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||附件ID|
|violationId|integer(int64)|true|none||举报详情ID|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|fileType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|fileDesc|string|false|none||附件描述|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|uploadedBy|string|true|none||上传者|
|uploadedAt|[Instant](#schemainstant)|false|none||持续改进优化id|
|isDeleted|boolean|false|none||是否删除|

<h2 id="tocS_ResponseEntityViolationDetailDTO">ResponseEntityViolationDetailDTO</h2>

<a id="schemaresponseentityviolationdetaildto"></a>
<a id="schema_ResponseEntityViolationDetailDTO"></a>
<a id="tocSresponseentityviolationdetaildto"></a>
<a id="tocsresponseentityviolationdetaildto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "violationCode": "string",
  "violationType": "FINANCIAL_VIOLATION",
  "title": "string",
  "detail": "string",
  "isAnonymous": true,
  "contactWay": "string",
  "status": "PENDING",
  "formId": 0,
  "objectMsg": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "violationId": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "uploadedBy": "string",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "violationDealDTO": {
    "id": 0,
    "tenantId": 0,
    "detailId": 0,
    "level": "LOW",
    "problemInvestigateId": "string",
    "responsibilityInvestigateId": "string",
    "continuousImprovementId": {
      "seconds": 0,
      "nanos": 0
    },
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  }
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID|
|violationCode|string|false|none||举报编号|
|violationType|string|false|none||举报类型|
|title|string|false|none||举报标题|
|detail|string|false|none||举报详情|
|isAnonymous|boolean|false|none||是否匿名|
|contactWay|string|false|none||联系方式|
|status|string|false|none||状态|
|formId|integer(int64)|false|none||表单ID|
|objectMsg|string|false|none||举报对象信息|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||持续改进优化id|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||持续改进优化id|
|isDeleted|boolean|false|none||是否删除|
|attachmentList|[[ViolationAttachmentDTO](#schemaviolationattachmentdto)]|false|none||关联的附件信息|
|violationDealDTO|[ViolationDealDTO](#schemaviolationdealdto)|false|none||关联的处理信息|

#### 枚举值

|属性|值|
|---|---|
|violationType|FINANCIAL_VIOLATION|
|violationType|BUSINESS_ETHIC|
|violationType|INFORMATION_SECURITY|
|violationType|HUMAN_RESOURCE|
|status|PENDING|
|status|REPLIED|
|status|CLOSED|
