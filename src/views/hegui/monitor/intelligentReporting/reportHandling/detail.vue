<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import {
  Check as ElIconCheck,
} from '@element-plus/icons-vue'
import intelligentReportingApi from '@/api/report/intelligentReporting'

const route = useRoute()
const activeTab = ref('evidence')
const loading = ref(false)

// 详情数据
const detailData = reactive({
  id: '',
  reportNumber: '',
  reportType: '',
  level: '',
  reporter: '',
  department: '',
  handler: '',
  currentStage: '',
  deadline: '',
  content: '',
  status: ''
})

const subSteps = ref([
  {
    name: '资料收集',
    person: '张三',
    startTime: '2024-02-20',
    endTime: '2024-02-25',
    status: '已完成',
  },
  {
    name: '现场调查',
    person: '李四',
    startTime: '2024-02-25',
    endTime: '2024-02-28',
    status: '进行中',
  },
])

const form = ref({
  investigationPlan: '',
  investigationRecords: [
    {
      time: '2024-02-20 10:00',
      content: '对相关人员进行访谈，收集证据材料...',
    },
  ],
  evidences: [
    {
      name: '访谈记录.docx',
      description: '访谈相关人员的谈话记录及录音文件',
    },
  ],
  conclusion: 'true',
  conclusionNote: '',
})

const evidenceList = ref([
  {
    name: '调查报告.docx',
    type: '文档',
    uploadTime: '2024-02-20',
    uploader: '张三',
  },
])

const similarCases = ref([
  {
    id: '#2023056',
    description: '员工违规操作客户资产案例',
    similarity: 85,
  },
])

function addRecord() {
  form.value.investigationRecords.push({
    time: new Date().toLocaleString(),
    content: '新的调查记录...',
  })
}

function removeRecord(index: number) {
  form.value.investigationRecords.splice(index, 1)
}

function completeStep(step: any) {
  step.status = '已完成'
}

function viewStepDetail(step: any) {
  console.log('查看步骤详情:', step)
}

function saveDraft() {
  console.log('保存草稿:', form.value)
}

function submitForm() {
  console.log('提交表单:', form.value)
}

function viewEvidence(evidence: any) {
  console.log('查看证据:', evidence)
}

function downloadEvidence(evidence: any) {
  console.log('下载证据:', evidence)
}

// 获取详情数据
async function getDetailData() {
  try {
    loading.value = true
    const id = route.params.id || route.query.id
    if (!id) {
      console.error('缺少ID参数')
      return
    }
    
    const response = await intelligentReportingApi.handling({ id }, 'info')
    if (response && response.data) {
      const data = response.data
      // 根据API返回的数据结构映射到detailData
      Object.assign(detailData, {
        id: data.id || '',
        reportNumber: data.detailId || data.id || '',
        reportType: '违规操作', // 根据实际API字段调整
        level: data.level || 'LOW',
        reporter: '匿名', // 根据实际API字段调整
        department: '合规管理部', // 根据实际API字段调整
        handler: data.createdBy || '未分配',
        currentStage: '调查中', // 根据实际API字段调整
        deadline: data.updatedAt ? formatTime(data.updatedAt) : '',
        content: data.metadata || '发现某员工涉嫌违规操作客户资产，具体表现为...', // 根据实际API字段调整
        status: data.isDeleted ? '已删除' : '受理中'
      })
    }
  } catch (error) {
    console.error('获取详情数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取风险等级文本
function getLevelText(level: string) {
  const levelMap = {
    'HIGH': '高风险',
    'MIDDLE': '中风险',
    'LOW': '低风险'
  }
  return levelMap[level as keyof typeof levelMap] || '未知'
}

// 获取风险等级标签类型
function getLevelTagType(level: string) {
  const typeMap = {
    'HIGH': 'danger',
    'MIDDLE': 'warning',
    'LOW': 'info'
  }
  return typeMap[level as keyof typeof typeMap] || 'info'
}

// 格式化时间
function formatTime(timestamp: any) {
  if (!timestamp) return ''
  if (timestamp.seconds) {
    return new Date(timestamp.seconds * 1000).toLocaleString()
  }
  return new Date(timestamp).toLocaleString()
}

// 组件挂载时获取数据
onMounted(() => {
  getDetailData()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              举报处理 #{{ detailData.reportNumber || detailData.id }}
            </h1>
            <el-tag type="info" class="ml-4" size="small">
              {{ detailData.status }}
            </el-tag>
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="whitespace-nowrap">
              <el-icon><i class="fas fa-plus mr-2" /></el-icon>新增审查
            </el-button>
            <el-button plain class="whitespace-nowrap">
              <el-icon><i class="fas fa-file-export mr-2" /></el-icon>导出数据
            </el-button>
            <el-button plain class="whitespace-nowrap">
              <el-icon><i class="fas fa-chart-bar mr-2" /></el-icon>审查统计
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div v-loading="loading">
        <el-row :gutter="20" class="">
          <el-col :span="20">
            <el-card class="">
              <div class="grid grid-cols-2 gap-6">
                <div class="space-y-4">
                  <div class="flex items-start">
                    <span class="w-24 text-sm text-gray-500">举报编号：</span>
                    <span class="text-sm text-gray-900">{{ detailData.reportNumber || detailData.id }}</span>
                  </div>
                  <div class="flex items-start">
                    <span class="w-24 text-sm text-gray-500">举报类型：</span>
                    <span class="text-sm text-gray-900">{{ detailData.reportType }}</span>
                  </div>
                  <div class="flex items-start">
                    <span class="w-24 text-sm text-gray-500">风险等级：</span>
                    <el-tag :type="getLevelTagType(detailData.level)">
                      {{ getLevelText(detailData.level) }}
                    </el-tag>
                  </div>
                  <div class="flex items-start">
                    <span class="w-24 text-sm text-gray-500">举报人：</span>
                    <span class="text-sm text-gray-900">{{ detailData.reporter }}</span>
                  </div>
                </div>
                <div class="space-y-4">
                  <div class="flex items-start">
                    <span class="w-24 text-sm text-gray-500">处理部门：</span>
                    <span class="text-sm text-gray-900">{{ detailData.department }}</span>
                  </div>
                  <div class="flex items-start">
                    <span class="w-24 text-sm text-gray-500">处理人：</span>
                    <span class="text-sm text-gray-900">{{ detailData.handler }}</span>
                  </div>
                  <div class="flex items-start">
                    <span class="w-24 text-sm text-gray-500">当前阶段：</span>
                    <span class="text-sm text-gray-900">{{ detailData.currentStage }}</span>
                  </div>
                  <div class="flex items-start">
                    <span class="w-24 text-sm text-gray-500">处理截止：</span>
                    <span class="text-sm text-red-600">{{ detailData.deadline }}</span>
                  </div>
                </div>
              </div>
              <div class="mt-6">
                <h3 class="mb-2 text-base font-bold">
                  举报内容
                </h3>
                <p class="text-sm text-gray-600">
                  {{ detailData.content }}
                </p>
                <el-link type="primary" class="mt-2">
                  查看完整内容
                </el-link>
              </div>
            </el-card>
            <el-card class="mt-32">
              <div class="mb-8 flex items-center justify-between">
                <div class="relative flex-1">
                  <div class="h-1 bg-blue-600">
                    <div class="absolute h-6 w-6 rounded-full bg-blue-600 -ml-3 -mt-2.5">
                      <span
                        class="absolute left-1/2 transform text-center text-xs text-gray-600 font-medium -mt-8 -translate-x-1/2"
                      >受理</span>
                      <el-icon class="h-6 w-6 text-white">
                        <ElIconCheck />
                      </el-icon>
                    </div>
                  </div>
                </div>
                <div class="relative flex-1">
                  <div class="h-1 bg-blue-600">
                    <div class="absolute h-6 w-6 rounded-full bg-blue-600 -ml-3 -mt-2.5">
                      <span
                        class="absolute left-1/2 transform text-center text-xs text-gray-600 font-medium -mt-8 -translate-x-1/2"
                      >调查</span>
                      <span class="h-full w-full flex items-center justify-center text-white">2</span>
                    </div>
                  </div>
                </div>
                <div class="relative flex-1">
                  <div class="h-1 bg-gray-200">
                    <div class="absolute h-6 w-6 rounded-full bg-gray-200 -ml-3 -mt-2.5">
                      <span
                        class="absolute left-1/2 transform text-center text-xs text-gray-600 font-medium -mt-8 -translate-x-1/2"
                      >处理</span>
                      <span class="h-full w-full flex items-center justify-center text-gray-500">3</span>
                    </div>
                  </div>
                </div>
                <div class="relative flex-1">
                  <div class="h-1 bg-gray-200">
                    <div class="absolute h-6 w-6 rounded-full bg-gray-200 -ml-3 -mt-2.5">
                      <span
                        class="absolute left-1/2 transform text-center text-xs text-gray-600 font-medium -mt-8 -translate-x-1/2"
                      >反馈</span>
                      <span class="h-full w-full flex items-center justify-center text-gray-500">4</span>
                    </div>
                  </div>
                </div>
                <div class="relative flex-1">
                  <div class="h-1 bg-gray-200">
                    <div class="absolute h-6 w-6 rounded-full bg-gray-200 -ml-3 -mt-2.5">
                      <span
                        class="absolute left-1/2 transform text-center text-xs text-gray-600 font-medium -mt-8 -translate-x-1/2"
                      >关闭</span>
                      <span class="h-full w-full flex items-center justify-center text-gray-500">5</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 子步骤表格 -->
              <el-table :data="subSteps" style="width: 100%;">
                <el-table-column prop="name" label="子步骤名称" />
                <el-table-column prop="person" label="负责人" />
                <el-table-column prop="startTime" label="开始时间" />
                <el-table-column prop="endTime" label="截止时间" />
                <el-table-column prop="status" label="状态">
                  <template #default="{ row }">
                    <el-tag v-if="row.status === '已完成'" type="success">
                      已完成
                    </el-tag>
                    <el-tag v-else-if="row.status === '进行中'" type="warning">
                      进行中
                    </el-tag>
                    <el-tag v-else type="info">
                      未开始
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template #default="{ row }">
                    <el-button v-if="row.status === '进行中'" type="text" @click="completeStep(row)">
                      完成
                    </el-button>
                    <el-button type="text" @click="viewStepDetail(row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
            <el-card class="mt-20">
              <h2 class="mb-6 text-xl font-bold">
                举报调查
              </h2>
              <el-form :model="form" label-width="120px" label-position="top">
                <el-form-item label="调查计划">
                  <el-input v-model="form.investigationPlan" type="textarea" :rows="4" placeholder="请输入调查计划..." />
                </el-form-item>

                <el-form-item label="调查过程记录">
                  <div class="border border-gray-300 rounded-md p-4">
                    <div
                      v-for="(record, index) in form.investigationRecords" :key="index"
                      class="mb-4 border-b border-gray-200 pb-4"
                    >
                      <div class="mb-2 flex items-center justify-between">
                        <span class="text-sm text-gray-500">{{ record.time }}</span>
                        <el-button
                          type="text" icon="el-icon-delete" class="text-red-600"
                          @click="removeRecord(index)"
                        >
                          删除
                        </el-button>
                      </div>
                      <p class="text-sm text-gray-700">
                        {{ record.content }}
                      </p>
                    </div>
                    <el-button type="text" icon="el-icon-plus" @click="addRecord">
                      添加记录
                    </el-button>
                  </div>
                </el-form-item>

                <el-form-item label="证据收集">
                  <div class="border border-gray-300 rounded-md p-4">
                    <div class="grid grid-cols-2 mb-4 gap-4">
                      <div
                        v-for="(evidence, index) in form.evidences" :key="index"
                        class="border border-gray-200 rounded p-4"
                      >
                        <div class="mb-2 flex justify-between">
                          <span class="text-sm font-medium">{{ evidence.name }}</span>
                          <div class="flex items-center space-x-2">
                            <el-button type="text" icon="el-icon-view" size="mini" />
                            <el-button type="text" icon="el-icon-download" size="mini" />
                            <el-button type="text" icon="el-icon-delete" size="mini" class="text-red-600" />
                          </div>
                        </div>
                        <p class="text-sm text-gray-500">
                          {{ evidence.description }}
                        </p>
                      </div>
                    </div>
                    <el-upload class="upload-demo" drag action="https://jsonplaceholder.typicode.com/posts/" multiple>
                      <i class="el-icon-upload" />
                      <div class="el-upload__text">
                        拖拽文件到此处或<em>点击上传</em>
                      </div>
                      <template #tip>
                        <div class="el-upload__tip">
                          支持PDF, JPG, PNG, DOC, XLS等格式
                        </div>
                      </template>
                    </el-upload>
                  </div>
                </el-form-item>

                <div class="grid grid-cols-2 gap-6">
                  <el-form-item label="调查结论">
                    <el-select v-model="form.conclusion" placeholder="请选择调查结论" class="w-full">
                      <el-option label="属实" value="true" />
                      <el-option label="部分属实" value="partially" />
                      <el-option label="不属实" value="false" />
                      <el-option label="无法判断" value="unknown" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="结论说明">
                    <el-input v-model="form.conclusionNote" type="textarea" placeholder="请输入结论说明" />
                  </el-form-item>
                </div>

                <div class="flex justify-end space-x-3">
                  <el-button @click="saveDraft">
                    保存草稿
                  </el-button>
                  <el-button type="primary" @click="submitForm">
                    提交
                  </el-button>
                </div>
              </el-form>
            </el-card>
            <!-- 相关信息区 -->
            <el-card class="mt-20">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="证据材料" name="evidence">
                  <el-table :data="evidenceList" style="width: 100%;">
                    <el-table-column prop="name" label="名称" />
                    <el-table-column prop="type" label="类型" />
                    <el-table-column prop="uploadTime" label="上传时间" />
                    <el-table-column prop="uploader" label="上传人" />
                    <el-table-column label="操作">
                      <template #default="{ row }">
                        <el-button type="text" @click="viewEvidence(row)">
                          查看
                        </el-button>
                        <el-button type="text" @click="downloadEvidence(row)">
                          下载
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>
                <el-tab-pane label="相关举报" name="related">
                  <p>相关举报内容</p>
                </el-tab-pane>
                <el-tab-pane label="处理记录" name="records">
                  <p>处理记录内容</p>
                </el-tab-pane>
                <el-tab-pane label="沟通记录" name="communications">
                  <p>沟通记录内容</p>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card class="">
              <template #header>
                <div class="f-16 fw-600">
                  处理指南
                </div>
              </template>
              <div class="text-sm text-gray-600 space-y-3">
                <p>1. 仔细审阅举报内容，确认重点关注事项</p>
                <p>2. 收集相关证据材料，完善调查记录</p>
                <p>3. 保持与相关方的及时沟通</p>
              </div>
              <el-link type="primary" class="mt-4">
                查看详细指南
              </el-link>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  处理时限
                </div>
              </template>
              <el-alert title="处理即将超时" type="error" description="距离截止时间还剩 3 天" show-icon />
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  AI 辅助
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <el-button type="primary" class="w-full" icon="el-icon-cpu">
                    AI分析内容
                  </el-button>
                </div>
                <div>
                  <el-button type="primary" class="w-full" icon="el-icon-document-add">
                    AI生成调查计划
                  </el-button>
                </div>
                <div>
                  <el-button type="primary" class="w-full" icon="el-icon-connection">
                    AI辅助处理
                  </el-button>
                </div>
              </div>
              <div class="mt-4 rounded-md bg-gray-50 p-4">
                <h4 class="mb-2 text-sm text-gray-700 font-medium">
                  AI建议
                </h4>
                <p class="text-sm text-gray-600">
                  根据举报内容分析，建议重点关注以下几个方面：...
                </p>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相似案例
                </div>
              </template>
              <div class="space-y-4">
                <div
                  v-for="(caseItem, index) in similarCases" :key="index"
                  class="border border-gray-200 rounded-md p-4"
                >
                  <div class="mb-2">
                    <span class="text-sm text-gray-900 font-medium">{{ caseItem.id }}</span>
                    <el-tag type="success" size="mini" class="ml-2">
                      已处理
                    </el-tag>
                  </div>
                  <p class="mb-2 text-sm text-gray-600">
                    {{ caseItem.description }}
                  </p>
                  <div class="flex items-center justify-between">
                    <span class="text-xs text-gray-500">相似度：{{ caseItem.similarity }}%</span>
                    <el-button type="text" size="mini">
                      查看
                    </el-button>
                  </div>
                </div>
              </div>
              <el-link type="primary" class="mt-4">
                查看更多
              </el-link>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style scoped>
  /* 自定义样式 */
  .el-card {
    margin-bottom: 24px;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-table {
    margin-top: 16px;
  }

  .el-alert {
    margin-top: 12px;
  }

  .el-tag {
    margin-right: 8px;
  }

  .el-link {
    margin-top: 16px;
  }

  .el-upload {
    width: 100%;
  }

  .el-upload-dragger {
    width: 100%;
    padding: 20px;
  }

  .grid {
    display: grid;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .gap-6 {
    gap: 24px;
  }

  .space-y-3 > * + * {
    margin-top: 12px;
  }

  .space-y-4 > * + * {
    margin-top: 16px;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .font-bold {
    font-weight: 700;
  }

  .text-gray-500 {
    color: #6b7280;
  }

  .text-gray-600 {
    color: #4b5563;
  }

  .text-gray-700 {
    color: #374151;
  }

  .text-gray-900 {
    color: #111827;
  }

  .text-red-600 {
    color: #dc2626;
  }

  .bg-gray-50 {
    background-color: #f9fafb;
  }

  .bg-gray-100 {
    background-color: #f3f4f6;
  }

  .bg-blue-600 {
    background-color: #2563eb;
  }

  .bg-red-50 {
    background-color: #fef2f2;
  }

  .rounded-md {
    border-radius: 0.375rem;
  }

  .rounded-lg {
    border-radius: 0.5rem;
  }

  .rounded-full {
    border-radius: 9999px;
  }

  .border {
    border-width: 1px;
  }

  .border-gray-200 {
    border-color: #e5e7eb;
  }

  .border-gray-300 {
    border-color: #d1d5db;
  }

  .p-4 {
    padding: 1rem;
  }

  .p-6 {
    padding: 1.5rem;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }

  .mb-4 {
    margin-bottom: 1rem;
  }

  .mb-6 {
    margin-bottom: 1.5rem;
  }

  .mt-2 {
    margin-top: 0.5rem;
  }

  .mt-4 {
    margin-top: 1rem;
  }

  .mt-6 {
    margin-top: 1.5rem;
  }

  .ml-2 {
    margin-left: 0.5rem;
  }

  .ml-3 {
    margin-left: 0.75rem;
  }

  .ml-4 {
    margin-left: 1rem;
  }

  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .flex-col {
    flex-direction: column;
  }

  .w-24 {
    width: 6rem;
  }

  .w-full {
    width: 100%;
  }

  .h-1 {
    height: 0.25rem;
  }

  .h-6 {
    height: 1.5rem;
  }

  .w-6 {
    width: 1.5rem;
  }

  .h-8 {
    height: 2rem;
  }

  .w-8 {
    width: 2rem;
  }

  .relative {
    position: relative;
  }

  .absolute {
    position: absolute;
  }

  .-ml-3 {
    margin-left: -0.75rem;
  }

  .-mt-2\.5 {
    margin-top: -0.625rem;
  }

  .-mt-8 {
    margin-top: -2rem;
  }

  .left-1\/2 {
    left: 50%;
  }

  .transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .-translate-x-1\/2 {
    --tw-translate-x: -50%;
  }

  .text-center {
    text-align: center;
  }

  .text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .font-medium {
    font-weight: 500;
  }

  .leading-5 {
    line-height: 1.25rem;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
