<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Warning as ElIconWarning,
  Clock as ElIconClock,
  Check as ElIconCheck,
  Tickets as ElIconTickets,
  User as ElIconUser,
  Top as ElIconTop,
  Bottom as ElIconBottom,
  // ChartLine as ElIconChartLine,
  Download as ElIconDownload,
  // Fire as ElIconFire,
  // Shield as ElIconShield
} from '@element-plus/icons-vue'

const timeRange = ref('7days')
const showYearOverYear = ref(true)
const showMonthOverMonth = ref(true)
const activeTab = ref('type')
const trendChart = ref<HTMLElement>()
const typePieChart = ref<HTMLElement>()
const typeTrendChart = ref<HTMLElement>()

const timeOptions = [
  { value: '7days', label: '最近7天' },
  { value: '30days', label: '最近30天' },
  { value: '90days', label: '最近90天' },
  { value: 'quarter', label: '本季度' },
  { value: 'year', label: '本年度' },
  { value: 'custom', label: '自定义' },
]

const reportTypeData = [
  { type: '财务违规', count: '342', percentage: '27.4%', yearOverYear: '+12.5%', monthOverMonth: '+3.2%', avgTime: '4.2天', truthRate: '72%' },
  { type: '人事违规', count: '298', percentage: '23.9%', yearOverYear: '+8.7%', monthOverMonth: '+2.1%', avgTime: '5.8天', truthRate: '65%' },
  { type: '信息安全', count: '187', percentage: '15.0%', yearOverYear: '+15.3%', monthOverMonth: '+4.2%', avgTime: '3.9天', truthRate: '81%' },
  { type: '商业道德', count: '156', percentage: '12.5%', yearOverYear: '+6.8%', monthOverMonth: '+1.5%', avgTime: '6.5天', truthRate: '58%' },
  { type: '其他', count: '265', percentage: '21.2%', yearOverYear: '+9.2%', monthOverMonth: '+2.8%', avgTime: '5.1天', truthRate: '63%' },
]

function initCharts() {
  // 举报趋势图
  const trendChartInstance = echarts.init(trendChart.value)
  const trendOption = {
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
    },
    legend: { data: ['举报量', '同比', '环比'] },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '举报量',
        type: 'line',
        data: [120, 132, 145, 160, 172, 190, 210],
        itemStyle: { color: '#3b82f6' },
        smooth: true,
      },
      {
        name: '同比',
        type: 'line',
        data: [110, 120, 130, 140, 150, 160, 170],
        itemStyle: { color: '#10b981' },
        smooth: true,
      },
      {
        name: '环比',
        type: 'line',
        data: [100, 115, 125, 135, 145, 155, 165],
        itemStyle: { color: '#f59e0b' },
        smooth: true,
      },
    ],
  }
  trendChartInstance.setOption(trendOption)

  // 举报类型饼图
  const typePieChartInstance = echarts.init(typePieChart.value)
  const typePieOption = {
    animation: false,
    tooltip: { trigger: 'item' },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '举报类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: { show: false, position: 'center' },
        emphasis: {
          label: { show: true, fontSize: '18', fontWeight: 'bold' },
        },
        labelLine: { show: false },
        data: [
          { value: 342, name: '财务违规' },
          { value: 298, name: '人事违规' },
          { value: 187, name: '信息安全' },
          { value: 156, name: '商业道德' },
          { value: 265, name: '其他' },
        ],
        color: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
      },
    ],
  }
  typePieChartInstance.setOption(typePieOption)

  // 类型趋势图
  const typeTrendChartInstance = echarts.init(typeTrendChart.value)
  const typeTrendOption = {
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
    },
    legend: {
      data: ['财务违规', '人事违规', '信息安全', '商业道德', '其他'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '财务违规',
        type: 'bar',
        stack: 'total',
        emphasis: { focus: 'series' },
        data: [30, 42, 50, 55, 60, 65, 70],
        itemStyle: { color: '#3b82f6' },
      },
      {
        name: '人事违规',
        type: 'bar',
        stack: 'total',
        emphasis: { focus: 'series' },
        data: [25, 30, 35, 40, 45, 50, 53],
        itemStyle: { color: '#10b981' },
      },
      {
        name: '信息安全',
        type: 'bar',
        stack: 'total',
        emphasis: { focus: 'series' },
        data: [20, 22, 25, 27, 30, 32, 35],
        itemStyle: { color: '#f59e0b' },
      },
      {
        name: '商业道德',
        type: 'bar',
        stack: 'total',
        emphasis: { focus: 'series' },
        data: [15, 18, 20, 22, 25, 27, 28],
        itemStyle: { color: '#ef4444' },
      },
      {
        name: '其他',
        type: 'bar',
        stack: 'total',
        emphasis: { focus: 'series' },
        data: [30, 40, 45, 50, 55, 60, 64],
        itemStyle: { color: '#8b5cf6' },
      },
    ],
  }
  typeTrendChartInstance.setOption(typeTrendOption)

  const handleResize = () => {
    trendChartInstance.resize()
    typePieChartInstance.resize()
    typeTrendChartInstance.resize()
  }

  window.addEventListener('resize', handleResize)

  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    trendChartInstance.dispose()
    typePieChartInstance.dispose()
    typeTrendChartInstance.dispose()
  })
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              举报统计分析
            </h1>
          </div>
          <div class="flex flex-col gap-4 sm:flex-row">
            <div class="flex items-center gap-2">
              <el-select v-model="timeRange" class="w-40" placeholder="选择时间范围">
                <el-option v-for="item in timeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-checkbox v-model="showYearOverYear">
                同比
              </el-checkbox>
              <el-checkbox v-model="showMonthOverMonth">
                环比
              </el-checkbox>
            </div>
            <div class="flex gap-2">
              <el-button type="primary" class="flex items-center gap-2">
                <el-icon><el-icon-chart-line /></el-icon>
                <span>生成报告</span>
              </el-button>
              <el-button plain type="primary" class="flex items-center gap-2">
                <el-icon><ElIconDownload /></el-icon>
                <span>导出数据</span>
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <!-- 统计概览区 -->
        <el-card class="mb-6">
          <div class="grid grid-cols-1 mb-6 gap-4 lg:grid-cols-5 md:grid-cols-2">
            <!-- 举报总量 -->
            <el-card shadow="hover">
              <div class="flex items-start justify-between">
                <div>
                  <p class="text-sm text-gray-500">
                    举报总量
                  </p>
                  <p class="mt-1 text-2xl font-bold">
                    1,248
                  </p>
                </div>
                <div class="text-primary rounded-full bg-blue-50 p-2">
                  <el-icon><ElIconWarning /></el-icon>
                </div>
              </div>
              <div class="mt-2 flex items-center">
                <span class="flex items-center text-xs text-green-500">
                  <el-icon><ElIconTop /></el-icon>12.5%
                </span>
                <span class="ml-2 text-xs text-gray-500">同比</span>
                <span class="ml-2 flex items-center text-xs text-blue-500">
                  <el-icon><ElIconTop /></el-icon>3.2%
                </span>
                <span class="ml-2 text-xs text-gray-500">环比</span>
              </div>
            </el-card>

            <!-- 平均处理时间 -->
            <el-card shadow="hover">
              <div class="flex items-start justify-between">
                <div>
                  <p class="text-sm text-gray-500">
                    平均处理时间
                  </p>
                  <p class="mt-1 text-2xl font-bold">
                    5.2<span class="text-sm">天</span>
                  </p>
                </div>
                <div class="text-secondary rounded-full bg-green-50 p-2">
                  <el-icon><ElIconClock /></el-icon>
                </div>
              </div>
              <div class="mt-2 flex items-center">
                <span class="flex items-center text-xs text-red-500">
                  <el-icon><ElIconBottom /></el-icon>8.7%
                </span>
                <span class="ml-2 text-xs text-gray-500">同比</span>
                <span class="ml-2 flex items-center text-xs text-green-500">
                  <el-icon><ElIconBottom /></el-icon>2.1%
                </span>
                <span class="ml-2 text-xs text-gray-500">环比</span>
              </div>
            </el-card>

            <!-- 举报属实率 -->
            <el-card shadow="hover">
              <div class="flex items-start justify-between">
                <div>
                  <p class="text-sm text-gray-500">
                    举报属实率
                  </p>
                  <p class="mt-1 text-2xl font-bold">
                    68%
                  </p>
                </div>
                <div class="rounded-full bg-purple-50 p-2 text-purple-600">
                  <el-icon><ElIconCheck /></el-icon>
                </div>
              </div>
              <div class="mt-2 flex items-center">
                <span class="flex items-center text-xs text-green-500">
                  <el-icon><ElIconTop /></el-icon>4.3%
                </span>
                <span class="ml-2 text-xs text-gray-500">同比</span>
                <span class="ml-2 flex items-center text-xs text-green-500">
                  <el-icon><ElIconTop /></el-icon>1.2%
                </span>
                <span class="ml-2 text-xs text-gray-500">环比</span>
              </div>
            </el-card>

            <!-- 已处理比例 -->
            <el-card shadow="hover">
              <div class="flex items-start justify-between">
                <div>
                  <p class="text-sm text-gray-500">
                    已处理比例
                  </p>
                  <p class="mt-1 text-2xl font-bold">
                    92%
                  </p>
                </div>
                <div class="rounded-full bg-yellow-50 p-2 text-yellow-600">
                  <el-icon><ElIconTickets /></el-icon>
                </div>
              </div>
              <div class="mt-2 flex items-center">
                <span class="flex items-center text-xs text-green-500">
                  <el-icon><ElIconTop /></el-icon>5.6%
                </span>
                <span class="ml-2 text-xs text-gray-500">同比</span>
                <span class="ml-2 flex items-center text-xs text-green-500">
                  <el-icon><ElIconTop /></el-icon>0.8%
                </span>
                <span class="ml-2 text-xs text-gray-500">环比</span>
              </div>
            </el-card>

            <!-- 员工参与度 -->
            <el-card shadow="hover">
              <div class="flex items-start justify-between">
                <div>
                  <p class="text-sm text-gray-500">
                    员工参与度
                  </p>
                  <p class="mt-1 text-2xl font-bold">
                    15%
                  </p>
                </div>
                <div class="rounded-full bg-red-50 p-2 text-red-600">
                  <el-icon><ElIconUser /></el-icon>
                </div>
              </div>
              <div class="mt-2 flex items-center">
                <span class="flex items-center text-xs text-green-500">
                  <el-icon><ElIconTop /></el-icon>3.2%
                </span>
                <span class="ml-2 text-xs text-gray-500">同比</span>
                <span class="ml-2 flex items-center text-xs text-green-500">
                  <el-icon><ElIconTop /></el-icon>0.9%
                </span>
                <span class="ml-2 text-xs text-gray-500">环比</span>
              </div>
            </el-card>
          </div>

          <!-- 举报趋势图 -->
          <div>
            <h3 class="mb-4 text-lg font-semibold">
              举报趋势
            </h3>
            <div ref="trendChart" class="chart-container" />
          </div>
        </el-card>
        <!-- 多维度分析区 -->
        <el-card class="mt-20">
          <!-- 标签页导航 -->
          <el-tabs v-model="activeTab">
            <el-tab-pane label="类型分布" name="type" />
            <el-tab-pane label="部门分布" name="department" />
            <el-tab-pane label="渠道分布" name="channel" />
            <el-tab-pane label="处理状态" name="status" />
            <el-tab-pane label="风险等级" name="risk" />
          </el-tabs>

          <!-- 类型分布内容 -->
          <div v-if="activeTab === 'type'" class="mt-6">
            <div class="grid grid-cols-1 mb-6 gap-6 lg:grid-cols-2">
              <div>
                <h3 class="mb-4 text-lg font-semibold">
                  举报类型分布
                </h3>
                <div ref="typePieChart" class="chart-container" />
              </div>
              <div>
                <h3 class="mb-4 text-lg font-semibold">
                  类型趋势变化
                </h3>
                <div ref="typeTrendChart" class="chart-container" />
              </div>
            </div>

            <h3 class="mb-4 text-lg font-semibold">
              类型明细
            </h3>
            <el-table :data="reportTypeData" style="width: 100%;">
              <el-table-column prop="type" label="举报类型" />
              <el-table-column prop="count" label="数量" />
              <el-table-column prop="percentage" label="占比" />
              <el-table-column prop="yearOverYear" label="同比变化">
                <template #default="{ row }">
                  <span
                    :class="row.yearOverYear.includes('+') ? 'text-green-500' : 'text-red-500'"
                  >{{ row.yearOverYear }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="monthOverMonth" label="环比变化">
                <template #default="{ row }">
                  <span
                    :class="row.monthOverMonth.includes('+') ? 'text-green-500' : 'text-red-500'"
                  >{{ row.monthOverMonth }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="avgTime" label="平均处理时间" />
              <el-table-column prop="truthRate" label="属实率" />
            </el-table>
          </div>
        </el-card>
        <!-- 热点分析区 -->
        <el-card class="mt-20">
          <h3 class="mb-4 text-lg font-semibold">
            热点问题分析
          </h3>
          <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
            <el-card shadow="hover">
              <div class="mb-3 flex items-center">
                <div class="mr-3 rounded-full bg-red-100 p-2 text-red-600">
                  <el-icon><el-icon-fire /></el-icon>
                </div>
                <h4 class="font-medium">
                  财务报销违规
                </h4>
              </div>
              <p class="mb-3 text-sm text-gray-600">
                近期财务报销违规举报激增，主要集中在虚假发票和超额报销问题，同比上升23%。
              </p>
              <div class="flex justify-between text-xs text-gray-500">
                <span>涉及部门: 财务部(45%)</span>
                <span>高风险占比: 68%</span>
              </div>
            </el-card>
            <el-card shadow="hover">
              <div class="mb-3 flex items-center">
                <div class="mr-3 rounded-full bg-yellow-100 p-2 text-yellow-600">
                  <el-icon><ElIconWarning /></el-icon>
                </div>
                <h4 class="font-medium">
                  考勤造假
                </h4>
              </div>
              <p class="mb-3 text-sm text-gray-600">
                人事部门考勤造假举报持续增加，涉及多个部门，属实率高达82%，需重点关注。
              </p>
              <div class="flex justify-between text-xs text-gray-500">
                <span>涉及部门: 人事部(32%)</span>
                <span>高风险占比: 54%</span>
              </div>
            </el-card>
            <el-card shadow="hover">
              <div class="mb-3 flex items-center">
                <div class="mr-3 rounded-full bg-blue-100 p-2 text-blue-600">
                  <el-icon><el-icon-shield /></el-icon>
                </div>
                <h4 class="font-medium">
                  数据泄露风险
                </h4>
              </div>
              <p class="mb-3 text-sm text-gray-600">
                技术部门员工违规外传客户数据问题突出，环比增长15%，处理及时率仅65%。
              </p>
              <div class="flex justify-between text-xs text-gray-500">
                <span>涉及部门: 技术部(28%)</span>
                <span>高风险占比: 76%</span>
              </div>
            </el-card>
          </div>
        </el-card>
      </div>
    </PageMain>
    <!-- </div> -->
  </div>
</template>

<style scoped>
  .font-pacifico {
    font-family: Pacifico, serif;
  }

  .chart-container {
    width: 100%;
    height: 400px;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
