<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import {
  ArrowDown as ElIconArrowDown,
  DataAnalysis as ElIconDataAnalysis,
  Document as ElIconDocument,
  Download as ElIconDownload,
  Plus as ElIconPlus,
  Refresh as ElIconRefresh,
  Search as ElIconSearch,
  SetUp as ElIconSetUp,
  Tickets as ElIconTickets,
} from '@element-plus/icons-vue'
import otherApi from '@/api/review/other'

// 路由
const router = useRouter()

// 加载状态
const loading = ref(false)

// 标签页
const activeTab = ref('other')
// 筛选条件
const filter = ref({
  reviewCode: '',
  name: '',
  reviewObject: '',
  status: '',
  level: '',
  department: '',
  auditBy: '',
  dateRange: [],
  keyword: '',
  reviewer: '',
  result: '',
  risk: '',
  complianceTypes: [],
})
const showAdvancedFilter = ref(false)
// 树形数据
const treeData = [
  {
    label: '合同审查',
    children: [{ label: '采购合同' }, { label: '销售合同' }, { label: '服务合同' }, { label: '租赁合同' }, { label: '其他合同' }],
  },
  {
    label: '重大决策审查',
    children: [{ label: '投资决策' }, { label: '融资决策' }, { label: '重组决策' }],
  },
]

// 表格数据
const contractData = ref([])
// 重大决策数据
const decisionData = ref([])

// 分页数据
const paging = ref({
  page: 1,
  limit: 10,
  total: 0,
})
// 分页
const currentPage = ref(1)
const decisionCurrentPage = ref(1)
const otherCurrentPage = ref(1)
const pageSize = 10
const totalItems = computed(() => contractData.value.length)
const decisionTotalItems = computed(() => decisionData.value.length)
const otherTotalItems = computed(() => otherData.value.length)
// 选择项
const selectedItems = ref([])
const selectedDecisionItems = ref([])
const selectedOtherItems = ref([])
const selectAll = ref(false)
const selectAllDecision = ref(false)
const selectAllOther = ref(false)
// 我的审查
const myReviews = ref([])
// 审查趋势数据
const trendData = ref([])
// 初始化图表
const trendChart = ref(null)
const pieChart = ref(null)
function initCharts() {
  if (trendChart.value) {
    const trendChartInstance = echarts.init(trendChart.value)
    const trendOption = {
      animation: false,
      xAxis: {
        type: 'category',
        data: trendData.value.map(item => item.month),
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: trendData.value.map(item => item.count),
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#409EFF',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.1)',
              },
            ]),
          },
        },
      ],
    }
    trendChartInstance.setOption(trendOption)
    if (pieChart.value) {
      const pieChartInstance = echarts.init(pieChart.value)
      const pieOption = {
        animation: false,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data:
              activeTab.value === 'contract'
                ? ['采购合同', '销售合同', '服务合同', '劳动合同', '技术许可合同', '其他合同']
                : activeTab.value === 'decision'
                  ? ['投资决策', '战略规划', '重组决策', '融资决策', '重大采购']
                  : ['安全合规', '品质合规', '环保合规', 'IT合规', '数据合规', '人事合规', '其他'],
        },
        series: [
          {
            name: '分布比例',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data:
                activeTab.value === 'contract'
                  ? [
                      { value: 32, name: '采购合同' },
                      { value: 28, name: '销售合同' },
                      { value: 18, name: '服务合同' },
                      { value: 10, name: '劳动合同' },
                      { value: 8, name: '技术许可合同' },
                      { value: 4, name: '其他合同' },
                    ]
                  : activeTab.value === 'decision'
                    ? [
                        { value: 38, name: '投资决策' },
                        { value: 24, name: '战略规划' },
                        { value: 18, name: '重组决策' },
                        { value: 12, name: '融资决策' },
                        { value: 8, name: '重大采购' },
                      ]
                    : [
                        { value: 26, name: '安全合规' },
                        { value: 20, name: '品质合规' },
                        { value: 16, name: '环保合规' },
                        { value: 14, name: 'IT合规' },
                        { value: 12, name: '数据合规' },
                        { value: 8, name: '人事合规' },
                        { value: 4, name: '其他' },
                      ],
          },
        ],
      }
      pieChartInstance.setOption(pieOption)
    }
    window.addEventListener('resize', () => {
      trendChartInstance.resize()
      if (pieChart.value && pieChartInstance) {
        pieChartInstance.resize()
      }
    })
  }
}
onMounted(() => {
  initCharts()
  getOtherList()
})
watch(activeTab, () => {
  nextTick(() => {
    initCharts()
  })
})
// 方法
function getTagType(type) {
  const types = {
    采购合同: '',
    销售合同: 'success',
    服务合同: 'info',
    租赁合同: 'warning',
    其他合同: 'danger',
  }
  return types[type] || ''
}
function getDecisionTagType(type) {
  const types = {
    投资决策: 'success',
    融资决策: 'danger',
    重组决策: 'warning',
    战略规划: 'info',
    重大采购: '',
  }
  return types[type] || ''
}
function getOtherTagType(type) {
  const types = {
    安全合规: 'danger',
    IT合规: 'warning',
    财务合规: 'success',
    数据合规: 'info',
    业务合规: '',
  }
  return types[type] || ''
}
function getStatusTagType(status) {
  const statusTypes = {
    待审查: 'info',
    审查中: '',
    已完成: 'success',
    已撤回: 'info',
    需修改: 'warning',
  }
  return statusTypes[status] || ''
}
function formatAmount(amount) {
  return `¥${amount.toLocaleString()}`
}
function handleSelectionChange(val) {
  selectedItems.value = val
  selectAll.value = val.length === contractData.value.length
}
function handleDecisionSelectionChange(val) {
  selectedDecisionItems.value = val
  selectAllDecision.value = val.length === decisionData.value.length
}
function handleOtherSelectionChange(val) {
  selectedOtherItems.value = val
  selectAllOther.value = val.length === otherData.value.length
}
function toggleSelectAll() {
  if (selectAll.value) {
    selectedItems.value = [...contractData]
  }
  else {
    selectedItems.value = []
  }
}
function resetFilter() {
  filter.value = {
    reviewCode: '',
    name: '',
    reviewObject: '',
    status: '',
    level: '',
    department: '',
    auditBy: '',
    dateRange: [],
    keyword: '',
    reviewer: '',
    result: '',
    risk: '',
    complianceTypes: [],
  }
}
function applyFilter() {
  paging.value.page = 1
  getOtherList()
}

// 查询按钮处理
function handleQuery() {
  paging.value.page = 1
  getOtherList()
}

// 重置按钮处理
function handleReset() {
  resetFilter()
  paging.value.page = 1
  getOtherList()
}
function viewDetail(row) {
  console.log('查看详情:', row)
}
function editItem(row) {
  console.log('编辑:', row)
}
function deleteItem(row) {
  console.log('删除:', row)
}
function exportItem(row) {
  console.log('导出:', row)
}
function withdrawItem(row) {
  console.log('撤回:', row)
}
function viewDecisionDetail(row) {
  console.log('查看决策详情:', row)
}
function editDecisionItem(row) {
  console.log('编辑决策:', row)
}
function deleteDecisionItem(row) {
  console.log('删除决策:', row)
}
function exportDecisionItem(row) {
  console.log('导出决策:', row)
}
function withdrawDecisionItem(row) {
  console.log('撤回决策:', row)
}
// 其他审查数据
const otherData = ref([])

// 获取其他审查列表
async function getOtherList() {
  try {
    loading.value = true
    const params = {
    }
    // 移除dateRange字段，因为已经拆分为startDate和endDate

    const response = await otherApi.supplementalReview(paging.value, params)
    if (response) {
      const pageData = response
      otherData.value = pageData.content || []
      paging.value.total = pageData.totalElements || 0

      // 通过统一接口获取其他数据
      contractData.value = pageData.contractReviews || []
      decisionData.value = pageData.decisionReviews || []
      myReviews.value = pageData.myReviews || []
      trendData.value = pageData.trendData || []
    }
  }
  catch (error) {
    console.error('获取其他审查列表失败:', error)
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

// 分页变化处理
function pagChange(page: number) {
  paging.value.page = page
  getOtherList()
}
// 查看其他审查详情
function viewOtherDetail(row) {
  router.push({
    path: '/monitor/examination/ohter/detail',
    query: { id: row.id }, // 传递数据库主键 ID
  })
}

// 编辑其他审查
function editOtherItem(row) {
  router.push({
    path: '/monitor/examination/ohter/addEdit',
    query: { id: row.id },
  })
}

// 删除其他审查
async function deleteOtherItem(row) {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条其他审查记录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await otherApi.supplementalReview(null, { id: row.id }, 'delete')
    ElMessage.success('删除成功')
    getOtherList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 导出其他审查
function exportOtherItem(row) {
  console.log('导出其他审查:', row)
  ElMessage.info('导出功能开发中')
}

// 撤回其他审查
function withdrawOtherItem(row) {
  console.log('撤回其他审查:', row)
  ElMessage.info('撤回功能开发中')
}

// 新增其他审查
function addOtherItem() {
  router.push('/monitor/examination/ohter/addEdit')
}

// 格式化日期
function formatDate(dateObj) {
  if (!dateObj || !dateObj.seconds) { return '-' }
  const date = new Date(dateObj.seconds * 1000)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 页面初始化
onMounted(() => {
  getOtherList()
  initCharts()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              其他审查
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button @click="addOtherItem">
              <el-icon class="mr-2">
                <ElIconPlus />
              </el-icon>
              新增审查
            </el-button>
            <el-button>
              <el-icon class="mr-2">
                <ElIconDownload />
              </el-icon>
              导出数据
            </el-button>
            <el-button>
              <el-icon class="mr-2">
                <ElIconDataAnalysis />
              </el-icon>
              审查统计
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template> -->
              <el-tabs v-model="activeTab" class="px-6 pt-4">
                <!-- <el-tab-pane label="其他审查" name="other"> -->
                <div class="p-6">
                  <!-- 查询表单 -->
                  <el-form :model="filter" inline class="mb-4">
                    <el-form-item label="审查编号">
                      <el-input v-model="filter.reviewCode" placeholder="请输入审查编号" clearable style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="审查名称">
                      <el-input v-model="filter.name" placeholder="请输入审查名称" clearable style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="审查对象">
                      <el-input v-model="filter.reviewObject" placeholder="请输入审查对象" clearable style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="发起部门">
                      <el-select v-model="filter.department" placeholder="请选择部门" clearable style="width: 150px">
                        <el-option label="产品部" value="产品部" />
                        <el-option label="IT部" value="IT部" />
                        <el-option label="财务部" value="财务部" />
                        <el-option label="数据部" value="数据部" />
                        <el-option label="业务部" value="业务部" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="审查级别">
                      <el-select v-model="filter.level" placeholder="请选择级别" clearable style="width: 120px">
                        <el-option label="高" value="高" />
                        <el-option label="中" value="中" />
                        <el-option label="低" value="低" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                      <el-select v-model="filter.status" placeholder="请选择状态" clearable style="width: 120px">
                        <el-option label="待审查" value="待审查" />
                        <el-option label="审查中" value="审查中" />
                        <el-option label="已完成" value="已完成" />
                        <el-option label="需修改" value="需修改" />
                        <el-option label="已撤回" value="已撤回" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="审查人">
                      <el-input v-model="filter.auditBy" placeholder="请输入审查人" clearable style="width: 120px" />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" @click="handleQuery">
                        <el-icon class="mr-1">
                          <ElIconSearch />
                        </el-icon>
                        查询
                      </el-button>
                      <el-button @click="handleReset">
                        <el-icon class="mr-1">
                          <ElIconRefresh />
                        </el-icon>
                        重置
                      </el-button>
                    </el-form-item>
                  </el-form>
                  <!-- 表格 -->
                  <el-table v-loading="loading" :data="otherData" style="width: 100%;" @selection-change="handleOtherSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="reviewCode" label="审查编号" width="150" />
                    <el-table-column prop="name" label="审查名称" width="200" />
                    <el-table-column prop="reviewObject" label="审查对象" width="150" />
                    <el-table-column prop="department" label="发起部门" width="120" />
                    <el-table-column prop="level" label="审查级别" width="100">
                      <template #default="{ row }">
                        <el-tag size="small">
                          {{ row.level }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)" size="small">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="auditBy" label="审查人" width="120" />
                    <el-table-column prop="deadlineDate" label="截止时间" width="150" />
                    <el-table-column prop="createdAt" label="创建时间" width="150">
                      <template #default="{ row }">
                        {{ formatDate(row.createdAt) }}
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="180" fixed="right">
                      <template #default="{ row }">
                        <el-button type="text" size="small" @click="viewOtherDetail(row)">
                          查看
                        </el-button>
                        <el-button type="text" size="small" @click="editOtherItem(row)">
                          编辑
                        </el-button>
                        <el-dropdown>
                          <el-button type="text" size="small">
                            更多
                            <el-icon class="el-icon--right">
                              <ElIconArrowDown />
                            </el-icon>
                          </el-button>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item @click="deleteOtherItem(row)">
                                删除
                              </el-dropdown-item>
                              <el-dropdown-item @click="exportOtherItem(row)">
                                导出
                              </el-dropdown-item>
                              <el-dropdown-item @click="withdrawOtherItem(row)">
                                撤回
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </template>
                    </el-table-column>
                  </el-table>
                  <page-compon
                    :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
                    @pag-change="pagChange"
                  />
                  <!-- <div class="flex justify-between items-center mt-4">
                      <div class="text-sm text-gray-500">共 {{ otherTotalItems }} 条记录</div>
                      <el-pagination v-model:current-page="otherCurrentPage" :page-size="pageSize"
                        :total="otherTotalItems" layout="prev, pager, next, jumper"></el-pagination>
                    </div> -->
                </div>
                <!-- </el-tab-pane> -->
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  审查统计
                </div>
              </template>
              <div class="grid grid-cols-2 mb-6 gap-4">
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    总审查数
                  </div>
                  <div class="text-2xl font-bold">
                    {{ activeTab === 'contract' ? '42' : activeTab === 'decision' ? '28' : '35' }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    待审查
                  </div>
                  <div class="text-2xl text-blue-500 font-bold">
                    {{ activeTab === 'contract' ? '9' : activeTab === 'decision' ? '6' : '7' }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    审查中
                  </div>
                  <div class="text-2xl text-yellow-500 font-bold">
                    {{ activeTab === 'contract' ? '15' : activeTab === 'decision' ? '10' : '12' }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    已完成
                  </div>
                  <div class="text-2xl text-green-500 font-bold">
                    {{ activeTab === 'contract' ? '16' : activeTab === 'decision' ? '11' : '14' }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    需修改
                  </div>
                  <div class="text-2xl text-red-500 font-bold">
                    {{ activeTab === 'contract' ? '2' : activeTab === 'decision' ? '1' : '2' }}
                  </div>
                </div>
              </div>
              <!-- 审查分布图 -->
              <div class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    审查分布
                  </div>
                  <el-link type="primary" :underline="false">
                    查看更多
                  </el-link>
                </div>
                <div class="h-40">
                  <div ref="pieChart" class="h-full w-full" />
                </div>
              </div>
              <!-- 审查趋势图 -->
              <div class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    审查趋势
                  </div>
                  <el-link type="primary" :underline="false">
                    查看更多
                  </el-link>
                </div>
                <div class="h-40">
                  <!-- 折线图 -->
                  <div ref="trendChart" class="h-full w-full" />
                </div>
              </div>
              <!-- 我的审查 -->
              <div class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    我的审查
                  </div>
                  <el-link type="primary" :underline="false">
                    查看全部
                  </el-link>
                </div>
                <div class="space-y-3">
                  <div v-for="item in myReviews" :key="item.id" class="border-b pb-3 last:border-b-0 last:pb-0">
                    <div class="flex justify-between">
                      <div class="font-medium">
                        {{ item.title }}
                      </div>
                      <el-tag :type="getStatusTagType(item.status)" size="small">
                        {{ item.status }}
                      </el-tag>
                    </div>
                    <div class="text-sm text-gray-500">
                      截止: {{ item.deadline }}
                    </div>
                  </div>
                </div>
              </div>
              <!-- 快捷功能 -->
              <div class="border rounded-lg p-4">
                <div class="mb-3 font-medium">
                  快捷功能
                </div>
                <div class="grid grid-cols-2 gap-3">
                  <button
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconDocument />
                    </el-icon>
                    模板管理
                  </button>
                  <button
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconSetUp />
                    </el-icon>
                    流程配置
                  </button>
                  <button
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconTickets />
                    </el-icon>
                    报告生成
                  </button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义样式 */
  :deep(.el-tabs__header) {
    margin: 0;
  }

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    background-color: #e4e7ed;
  }

  :deep(.el-tree) {
    background: transparent;
  }

  :deep(.el-tree-node__content) {
    height: 36px;
  }

  :deep(.el-table) {
    --el-table-border-color: #f0f0f0;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f8f8f8;
  }

  :deep(.el-table .el-table__cell) {
    padding: 12px 0;
  }

  :deep(.el-table .cell) {
    padding-right: 16px;
    padding-left: 16px;
  }
</style>
