<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { ref } from 'vue'
import {
  AlarmClock as ElIconAlarmClock,
  ArrowDown as ElIconArrowDown,
  Bell as ElIconBell,
  Check as ElIconCheck,
  DataLine as ElIconDataLine,
  Document as ElIconDocument,
  Edit as ElIconEdit,
  Monitor as ElIconMonitor,
  Search as ElIconSearch,
  Star as ElIconStar,
  Upload as ElIconUpload,
  User as ElIconUser,
  Warning as ElIconWarning,
} from '@element-plus/icons-vue'

const activeMenu = ref('1-2')
const activeTab = ref('content')
const approvalResult = ref('')
const approvalComment = ref('')
const modificationSuggestion = ref('')
const notifyPersons = ref([])
const confidentialLevel = ref('')
const objectAttributes = ref([
  { name: '产品型号', value: 'XT-2000' },
  { name: '硬件版本', value: 'V2.1' },
  { name: '软件版本', value: 'V3.2.1' },
  { name: '处理器', value: 'ARM Cortex-A72' },
  { name: '内存', value: '4GB LPDDR4' },
  { name: '存储', value: '32GB eMMC' },
  { name: '网络', value: '4G/5G/Wi-Fi 6' },
  { name: '操作系统', value: '定制Linux系统' },
])
const materialList = ref([
  { name: '产品安全设计说明书', type: 'PDF', uploadTime: '2024-04-28 14:30', uploader: '张三', description: '产品安全架构设计文档' },
  { name: '安全测试报告', type: 'Word', uploadTime: '2024-04-29 10:15', uploader: '李四', description: '第三方测试机构提供的安全测试报告' },
  { name: '加密算法说明', type: 'PDF', uploadTime: '2024-04-30 09:30', uploader: '王五', description: '产品使用的加密算法详细说明' },
  { name: '用户权限设计', type: 'Excel', uploadTime: '2024-04-27 16:45', uploader: '赵六', description: '用户角色和权限矩阵表' },
])
const complianceResult = ref('')
const complianceComment = ref('')
const complianceSuggestion = ref('')
const checklist = ref([
  { item: '是否符合相关法律法规要求', status: '合规', remark: '已确认符合GB/T 22239-2019要求' },
  { item: '是否符合行业标准和规范', status: '不合规', remark: '部分接口未达到行业加密标准' },
  { item: '是否符合公司内部制度', status: '合规', remark: '符合公司安全设计规范V2.1' },
  { item: '是否具备必要的安全措施', status: '不合规', remark: '缺少API限流和访问控制' },
  { item: '是否通过必要的测试和验证', status: '合规', remark: '已通过第三方安全测试' },
])
const regulations = ref([
  { name: 'GB/T 22239-2019', clause: '8.1.3', description: '数据传输加密要求' },
  { name: 'ISO/IEC 27001:2022', clause: 'A.10.1', description: '密码控制要求' },
  { name: '企业数据安全管理规范', clause: '第5章', description: '接口安全要求' },
])
const approvalSteps = ref([
  { name: '提交申请', role: '申请人', status: 'completed' },
  { name: '部门审核', role: '部门主管', status: 'completed' },
  { name: '合规审查', role: '合规专员', status: 'current' },
  { name: '最终审批', role: '合规经理', status: 'pending' },
])
const approvalRecords = ref([
  {
    approver: '张三',
    department: '产品部',
    time: '2024-04-30 09:30',
    result: '通过',
    comment: '申请材料完整，符合基本要求，建议进入合规审查环节。',
    attachments: [{ name: '申请材料.pdf' }],
  },
  {
    approver: '李四',
    department: '技术部',
    time: '2024-04-30 14:15',
    result: '有条件通过',
    comment: '技术方案基本可行，但需要补充API安全设计说明。',
    attachments: [{ name: '技术评审意见.docx' }],
  },
])
const hasApprovalPermission = ref(true)
const showUploadDialog = ref(false)
const uploadDocumentType = ref('')
const uploadDocumentDesc = ref('')
const uploadDocumentPermissions = ref([])
const documentList = ref([
  { name: '产品安全审查申请表', type: '审查申请', uploadTime: '2024-04-28 10:30', uploader: '张三' },
  { name: '产品安全设计说明书', type: '审查材料', uploadTime: '2024-04-28 14:30', uploader: '张三' },
  { name: '第三方安全测试报告', type: '测试报告', uploadTime: '2024-04-29 10:15', uploader: '李四' },
  { name: 'ISO27001合规证明', type: '合规证明', uploadTime: '2024-04-29 16:45', uploader: '王五' },
  { name: '法律风险评估意见书', type: '法律意见书', uploadTime: '2024-04-30 09:00', uploader: '赵六' },
])
const riskList = ref([
  {
    id: 1,
    description: '数据传输未加密',
    type: '数据安全',
    level: '高风险',
    scope: '所有通信数据',
    suggestion: '实现端到端TLS加密',
    status: '待处理',
    detail: '产品在与其他系统通信时，未对传输数据进行加密处理，可能导致敏感信息在传输过程中被窃取或篡改。',
    impact: '可能导致用户隐私数据泄露、系统配置被篡改等严重后果，影响产品安全性和企业声誉。',
  },
  {
    id: 2,
    description: '用户权限分级不足',
    type: '权限管理',
    level: '中风险',
    scope: '管理系统',
    suggestion: '增加RBAC权限模型',
    status: '处理中',
    detail: '当前系统仅区分管理员和普通用户两种角色，无法满足细粒度权限控制需求。',
    impact: '可能导致权限提升漏洞，普通用户可能获得不应有的操作权限，造成数据泄露或系统配置错误。',
  },
  {
    id: 3,
    description: '日志记录不完整',
    type: '审计追踪',
    level: '中风险',
    scope: '系统日志',
    suggestion: '完善日志记录策略',
    status: '待处理',
    detail: '关键操作如用户登录、配置修改等未记录完整日志，缺乏时间戳和操作用户信息。',
    impact: '影响安全事件调查和问题追溯，不符合等级保护审计要求。',
  },
  {
    id: 4,
    description: '默认密码未强制修改',
    type: '认证安全',
    level: '高风险',
    scope: '初始账户',
    suggestion: '首次登录强制修改密码',
    status: '待处理',
    detail: '系统预置的管理员账户使用默认密码，且未强制要求首次登录时修改。',
    impact: '可能导致未授权访问，攻击者可利用默认密码获取管理员权限，完全控制系统。',
  },
  {
    id: 5,
    description: 'API接口缺乏限流',
    type: '服务安全',
    level: '中风险',
    scope: '外部接口',
    suggestion: '实现接口访问频率限制',
    status: '已处理',
    detail: '公开API接口未实施访问频率限制，可能遭受暴力破解或DDoS攻击。',
    impact: '可能导致服务不可用或接口被滥用，影响系统稳定性和安全性。',
  },
])
</script>

<template>
  <div class="absolute-container">
    <pageHeader>
      <template #content>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <h1 class="mr-4 text-2xl c-[#000000] font-bold">
              某产品安全合规审查 - OR20240430001
            </h1>
            <el-tag type="primary" size="large">
              审查中
            </el-tag>
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              审批
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              查看流程
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              导出审核报告
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              返回
            </el-button>
          </div>
        </div>
      </template>
    </pageHeader>
    <PageMain style="background-color: transparent;">
      <!-- 主内容区 -->
      <div class="flex">
        <!-- 页面内容 -->
        <div class="flex-1 overflow-y-auto">
          <!-- 页面标题和操作按钮 -->

          <div class="grid grid-cols-3 gap-6">
            <!-- 左侧内容区 -->
            <div class="col-span-2 space-y-6">
              <!-- 基本信息卡片 -->
              <el-card shadow="hover" class="!rounded-lg">
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <p class="text-gray-500">
                      审查编号
                    </p>
                    <p class="font-medium">
                      OR20240430001
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      审查名称
                    </p>
                    <p class="font-medium">
                      某产品安全合规审查
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      审查类型
                    </p>
                    <p class="font-medium">
                      安全合规
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      审查对象
                    </p>
                    <p class="font-medium">
                      XX智能终端产品
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      申请部门
                    </p>
                    <p class="font-medium">
                      产品部
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      申请人
                    </p>
                    <p class="font-medium">
                      张三
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      申请时间
                    </p>
                    <p class="font-medium">
                      2024-04-30 09:30
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      当前审核人
                    </p>
                    <p class="font-medium">
                      李四（合规部）
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      审查级别
                    </p>
                    <el-tag type="warning">
                      重要
                    </el-tag>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      预计完成时间
                    </p>
                    <p class="font-medium">
                      2024-05-02
                    </p>
                  </div>
                </div>
              </el-card>
              <!-- 标签页 -->
              <el-card shadow="hover" class="!rounded-lg">
                <el-tabs v-model="activeTab">
                  <el-tab-pane label="审查内容" name="content">
                    <div class="space-y-6">
                      <!-- 审查对象信息 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          审查对象
                        </h3>
                        <p class="mb-2 font-medium">
                          XX智能终端产品
                        </p>
                        <p class="mb-4 text-gray-700">
                          该产品是一款面向企业用户的智能终端设备，集成了多种传感器和通信模块，主要用于工业环境下的数据采集和远程监控。产品采用ARM架构处理器，支持4G/5G网络连接，内置多种安全加密算法。
                        </p>
                        <el-table :data="objectAttributes" border class="w-full">
                          <el-table-column prop="name" label="属性名称" width="180" />
                          <el-table-column prop="value" label="属性值" />
                        </el-table>
                      </div>
                      <!-- 审查范围与目标 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          审查范围与目标
                        </h3>
                        <p class="mb-4 text-gray-700">
                          本次审查涵盖产品的硬件设计、软件架构、通信协议、数据存储和安全机制等方面。重点评估产品是否符合国家信息安全等级保护要求、行业安全标准以及企业内部安全规范。
                        </p>
                        <p class="mb-4 text-gray-700">
                          审查目标是通过系统化的评估，识别产品存在的潜在安全风险，提出改进建议，确保产品在上市前满足所有合规要求，降低企业运营风险。
                        </p>
                        <div class="rounded bg-gray-50 p-4">
                          <h4 class="mb-2 font-medium">
                            审查依据
                          </h4>
                          <ul class="list-disc pl-5 space-y-1">
                            <li>《信息安全技术 网络安全等级保护基本要求》GB/T 22239-2019</li>
                            <li>《工业控制系统信息安全防护指南》</li>
                            <li>《企业数据安全管理规范》2023版</li>
                            <li>《产品安全设计标准》V2.1</li>
                          </ul>
                        </div>
                      </div>
                      <!-- 审查材料 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          审查材料
                        </h3>
                        <div class="mb-4 border rounded-lg p-4">
                          <div class="mb-4 flex items-center justify-between">
                            <div class="flex space-x-2">
                              <el-button size="small" class="!rounded-button whitespace-nowrap">
                                放大
                              </el-button>
                              <el-button size="small" class="!rounded-button whitespace-nowrap">
                                缩小
                              </el-button>
                              <el-button size="small" class="!rounded-button whitespace-nowrap">
                                全屏
                              </el-button>
                            </div>
                            <div class="flex items-center">
                              <span class="mr-2">1/5</span>
                              <el-button size="small" class="!rounded-button whitespace-nowrap">
                                上一页
                              </el-button>
                              <el-button size="small" class="!rounded-button whitespace-nowrap">
                                下一页
                              </el-button>
                            </div>
                          </div>
                          <div class="h-96 flex items-center justify-center bg-gray-100">
                            <p class="text-gray-500">
                              PDF文档预览区域
                            </p>
                          </div>
                        </div>
                        <el-table :data="materialList" border class="w-full">
                          <el-table-column prop="name" label="材料名称" width="200" />
                          <el-table-column prop="type" label="类型" width="120" />
                          <el-table-column prop="uploadTime" label="上传时间" width="180" />
                          <el-table-column prop="uploader" label="上传人" width="120" />
                          <el-table-column prop="description" label="描述" />
                          <el-table-column label="操作" width="120">
                            <template #default>
                              <el-button type="text" size="small">
                                查看
                              </el-button>
                              <el-button type="text" size="small">
                                下载
                              </el-button>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="风险评估" name="risk">
                    <div class="space-y-6">
                      <!-- 风险评估总览 -->
                      <div class="grid grid-cols-3 gap-4">
                        <div class="col-span-1">
                          <div class="h-48 flex items-center justify-center">
                            <div class="text-center">
                              <div class="mx-auto mb-2 h-32 w-32 flex items-center justify-center border-8 border-blue-200 rounded-full">
                                <span class="text-2xl font-bold">85</span>
                              </div>
                              <el-tag type="danger" size="large">
                                高风险
                              </el-tag>
                            </div>
                          </div>
                        </div>
                        <div class="col-span-2">
                          <div class="grid grid-cols-3 gap-2 text-center">
                            <div class="rounded bg-red-50 p-3">
                              <p class="text-sm text-gray-500">
                                高风险
                              </p>
                              <p class="text-xl text-red-600 font-bold">
                                3
                              </p>
                            </div>
                            <div class="rounded bg-orange-50 p-3">
                              <p class="text-sm text-gray-500">
                                中风险
                              </p>
                              <p class="text-xl text-orange-500 font-bold">
                                5
                              </p>
                            </div>
                            <div class="rounded bg-yellow-50 p-3">
                              <p class="text-sm text-gray-500">
                                低风险
                              </p>
                              <p class="text-xl text-yellow-500 font-bold">
                                2
                              </p>
                            </div>
                          </div>
                          <div class="mt-4">
                            <p class="text-gray-700">
                              本次审查共识别出10个风险点，其中高风险3个，主要集中在数据安全和通信加密方面；中风险5个，涉及权限管理和日志记录；低风险2个，为界面设计缺陷。
                            </p>
                          </div>
                        </div>
                      </div>
                      <!-- 风险点列表 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          风险点列表
                        </h3>
                        <el-table :data="riskList" border class="w-full">
                          <el-table-column type="expand">
                            <template #default="{ row }">
                              <div class="bg-gray-50 p-4">
                                <p class="mb-2 font-medium">
                                  详细描述：
                                </p>
                                <p class="mb-4">
                                  {{ row.detail }}
                                </p>
                                <p class="mb-2 font-medium">
                                  影响分析：
                                </p>
                                <p class="mb-4">
                                  {{ row.impact }}
                                </p>
                                <p class="mb-2 font-medium">
                                  建议措施：
                                </p>
                                <p>{{ row.suggestion }}</p>
                              </div>
                            </template>
                          </el-table-column>
                          <el-table-column prop="id" label="序号" width="60" />
                          <el-table-column prop="description" label="风险描述" width="300" />
                          <el-table-column prop="type" label="风险类型" width="120" />
                          <el-table-column prop="level" label="风险等级" width="100">
                            <template #default="{ row }">
                              <el-tag :type="row.level === '高风险' ? 'danger' : row.level === '中风险' ? 'warning' : 'info'">
                                {{ row.level }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column prop="scope" label="影响范围" width="150" />
                          <el-table-column prop="suggestion" label="处理建议" />
                          <el-table-column prop="status" label="处理状态" width="120">
                            <template #default="{ row }">
                              <el-tag :type="row.status === '待处理' ? 'info' : row.status === '处理中' ? 'warning' : 'success'">
                                {{ row.status }}
                              </el-tag>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                      <!-- 风险趋势分析 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          风险趋势
                        </h3>
                        <div class="h-80 flex items-center justify-center rounded bg-gray-50 p-4">
                          <p class="text-gray-500">
                            风险趋势图表区域
                          </p>
                        </div>
                      </div>
                      <!-- AI风险分析 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          AI风险洞察
                        </h3>
                        <el-card shadow="never" class="!border !border-blue-100 !bg-blue-50">
                          <div class="flex">
                            <el-icon class="mr-3 text-2xl text-blue-500">
                              <ElIconWarning />
                            </el-icon>
                            <div>
                              <p class="mb-2 font-medium">
                                AI识别到3个关键风险点：
                              </p>
                              <ul class="list-disc pl-5 space-y-1">
                                <li>数据传输未使用端到端加密，存在中间人攻击风险</li>
                                <li>用户权限分级不足，可能导致权限提升漏洞</li>
                                <li>日志记录不完整，影响安全事件追溯</li>
                              </ul>
                              <p class="mt-3 text-sm text-gray-600">
                                与行业平均水平相比，本产品的风险评分高于75%的同类产品，建议优先处理高风险项。
                              </p>
                            </div>
                          </div>
                        </el-card>
                      </div>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="合规审查" name="compliance">
                    <div class="space-y-6">
                      <!-- 审查结论 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          审查结论
                        </h3>
                        <el-form label-width="120px">
                          <el-form-item label="审查结论">
                            <el-radio-group v-model="complianceResult">
                              <el-radio label="pass">
                                通过
                              </el-radio>
                              <el-radio label="conditional">
                                有条件通过
                              </el-radio>
                              <el-radio label="reject">
                                不通过
                              </el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="审查意见">
                            <el-input
                              v-model="complianceComment"
                              type="textarea"
                              :rows="4"
                              placeholder="请输入审查意见"
                            />
                          </el-form-item>
                        </el-form>
                      </el-card>
                      <!-- 审查清单 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          审查清单
                        </h3>
                        <el-table :data="checklist" border class="w-full">
                          <el-table-column prop="item" label="检查项" width="300" />
                          <el-table-column prop="status" label="合规状态" width="120">
                            <template #default="{ row }">
                              <el-tag :type="row.status === '合规' ? 'success' : row.status === '不合规' ? 'danger' : 'info'">
                                {{ row.status }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column prop="remark" label="备注" />
                        </el-table>
                      </el-card>
                      <!-- 处理建议 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          处理建议
                        </h3>
                        <el-input
                          v-model="complianceSuggestion"
                          type="textarea"
                          :rows="4"
                          placeholder="请输入合规性修改建议"
                        />
                        <div class="mt-4">
                          <el-button type="primary" class="!rounded-button whitespace-nowrap">
                            引用法规依据
                          </el-button>
                        </div>
                      </el-card>
                      <!-- 法规依据 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <div class="mb-4 flex items-center justify-between">
                          <h3 class="text-lg font-bold">
                            法规依据
                          </h3>
                          <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap">
                            添加依据
                          </el-button>
                        </div>
                        <el-table :data="regulations" border class="w-full">
                          <el-table-column prop="name" label="法规名称" width="300" />
                          <el-table-column prop="clause" label="具体条款" width="150" />
                          <el-table-column prop="description" label="适用说明" />
                          <el-table-column label="操作" width="120">
                            <template #default>
                              <el-button type="text" size="small">
                                编辑
                              </el-button>
                              <el-button type="text" size="small" class="text-red-500">
                                删除
                              </el-button>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-card>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="审批记录" name="approval">
                    <div class="space-y-6">
                      <!-- 审批流程图 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          审批流程
                        </h3>
                        <div class="flex items-center justify-between px-10 py-4">
                          <div v-for="(step, index) in approvalSteps" :key="index" class="text-center">
                            <div class="relative">
                              <div
                                class="mx-auto h-16 w-16 flex items-center justify-center rounded-full"
                                :class="{
                                  'bg-blue-100 text-blue-600': step.status === 'current',
                                  'bg-green-100 text-green-600': step.status === 'completed',
                                  'bg-gray-100 text-gray-400': step.status === 'pending',
                                }"
                              >
                                <el-icon v-if="step.status === 'completed'" class="text-2xl">
                                  <ElIconCheck />
                                </el-icon>
                                <el-icon v-else-if="step.status === 'current'" class="text-2xl">
                                  <ElIconEdit />
                                </el-icon>
                                <span v-else class="text-xl font-bold">{{ index + 1 }}</span>
                              </div>
                              <div
                                v-if="index < approvalSteps.length - 1" class="absolute right-0 top-1/2 h-1 w-8 translate-x-1/2 transform -translate-y-1/2"
                                :class="{
                                  'bg-blue-200': step.status === 'completed',
                                  'bg-gray-200': step.status !== 'completed',
                                }"
                              />
                            </div>
                            <p
                              class="mt-2 font-medium" :class="{
                                'text-blue-600': step.status === 'current',
                                'text-green-600': step.status === 'completed',
                                'text-gray-400': step.status === 'pending',
                              }"
                            >
                              {{ step.name }}
                            </p>
                            <p class="text-sm text-gray-500">
                              {{ step.role }}
                            </p>
                          </div>
                        </div>
                      </el-card>
                      <!-- 审批记录 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          审批记录
                        </h3>
                        <div class="space-y-4">
                          <div
                            v-for="(record, index) in approvalRecords" :key="index" class="border-l-2 pb-4 pl-4"
                            :class="{
                              'border-blue-500': index === 0,
                              'border-gray-200': index !== 0,
                            }"
                          >
                            <div class="flex items-start">
                              <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-600">
                                <el-icon><ElIconUser /></el-icon>
                              </div>
                              <div class="flex-1">
                                <div class="flex items-center justify-between">
                                  <p class="font-medium">
                                    {{ record.approver }}
                                  </p>
                                  <el-tag :type="record.result === '通过' ? 'success' : record.result === '有条件通过' ? 'warning' : 'danger'" size="small">
                                    {{ record.result }}
                                  </el-tag>
                                </div>
                                <p class="text-sm text-gray-500">
                                  {{ record.department }} • {{ record.time }}
                                </p>
                                <p class="mt-2 text-gray-700">
                                  {{ record.comment }}
                                </p>
                                <div v-if="record.attachments.length > 0" class="mt-2">
                                  <p class="mb-1 text-sm text-gray-500">
                                    附件：
                                  </p>
                                  <div class="flex flex-wrap gap-2">
                                    <el-tag v-for="(file, i) in record.attachments" :key="i" size="small" class="cursor-pointer">
                                      <el-icon class="mr-1">
                                        <ElIconDocument />
                                      </el-icon>
                                      {{ file.name }}
                                    </el-tag>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-card>
                      <!-- 处理意见 -->
                      <el-card v-if="hasApprovalPermission" shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          处理意见
                        </h3>
                        <el-form label-width="120px">
                          <el-form-item label="处理结果">
                            <el-radio-group v-model="approvalResult">
                              <el-radio label="pass">
                                通过
                              </el-radio>
                              <el-radio label="conditional">
                                有条件通过
                              </el-radio>
                              <el-radio label="reject">
                                不通过
                              </el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="处理意见">
                            <el-input
                              v-model="approvalComment"
                              type="textarea"
                              :rows="4"
                              placeholder="请输入处理意见"
                            />
                          </el-form-item>
                          <el-form-item>
                            <div class="flex justify-end space-x-3">
                              <el-button type="primary" class="!rounded-button whitespace-nowrap">
                                提交
                              </el-button>
                              <el-button class="!rounded-button whitespace-nowrap">
                                保存草稿
                              </el-button>
                            </div>
                          </el-form-item>
                        </el-form>
                      </el-card>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="相关文档" name="documents">
                    <div class="space-y-4">
                      <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-lg font-bold">
                          相关文档
                        </h3>
                        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showUploadDialog = true">
                          <el-icon class="mr-1">
                            <ElIconUpload />
                          </el-icon>
                          上传文档
                        </el-button>
                      </div>
                      <el-table :data="documentList" border class="w-full">
                        <el-table-column prop="name" label="文档名称" width="200" />
                        <el-table-column prop="type" label="文档类型" width="120">
                          <template #default="{ row }">
                            <el-tag :type="row.type === '审查申请' ? '' : row.type === '审查材料' ? 'info' : row.type === '测试报告' ? 'warning' : row.type === '合规证明' ? 'success' : 'danger'">
                              {{ row.type }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="uploadTime" label="上传时间" width="180" />
                        <el-table-column prop="uploader" label="上传人" width="120" />
                        <el-table-column label="操作" width="150">
                          <template #default>
                            <el-button type="text" size="small">
                              查看
                            </el-button>
                            <el-button type="text" size="small">
                              下载
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>

                    <el-dialog v-model="showUploadDialog" title="上传文档" width="500px">
                      <el-upload
                        drag
                        action="https://jsonplaceholder.typicode.com/posts/"
                        multiple
                        :limit="5"
                        class="mb-4"
                      >
                        <el-icon class="el-icon--upload">
                          <ElIconUpload />
                        </el-icon>
                        <div class="el-upload__text">
                          将文件拖到此处，或<em>点击上传</em>
                        </div>
                        <template #tip>
                          <div class="el-upload__tip">
                            支持上传PDF、Word、Excel文件，单个文件不超过10MB
                          </div>
                        </template>
                      </el-upload>

                      <el-form label-width="80px">
                        <el-form-item label="文档类型">
                          <el-select v-model="uploadDocumentType" placeholder="请选择文档类型" class="w-full">
                            <el-option label="审查申请" value="审查申请" />
                            <el-option label="审查材料" value="审查材料" />
                            <el-option label="测试报告" value="测试报告" />
                            <el-option label="合规证明" value="合规证明" />
                            <el-option label="法律意见书" value="法律意见书" />
                            <el-option label="审批表单" value="审批表单" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="文档描述">
                          <el-input
                            v-model="uploadDocumentDesc"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入文档描述"
                          />
                        </el-form-item>
                        <el-form-item label="权限设置">
                          <el-checkbox-group v-model="uploadDocumentPermissions">
                            <el-checkbox label="审查人员可见" />
                            <el-checkbox label="申请人可见" />
                            <el-checkbox label="审批人员可见" />
                          </el-checkbox-group>
                        </el-form-item>
                      </el-form>

                      <template #footer>
                        <div class="dialog-footer">
                          <el-button @click="showUploadDialog = false">
                            取消
                          </el-button>
                          <el-button type="primary" @click="handleUploadConfirm">
                            上传
                          </el-button>
                        </div>
                      </template>
                    </el-dialog>
                  </el-tab-pane>
                </el-tabs>
              </el-card>
              <!-- 审批操作区 -->
              <el-card shadow="hover" class="!rounded-lg">
                <h3 class="mb-4 text-lg font-bold">
                  审批操作
                </h3>
                <el-form label-width="120px">
                  <el-form-item label="审批结论">
                    <el-radio-group v-model="approvalResult">
                      <el-radio label="pass">
                        通过
                      </el-radio>
                      <el-radio label="conditional">
                        有条件通过
                      </el-radio>
                      <el-radio label="reject">
                        不通过
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="审批意见">
                    <el-input
                      v-model="approvalComment"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入审批意见"
                    />
                  </el-form-item>
                  <el-form-item label="修改建议">
                    <el-input
                      v-model="modificationSuggestion"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入修改建议（如有）"
                    />
                  </el-form-item>
                  <el-form-item label="附件上传">
                    <el-upload
                      action="https://jsonplaceholder.typicode.com/posts/"
                      multiple
                      :limit="3"
                    >
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        点击上传
                      </el-button>
                      <template #tip>
                        <div class="el-upload__tip">
                          支持上传PDF、Word、Excel文件，单个文件不超过10MB
                        </div>
                      </template>
                    </el-upload>
                  </el-form-item>
                  <el-form-item label="知会人员">
                    <el-select
                      v-model="notifyPersons"
                      multiple
                      placeholder="请选择需要知会的人员"
                      class="w-full"
                    >
                      <el-option label="王五（技术部）" value="wangwu" />
                      <el-option label="赵六（质量部）" value="zhaoliu" />
                      <el-option label="钱七（法务部）" value="qianqi" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="保密级别">
                    <el-select v-model="confidentialLevel" placeholder="请选择保密级别" class="w-full">
                      <el-option label="普通" value="normal" />
                      <el-option label="内部" value="internal" />
                      <el-option label="机密" value="confidential" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <div class="flex justify-end space-x-3">
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        提交
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        保存草稿
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap border-orange-200 bg-orange-100 text-orange-500">
                        转交
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        取消
                      </el-button>
                    </div>
                  </el-form-item>
                </el-form>
              </el-card>
            </div>
            <!-- 右侧信息区 -->
            <div class="col-span-1 space-y-6">
              <!-- 审查信息摘要 -->
              <el-card shadow="hover" class="!rounded-lg">
                <h3 class="mb-4 text-lg font-bold">
                  审查信息
                </h3>
                <div class="mb-4 flex items-center">
                  <el-icon class="mr-3 text-2xl text-blue-500">
                    <ElIconDocument />
                  </el-icon>
                  <div>
                    <p class="font-medium">
                      安全合规审查
                    </p>
                    <p class="text-sm text-gray-500">
                      产品安全评估
                    </p>
                  </div>
                </div>
                <div class="mb-4 flex items-center justify-between">
                  <el-tag type="warning">
                    重要
                  </el-tag>
                  <div class="text-red-500 font-medium">
                    <el-icon class="mr-1">
                      <ElIconAlarmClock />
                    </el-icon>
                    剩余2天
                  </div>
                </div>
                <div class="border-t pt-4">
                  <div class="flex items-center">
                    <el-avatar :size="40" src="https://ai-public.mastergo.com/ai/img_res/02d5f037514bf1788125552caab7e5f2.jpg" />
                    <div class="ml-3">
                      <p class="font-medium">
                        张三
                      </p>
                      <p class="text-sm text-gray-500">
                        产品部 | 高级产品经理
                      </p>
                    </div>
                  </div>
                </div>
              </el-card>
              <!-- 相关法规与标准 -->
              <el-card shadow="hover" class="!rounded-lg">
                <div class="mb-4 flex items-center justify-between">
                  <h3 class="text-lg font-bold">
                    相关法规与标准
                  </h3>
                  <el-button type="text" size="small">
                    查看更多
                  </el-button>
                </div>
                <ul class="space-y-3">
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-gray-400">
                      <ElIconDocument />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        GB/T 22239-2019
                      </p>
                      <p class="text-sm text-gray-500">
                        信息安全技术 网络安全等级保护基本要求
                      </p>
                    </div>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-gray-400">
                      <ElIconDocument />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        GB/T 25069-2020
                      </p>
                      <p class="text-sm text-gray-500">
                        信息安全技术 术语
                      </p>
                    </div>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-gray-400">
                      <ElIconDocument />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        ISO/IEC 27001:2022
                      </p>
                      <p class="text-sm text-gray-500">
                        信息安全管理体系要求
                      </p>
                    </div>
                  </li>
                </ul>
              </el-card>
              <!-- 历史审查记录 -->
              <el-card shadow="hover" class="!rounded-lg">
                <div class="mb-4 flex items-center justify-between">
                  <h3 class="text-lg font-bold">
                    历史审查
                  </h3>
                  <el-button type="text" size="small">
                    查看全部
                  </el-button>
                </div>
                <ul class="space-y-4">
                  <li>
                    <p class="font-medium">
                      XX智能网关安全审查
                    </p>
                    <div class="mt-1 flex items-center justify-between">
                      <span class="text-sm text-gray-500">2024-03-15</span>
                      <el-tag type="success" size="small">
                        已通过
                      </el-tag>
                    </div>
                  </li>
                  <li>
                    <p class="font-medium">
                      YY数据平台合规审查
                    </p>
                    <div class="mt-1 flex items-center justify-between">
                      <span class="text-sm text-gray-500">2024-02-28</span>
                      <el-tag type="success" size="small">
                        已通过
                      </el-tag>
                    </div>
                  </li>
                  <li>
                    <p class="font-medium">
                      ZZ管理系统安全评估
                    </p>
                    <div class="mt-1 flex items-center justify-between">
                      <span class="text-sm text-gray-500">2024-01-10</span>
                      <el-tag type="danger" size="small">
                        不通过
                      </el-tag>
                    </div>
                  </li>
                </ul>
              </el-card>
              <!-- 快捷操作 -->
              <el-card shadow="hover" class="!rounded-lg">
                <h3 class="mb-4 text-lg font-bold">
                  快捷操作
                </h3>
                <div class="grid grid-cols-2 gap-3">
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <ElIconEdit />
                    </el-icon>
                    添加意见
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <ElIconUser />
                    </el-icon>
                    分配审查人
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <ElIconBell />
                    </el-icon>
                    设置通知
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <ElIconStar />
                    </el-icon>
                    标记关注
                  </el-button>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

  <style scoped>
  .el-menu {
  --el-menu-bg-color: transparent;
  --el-menu-hover-bg-color: rgba(255, 255, 255, 0.05);
  --el-menu-active-color: #ffffff;
  }
  .el-menu-item.is-active {
  background-color: rgba(255, 255, 255, 0.1) !important;
  font-weight: bold;
  border-left: 4px solid #1890ff;
  }
  .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
  }
  .el-sub-menu__title:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
  }
  .el-card {
  --el-card-border-color: #ebeef5;
  --el-card-border-radius: 8px;
  }
  .el-tabs {
  --el-tabs-header-height: 48px;
  }
  .el-tabs__item {
  font-weight: 500;
  }
  .el-tabs__item.is-active {
  color: #1890ff;
  font-weight: 600;
  }
  .el-tabs__active-bar {
  background-color: #1890ff;
  height: 3px;
  }
  .el-form-item {
  margin-bottom: 20px;
  }
  .el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 7px;
  }
  .el-breadcrumb {
  font-size: 14px;
  }
  .el-breadcrumb :deep(.el-breadcrumb__inner) {
  font-weight: normal;
  }
  .el-breadcrumb :deep(.el-breadcrumb__inner.is-link) {
  color: #606266;
  }
  .el-breadcrumb :deep(.el-breadcrumb__inner.is-link:hover) {
  color: #1890ff;
  }
  .el-breadcrumb :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #303133;
  font-weight: 500;
  }
  </style>
