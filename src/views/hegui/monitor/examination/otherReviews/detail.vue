<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  AlarmClock as ElIconAlarmClock,
  Bell as ElIconBell,
  Check as ElIconCheck,
  Document as ElIconDocument,
  Edit as ElIconEdit,
  Star as ElIconStar,
  Upload as ElIconUpload,
  User as ElIconUser,
  Warning as ElIconWarning,
  Promotion,
  VideoPlay,
} from '@element-plus/icons-vue'
import otherApi from '@/api/review/other'
import decisionApi from '@/api/review/decision'
import InitiateDialog from '@/components/initiate/index.vue'
import OtherResultDialog from '@/components/OtherResultDialog/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'

// 路由
const route = useRoute()
const router = useRouter()

// 加载状态
const loading = ref(false)

// 详情数据
const detailData = ref(null)

const activeMenu = ref('1-2')
const activeTab = ref('content')
const approvalResult = ref('')
const approvalComment = ref('')
const modificationSuggestion = ref('')
const notifyPersons = ref([])
const confidentialLevel = ref('')
const objectAttributes = ref([])
const materialList = ref([])
const complianceResult = ref('')
const complianceComment = ref('')
const complianceSuggestion = ref('')
const checklist = ref([])
const regulations = ref([])
const approvalSteps = ref([])
const approvalRecords = ref([])

// 获取详情数据
async function getDetailData() {
  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少审查ID参数')
    router.back()
    return
  }

  try {
    loading.value = true
    // 使用数据库主键 ID 调用详情接口
    const response = await otherApi.supplementalReview(null, { id }, 'info')
    if (response) {
      detailData.value = response

      // 解析关键属性
      if (detailData.value.keyAttribute) {
        try {
          if (typeof detailData.value.keyAttribute === 'string') {
            detailData.value.keyAttribute = JSON.parse(detailData.value.keyAttribute)
          }
        }
        catch (e) {
          console.warn('解析keyAttribute失败:', e)
          detailData.value.keyAttribute = []
        }
      }
      else {
        detailData.value.keyAttribute = []
      }
      // 从接口数据中填充其他详情数据
    }
  }
  catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
  finally {
    loading.value = false
  }
}

// 格式化日期
function formatDate(dateObj) {
  if (!dateObj || !dateObj.seconds) { return '-' }
  const date = new Date(dateObj.seconds * 1000)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 格式化级别显示
function formatLevel(level) {
  const levelMap = {
    GENERAL: '一般',
    IMPORTANT: '重要',
    CRITICAL: '关键',
  }
  return levelMap[level] || level
}

// 格式化状态显示
function formatStatus(status) {
  const statusMap = {
    MODIFY: '需修改',
    PENDING: '待审查',
    PUBLISHED: '发布',
    REVIEWING: '审核中',
    REVOKE: '已撤回',
  }
  return statusMap[status] || status
}

// 格式化通知方式
function formatNoticeMethod(method) {
  const methodMap = {
    SYSTEM_MESSAGE: '系统消息',
    EMAIL: '邮件通知',
    SMS: '短信提醒',
    OTHER: '其他方式',
  }
  return methodMap[method] || method
}

// 格式化文件大小
function formatFileSize(size) {
  if (size === null || size === undefined || size === '' || Number.isNaN(Number(size))) {
    return ''
  }

  const numSize = Number(size)
  if (numSize < 0) {
    return ''
  }

  if (numSize < 1024) {
    return `${numSize} B`
  }
  if (numSize < 1024 * 1024) {
    return `${(numSize / 1024).toFixed(2)} KB`
  }
  return `${(numSize / 1024 / 1024).toFixed(2)} MB`
}

// 返回上一页
function goBack() {
  router.back()
}

// 编辑审查
function editReview() {
  if (!detailData.value?.id) {
    ElMessage.error('缺少审查ID参数')
    return
  }
  router.push({
    path: '/monitor/examination/ohter/addEdit',
    query: { id: detailData.value.id },
  })
}

// 处理上传确认
function handleUploadConfirm() {
  // 这里可以添加上传逻辑
  showUploadDialog.value = false
  ElMessage.success('文档上传成功')
}

// 检查审核权限
async function checkAuditPermission() {
  if (!detailData.value.id) {
    return false
  }

  try {
    const res = await decisionApi.getComplianceProcess({
      objectId: detailData.value.id,
      reviewType: 'SUPPLEMENTAL',
    })
    // 根据接口返回的isAudit字段判断审核权限
    return res
  }
  catch (err) {
    // console.error('检查审核权限失败:', err)
    return false
  }
}

// 开始审查
function startReview() {
  ElMessage.info('开始审查功能待实现')
}

// 提交审查
async function submitReview() {
  if (!detailData.value.id) {
    ElMessage.error('参数错误')
    return
  }

  // 根据complianceReview字段判断跳转页面
  if (detailData.value.complianceReview?.id === null) {
    // complianceReview为null，显示审查发起弹窗
    showInitiateDialog.value = true
  }
  else {
    // complianceReview不为null，先检查审核权限
    const hasAuditPermission = await checkAuditPermission()
    if (!hasAuditPermission) {
      ElMessage.error('暂无审核权限')
      return
    }

    if (hasAuditPermission.isSubmit) {
      // 显示审查发起弹窗
      showInitiateDialog.value = true
      return
    }
    else {
      if (!hasAuditPermission.isAudit) {
        ElMessage.error('暂无审核权限')
        return
      }
    }

    // 显示审查结果弹窗
    ElMessageBox.confirm(
      '确定要进行合规审查吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    ).then(() => {
      showOtherResultDialog.value = true
    }).catch(() => {
      // 用户点击了取消
      // 不进行智能审查操作
    })
  }
}

// 处理审查发起弹窗成功事件
function handleInitiateSuccess() {
  // 刷新页面数据
  getDetailData()
}

// 审查记录
function reviewRecord() {
  if (!detailData.value.id) {
    ElMessage.error('缺少审查ID参数')
    return
  }
  showRecordsDialog.value = true
  getReviewRecords()
}

// 获取审查记录
async function getReviewRecords() {
  try {
    recordsLoading.value = true
    const response = await decisionApi.queryAllOtherReviews({
      page: 0,
      size: 20,
      reviewId: detailData.value.id,
      reviewType: 'SUPPLEMENTAL',
    })
    reviewRecords.value = response
  }
  catch (error) {
    console.error('获取审查记录失败:', error)
    ElMessage.error('获取审查记录失败')
  }
  finally {
    recordsLoading.value = false
  }
}

// 获取审查状态标签类型
function getStatusType(status: string) {
  const statusMap: Record<string, string> = {
    PENDING: 'warning',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    REJECTED: 'danger',
  }
  return statusMap[status] || 'info'
}

// 获取审查状态文本
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    PENDING: '待审查',
    IN_PROGRESS: '审查中',
    COMPLETED: '已完成',
    REJECTED: '已拒绝',
  }
  return statusMap[status] || status
}

// 关闭审查记录弹窗
function handleRecordsDialogClose() {
  showRecordsDialog.value = false
  reviewRecords.value = []
}

// 页面初始化
onMounted(() => {
  getDetailData()
})
const hasApprovalPermission = ref(true)
const showUploadDialog = ref(false)
const uploadDocumentType = ref('')
const uploadDocumentDesc = ref('')
const uploadDocumentPermissions = ref([])
const documentList = ref([])
const riskList = ref([])

// 审查发起弹窗相关数据
const showInitiateDialog = ref(false)

// 审查结果弹窗相关数据
const showOtherResultDialog = ref(false)

// 审查记录弹窗相关数据
const showRecordsDialog = ref(false)
const reviewRecords = ref<any[]>([])
const recordsLoading = ref(false)
</script>

<template>
  <div class="absolute-container">
    <pageHeader>
      <template #content>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <h1 class="mr-4 text-2xl c-[#000000] font-bold">
              {{ detailData?.name || '审查详情' }} - {{ detailData?.reviewCode || '' }}
            </h1>
            <el-tag :type="detailData?.status === 'REVIEWING' ? 'primary' : 'info'" size="large">
              {{ formatStatus(detailData?.status) }}
            </el-tag>
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button" @click="startReview">
              <el-icon class="mr-2">
                <VideoPlay />
              </el-icon>开始审查
            </el-button>
            <el-button plain class="!rounded-button" @click="editReview">
              <el-icon class="mr-2">
                <ElIconEdit />
              </el-icon>编辑
            </el-button>
            <el-button plain class="!rounded-button" @click="submitReview">
              <el-icon class="mr-2">
                <Promotion />
              </el-icon>提交审查
            </el-button>
            <el-button plain class="!rounded-button" @click="reviewRecord">
              <el-icon class="mr-2">
                <ElIconDocument />
              </el-icon>审查记录
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              查看流程
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              导出审核报告
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="goBack">
              <el-icon class="mr-2">
                <ArrowLeft />
              </el-icon>返回
            </el-button>
          </div>
        </div>
      </template>
    </pageHeader>
    <PageMain v-loading="loading" style="background-color: transparent;">
      <!-- 主内容区 -->
      <div class="flex">
        <!-- 页面内容 -->
        <div class="flex-1 overflow-y-auto">
          <!-- 页面标题和操作按钮 -->

          <div class="grid grid-cols-3 gap-6">
            <!-- 左侧内容区 -->
            <div class="col-span-2 space-y-6">
              <!-- 基本信息卡片 -->
              <el-card shadow="hover" class="!rounded-lg">
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <p class="text-gray-500">
                      审查编号
                    </p>
                    <p class="font-medium">
                      {{ detailData?.reviewCode || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      审查名称
                    </p>
                    <p class="font-medium">
                      {{ detailData?.name || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      审查对象
                    </p>
                    <p class="font-medium">
                      {{ detailData?.reviewObject || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      发起部门
                    </p>
                    <p class="font-medium">
                      {{ detailData?.department || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      审查人员
                    </p>
                    <p class="font-medium">
                      {{ detailData?.auditBy || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      创建时间
                    </p>
                    <p class="font-medium">
                      {{ formatDate(detailData?.createdAt) }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      更新时间
                    </p>
                    <p class="font-medium">
                      {{ formatDate(detailData?.updatedAt) }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      审查级别
                    </p>
                    <el-tag :type="detailData?.level === 'CRITICAL' ? 'danger' : detailData?.level === 'IMPORTANT' ? 'warning' : 'info'">
                      {{ formatLevel(detailData?.level) }}
                    </el-tag>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      截止日期
                    </p>
                    <p class="font-medium">
                      {{ detailData?.deadlineDate || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      状态
                    </p>
                    <el-tag :type="detailData?.status === 'REVIEWING' ? 'primary' : 'info'">
                      {{ formatStatus(detailData?.status) }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
              <!-- 标签页 -->
              <el-card shadow="hover" class="!rounded-lg">
                <el-tabs v-model="activeTab">
                  <el-tab-pane label="审查内容" name="content">
                    <div class="space-y-6">
                      <!-- 审查对象信息 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          审查对象
                        </h3>
                        <p class="mb-2 font-medium">
                          {{ detailData?.reviewObject || '-' }}
                        </p>
                        <p class="mb-4 text-gray-700">
                          {{ detailData?.explain || '-' }}
                        </p>
                        <el-table :data="detailData?.keyAttribute || []" border class="w-full">
                          <el-table-column prop="name" label="属性名称" width="180" />
                          <el-table-column prop="value" label="属性值" />
                          <el-table-column prop="explain" label="说明" />
                        </el-table>
                      </div>
                      <!-- 审查范围与目标 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          审查范围与目标
                        </h3>
                        <div class="mb-4">
                          <h4 class="mb-2 text-gray-600 font-medium">
                            审查范围
                          </h4>
                          <p class="mb-4 text-gray-700">
                            {{ detailData?.reviewRange || '-' }}
                          </p>
                        </div>
                        <div class="mb-4">
                          <h4 class="mb-2 text-gray-600 font-medium">
                            审查目标
                          </h4>
                          <p class="mb-4 text-gray-700">
                            {{ detailData?.reviewTarget || '-' }}
                          </p>
                        </div>
                        <div class="rounded bg-gray-50 p-4">
                          <h4 class="mb-2 font-medium">
                            审查依据
                          </h4>
                          <p class="text-gray-700">
                            {{ detailData?.reviewAccording || '-' }}
                          </p>
                        </div>
                      </div>
                      <!-- 审查附件 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          审查附件
                        </h3>
                        <UploadMbb
                          :model-value="detailData?.supplementalAttachments || []"
                          :readonly="true"
                          :use-file-path="true"
                          category-name="attachment"
                          tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 20MB"
                        />
                      </div>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="风险评估" name="risk">
                    <div class="space-y-6">
                      <!-- 自动风险评估 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          自动风险评估
                        </h3>
                        <div class="rounded bg-gray-50 p-4">
                          <div class="text-gray-500">
                            评估结果将显示在此处
                          </div>
                        </div>
                      </div>
                      <!-- 风险点管理 -->
                      <div>
                        <h3 class="mb-4 text-lg font-bold">
                          风险点管理
                        </h3>
                        <div v-if="detailData?.supplementalReviewRisks?.length > 0" class="space-y-4">
                          <div v-for="(risk, index) in detailData.supplementalReviewRisks" :key="index" class="border rounded-lg p-6 space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                              <div>
                                <p class="mb-1 text-gray-500">
                                  风险自评
                                </p>
                                <p class="font-medium">
                                  {{ risk.riskSelf || '-' }}
                                </p>
                              </div>
                              <div>
                                <p class="mb-1 text-gray-500">
                                  风险类型
                                </p>
                                <el-tag size="small">
                                  {{ risk.riskType === 'LEGAL' ? '法律风险' : risk.riskType === 'COMPLIANCE' ? '合规风险' : risk.riskType === 'TECHNICAL' ? '技术风险' : risk.riskType === 'BUSINESS' ? '商业风险' : risk.riskType === 'REPUTATION' ? '声誉风险' : '其他' }}
                                </el-tag>
                              </div>
                              <div>
                                <p class="mb-1 text-gray-500">
                                  风险等级
                                </p>
                                <el-tag :type="risk.level === 'HIGH' ? 'danger' : risk.level === 'MEDIUM' ? 'warning' : 'info'" size="small">
                                  {{ risk.level === 'HIGH' ? '高' : risk.level === 'MEDIUM' ? '中' : '低' }}
                                </el-tag>
                              </div>
                              <div>
                                <p class="mb-1 text-gray-500">
                                  影响范围
                                </p>
                                <el-tag size="small">
                                  {{ risk.effectRange === 'FINANCE' ? '财务' : risk.effectRange === 'OPERATION' ? '运营' : risk.effectRange === 'CUSTOMER' ? '客户' : '声誉' }}
                                </el-tag>
                              </div>
                            </div>
                            <div>
                              <p class="mb-1 text-gray-500">
                                风险评估
                              </p>
                              <p class="text-gray-700">
                                {{ risk.riskAssessment || '-' }}
                              </p>
                            </div>
                            <div>
                              <p class="mb-1 text-gray-500">
                                风险描述
                              </p>
                              <p class="text-gray-700">
                                {{ risk.riskDesc || '-' }}
                              </p>
                            </div>
                            <div>
                              <p class="mb-1 text-gray-500">
                                应对措施
                              </p>
                              <p class="text-gray-700">
                                {{ risk.countermeasures || '-' }}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div v-else class="py-8 text-center text-gray-500">
                          暂无风险点数据
                        </div>
                      </div>
                      <!-- AI风险分析 -->
                      <div v-if="false">
                        <h3 class="mb-4 text-lg font-bold">
                          AI风险洞察
                        </h3>
                        <el-card shadow="never" class="!border !border-blue-100 !bg-blue-50">
                          <div class="flex">
                            <el-icon class="mr-3 text-2xl text-blue-500">
                              <ElIconWarning />
                            </el-icon>
                            <div>
                              <p class="mb-2 font-medium">
                                AI识别到3个关键风险点：
                              </p>
                              <ul class="list-disc pl-5 space-y-1">
                                <li>数据传输未使用端到端加密，存在中间人攻击风险</li>
                                <li>用户权限分级不足，可能导致权限提升漏洞</li>
                                <li>日志记录不完整，影响安全事件追溯</li>
                              </ul>
                              <p class="mt-3 text-sm text-gray-600">
                                与行业平均水平相比，本产品的风险评分高于75%的同类产品，建议优先处理高风险项。
                              </p>
                            </div>
                          </div>
                        </el-card>
                      </div>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="合规审查" name="compliance">
                    <div class="space-y-6">
                      <!-- 特殊要求 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          特殊要求
                        </h3>
                        <div v-if="detailData?.supplementalReviewSpecials && detailData.supplementalReviewSpecials.length > 0" class="space-y-4">
                          <div v-for="(special, index) in detailData.supplementalReviewSpecials" :key="index" class="border border-gray-200 rounded-lg p-6 space-y-4">
                            <div>
                              <p class="mb-1 text-gray-500">
                                关注重点
                              </p>
                              <p class="text-gray-700">
                                {{ special.focus || '-' }}
                              </p>
                            </div>
                            <div>
                              <p class="mb-1 text-gray-500">
                                特殊审查要求
                              </p>
                              <el-tag size="small">
                                {{ special.reviewRequirement === 'LEGAL_COMPLIANCE' ? '法律合规性审查' : special.reviewRequirement === 'TECHNICAL_SECURITY' ? '技术安全性审查' : special.reviewRequirement === 'BUSINESS_RISK' ? '商业风险评估' : special.reviewRequirement === 'PRIVACY_PROTECTION' ? '隐私保护审查' : '其他特殊要求' }}
                              </el-tag>
                            </div>
                            <div>
                              <p class="mb-1 text-gray-500">
                                特殊说明
                              </p>
                              <p class="text-gray-700">
                                {{ special.explain || '-' }}
                              </p>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                              <div>
                                <p class="mb-1 text-gray-500">
                                  相关方名称
                                </p>
                                <p class="text-gray-700">
                                  {{ special.relatedName || '-' }}
                                </p>
                              </div>
                              <div>
                                <p class="mb-1 text-gray-500">
                                  相关方类型
                                </p>
                                <el-tag size="small">
                                  {{ special.relatedType === 'INTERNAL_DEPT' ? '内部部门' : special.relatedType === 'EXTERNAL_ORG' ? '外部机构' : special.relatedType === 'PARTNER' ? '合作伙伴' : special.relatedType === 'REGULATOR' ? '监管机构' : '-' }}
                                </el-tag>
                              </div>
                            </div>
                            <div>
                              <p class="mb-1 text-gray-500">
                                关系说明
                              </p>
                              <p class="text-gray-700">
                                {{ special.relatedExplain || '-' }}
                              </p>
                            </div>
                          </div>
                        </div>
                      </el-card>
                      <!-- 审查结论 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          审查结论
                        </h3>
                        <el-form label-width="120px">
                          <el-form-item label="审查结论">
                            <el-radio-group v-model="complianceResult">
                              <el-radio label="pass">
                                通过
                              </el-radio>
                              <el-radio label="conditional">
                                有条件通过
                              </el-radio>
                              <el-radio label="reject">
                                不通过
                              </el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="审查意见">
                            <el-input
                              v-model="complianceComment"
                              type="textarea"
                              :rows="4"
                              placeholder="请输入审查意见"
                            />
                          </el-form-item>
                        </el-form>
                      </el-card>
                      <!-- 审查清单 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          审查清单
                        </h3>
                        <el-table :data="checklist" border class="w-full">
                          <el-table-column prop="item" label="检查项" width="300" />
                          <el-table-column prop="status" label="合规状态" width="120">
                            <template #default="{ row }">
                              <el-tag :type="row.status === '合规' ? 'success' : row.status === '不合规' ? 'danger' : 'info'">
                                {{ row.status }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column prop="remark" label="备注" />
                        </el-table>
                      </el-card>
                      <!-- 处理建议 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          处理建议
                        </h3>
                        <el-input
                          v-model="complianceSuggestion"
                          type="textarea"
                          :rows="4"
                          placeholder="请输入合规性修改建议"
                        />
                        <div class="mt-4">
                          <el-button type="primary" class="!rounded-button whitespace-nowrap">
                            引用法规依据
                          </el-button>
                        </div>
                      </el-card>
                      <!-- 法规依据 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <div class="mb-4 flex items-center justify-between">
                          <h3 class="text-lg font-bold">
                            法规依据
                          </h3>
                          <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap">
                            添加依据
                          </el-button>
                        </div>
                        <el-table :data="regulations" border class="w-full">
                          <el-table-column prop="name" label="法规名称" width="300" />
                          <el-table-column prop="clause" label="具体条款" width="150" />
                          <el-table-column prop="description" label="适用说明" />
                          <el-table-column label="操作" width="120">
                            <template #default>
                              <el-button type="text" size="small">
                                编辑
                              </el-button>
                              <el-button type="text" size="small" class="text-red-500">
                                删除
                              </el-button>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-card>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="审批记录" name="approval">
                    <div class="space-y-6">
                      <!-- 审批流程图 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          审批流程
                        </h3>
                        <div class="flex items-center justify-between px-10 py-4">
                          <div v-for="(step, index) in approvalSteps" :key="index" class="text-center">
                            <div class="relative">
                              <div
                                class="mx-auto h-16 w-16 flex items-center justify-center rounded-full"
                                :class="{
                                  'bg-blue-100 text-blue-600': step.status === 'current',
                                  'bg-green-100 text-green-600': step.status === 'completed',
                                  'bg-gray-100 text-gray-400': step.status === 'pending',
                                }"
                              >
                                <el-icon v-if="step.status === 'completed'" class="text-2xl">
                                  <ElIconCheck />
                                </el-icon>
                                <el-icon v-else-if="step.status === 'current'" class="text-2xl">
                                  <ElIconEdit />
                                </el-icon>
                                <span v-else class="text-xl font-bold">{{ index + 1 }}</span>
                              </div>
                              <div
                                v-if="index < approvalSteps.length - 1" class="absolute right-0 top-1/2 h-1 w-8 translate-x-1/2 transform -translate-y-1/2"
                                :class="{
                                  'bg-blue-200': step.status === 'completed',
                                  'bg-gray-200': step.status !== 'completed',
                                }"
                              />
                            </div>
                            <p
                              class="mt-2 font-medium" :class="{
                                'text-blue-600': step.status === 'current',
                                'text-green-600': step.status === 'completed',
                                'text-gray-400': step.status === 'pending',
                              }"
                            >
                              {{ step.name }}
                            </p>
                            <p class="text-sm text-gray-500">
                              {{ step.role }}
                            </p>
                          </div>
                        </div>
                      </el-card>
                      <!-- 审批记录 -->
                      <el-card shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          审批记录
                        </h3>
                        <div class="space-y-4">
                          <div
                            v-for="(record, index) in approvalRecords" :key="index" class="border-l-2 pb-4 pl-4"
                            :class="{
                              'border-blue-500': index === 0,
                              'border-gray-200': index !== 0,
                            }"
                          >
                            <div class="flex items-start">
                              <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-600">
                                <el-icon><ElIconUser /></el-icon>
                              </div>
                              <div class="flex-1">
                                <div class="flex items-center justify-between">
                                  <p class="font-medium">
                                    {{ record.approver }}
                                  </p>
                                  <el-tag :type="record.result === '通过' ? 'success' : record.result === '有条件通过' ? 'warning' : 'danger'" size="small">
                                    {{ record.result }}
                                  </el-tag>
                                </div>
                                <p class="text-sm text-gray-500">
                                  {{ record.department }} • {{ record.time }}
                                </p>
                                <p class="mt-2 text-gray-700">
                                  {{ record.comment }}
                                </p>
                                <div v-if="record.attachments.length > 0" class="mt-2">
                                  <p class="mb-1 text-sm text-gray-500">
                                    附件：
                                  </p>
                                  <div class="flex flex-wrap gap-2">
                                    <el-tag v-for="(file, i) in record.attachments" :key="i" size="small" class="cursor-pointer">
                                      <el-icon class="mr-1">
                                        <ElIconDocument />
                                      </el-icon>
                                      {{ file.name }}
                                    </el-tag>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-card>
                      <!-- 处理意见 -->
                      <el-card v-if="hasApprovalPermission" shadow="never" class="!border !border-gray-200">
                        <h3 class="mb-4 text-lg font-bold">
                          处理意见
                        </h3>
                        <el-form label-width="120px">
                          <el-form-item label="处理结果">
                            <el-radio-group v-model="approvalResult">
                              <el-radio label="pass">
                                通过
                              </el-radio>
                              <el-radio label="conditional">
                                有条件通过
                              </el-radio>
                              <el-radio label="reject">
                                不通过
                              </el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="处理意见">
                            <el-input
                              v-model="approvalComment"
                              type="textarea"
                              :rows="4"
                              placeholder="请输入处理意见"
                            />
                          </el-form-item>
                          <el-form-item>
                            <div class="flex justify-end space-x-3">
                              <el-button type="primary" class="!rounded-button whitespace-nowrap">
                                提交
                              </el-button>
                              <el-button class="!rounded-button whitespace-nowrap">
                                保存草稿
                              </el-button>
                            </div>
                          </el-form-item>
                        </el-form>
                      </el-card>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="相关文档" name="documents">
                    <div class="space-y-4">
                      <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-lg font-bold">
                          相关文档
                        </h3>
                        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showUploadDialog = true">
                          <el-icon class="mr-1">
                            <ElIconUpload />
                          </el-icon>
                          上传文档
                        </el-button>
                      </div>
                      <el-table :data="documentList" border class="w-full">
                        <el-table-column prop="name" label="文档名称" width="200" />
                        <el-table-column prop="type" label="文档类型" width="120">
                          <template #default="{ row }">
                            <el-tag :type="row.type === '审查申请' ? '' : row.type === '审查材料' ? 'info' : row.type === '测试报告' ? 'warning' : row.type === '合规证明' ? 'success' : 'danger'">
                              {{ row.type }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="uploadTime" label="上传时间" width="180" />
                        <el-table-column prop="uploader" label="上传人" width="120" />
                        <el-table-column label="操作" width="150">
                          <template #default>
                            <el-button type="text" size="small">
                              查看
                            </el-button>
                            <el-button type="text" size="small">
                              下载
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>

                    <el-dialog v-model="showUploadDialog" title="上传文档" width="500px">
                      <el-upload
                        drag
                        action="https://jsonplaceholder.typicode.com/posts/"
                        multiple
                        :limit="5"
                        class="mb-4"
                      >
                        <el-icon class="el-icon--upload">
                          <ElIconUpload />
                        </el-icon>
                        <div class="el-upload__text">
                          将文件拖到此处，或<em>点击上传</em>
                        </div>
                        <template #tip>
                          <div class="el-upload__tip">
                            支持上传PDF、Word、Excel文件，单个文件不超过10MB
                          </div>
                        </template>
                      </el-upload>

                      <el-form label-width="80px">
                        <el-form-item label="文档类型">
                          <el-select v-model="uploadDocumentType" placeholder="请选择文档类型" class="w-full">
                            <el-option label="审查申请" value="审查申请" />
                            <el-option label="审查材料" value="审查材料" />
                            <el-option label="测试报告" value="测试报告" />
                            <el-option label="合规证明" value="合规证明" />
                            <el-option label="法律意见书" value="法律意见书" />
                            <el-option label="审批表单" value="审批表单" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="文档描述">
                          <el-input
                            v-model="uploadDocumentDesc"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入文档描述"
                          />
                        </el-form-item>
                        <el-form-item label="权限设置">
                          <el-checkbox-group v-model="uploadDocumentPermissions">
                            <el-checkbox label="审查人员可见" />
                            <el-checkbox label="申请人可见" />
                            <el-checkbox label="审批人员可见" />
                          </el-checkbox-group>
                        </el-form-item>
                      </el-form>

                      <template #footer>
                        <div class="dialog-footer">
                          <el-button @click="showUploadDialog = false">
                            取消
                          </el-button>
                          <el-button type="primary" @click="handleUploadConfirm">
                            上传
                          </el-button>
                        </div>
                      </template>
                    </el-dialog>
                  </el-tab-pane>
                </el-tabs>
              </el-card>

              <!-- 审批操作区 -->
              <el-card v-if="false" shadow="hover" class="!rounded-lg">
                <h3 class="mb-4 text-lg font-bold">
                  审批操作
                </h3>
                <el-form label-width="120px">
                  <el-form-item label="审批结论">
                    <el-radio-group v-model="approvalResult">
                      <el-radio label="pass">
                        通过
                      </el-radio>
                      <el-radio label="conditional">
                        有条件通过
                      </el-radio>
                      <el-radio label="reject">
                        不通过
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="审批意见">
                    <el-input
                      v-model="approvalComment"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入审批意见"
                    />
                  </el-form-item>
                  <el-form-item label="修改建议">
                    <el-input
                      v-model="modificationSuggestion"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入修改建议（如有）"
                    />
                  </el-form-item>
                  <el-form-item label="附件上传">
                    <el-upload
                      action="https://jsonplaceholder.typicode.com/posts/"
                      multiple
                      :limit="3"
                    >
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        点击上传
                      </el-button>
                      <template #tip>
                        <div class="el-upload__tip">
                          支持上传PDF、Word、Excel文件，单个文件不超过10MB
                        </div>
                      </template>
                    </el-upload>
                  </el-form-item>
                  <el-form-item label="知会人员">
                    <el-select
                      v-model="notifyPersons"
                      multiple
                      placeholder="请选择需要知会的人员"
                      class="w-full"
                    >
                      <el-option label="王五（技术部）" value="wangwu" />
                      <el-option label="赵六（质量部）" value="zhaoliu" />
                      <el-option label="钱七（法务部）" value="qianqi" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="保密级别">
                    <el-select v-model="confidentialLevel" placeholder="请选择保密级别" class="w-full">
                      <el-option label="普通" value="normal" />
                      <el-option label="内部" value="internal" />
                      <el-option label="机密" value="confidential" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <div class="flex justify-end space-x-3">
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        提交
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        保存草稿
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap border-orange-200 bg-orange-100 text-orange-500">
                        转交
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        取消
                      </el-button>
                    </div>
                  </el-form-item>
                </el-form>
              </el-card>
            </div>
            <!-- 右侧信息区 -->
            <div class="col-span-1 space-y-6">
              <!-- 审查信息摘要 -->
              <el-card shadow="hover" class="!rounded-lg">
                <h3 class="mb-4 text-lg font-bold">
                  审查信息
                </h3>
                <div class="mb-4 flex items-center">
                  <el-icon class="mr-3 text-2xl text-blue-500">
                    <ElIconDocument />
                  </el-icon>
                  <div>
                    <p class="font-medium">
                      {{ detailData?.name || '-' }}
                    </p>
                    <p class="text-sm text-gray-500">
                      {{ detailData?.reviewObject || '-' }}
                    </p>
                  </div>
                </div>
                <div class="mb-4 flex items-center justify-between">
                  <el-tag :type="detailData?.level === 'CRITICAL' ? 'danger' : detailData?.level === 'IMPORTANT' ? 'warning' : 'info'">
                    {{ formatLevel(detailData?.level) }}
                  </el-tag>
                  <div class="text-red-500 font-medium">
                    <el-icon class="mr-1">
                      <ElIconAlarmClock />
                    </el-icon>
                    截止：{{ detailData?.deadlineDate || '-' }}
                  </div>
                </div>
                <div class="border-t pt-4">
                  <div class="space-y-2">
                    <div class="flex justify-between">
                      <span class="text-gray-500">审查人员：</span>
                      <span class="font-medium">{{ detailData?.auditBy || '-' }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-500">发起部门：</span>
                      <span class="font-medium">{{ detailData?.department || '-' }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-500">通知方式：</span>
                      <span class="font-medium">{{ formatNoticeMethod(detailData?.noticeMethod) }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-500">通知对象：</span>
                      <span class="font-medium">{{ detailData?.noticeObject || '-' }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-500">重要状态通知：</span>
                      <span class="font-medium">{{ detailData?.statusNotice || '-' }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-500">开始提醒：</span>
                      <span class="font-medium">{{ detailData?.startRemind ? `提前${detailData.startRemind}天` : '-' }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-500">截止提醒：</span>
                      <span class="font-medium">{{ detailData?.deadlineRemind ? `提前${detailData.deadlineRemind}天` : '-' }}</span>
                    </div>
                    <div v-if="false" class="flex justify-between">
                      <span class="text-gray-500">补充审查规则：</span>
                      <span class="font-medium">{{ detailData?.supplementalReviewRule || '-' }}</span>
                    </div>
                  </div>
                </div>

                <!-- 特殊要求 -->
                <div v-if="false" class="mb-6 border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
                  <h3 class="mb-4 text-lg text-gray-800 font-semibold">
                    特殊要求
                  </h3>
                  <div class="space-y-4">
                    <div v-for="(special, index) in detailData.supplementalReviewSpecials" :key="index" class="border border-gray-200 rounded-lg p-4">
                      <div class="grid grid-cols-2 gap-4">
                        <div class="flex justify-between">
                          <span class="text-gray-500">要求类型：</span>
                          <span class="font-medium">{{ special.type || '-' }}</span>
                        </div>
                        <div class="flex justify-between">
                          <span class="text-gray-500">要求描述：</span>
                          <span class="font-medium">{{ special.description || '-' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 风险点管理 -->
                <div v-if="false" class="mb-6 border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
                  <h3 class="mb-4 text-lg text-gray-800 font-semibold">
                    风险点管理
                  </h3>
                  <div class="space-y-4">
                    <div v-for="(risk, index) in detailData.supplementalReviewRisks" :key="index" class="border border-gray-200 rounded-lg p-4">
                      <div class="grid grid-cols-2 gap-4">
                        <div class="flex justify-between">
                          <span class="text-gray-500">风险类型：</span>
                          <span class="font-medium">{{ risk.type || '-' }}</span>
                        </div>
                        <div class="flex justify-between">
                          <span class="text-gray-500">风险描述：</span>
                          <span class="font-medium">{{ risk.description || '-' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
              <!-- 相关法规与标准 -->
              <el-card v-if="false" shadow="hover" class="!rounded-lg">
                <div class="mb-4 flex items-center justify-between">
                  <h3 class="text-lg font-bold">
                    相关法规与标准
                  </h3>
                  <el-button type="text" size="small">
                    查看更多
                  </el-button>
                </div>
                <ul class="space-y-3">
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-gray-400">
                      <ElIconDocument />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        GB/T 22239-2019
                      </p>
                      <p class="text-sm text-gray-500">
                        信息安全技术 网络安全等级保护基本要求
                      </p>
                    </div>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-gray-400">
                      <ElIconDocument />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        GB/T 25069-2020
                      </p>
                      <p class="text-sm text-gray-500">
                        信息安全技术 术语
                      </p>
                    </div>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-gray-400">
                      <ElIconDocument />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        ISO/IEC 27001:2022
                      </p>
                      <p class="text-sm text-gray-500">
                        信息安全管理体系要求
                      </p>
                    </div>
                  </li>
                </ul>
              </el-card>
              <!-- 历史审查记录 -->
              <el-card v-if="false" shadow="hover" class="!rounded-lg">
                <div class="mb-4 flex items-center justify-between">
                  <h3 class="text-lg font-bold">
                    历史审查
                  </h3>
                  <el-button type="text" size="small">
                    查看全部
                  </el-button>
                </div>
                <ul class="space-y-4">
                  <li>
                    <p class="font-medium">
                      XX智能网关安全审查
                    </p>
                    <div class="mt-1 flex items-center justify-between">
                      <span class="text-sm text-gray-500">2024-03-15</span>
                      <el-tag type="success" size="small">
                        已通过
                      </el-tag>
                    </div>
                  </li>
                  <li>
                    <p class="font-medium">
                      YY数据平台合规审查
                    </p>
                    <div class="mt-1 flex items-center justify-between">
                      <span class="text-sm text-gray-500">2024-02-28</span>
                      <el-tag type="success" size="small">
                        已通过
                      </el-tag>
                    </div>
                  </li>
                  <li>
                    <p class="font-medium">
                      ZZ管理系统安全评估
                    </p>
                    <div class="mt-1 flex items-center justify-between">
                      <span class="text-sm text-gray-500">2024-01-10</span>
                      <el-tag type="danger" size="small">
                        不通过
                      </el-tag>
                    </div>
                  </li>
                </ul>
              </el-card>
              <!-- 快捷操作 -->
              <el-card shadow="hover" class="!rounded-lg">
                <h3 class="mb-4 text-lg font-bold">
                  快捷操作
                </h3>
                <div class="grid grid-cols-2 gap-3">
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <ElIconEdit />
                    </el-icon>
                    添加意见
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <ElIconUser />
                    </el-icon>
                    分配审查人
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <ElIconBell />
                    </el-icon>
                    设置通知
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <ElIconStar />
                    </el-icon>
                    标记关注
                  </el-button>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
    </PageMain>

    <!-- 审查发起弹窗 -->
    <InitiateDialog
      v-model:visible="showInitiateDialog"
      :object-id="detailData?.id"
      review-type="SUPPLEMENTAL"
      :compliance-review="detailData?.complianceReview"
      @success="handleInitiateSuccess"
    />

    <!-- 审查结果弹窗 -->
    <OtherResultDialog
      v-model:visible="showOtherResultDialog"
      :object-id="detailData?.id"
      review-type="SUPPLEMENTAL"
      :compliance-review="detailData?.complianceReview"
      @success="handleInitiateSuccess"
    />

    <!-- 审查记录弹窗 -->
    <el-dialog
      v-model="showRecordsDialog"
      title="审查记录"
      width="80%"
      :before-close="handleRecordsDialogClose"
    >
      <div class="records-content">
        <el-table
          :data="reviewRecords"
          :loading="recordsLoading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="reviewComment" label="审查意见" min-width="200" show-overflow-tooltip />
          <el-table-column prop="reviewerName" label="审查人" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdTime" label="创建时间" width="180" />
          <el-table-column prop="completedTime" label="完成时间" width="180" />
          <el-table-column prop="reviewRecord" label="审查记录" min-width="200" show-overflow-tooltip />
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleRecordsDialogClose">
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

  <style scoped>
  .el-menu {
  --el-menu-bg-color: transparent;
  --el-menu-hover-bg-color: rgba(255, 255, 255, 0.05);
  --el-menu-active-color: #ffffff;
  }
  .el-menu-item.is-active {
  background-color: rgba(255, 255, 255, 0.1) !important;
  font-weight: bold;
  border-left: 4px solid #1890ff;
  }
  .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
  }
  .el-sub-menu__title:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
  }
  .el-card {
  --el-card-border-color: #ebeef5;
  --el-card-border-radius: 8px;
  }
  .el-tabs {
  --el-tabs-header-height: 48px;
  }
  .el-tabs__item {
  font-weight: 500;
  }
  .el-tabs__item.is-active {
  color: #1890ff;
  font-weight: 600;
  }
  .el-tabs__active-bar {
  background-color: #1890ff;
  height: 3px;
  }
  .el-form-item {
  margin-bottom: 20px;
  }
  .el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 7px;
  }
  .el-breadcrumb {
  font-size: 14px;
  }
  .el-breadcrumb :deep(.el-breadcrumb__inner) {
  font-weight: normal;
  }
  .el-breadcrumb :deep(.el-breadcrumb__inner.is-link) {
  color: #606266;
  }
  .el-breadcrumb :deep(.el-breadcrumb__inner.is-link:hover) {
  color: #1890ff;
  }
  .el-breadcrumb :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #303133;
  font-weight: 500;
  }
  .records-content {
  max-height: 500px;
  overflow-y: auto;
  }
  .dialog-footer {
  text-align: right;
  }
  </style>
