---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 08-合同管理服务/其他审查管理

## GET 获取指定 ID 的 其他审查

GET /whiskerguardcontractservice/api/supplemental/reviews/id

描述：获取指定 ID 的 其他审查。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "",
  "name": "",
  "reviewObject": "",
  "level": "",
  "department": "",
  "auditBy": "",
  "explain": "",
  "deadlineDate": "",
  "status": "",
  "reviewRange": "",
  "reviewTarget": "",
  "reviewAccording": "",
  "keyAttribute": "",
  "material": "",
  "noticeMethod": "",
  "noticeObject": "",
  "statusNotice": "",
  "startRemind": 0,
  "deadlineRemind": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "supplementalAttachments": [
    {
      "id": 0,
      "supplementalId": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "uploadedBy": "",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewRisks": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "riskSelf": "",
      "riskType": "",
      "riskAssessment": "",
      "riskDesc": "",
      "level": "",
      "effectRange": "",
      "countermeasures": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewSpecials": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "focus": "",
      "reviewRequirement": "",
      "explain": "",
      "relatedName": "",
      "relatedType": "",
      "relatedExplain": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewRule": {
    "id": 0,
    "tenantId": 0,
    "supplementalId": 0,
    "processId": 0,
    "importantConfig": "",
    "matchRange": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntitySupplementalReviewDTO](#schemaresponseentitysupplementalreviewdto)|

# 数据模型

<h2 id="tocS_SupplementalAttachmentDTO">SupplementalAttachmentDTO</h2>

<a id="schemasupplementalattachmentdto"></a>
<a id="schema_SupplementalAttachmentDTO"></a>
<a id="tocSsupplementalattachmentdto"></a>
<a id="tocssupplementalattachmentdto"></a>

```json
{
  "id": 0,
  "supplementalId": 0,
  "fileName": "string",
  "filePath": "string",
  "fileType": "string",
  "fileSize": "string",
  "fileDesc": "string",
  "metadata": "string",
  "version": 1,
  "uploadedBy": "string",
  "uploadedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|supplementalId|integer(int64)|true|none||审查ID|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|fileType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|fileDesc|string|false|none||附件描述|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|uploadedBy|string|true|none||上传者|
|uploadedAt|[Instant](#schemainstant)|false|none||上传时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_SupplementalReviewSpecialDTO">SupplementalReviewSpecialDTO</h2>

<a id="schemasupplementalreviewspecialdto"></a>
<a id="schema_SupplementalReviewSpecialDTO"></a>
<a id="tocSsupplementalreviewspecialdto"></a>
<a id="tocssupplementalreviewspecialdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "supplementalId": 0,
  "focus": "string",
  "reviewRequirement": "LEGAL_COMPLIANCE",
  "explain": "string",
  "relatedName": "string",
  "relatedType": "string",
  "relatedExplain": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|supplementalId|integer(int64)|true|none||审查ID|
|focus|string|false|none||关注重点|
|reviewRequirement|string|false|none||特殊审查要求：法律合规性审查、技术安全性审查、商业风险评估、隐私保护审查、其他|
|explain|string|false|none||特殊说明|
|relatedName|string|false|none||相关方名称|
|relatedType|string|false|none||相关方类型|
|relatedExplain|string|false|none||关系说明|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|reviewRequirement|LEGAL_COMPLIANCE|
|reviewRequirement|TECHNICAL_SECURITY|
|reviewRequirement|COMMERCIAL_RISK|
|reviewRequirement|PRIVACY_PROTECTION|
|reviewRequirement|OTHER|

<h2 id="tocS_SupplementalReviewRuleDTO">SupplementalReviewRuleDTO</h2>

<a id="schemasupplementalreviewruledto"></a>
<a id="schema_SupplementalReviewRuleDTO"></a>
<a id="tocSsupplementalreviewruledto"></a>
<a id="tocssupplementalreviewruledto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "supplementalId": 0,
  "processId": 0,
  "importantConfig": "string",
  "matchRange": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|supplementalId|integer(int64)|true|none||审查ID|
|processId|integer(int64)|true|none||审查流程ID|
|importantConfig|string|false|none||审查重点配置，使用逗号分隔|
|matchRange|string|false|none||法规匹配范围，使用逗号分隔|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_SupplementalReviewRiskDTO">SupplementalReviewRiskDTO</h2>

<a id="schemasupplementalreviewriskdto"></a>
<a id="schema_SupplementalReviewRiskDTO"></a>
<a id="tocSsupplementalreviewriskdto"></a>
<a id="tocssupplementalreviewriskdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "supplementalId": 0,
  "riskSelf": "string",
  "riskType": "LAWS",
  "riskAssessment": "string",
  "riskDesc": "string",
  "level": "LOW",
  "effectRange": "FINANCE",
  "countermeasures": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|supplementalId|integer(int64)|true|none||审查ID|
|riskSelf|string|false|none||风险自评|
|riskType|string|false|none||风险类型：法律风险、财务风险、操作风险、声誉风险、其他|
|riskAssessment|string|false|none||风险评估|
|riskDesc|string|false|none||风险描述|
|level|string|false|none||风险等级：高、中、低|
|effectRange|string|false|none||影响范围：财务、运营、客户|
|countermeasures|string|false|none||应对措施|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|riskType|LAWS|
|riskType|FINANCE|
|riskType|OPERATION|
|riskType|REPUTATION|
|riskType|OTHER|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|effectRange|FINANCE|
|effectRange|OPERATION|
|effectRange|CUSTOMER|

<h2 id="tocS_ResponseEntitySupplementalReviewDTO">ResponseEntitySupplementalReviewDTO</h2>

<a id="schemaresponseentitysupplementalreviewdto"></a>
<a id="schema_ResponseEntitySupplementalReviewDTO"></a>
<a id="tocSresponseentitysupplementalreviewdto"></a>
<a id="tocsresponseentitysupplementalreviewdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "reviewObject": "string",
  "level": "GENERAL",
  "department": "string",
  "auditBy": "string",
  "explain": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "reviewRange": "string",
  "reviewTarget": "string",
  "reviewAccording": "string",
  "keyAttribute": "string",
  "material": "string",
  "noticeMethod": "SYSTEM_MESSAGE",
  "noticeObject": "string",
  "statusNotice": "REVIEW_START",
  "startRemind": 0,
  "deadlineRemind": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "supplementalAttachments": [
    {
      "id": 0,
      "supplementalId": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 1,
      "uploadedBy": "string",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "supplementalReviewRisks": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "riskSelf": "string",
      "riskType": "LAWS",
      "riskAssessment": "string",
      "riskDesc": "string",
      "level": "LOW",
      "effectRange": "FINANCE",
      "countermeasures": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "supplementalReviewSpecials": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "focus": "string",
      "reviewRequirement": "LEGAL_COMPLIANCE",
      "explain": "string",
      "relatedName": "string",
      "relatedType": "string",
      "relatedExplain": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "supplementalReviewRule": {
    "id": 0,
    "tenantId": 0,
    "supplementalId": 0,
    "processId": 0,
    "importantConfig": "string",
    "matchRange": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|reviewCode|string|true|none||审查编号|
|name|string|true|none||审查名称|
|reviewObject|string|true|none||审查对象|
|level|string|false|none||审查级别：一般、重要、关键|
|department|string|false|none||发起部门|
|auditBy|string|false|none||审查人员|
|explain|string|false|none||审查说明|
|deadlineDate|string|false|none||审查截止日期|
|status|string|false|none||状态：需修改、待审查、发布、审核中、已撤回|
|reviewRange|string|false|none||审查范围|
|reviewTarget|string|false|none||审查目标|
|reviewAccording|string|false|none||审查依据|
|keyAttribute|string|false|none||关键属性：json数组[{"name":"","value":"","explain":""}]|
|material|string|false|none||审查材料|
|noticeMethod|string|false|none||通知方式：系统消息、邮件通知、短信提醒、其他方式|
|noticeObject|string|false|none||通知对象|
|statusNotice|string|false|none||重要状态通知：审查开始、审查延期、审查完成、风险升级、审批通过、审批驳回|
|startRemind|integer|false|none||开始提醒|
|deadlineRemind|integer|false|none||截止提醒|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|supplementalAttachments|[[SupplementalAttachmentDTO](#schemasupplementalattachmentdto)]|false|none||附件列表|
|supplementalReviewRisks|[[SupplementalReviewRiskDTO](#schemasupplementalreviewriskdto)]|false|none||风险列表|
|supplementalReviewSpecials|[[SupplementalReviewSpecialDTO](#schemasupplementalreviewspecialdto)]|false|none||特殊要求列表|
|supplementalReviewRule|[SupplementalReviewRuleDTO](#schemasupplementalreviewruledto)|false|none||审查规则|

#### 枚举值

|属性|值|
|---|---|
|level|GENERAL|
|level|IMPORTANT|
|level|CRITICAL|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|
|noticeMethod|SYSTEM_MESSAGE|
|noticeMethod|EMAIL|
|noticeMethod|SMS|
|noticeMethod|OTHER|
|statusNotice|REVIEW_START|
|statusNotice|REVIEW_DELAY|
|statusNotice|REVIEW_FINISH|
|statusNotice|RISK_ESCALATION|
|statusNotice|REVIEW_PASS|
|statusNotice|REVIEW_REJECTED|

