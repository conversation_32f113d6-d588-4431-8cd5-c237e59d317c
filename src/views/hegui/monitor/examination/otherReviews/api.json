{"id": 0, "tenantId": 0, "reviewCode": "string", "name": "string", "reviewObject": "string", "level": "GENERAL", "department": "string", "auditBy": "string", "explain": "string", "deadlineDate": "string", "status": "MODIFY", "reviewRange": "string", "reviewTarget": "string", "reviewAccording": "string", "keyAttribute": "string", "noticeMethod": "SYSTEM_MESSAGE", "noticeObject": "string", "statusNotice": "REVIEW_START", "startRemind": 0, "deadlineRemind": 0, "metadata": "string", "version": 0, "createdBy": "string", "createdAt": {"seconds": 0, "nanos": 0}, "updatedBy": "string", "updatedAt": {"seconds": 0, "nanos": 0}, "isDeleted": true, "supplementalAttachments": [{"id": 0, "supplementalId": 0, "fileName": "string", "filePath": "string", "fileType": "string", "fileSize": "string", "fileDesc": "string", "metadata": "string", "version": 1, "uploadedBy": "string", "uploadedAt": {"seconds": 0, "nanos": 0}, "isDeleted": true}], "supplementalReviewRisks": [{"id": 0, "tenantId": 0, "supplementalId": 0, "riskSelf": "string", "riskType": "LAWS", "riskAssessment": "string", "riskDesc": "string", "level": "LOW", "effectRange": "FINANCE", "countermeasures": "string", "metadata": "string", "version": 0, "createdBy": "string", "createdAt": {"seconds": 0, "nanos": 0}, "updatedBy": "string", "updatedAt": {"seconds": 0, "nanos": 0}, "isDeleted": true}], "supplementalReviewSpecials": [{"id": 0, "tenantId": 0, "supplementalId": 0, "focus": "string", "reviewRequirement": "LEGAL_COMPLIANCE", "explain": "string", "relatedName": "string", "relatedType": "string", "relatedExplain": "string", "metadata": "string", "version": 0, "createdBy": "string", "createdAt": {"seconds": 0, "nanos": 0}, "updatedBy": "string", "updatedAt": {"seconds": 0, "nanos": 0}, "isDeleted": true}], "supplementalReviewRule": {"id": 0, "tenantId": 0, "supplementalId": 0, "processId": 0, "importantConfig": "string", "matchRange": "string", "metadata": "string", "version": 0, "createdBy": "string", "createdAt": {"seconds": 0, "nanos": 0}, "updatedBy": "string", "updatedAt": {"seconds": 0, "nanos": 0}, "isDeleted": true}}