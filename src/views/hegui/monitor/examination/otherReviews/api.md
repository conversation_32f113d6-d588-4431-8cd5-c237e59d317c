---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 08-合同管理服务/其他审查管理

## POST 创建新的其他审查

POST /whiskerguardcontractservice/api/supplemental/reviews

描述：创建新的其他审查。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "reviewObject": "string",
  "level": "GENERAL",
  "department": "string",
  "auditBy": "string",
  "explain": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "reviewRange": "string",
  "reviewTarget": "string",
  "reviewAccording": "string",
  "keyAttribute": "string",
  "noticeMethod": "SYSTEM_MESSAGE",
  "noticeObject": "string",
  "statusNotice": "REVIEW_START",
  "startRemind": 0,
  "deadlineRemind": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "supplementalAttachments": [
    {
      "id": 0,
      "supplementalId": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 1,
      "uploadedBy": "string",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "supplementalReviewRisks": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "riskSelf": "string",
      "riskType": "LAWS",
      "riskAssessment": "string",
      "riskDesc": "string",
      "level": "LOW",
      "effectRange": "FINANCE",
      "countermeasures": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "supplementalReviewSpecials": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "focus": "string",
      "reviewRequirement": "LEGAL_COMPLIANCE",
      "explain": "string",
      "relatedName": "string",
      "relatedType": "string",
      "relatedExplain": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ],
  "supplementalReviewRule": {
    "id": 0,
    "tenantId": 0,
    "supplementalId": 0,
    "processId": 0,
    "importantConfig": "string",
    "matchRange": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|object| 否 |none|
|» id|body|integer(int64)| 否 |主键ID|
|» tenantId|body|integer(int64)| 是 |租户ID，标识不同公司的数据隔离|
|» reviewCode|body|string| 是 |审查编号|
|» name|body|string| 是 |审查名称|
|» reviewObject|body|string| 是 |审查对象|
|» level|body|string| 否 |审查级别：一般、重要、关键|
|» department|body|string| 否 |发起部门|
|» auditBy|body|string| 否 |审查人员|
|» explain|body|string| 否 |审查说明|
|» deadlineDate|body|string| 否 |审查截止日期|
|» status|body|string| 否 |状态：需修改、待审查、发布、审核中、已撤回|
|» reviewRange|body|string| 否 |审查范围|
|» reviewTarget|body|string| 否 |审查目标|
|» reviewAccording|body|string| 否 |审查依据|
|» keyAttribute|body|string| 否 |关键属性：json数组[{"name":"","value":"","explain":""}]|
|» noticeMethod|body|string| 否 |通知方式：系统消息、邮件通知、短信提醒、其他方式|
|» noticeObject|body|string| 否 |通知对象|
|» statusNotice|body|string| 否 |重要状态通知：审查开始、审查延期、审查完成、风险升级、审批通过、审批驳回|
|» startRemind|body|integer| 否 |开始提醒|
|» deadlineRemind|body|integer| 否 |截止提醒|
|» metadata|body|string| 否 |补充字段|
|» version|body|integer| 否 |当前版本号|
|» createdBy|body|string| 是 |创建者账号或姓名|
|» createdAt|body|[Instant](#schemainstant)| 否 |上传时间|
|»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|» updatedBy|body|string| 否 |最后修改者|
|» updatedAt|body|[Instant](#schemainstant)| 否 |上传时间|
|»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|» isDeleted|body|boolean| 否 |是否删除：0 表示正常 1 表示已删除|
|» supplementalAttachments|body|[[SupplementalAttachmentDTO](#schemasupplementalattachmentdto)]| 否 |附件列表|
|»» id|body|integer(int64)| 否 |none|
|»» supplementalId|body|integer(int64)| 是 |审查ID|
|»» fileName|body|string| 是 |附件名称|
|»» filePath|body|string| 是 |附件存储路径或URL|
|»» fileType|body|string| 是 |附件类型|
|»» fileSize|body|string| 否 |附件大小|
|»» fileDesc|body|string| 否 |附件描述|
|»» metadata|body|string| 否 |补充字段|
|»» version|body|integer| 否 |当前版本号|
|»» uploadedBy|body|string| 是 |上传者|
|»» uploadedAt|body|[Instant](#schemainstant)| 否 |上传时间|
|»»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|»» isDeleted|body|boolean| 否 |是否删除：0 表示正常 1 表示已删除|
|» supplementalReviewRisks|body|[[SupplementalReviewRiskDTO](#schemasupplementalreviewriskdto)]| 否 |风险列表|
|»» id|body|integer(int64)| 否 |主键ID|
|»» tenantId|body|integer(int64)| 是 |租户ID，标识不同公司的数据隔离|
|»» supplementalId|body|integer(int64)| 是 |审查ID|
|»» riskSelf|body|string| 否 |风险自评|
|»» riskType|body|string| 否 |风险类型：法律风险、财务风险、操作风险、声誉风险、其他|
|»» riskAssessment|body|string| 否 |风险评估|
|»» riskDesc|body|string| 否 |风险描述|
|»» level|body|string| 否 |风险等级：高、中、低|
|»» effectRange|body|string| 否 |影响范围：财务、运营、客户|
|»» countermeasures|body|string| 否 |应对措施|
|»» metadata|body|string| 否 |补充字段|
|»» version|body|integer| 否 |当前版本号|
|»» createdBy|body|string| 是 |创建者账号或姓名|
|»» createdAt|body|[Instant](#schemainstant)| 否 |上传时间|
|»»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|»» updatedBy|body|string| 否 |最后修改者|
|»» updatedAt|body|[Instant](#schemainstant)| 否 |上传时间|
|»»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|»» isDeleted|body|boolean| 否 |是否删除：0 表示正常 1 表示已删除|
|» supplementalReviewSpecials|body|[[SupplementalReviewSpecialDTO](#schemasupplementalreviewspecialdto)]| 否 |特殊要求列表|
|»» id|body|integer(int64)| 否 |主键ID|
|»» tenantId|body|integer(int64)| 是 |租户ID，标识不同公司的数据隔离|
|»» supplementalId|body|integer(int64)| 是 |审查ID|
|»» focus|body|string| 否 |关注重点|
|»» reviewRequirement|body|string| 否 |特殊审查要求：法律合规性审查、技术安全性审查、商业风险评估、隐私保护审查、其他|
|»» explain|body|string| 否 |特殊说明|
|»» relatedName|body|string| 否 |相关方名称|
|»» relatedType|body|string| 否 |相关方类型|
|»» relatedExplain|body|string| 否 |关系说明|
|»» metadata|body|string| 否 |补充字段|
|»» version|body|integer| 否 |当前版本号|
|»» createdBy|body|string| 是 |创建者账号或姓名|
|»» createdAt|body|[Instant](#schemainstant)| 否 |上传时间|
|»»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|»» updatedBy|body|string| 否 |最后修改者|
|»» updatedAt|body|[Instant](#schemainstant)| 否 |上传时间|
|»»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|»» isDeleted|body|boolean| 否 |是否删除：0 表示正常 1 表示已删除|
|» supplementalReviewRule|body|[SupplementalReviewRuleDTO](#schemasupplementalreviewruledto)| 否 |审查规则|
|»» id|body|integer(int64)| 否 |主键ID|
|»» tenantId|body|integer(int64)| 是 |租户ID，标识不同公司的数据隔离|
|»» supplementalId|body|integer(int64)| 是 |审查ID|
|»» processId|body|integer(int64)| 是 |审查流程ID|
|»» importantConfig|body|string| 否 |审查重点配置，使用逗号分隔|
|»» matchRange|body|string| 否 |法规匹配范围，使用逗号分隔|
|»» metadata|body|string| 否 |补充字段|
|»» version|body|integer| 否 |当前版本号|
|»» createdBy|body|string| 是 |创建者账号或姓名|
|»» createdAt|body|[Instant](#schemainstant)| 否 |上传时间|
|»»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|»» updatedBy|body|string| 否 |最后修改者|
|»» updatedAt|body|[Instant](#schemainstant)| 否 |上传时间|
|»»» seconds|body|integer(int64)| 否 |The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|body|integer| 否 |The number of nanoseconds, later along the time-line, from the seconds field.|
|»» isDeleted|body|boolean| 否 |是否删除：0 表示正常 1 表示已删除|

#### 详细说明

**»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

**»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

**»»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

**»»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

**»»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

**»»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

**»»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

**»»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

**»»» nanos**: The number of nanoseconds, later along the time-line, from the seconds field.
This is always positive, and never exceeds 999,999,999.

#### 枚举值

|属性|值|
|---|---|
|» level|GENERAL|
|» level|IMPORTANT|
|» level|CRITICAL|
|» status|MODIFY|
|» status|PENDING|
|» status|PUBLISHED|
|» status|REVIEWING|
|» status|REVOKE|
|» noticeMethod|SYSTEM_MESSAGE|
|» noticeMethod|EMAIL|
|» noticeMethod|SMS|
|» noticeMethod|OTHER|
|» statusNotice|REVIEW_START|
|» statusNotice|REVIEW_DELAY|
|» statusNotice|REVIEW_FINISH|
|» statusNotice|RISK_ESCALATION|
|» statusNotice|REVIEW_PASS|
|» statusNotice|REVIEW_REJECTED|
|»» riskType|LAWS|
|»» riskType|FINANCE|
|»» riskType|OPERATION|
|»» riskType|REPUTATION|
|»» riskType|OTHER|
|»» level|LOW|
|»» level|MIDDLE|
|»» level|HIGH|
|»» effectRange|FINANCE|
|»» effectRange|OPERATION|
|»» effectRange|CUSTOMER|
|»» reviewRequirement|LEGAL_COMPLIANCE|
|»» reviewRequirement|TECHNICAL_SECURITY|
|»» reviewRequirement|COMMERCIAL_RISK|
|»» reviewRequirement|PRIVACY_PROTECTION|
|»» reviewRequirement|OTHER|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "",
  "name": "",
  "reviewObject": "",
  "level": "",
  "department": "",
  "auditBy": "",
  "explain": "",
  "deadlineDate": "",
  "status": "",
  "reviewRange": "",
  "reviewTarget": "",
  "reviewAccording": "",
  "keyAttribute": "",
  "material": "",
  "noticeMethod": "",
  "noticeObject": "",
  "statusNotice": "",
  "startRemind": 0,
  "deadlineRemind": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "supplementalAttachments": [
    {
      "id": 0,
      "supplementalId": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "uploadedBy": "",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewRisks": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "riskSelf": "",
      "riskType": "",
      "riskAssessment": "",
      "riskDesc": "",
      "level": "",
      "effectRange": "",
      "countermeasures": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewSpecials": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "focus": "",
      "reviewRequirement": "",
      "explain": "",
      "relatedName": "",
      "relatedType": "",
      "relatedExplain": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewRule": {
    "id": 0,
    "tenantId": 0,
    "supplementalId": 0,
    "processId": 0,
    "importantConfig": "",
    "matchRange": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntitySupplementalReviewDTO](#schemaresponseentitysupplementalreviewdto)|

## PATCH 部分更新其他审查

PATCH /whiskerguardcontractservice/api/supplemental/reviews/id

描述：部分更新其他审查。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "reviewObject": "string",
  "level": "GENERAL",
  "department": "string",
  "auditBy": "string",
  "explain": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "reviewRange": "string",
  "reviewTarget": "string",
  "reviewAccording": "string",
  "keyAttribute": "string",
  "material": "string",
  "noticeMethod": "SYSTEM_MESSAGE",
  "noticeObject": "string",
  "statusNotice": "REVIEW_START",
  "startRemind": 0,
  "deadlineRemind": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "supplementalAttachments": [
    {}
  ],
  "supplementalReviewRisks": [
    {}
  ],
  "supplementalReviewSpecials": [
    {}
  ],
  "supplementalReviewRule": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[SupplementalReviewDTO](#schemasupplementalreviewdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "",
  "name": "",
  "reviewObject": "",
  "level": "",
  "department": "",
  "auditBy": "",
  "explain": "",
  "deadlineDate": "",
  "status": "",
  "reviewRange": "",
  "reviewTarget": "",
  "reviewAccording": "",
  "keyAttribute": "",
  "material": "",
  "noticeMethod": "",
  "noticeObject": "",
  "statusNotice": "",
  "startRemind": 0,
  "deadlineRemind": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "supplementalAttachments": [
    {
      "id": 0,
      "supplementalId": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "uploadedBy": "",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewRisks": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "riskSelf": "",
      "riskType": "",
      "riskAssessment": "",
      "riskDesc": "",
      "level": "",
      "effectRange": "",
      "countermeasures": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewSpecials": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "focus": "",
      "reviewRequirement": "",
      "explain": "",
      "relatedName": "",
      "relatedType": "",
      "relatedExplain": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewRule": {
    "id": 0,
    "tenantId": 0,
    "supplementalId": 0,
    "processId": 0,
    "importantConfig": "",
    "matchRange": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntitySupplementalReviewDTO](#schemaresponseentitysupplementalreviewdto)|

## GET 获取指定 ID 的 其他审查

GET /whiskerguardcontractservice/api/supplemental/reviews/id

描述：获取指定 ID 的 其他审查。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "",
  "name": "",
  "reviewObject": "",
  "level": "",
  "department": "",
  "auditBy": "",
  "explain": "",
  "deadlineDate": "",
  "status": "",
  "reviewRange": "",
  "reviewTarget": "",
  "reviewAccording": "",
  "keyAttribute": "",
  "material": "",
  "noticeMethod": "",
  "noticeObject": "",
  "statusNotice": "",
  "startRemind": 0,
  "deadlineRemind": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "supplementalAttachments": [
    {
      "id": 0,
      "supplementalId": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "uploadedBy": "",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewRisks": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "riskSelf": "",
      "riskType": "",
      "riskAssessment": "",
      "riskDesc": "",
      "level": "",
      "effectRange": "",
      "countermeasures": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewSpecials": [
    {
      "id": 0,
      "tenantId": 0,
      "supplementalId": 0,
      "focus": "",
      "reviewRequirement": "",
      "explain": "",
      "relatedName": "",
      "relatedType": "",
      "relatedExplain": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "supplementalReviewRule": {
    "id": 0,
    "tenantId": 0,
    "supplementalId": 0,
    "processId": 0,
    "importantConfig": "",
    "matchRange": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntitySupplementalReviewDTO](#schemaresponseentitysupplementalreviewdto)|

## DELETE 删除指定 ID 的 其他审查

DELETE /whiskerguardcontractservice/api/supplemental/reviews/id

描述：删除指定 ID 的 其他审查。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 获取所有其他审查分页列表

POST /whiskerguardcontractservice/api/supplemental/reviews/page

描述：获取所有其他审查分页列表。

> Body 请求参数

```json
{
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "contractType": "TECHNICAL_SERVICES",
  "level": "GENERAL",
  "department": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "createdAtStart": "string",
  "createdAtEnd": "string",
  "decisionType": "MARKET_STRATEGY",
  "reviewType": "CONTRACT"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |none|
|size|query|integer| 否 |none|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[ReviewReq](#schemareviewreq)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "reviewCode": "",
      "name": "",
      "reviewObject": "",
      "level": "",
      "department": "",
      "auditBy": "",
      "explain": "",
      "deadlineDate": "",
      "status": "",
      "reviewRange": "",
      "reviewTarget": "",
      "reviewAccording": "",
      "keyAttribute": "",
      "material": "",
      "noticeMethod": "",
      "noticeObject": "",
      "statusNotice": "",
      "startRemind": 0,
      "deadlineRemind": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false,
      "supplementalAttachments": [
        {
          "id": 0,
          "supplementalId": 0,
          "fileName": "",
          "filePath": "",
          "fileType": "",
          "fileSize": "",
          "fileDesc": "",
          "metadata": "",
          "version": 0,
          "uploadedBy": "",
          "uploadedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        }
      ],
      "supplementalReviewRisks": [
        {
          "id": 0,
          "tenantId": 0,
          "supplementalId": 0,
          "riskSelf": "",
          "riskType": "",
          "riskAssessment": "",
          "riskDesc": "",
          "level": "",
          "effectRange": "",
          "countermeasures": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        }
      ],
      "supplementalReviewSpecials": [
        {
          "id": 0,
          "tenantId": 0,
          "supplementalId": 0,
          "focus": "",
          "reviewRequirement": "",
          "explain": "",
          "relatedName": "",
          "relatedType": "",
          "relatedExplain": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        }
      ],
      "supplementalReviewRule": {
        "id": 0,
        "tenantId": 0,
        "supplementalId": 0,
        "processId": 0,
        "importantConfig": "",
        "matchRange": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "isDeleted": false
      }
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "total": 0,
  "empty": false,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "totalPages": 0,
  "totalElements": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageSupplementalReviewDTO](#schemaresponseentitypagesupplementalreviewdto)|

## GET 根据审核状态统计数据

GET /whiskerguardcontractservice/api/supplemental/reviews/count/reviewStatus/tenantId

描述：根据ReviewStatus分组统计数据。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityMapLong](#schemaresponseentitymaplong)|

# 数据模型

<h2 id="tocS_ReviewReq">ReviewReq</h2>

<a id="schemareviewreq"></a>
<a id="schema_ReviewReq"></a>
<a id="tocSreviewreq"></a>
<a id="tocsreviewreq"></a>

```json
{
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "contractType": "TECHNICAL_SERVICES",
  "level": "GENERAL",
  "department": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "createdAtStart": "string",
  "createdAtEnd": "string",
  "decisionType": "MARKET_STRATEGY",
  "reviewType": "CONTRACT"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|reviewCode|string|false|none||审查编号|
|name|string|false|none||合同名称|
|contractType|string|false|none||合同类型：技术服务合同、重大决策、数据合同、采购合同|
|level|string|false|none||审查级别：一般、重要、关键|
|department|string|false|none||发起部门|
|deadlineDate|string|false|none||审查截止日期|
|status|string|false|none||状态：需修改、待审查、发布、审核中、已撤回|
|createdAtStart|string|false|none||创建时间开始|
|createdAtEnd|string|false|none||创建时间结束|
|decisionType|string|false|none||决策类型：市场战略|
|reviewType|string|false|none||审查类型：合同、决策、其他|

#### 枚举值

|属性|值|
|---|---|
|contractType|TECHNICAL_SERVICES|
|contractType|MAJOR_DECISIONS|
|contractType|DATA|
|contractType|PROCUREMENT|
|level|GENERAL|
|level|IMPORTANT|
|level|CRITICAL|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|
|decisionType|MARKET_STRATEGY|
|reviewType|CONTRACT|
|reviewType|DECISION|
|reviewType|SUPPLEMENTAL|
|reviewType|VIOLATION|
|reviewType|INVESTIGATE_TASK|
|reviewType|INVESTIGATE_RECORD|
|reviewType|INVESTIGATE_REPORT|
|reviewType|RESPONSIBILITY_CORRECTION|
|reviewType|RESPONSIBILITY_DEAL|
|reviewType|CONTINUOUS_EXPERIENCE|
|reviewType|CONTINUOUS_IMPROVE|
|reviewType|CONTINUOUS_REPORT|

<h2 id="tocS_ResponseEntityMapLong">ResponseEntityMapLong</h2>

<a id="schemaresponseentitymaplong"></a>
<a id="schema_ResponseEntityMapLong"></a>
<a id="tocSresponseentitymaplong"></a>
<a id="tocsresponseentitymaplong"></a>

```json
{
  "key": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|integer|false|none||none|

<h2 id="tocS_SupplementalAttachmentDTO">SupplementalAttachmentDTO</h2>

<a id="schemasupplementalattachmentdto"></a>
<a id="schema_SupplementalAttachmentDTO"></a>
<a id="tocSsupplementalattachmentdto"></a>
<a id="tocssupplementalattachmentdto"></a>

```json
{
  "id": 0,
  "supplementalId": 0,
  "fileName": "string",
  "filePath": "string",
  "fileType": "string",
  "fileSize": "string",
  "fileDesc": "string",
  "metadata": "string",
  "version": 1,
  "uploadedBy": "string",
  "uploadedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|supplementalId|integer(int64)|true|none||审查ID|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|fileType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|fileDesc|string|false|none||附件描述|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|uploadedBy|string|true|none||上传者|
|uploadedAt|[Instant](#schemainstant)|false|none||上传时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE",
  "ascending": true,
  "descending": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|
|ascending|boolean|false|none||none|
|descending|boolean|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

<h2 id="tocS_SupplementalReviewSpecialDTO">SupplementalReviewSpecialDTO</h2>

<a id="schemasupplementalreviewspecialdto"></a>
<a id="schema_SupplementalReviewSpecialDTO"></a>
<a id="tocSsupplementalreviewspecialdto"></a>
<a id="tocssupplementalreviewspecialdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "supplementalId": 0,
  "focus": "string",
  "reviewRequirement": "LEGAL_COMPLIANCE",
  "explain": "string",
  "relatedName": "string",
  "relatedType": "string",
  "relatedExplain": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|supplementalId|integer(int64)|true|none||审查ID|
|focus|string|false|none||关注重点|
|reviewRequirement|string|false|none||特殊审查要求：法律合规性审查、技术安全性审查、商业风险评估、隐私保护审查、其他|
|explain|string|false|none||特殊说明|
|relatedName|string|false|none||相关方名称|
|relatedType|string|false|none||相关方类型|
|relatedExplain|string|false|none||关系说明|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||上传时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||上传时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|reviewRequirement|LEGAL_COMPLIANCE|
|reviewRequirement|TECHNICAL_SECURITY|
|reviewRequirement|COMMERCIAL_RISK|
|reviewRequirement|PRIVACY_PROTECTION|
|reviewRequirement|OTHER|

<h2 id="tocS_SupplementalReviewRuleDTO">SupplementalReviewRuleDTO</h2>

<a id="schemasupplementalreviewruledto"></a>
<a id="schema_SupplementalReviewRuleDTO"></a>
<a id="tocSsupplementalreviewruledto"></a>
<a id="tocssupplementalreviewruledto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "supplementalId": 0,
  "processId": 0,
  "importantConfig": "string",
  "matchRange": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|supplementalId|integer(int64)|true|none||审查ID|
|processId|integer(int64)|true|none||审查流程ID|
|importantConfig|string|false|none||审查重点配置，使用逗号分隔|
|matchRange|string|false|none||法规匹配范围，使用逗号分隔|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||上传时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||上传时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_SupplementalReviewRiskDTO">SupplementalReviewRiskDTO</h2>

<a id="schemasupplementalreviewriskdto"></a>
<a id="schema_SupplementalReviewRiskDTO"></a>
<a id="tocSsupplementalreviewriskdto"></a>
<a id="tocssupplementalreviewriskdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "supplementalId": 0,
  "riskSelf": "string",
  "riskType": "LAWS",
  "riskAssessment": "string",
  "riskDesc": "string",
  "level": "LOW",
  "effectRange": "FINANCE",
  "countermeasures": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|supplementalId|integer(int64)|true|none||审查ID|
|riskSelf|string|false|none||风险自评|
|riskType|string|false|none||风险类型：法律风险、财务风险、操作风险、声誉风险、其他|
|riskAssessment|string|false|none||风险评估|
|riskDesc|string|false|none||风险描述|
|level|string|false|none||风险等级：高、中、低|
|effectRange|string|false|none||影响范围：财务、运营、客户|
|countermeasures|string|false|none||应对措施|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||上传时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||上传时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|riskType|LAWS|
|riskType|FINANCE|
|riskType|OPERATION|
|riskType|REPUTATION|
|riskType|OTHER|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|effectRange|FINANCE|
|effectRange|OPERATION|
|effectRange|CUSTOMER|

<h2 id="tocS_ResponseEntitySupplementalReviewDTO">ResponseEntitySupplementalReviewDTO</h2>

<a id="schemaresponseentitysupplementalreviewdto"></a>
<a id="schema_ResponseEntitySupplementalReviewDTO"></a>
<a id="tocSresponseentitysupplementalreviewdto"></a>
<a id="tocsresponseentitysupplementalreviewdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "reviewObject": "string",
  "level": "GENERAL",
  "department": "string",
  "auditBy": "string",
  "explain": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "reviewRange": "string",
  "reviewTarget": "string",
  "reviewAccording": "string",
  "keyAttribute": "string",
  "material": "string",
  "noticeMethod": "SYSTEM_MESSAGE",
  "noticeObject": "string",
  "statusNotice": "REVIEW_START",
  "startRemind": 0,
  "deadlineRemind": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "supplementalAttachments": [
    {}
  ],
  "supplementalReviewRisks": [
    {}
  ],
  "supplementalReviewSpecials": [
    {}
  ],
  "supplementalReviewRule": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|reviewCode|string|true|none||审查编号|
|name|string|true|none||审查名称|
|reviewObject|string|true|none||审查对象|
|level|string|false|none||审查级别：一般、重要、关键|
|department|string|false|none||发起部门|
|auditBy|string|false|none||审查人员|
|explain|string|false|none||审查说明|
|deadlineDate|string|false|none||审查截止日期|
|status|string|false|none||状态：需修改、待审查、发布、审核中、已撤回|
|reviewRange|string|false|none||审查范围|
|reviewTarget|string|false|none||审查目标|
|reviewAccording|string|false|none||审查依据|
|keyAttribute|string|false|none||关键属性：json数组[{"name":"","value":"","explain":""}]|
|material|string|false|none||审查材料|
|noticeMethod|string|false|none||通知方式：系统消息、邮件通知、短信提醒、其他方式|
|noticeObject|string|false|none||通知对象|
|statusNotice|string|false|none||重要状态通知：审查开始、审查延期、审查完成、风险升级、审批通过、审批驳回|
|startRemind|integer|false|none||开始提醒|
|deadlineRemind|integer|false|none||截止提醒|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|object|false|none||上传时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|object|false|none||上传时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|supplementalAttachments|[object]|false|none||附件列表|
|supplementalReviewRisks|[object]|false|none||风险列表|
|supplementalReviewSpecials|[object]|false|none||特殊要求列表|
|supplementalReviewRule|object|false|none||上传时间|

#### 枚举值

|属性|值|
|---|---|
|level|GENERAL|
|level|IMPORTANT|
|level|CRITICAL|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|
|noticeMethod|SYSTEM_MESSAGE|
|noticeMethod|EMAIL|
|noticeMethod|SMS|
|noticeMethod|OTHER|
|statusNotice|REVIEW_START|
|statusNotice|REVIEW_DELAY|
|statusNotice|REVIEW_FINISH|
|statusNotice|RISK_ESCALATION|
|statusNotice|REVIEW_PASS|
|statusNotice|REVIEW_REJECTED|

<h2 id="tocS_SupplementalReviewDTO">SupplementalReviewDTO</h2>

<a id="schemasupplementalreviewdto"></a>
<a id="schema_SupplementalReviewDTO"></a>
<a id="tocSsupplementalreviewdto"></a>
<a id="tocssupplementalreviewdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "reviewObject": "string",
  "level": "GENERAL",
  "department": "string",
  "auditBy": "string",
  "explain": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "reviewRange": "string",
  "reviewTarget": "string",
  "reviewAccording": "string",
  "keyAttribute": "string",
  "material": "string",
  "noticeMethod": "SYSTEM_MESSAGE",
  "noticeObject": "string",
  "statusNotice": "REVIEW_START",
  "startRemind": 0,
  "deadlineRemind": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "supplementalAttachments": [
    {}
  ],
  "supplementalReviewRisks": [
    {}
  ],
  "supplementalReviewSpecials": [
    {}
  ],
  "supplementalReviewRule": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|reviewCode|string|true|none||审查编号|
|name|string|true|none||审查名称|
|reviewObject|string|true|none||审查对象|
|level|string|false|none||审查级别：一般、重要、关键|
|department|string|false|none||发起部门|
|auditBy|string|false|none||审查人员|
|explain|string|false|none||审查说明|
|deadlineDate|string|false|none||审查截止日期|
|status|string|false|none||状态：需修改、待审查、发布、审核中、已撤回|
|reviewRange|string|false|none||审查范围|
|reviewTarget|string|false|none||审查目标|
|reviewAccording|string|false|none||审查依据|
|keyAttribute|string|false|none||关键属性：json数组[{"name":"","value":"","explain":""}]|
|material|string|false|none||审查材料|
|noticeMethod|string|false|none||通知方式：系统消息、邮件通知、短信提醒、其他方式|
|noticeObject|string|false|none||通知对象|
|statusNotice|string|false|none||重要状态通知：审查开始、审查延期、审查完成、风险升级、审批通过、审批驳回|
|startRemind|integer|false|none||开始提醒|
|deadlineRemind|integer|false|none||截止提醒|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|object|false|none||上传时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|object|false|none||上传时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|supplementalAttachments|[object]|false|none||附件列表|
|supplementalReviewRisks|[object]|false|none||风险列表|
|supplementalReviewSpecials|[object]|false|none||特殊要求列表|
|supplementalReviewRule|object|false|none||上传时间|

#### 枚举值

|属性|值|
|---|---|
|level|GENERAL|
|level|IMPORTANT|
|level|CRITICAL|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|
|noticeMethod|SYSTEM_MESSAGE|
|noticeMethod|EMAIL|
|noticeMethod|SMS|
|noticeMethod|OTHER|
|statusNotice|REVIEW_START|
|statusNotice|REVIEW_DELAY|
|statusNotice|REVIEW_FINISH|
|statusNotice|RISK_ESCALATION|
|statusNotice|REVIEW_PASS|
|statusNotice|REVIEW_REJECTED|

<h2 id="tocS_ResponseEntityPageSupplementalReviewDTO">ResponseEntityPageSupplementalReviewDTO</h2>

<a id="schemaresponseentitypagesupplementalreviewdto"></a>
<a id="schema_ResponseEntityPageSupplementalReviewDTO"></a>
<a id="tocSresponseentitypagesupplementalreviewdto"></a>
<a id="tocsresponseentitypagesupplementalreviewdto"></a>

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "reviewCode": "string",
      "name": "string",
      "reviewObject": "string",
      "level": "GENERAL",
      "department": "string",
      "auditBy": "string",
      "explain": "string",
      "deadlineDate": "string",
      "status": "MODIFY",
      "reviewRange": "string",
      "reviewTarget": "string",
      "reviewAccording": "string",
      "keyAttribute": "string",
      "material": "string",
      "noticeMethod": "SYSTEM_MESSAGE",
      "noticeObject": "string",
      "statusNotice": "REVIEW_START",
      "startRemind": 0,
      "deadlineRemind": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "supplementalAttachments": [
        {}
      ],
      "supplementalReviewRisks": [
        {}
      ],
      "supplementalReviewSpecials": [
        {}
      ],
      "supplementalReviewRule": {}
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "total": 0,
  "empty": true,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "totalPages": 0,
  "totalElements": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|content|[[SupplementalReviewDTO](#schemasupplementalreviewdto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|total|integer(int64)|false|none||none|
|empty|boolean|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|

