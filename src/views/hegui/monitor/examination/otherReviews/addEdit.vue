<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import {
  Bell,
  DataBoard,
  Delete,
  Document,
  Monitor,
  Plus,
  Search,
  UploadFilled,
  Warning,
} from '@element-plus/icons-vue'
import otherApi from '@/api/review/other'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  reviewCode: '',
  reviewObject: '',
  level: 'GENERAL',
  department: '',
  auditBy: '',
  deadlineDate: null,
  explain: '',
  reviewRange: '',
  reviewTarget: '',
  reviewAccording: '',
  keyAttribute: '',
  noticeMethod: 'SYSTEM_MESSAGE',
  noticeObject: '',
  statusNotice: 'REVIEW_START',
  startRemind: 1,
  deadlineRemind: 1,
  document: '',
  documentUrl: '',
  riskTypes: [],
  riskDescription: '',
  focusPoints: '',
  specialRequirements: [],
  specialInstructions: '',
  reviewProcess: '',
  reviewFocus: [],
  regulationScope: [],
  notificationMethods: [],
  notificationTargets: '',
  statusNotifications: [],
  startReminder: 1,
  deadlineReminder: 1,
  status: 'MODIFY',
  // 子对象数据
  supplementalAttachments: [],
  supplementalReviewRisks: [],
  supplementalReviewSpecials: [],
  supplementalReviewRule: {
    processId: 0,
    importantConfig: '',
    matchRange: '',
  },
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入审查名称', trigger: 'blur' },
  ],
  reviewObject: [
    { required: true, message: '请输入审查对象', trigger: 'blur' },
  ],
  level: [
    { required: true, message: '请选择审查级别', trigger: 'change' },
  ],
  department: [
    { required: true, message: '请输入发起部门', trigger: 'blur' },
  ],
  auditBy: [
    { required: true, message: '请输入审查人员', trigger: 'blur' },
  ],
  reviewRange: [
    { required: true, message: '请输入审查范围', trigger: 'blur' },
  ],
  reviewTarget: [
    { required: true, message: '请输入审查目标', trigger: 'blur' },
  ],
  document: [
    { required: true, message: '请输入审查材料', trigger: 'blur' },
  ],
})

// 动态数据
const keyAttributes = ref([])
const riskItems = ref([])
const specialRequirements = ref([])
const stakeholders = ref([])

// 保存草稿
async function handleSave() {
  if (!formRef.value) { return }

  try {
    await formRef.value.validate()

    loading.value = true

    // 准备提交的数据，按照API文档规范
    const submitData = {
      id: formData.id || undefined,
      tenantId: formData.tenantId || 0,
      reviewCode: formData.reviewCode || '',
      name: formData.name || '',
      reviewObject: formData.reviewObject || '',
      level: formData.level || 'GENERAL',
      department: formData.department || '',
      auditBy: formData.auditBy || '',
      explain: formData.explain || '',
      deadlineDate: formData.deadlineDate ? new Date(formData.deadlineDate).toISOString().split('T')[0] : '',
      status: 'MODIFY',
      reviewRange: formData.reviewRange || '',
      reviewTarget: formData.reviewTarget || '',
      reviewAccording: formData.reviewAccording || '',
      keyAttribute: formData.keyAttribute || '',
      noticeMethod: formData.notificationMethods && formData.notificationMethods.length > 0 ? formData.notificationMethods[0] : 'SYSTEM_MESSAGE',
      noticeObject: formData.notificationTargets || '',
      statusNotice: formData.statusNotifications && formData.statusNotifications.length > 0 ? formData.statusNotifications[0] : 'REVIEW_START',
      startRemind: formData.startReminder || 0,
      deadlineRemind: formData.deadlineReminder || 0,
      metadata: '',
      version: formData.version || 0,
      createdBy: formData.createdBy || '',
      updatedBy: '',
      isDeleted: false,
    }

    // 处理附件数据
    if (formData.document || formData.documentUrl) {
      submitData.supplementalAttachments = [{
        supplementalId: formData.id || 0,
        fileName: formData.document || '',
        filePath: formData.documentUrl || '',
        fileType: 'document',
        fileSize: '',
        fileDesc: '',
        metadata: '',
        version: 1,
        uploadedBy: formData.createdBy || '',
        isDeleted: false,
      }]
    }
    else {
      submitData.supplementalAttachments = []
    }

    // 处理风险评估数据
    if (formData.riskTypes && formData.riskTypes.length > 0) {
      submitData.supplementalReviewRisks = [{
        tenantId: formData.tenantId || 0,
        supplementalId: formData.id || 0,
        riskSelf: formData.riskTypes.join(','),
        riskType: 'LAWS',
        riskAssessment: '',
        riskDesc: formData.riskDescription || '',
        level: 'LOW',
        effectRange: 'FINANCE',
        countermeasures: '',
        metadata: '',
        version: 0,
        createdBy: formData.createdBy || '',
        updatedBy: '',
        isDeleted: false,
      }]
    }
    else {
      submitData.supplementalReviewRisks = []
    }

    // 处理特殊要求数据
    if (formData.focusPoints || (formData.specialRequirements && formData.specialRequirements.length > 0)) {
      submitData.supplementalReviewSpecials = [{
        tenantId: formData.tenantId || 0,
        supplementalId: formData.id || 0,
        focus: formData.focusPoints || '',
        reviewRequirement: formData.specialRequirements && formData.specialRequirements.length > 0 ? formData.specialRequirements[0] : 'LEGAL_COMPLIANCE',
        explain: formData.specialInstructions || '',
        relatedName: '',
        relatedType: '',
        relatedExplain: '',
        metadata: '',
        version: 0,
        createdBy: formData.createdBy || '',
        updatedBy: '',
        isDeleted: false,
      }]
    }
    else {
      submitData.supplementalReviewSpecials = []
    }

    // 处理审查规则数据
    if (formData.reviewProcess || (formData.reviewFocus && formData.reviewFocus.length > 0)) {
      submitData.supplementalReviewRule = {
        tenantId: formData.tenantId || 0,
        supplementalId: formData.id || 0,
        processId: formData.reviewProcess ? Number.parseInt(formData.reviewProcess) : 0,
        importantConfig: formData.reviewFocus && formData.reviewFocus.length > 0 ? formData.reviewFocus.join(',') : '',
        matchRange: formData.regulationScope && formData.regulationScope.length > 0 ? formData.regulationScope.join(',') : '',
        metadata: '',
        version: 0,
        createdBy: formData.createdBy || '',
        updatedBy: '',
        isDeleted: false,
      }
    }
    else {
      submitData.supplementalReviewRule = {
        tenantId: formData.tenantId || 0,
        supplementalId: formData.id || 0,
        processId: 0,
        importantConfig: '',
        matchRange: '',
        metadata: '',
        version: 0,
        createdBy: formData.createdBy || '',
        updatedBy: '',
        isDeleted: false,
      }
    }

    // 根据是否有ID判断是新增还是编辑
    let result
    if (submitData.id) {
      // 编辑
      result = await otherApi.supplementalReview(null, submitData, 'update')
    }
    else {
      // 新增
      result = await otherApi.supplementalReview(null, submitData, 'create')
    }

    if (result.code === 0) {
      ElMessage.success('保存成功')
      if (!formData.id && result.data) {
        formData.id = result.data.id
      }
    }
    else {
      ElMessage.error(result.msg || '保存失败')
    }
  }
  catch (error) {
    console.error('表单验证失败', error)
    ElMessage.error('请完善必填信息')
  }
  finally {
    loading.value = false
  }
}

// 提交审查
async function handleSubmit() {
  if (!formRef.value) { return }

  try {
    await formRef.value.validate()

    loading.value = true

    // 准备提交的数据，按照API文档规范
    const submitData = {
      id: formData.id || undefined,
      tenantId: formData.tenantId || 0,
      reviewCode: formData.reviewCode || '',
      name: formData.name || '',
      reviewObject: formData.reviewObject || '',
      level: formData.level || 'GENERAL',
      department: formData.department || '',
      auditBy: formData.auditBy || '',
      explain: formData.explain || '',
      deadlineDate: formData.deadlineDate ? new Date(formData.deadlineDate).toISOString().split('T')[0] : '',
      status: 'PENDING',
      reviewRange: formData.reviewRange || '',
      reviewTarget: formData.reviewTarget || '',
      reviewAccording: formData.reviewAccording || '',
      keyAttribute: formData.keyAttribute || '',
      noticeMethod: formData.notificationMethods && formData.notificationMethods.length > 0 ? formData.notificationMethods[0] : 'SYSTEM_MESSAGE',
      noticeObject: formData.notificationTargets || '',
      statusNotice: formData.statusNotifications && formData.statusNotifications.length > 0 ? formData.statusNotifications[0] : 'REVIEW_START',
      startRemind: formData.startReminder || 0,
      deadlineRemind: formData.deadlineReminder || 0,
      metadata: '',
      version: formData.version || 0,
      createdBy: formData.createdBy || '',
      updatedBy: '',
      isDeleted: false,
    }

    // 处理附件数据
    if (formData.document || formData.documentUrl) {
      submitData.supplementalAttachments = [{
        supplementalId: formData.id || 0,
        fileName: formData.document || '',
        filePath: formData.documentUrl || '',
        fileType: 'document',
        fileSize: '',
        fileDesc: '',
        metadata: '',
        version: 1,
        uploadedBy: formData.createdBy || '',
        isDeleted: false,
      }]
    }
    else {
      submitData.supplementalAttachments = []
    }

    // 处理风险评估数据
    if (formData.riskTypes && formData.riskTypes.length > 0) {
      submitData.supplementalReviewRisks = [{
        tenantId: formData.tenantId || 0,
        supplementalId: formData.id || 0,
        riskSelf: formData.riskTypes.join(','),
        riskType: 'LAWS',
        riskAssessment: '',
        riskDesc: formData.riskDescription || '',
        level: 'LOW',
        effectRange: 'FINANCE',
        countermeasures: '',
        metadata: '',
        version: 0,
        createdBy: formData.createdBy || '',
        updatedBy: '',
        isDeleted: false,
      }]
    }
    else {
      submitData.supplementalReviewRisks = []
    }

    // 处理特殊要求数据
    if (formData.focusPoints || (formData.specialRequirements && formData.specialRequirements.length > 0)) {
      submitData.supplementalReviewSpecials = [{
        tenantId: formData.tenantId || 0,
        supplementalId: formData.id || 0,
        focus: formData.focusPoints || '',
        reviewRequirement: formData.specialRequirements && formData.specialRequirements.length > 0 ? formData.specialRequirements[0] : 'LEGAL_COMPLIANCE',
        explain: formData.specialInstructions || '',
        relatedName: '',
        relatedType: '',
        relatedExplain: '',
        metadata: '',
        version: 0,
        createdBy: formData.createdBy || '',
        updatedBy: '',
        isDeleted: false,
      }]
    }
    else {
      submitData.supplementalReviewSpecials = []
    }

    // 处理审查规则数据
    if (formData.reviewProcess || (formData.reviewFocus && formData.reviewFocus.length > 0)) {
      submitData.supplementalReviewRule = {
        tenantId: formData.tenantId || 0,
        supplementalId: formData.id || 0,
        processId: formData.reviewProcess ? Number.parseInt(formData.reviewProcess) : 0,
        importantConfig: formData.reviewFocus && formData.reviewFocus.length > 0 ? formData.reviewFocus.join(',') : '',
        matchRange: formData.regulationScope && formData.regulationScope.length > 0 ? formData.regulationScope.join(',') : '',
        metadata: '',
        version: 0,
        createdBy: formData.createdBy || '',
        updatedBy: '',
        isDeleted: false,
      }
    }
    else {
      submitData.supplementalReviewRule = {
        tenantId: formData.tenantId || 0,
        supplementalId: formData.id || 0,
        processId: 0,
        importantConfig: '',
        matchRange: '',
        metadata: '',
        version: 0,
        createdBy: formData.createdBy || '',
        updatedBy: '',
        isDeleted: false,
      }
    }

    // 根据是否有ID判断是新增还是编辑
    let result
    if (submitData.id) {
      // 编辑
      result = await otherApi.supplementalReview(null, submitData, 'update')
    }
    else {
      // 新增
      result = await otherApi.supplementalReview(null, submitData, 'create')
    }

    if (result.code === 0) {
      ElMessage.success('提交成功')
      router.push('/hegui/monitor/examination/otherReviews')
    }
    else {
      ElMessage.error(result.msg || '提交失败')
    }
  }
  catch (error) {
    console.error('表单验证失败', error)
    ElMessage.error('请完善必填信息')
  }
  finally {
    loading.value = false
  }
}

// 取消
function handleCancel() {
  ElMessageBox.confirm('确定要取消编辑吗？未保存的内容将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.push('/hegui/monitor/examination/otherReviews')
  }).catch(() => {})
}

// 预览
function handlePreview() {
  ElMessage.info('预览功能开发中')
}

// 获取详情数据
async function getDetailData() {
  if (!route.params.id) { return }

  try {
    loading.value = true
    const response = await otherApi.supplementalReview({}, { id: route.params.id }, 'info')
    const data = response.data

    // 基本信息字段映射
    formData.id = data.id
    formData.tenantId = data.tenantId
    formData.reviewCode = data.reviewCode || ''
    formData.name = data.name || ''
    formData.reviewObject = data.reviewObject || ''
    formData.level = data.level || 'GENERAL'
    formData.department = data.department || ''
    formData.auditBy = data.auditBy || ''
    formData.explain = data.explain || ''
    formData.reviewRange = data.reviewRange || ''
    formData.reviewTarget = data.reviewTarget || ''
    formData.reviewAccording = data.reviewAccording || ''
    formData.keyAttribute = data.keyAttribute || ''
    formData.notificationTargets = data.noticeObject || ''
    formData.startReminder = data.startRemind || 0
    formData.deadlineReminder = data.deadlineRemind || 0
    formData.version = data.version || 0
    formData.createdBy = data.createdBy || ''
    formData.status = data.status || 'MODIFY'

    // 格式化日期
    if (data.deadlineDate) {
      formData.deadlineDate = new Date(data.deadlineDate).toISOString().split('T')[0]
    }

    // 处理通知方式和状态通知
    if (data.noticeMethod) {
      formData.notificationMethods = [data.noticeMethod]
    }
    if (data.statusNotice) {
      formData.statusNotifications = [data.statusNotice]
    }

    // 处理附件数据
    if (data.supplementalAttachments && data.supplementalAttachments.length > 0) {
      const attachment = data.supplementalAttachments[0]
      formData.document = attachment.fileName || ''
      formData.documentUrl = attachment.filePath || ''
    }

    // 处理风险评估数据
    if (data.supplementalReviewRisks && data.supplementalReviewRisks.length > 0) {
      const risk = data.supplementalReviewRisks[0]
      if (risk.riskSelf) {
        formData.riskTypes = risk.riskSelf.split(',')
      }
      formData.riskDescription = risk.riskDesc || ''
    }

    // 处理特殊要求数据
    if (data.supplementalReviewSpecials && data.supplementalReviewSpecials.length > 0) {
      const special = data.supplementalReviewSpecials[0]
      formData.focusPoints = special.focus || ''
      if (special.reviewRequirement) {
        formData.specialRequirements = [special.reviewRequirement]
      }
      formData.specialInstructions = special.explain || ''
    }

    // 处理审查规则数据
    if (data.supplementalReviewRule) {
      const rule = data.supplementalReviewRule
      if (rule.processId) {
        formData.reviewProcess = rule.processId.toString()
      }
      if (rule.importantConfig) {
        formData.reviewFocus = rule.importantConfig.split(',')
      }
      if (rule.matchRange) {
        formData.regulationScope = rule.matchRange.split(',')
      }
    }
  }
  catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
  finally {
    loading.value = false
  }
}

// 添加关键属性
function addKeyAttribute() {
  keyAttributes.value.push({ name: '', value: '', explain: '' })
}

// 删除关键属性
function removeKeyAttribute(index: number) {
  keyAttributes.value.splice(index, 1)
}

// 添加风险点
function addRiskItem() {
  riskItems.value.push({
    riskSelf: '',
    riskType: 'LAWS',
    riskAssessment: '',
    riskDesc: '',
    level: 'LOW',
    effectRange: 'FINANCE',
    countermeasures: '',
  })
}

// 删除风险点
function removeRiskItem(index: number) {
  riskItems.value.splice(index, 1)
}

// 添加利益相关方
function addStakeholder() {
  stakeholders.value.push({
    relatedName: '',
    relatedType: '',
    relatedExplain: '',
  })
}

// 删除利益相关方
function removeStakeholder(index: number) {
  stakeholders.value.splice(index, 1)
}

onMounted(async () => {
  if (route.params.id) {
    // 编辑模式，加载数据
    await getDetailData()
  }
})

// 加载数据（保持兼容性）
async function loadData(id) {
  // 直接调用 getDetailData 函数
  await getDetailData()
}
</script>

<template>
  <div class="absolute-container">
    <pageHeader>
      <template #content>
        <!-- 顶部标题栏 -->
        <div class="mb-6 flex items-center justify-between">
          <h1 class="text-xl font-bold">
            新增其他审查
          </h1>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="handleSave">
              保存
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" :loading="loading" @click="handleSubmit">
              提交审查
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="handleCancel">
              取消
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="handlePreview">
              预览
            </el-button>
          </div>
        </div>
      </template>
    </pageHeader>
    <!-- 主内容区 -->
    <PageMain style="background-color: transparent;">
      <div>
        <!-- 页面内容 -->
        <div class="p-6">
          <div class="grid grid-cols-4 gap-6">
            <!-- 主表单区 -->
            <div class="col-span-3 space-y-6">
              <!-- 基本信息 -->
              <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      基本信息
                    </div>
                  </template>
                  <div class="grid grid-cols-2 gap-4">
                    <el-form-item label="审查名称" prop="name" required>
                      <el-input v-model="formData.name" placeholder="请输入审查名称" />
                    </el-form-item>
                    <el-form-item label="审查编号">
                      <div class="flex">
                        <el-input v-model="formData.reviewCode" placeholder="系统自动生成" disabled />
                        <el-button class="ml-2">
                          自动生成
                        </el-button>
                      </div>
                    </el-form-item>
                    <el-form-item label="审查对象" prop="reviewObject" required>
                      <el-input v-model="formData.reviewObject" placeholder="请输入审查对象" />
                    </el-form-item>
                    <el-form-item label="审查级别" prop="level" required>
                      <el-radio-group v-model="formData.level">
                        <el-radio-button value="GENERAL">
                          一般
                        </el-radio-button>
                        <el-radio-button value="IMPORTANT">
                          重要
                        </el-radio-button>
                        <el-radio-button value="CRITICAL">
                          关键
                        </el-radio-button>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="发起部门" prop="department" required>
                      <el-input v-model="formData.department" placeholder="请输入发起部门" />
                    </el-form-item>
                    <el-form-item label="审查人员" prop="auditBy" required>
                      <el-input v-model="formData.auditBy" placeholder="请输入审查人员" />
                    </el-form-item>
                    <el-form-item label="审查截止日期">
                      <el-date-picker v-model="formData.deadlineDate" type="date" placeholder="选择日期" class="w-full" />
                    </el-form-item>
                    <el-form-item label="审查说明" class="col-span-2">
                      <el-input v-model="formData.explain" type="textarea" :rows="3" placeholder="请输入审查说明" />
                    </el-form-item>
                  </div>
                </el-card>
                <!-- 审查内容 -->
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      审查内容
                    </div>
                  </template>
                  <div class="space-y-4">
                    <el-form-item label="审查范围" prop="reviewRange" required>
                      <el-input v-model="formData.reviewRange" type="textarea" :rows="2" placeholder="请输入审查范围" />
                    </el-form-item>
                    <el-form-item label="审查目标" prop="reviewTarget" required>
                      <el-input v-model="formData.reviewTarget" type="textarea" :rows="2" placeholder="请输入审查目标" />
                    </el-form-item>
                    <el-form-item label="对象描述">
                      <div class="border rounded">
                        <el-tiptap :height="200" />
                      </div>
                    </el-form-item>
                    <el-form-item label="审查依据">
                      <el-input v-model="formData.reviewAccording" type="textarea" :rows="2" placeholder="请输入审查依据" />
                    </el-form-item>
                    <el-form-item label="关键属性">
                      <div class="space-y-3">
                        <div class="flex items-center space-x-2">
                          <el-input placeholder="属性名称" />
                          <el-input placeholder="属性值" />
                          <el-input placeholder="属性说明" />
                          <el-button type="danger" text>
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                        <el-button type="primary" text>
                          <el-icon><Plus /></el-icon>
                          添加属性
                        </el-button>
                      </div>
                    </el-form-item>
                  </div>
                </el-card>
                <!-- 文档上传 -->
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      文档上传
                    </div>
                  </template>
                  <div class="space-y-6">
                    <el-form-item label="审查材料" prop="document" required>
                      <el-input v-model="formData.document" placeholder="请输入文档名称" />
                    </el-form-item>
                    <el-form-item label="文档URL">
                      <el-input v-model="formData.documentUrl" placeholder="请输入文档链接" />
                    </el-form-item>
                  </div>
                </el-card>
                <!-- 风险评估 -->
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      风险评估
                    </div>
                  </template>
                  <div class="space-y-6">
                    <el-form-item label="风险自评">
                      <el-checkbox-group v-model="formData.riskTypes">
                        <el-checkbox value="LEGAL">
                          法律风险
                        </el-checkbox>
                        <el-checkbox value="COMPLIANCE">
                          合规风险
                        </el-checkbox>
                        <el-checkbox value="TECHNICAL">
                          技术风险
                        </el-checkbox>
                        <el-checkbox value="BUSINESS">
                          商业风险
                        </el-checkbox>
                        <el-checkbox value="REPUTATION">
                          声誉风险
                        </el-checkbox>
                        <el-checkbox value="OTHER">
                          其他
                        </el-checkbox>
                      </el-checkbox-group>
                      <el-input
                        v-model="formData.riskDescription"
                        type="textarea"
                        :rows="2"
                        placeholder="请输入风险描述"
                        class="mt-2"
                      />
                    </el-form-item>
                    <el-form-item label="自动风险评估">
                      <div class="flex items-center space-x-4">
                        <el-button type="primary" class="!rounded-button whitespace-nowrap">
                          开始评估
                        </el-button>
                        <div class="text-gray-500">
                          点击按钮进行自动风险分析
                        </div>
                      </div>
                      <div class="mt-4 rounded bg-gray-50 p-4">
                        <div class="text-gray-500">
                          评估结果将显示在此处
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item label="风险点管理">
                      <div class="space-y-3">
                        <div class="border rounded p-4">
                          <div class="grid grid-cols-2 gap-4">
                            <el-form-item label="风险描述">
                              <el-input placeholder="请输入风险描述" />
                            </el-form-item>
                            <el-form-item label="风险类型">
                              <el-select placeholder="请选择风险类型">
                                <el-option label="法律风险" value="1" />
                                <el-option label="合规风险" value="2" />
                                <el-option label="技术风险" value="3" />
                              </el-select>
                            </el-form-item>
                            <el-form-item label="风险等级">
                              <el-radio-group>
                                <el-radio label="高" />
                                <el-radio label="中" />
                                <el-radio label="低" />
                              </el-radio-group>
                            </el-form-item>
                            <el-form-item label="影响范围">
                              <el-checkbox-group>
                                <el-checkbox label="财务" />
                                <el-checkbox label="运营" />
                                <el-checkbox label="客户" />
                              </el-checkbox-group>
                            </el-form-item>
                          </div>
                          <el-form-item label="应对措施" class="mt-2">
                            <el-input
                              type="textarea"
                              :rows="2"
                              placeholder="请输入应对措施"
                            />
                          </el-form-item>
                          <div class="flex justify-end">
                            <el-button type="danger" text>
                              <el-icon><Delete /></el-icon>
                              删除
                            </el-button>
                          </div>
                        </div>
                        <el-button type="primary" text>
                          <el-icon><Plus /></el-icon>
                          添加风险点
                        </el-button>
                      </div>
                    </el-form-item>
                  </div>
                <!-- 第一个 -->
                </el-card>
                <!-- 特殊要求区 -->
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      特殊要求
                    </div>
                  </template>
                  <div class="space-y-6">
                    <el-form-item label="关注重点">
                      <el-input v-model="formData.focusPoints" type="textarea" :rows="3" placeholder="请输入审查中需特别关注的内容" />
                    </el-form-item>
                    <el-form-item label="特殊审查要求">
                      <el-checkbox-group v-model="formData.specialRequirements">
                        <el-checkbox value="LEGAL_COMPLIANCE">
                          法律合规性审查
                        </el-checkbox>
                        <el-checkbox value="TECHNICAL_SECURITY">
                          技术安全性审查
                        </el-checkbox>
                        <el-checkbox value="BUSINESS_RISK">
                          商业风险评估
                        </el-checkbox>
                        <el-checkbox value="PRIVACY_PROTECTION">
                          隐私保护审查
                        </el-checkbox>
                        <el-checkbox value="OTHER">
                          其他特殊要求
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="特殊说明">
                      <el-input v-model="formData.specialInstructions" type="textarea" :rows="3" placeholder="请输入特殊说明" />
                    </el-form-item>
                    <el-form-item label="相关利益方">
                      <div class="space-y-3">
                        <div class="border rounded p-4">
                          <div class="grid grid-cols-2 gap-4">
                            <el-form-item label="相关方名称">
                              <el-input placeholder="请输入相关方名称" />
                            </el-form-item>
                            <el-form-item label="相关方类型">
                              <el-select placeholder="请选择类型">
                                <el-option label="内部部门" value="1" />
                                <el-option label="外部机构" value="2" />
                                <el-option label="合作伙伴" value="3" />
                                <el-option label="监管机构" value="4" />
                              </el-select>
                            </el-form-item>
                            <el-form-item label="关系说明" class="col-span-2">
                              <el-input type="textarea" :rows="2" placeholder="请输入关系说明" />
                            </el-form-item>
                          </div>
                          <div class="flex justify-end">
                            <el-button type="danger" text>
                              <el-icon><Delete /></el-icon>
                              删除
                            </el-button>
                          </div>
                        </div>
                        <el-button type="primary" text>
                          <el-icon><Plus /></el-icon>
                          添加利益方
                        </el-button>
                      </div>
                    </el-form-item>
                  </div>
                <!-- 第二个 -->
                </el-card>
                <!-- 审查规则配置区 -->
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      审查规则
                    </div>
                  </template>
                  <div class="space-y-6">
                    <el-form-item label="审查流程选择">
                      <el-select v-model="formData.reviewProcess" placeholder="请选择审查流程" class="w-full">
                        <el-option label="标准流程" value="STANDARD" />
                        <el-option label="简化流程" value="SIMPLIFIED" />
                        <el-option label="严格流程" value="STRICT" />
                        <el-option label="自定义流程" value="CUSTOM" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="审查重点配置">
                      <el-checkbox-group v-model="formData.reviewFocus">
                        <el-checkbox value="LEGAL_COMPLIANCE">
                          法律合规性
                        </el-checkbox>
                        <el-checkbox value="TECHNICAL_SECURITY">
                          技术安全性
                        </el-checkbox>
                        <el-checkbox value="DATA_PRIVACY">
                          数据隐私
                        </el-checkbox>
                        <el-checkbox value="BUSINESS_SUSTAINABILITY">
                          商业可持续性
                        </el-checkbox>
                        <el-checkbox value="OPERATIONAL_STABILITY">
                          运营稳定性
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="法规匹配范围">
                      <el-checkbox-group v-model="formData.regulationScope">
                        <el-checkbox value="CYBERSECURITY_LAW">
                          网络安全法
                        </el-checkbox>
                        <el-checkbox value="PERSONAL_INFO_PROTECTION">
                          个人信息保护法
                        </el-checkbox>
                        <el-checkbox value="DATA_SECURITY_LAW">
                          数据安全法
                        </el-checkbox>
                        <el-checkbox value="INDUSTRY_REGULATION">
                          行业监管要求
                        </el-checkbox>
                        <el-checkbox value="INTERNAL_STANDARDS">
                          企业内部规范
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="审批流程">
                      <div class="border rounded p-4">
                        <div class="mb-4 flex items-center justify-between">
                          <div class="text-gray-500">
                            当前审批流程
                          </div>
                          <el-button type="primary" text>
                            <el-icon><Plus /></el-icon>
                            添加节点
                          </el-button>
                        </div>
                        <div class="space-y-3">
                          <div class="flex items-center justify-between rounded bg-gray-50 p-3">
                            <div>
                              <div class="font-medium">
                                初审
                              </div>
                              <div class="text-sm text-gray-500">
                                技术部负责人
                              </div>
                            </div>
                            <div class="flex space-x-2">
                              <el-button size="small" text>
                                编辑
                              </el-button>
                              <el-button size="small" text type="danger">
                                删除
                              </el-button>
                            </div>
                          </div>
                          <div class="flex items-center justify-between rounded bg-gray-50 p-3">
                            <div>
                              <div class="font-medium">
                                复审
                              </div>
                              <div class="text-sm text-gray-500">
                                合规委员会
                              </div>
                            </div>
                            <div class="flex space-x-2">
                              <el-button size="small" text>
                                编辑
                              </el-button>
                              <el-button size="small" text type="danger">
                                删除
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-form-item>
                  </div>
                <!-- 第三个 -->
                </el-card>

                <!-- 通知设置区 -->
                <el-card shadow="hover" class="!border-0">
                  <template #header>
                    <div class="text-base font-bold">
                      通知设置
                    </div>
                  </template>
                  <div class="space-y-6">
                    <el-form-item label="通知方式">
                      <el-checkbox-group v-model="formData.notificationMethods">
                        <el-checkbox value="SYSTEM">
                          系统消息
                        </el-checkbox>
                        <el-checkbox value="EMAIL">
                          邮件通知
                        </el-checkbox>
                        <el-checkbox value="SMS">
                          短信提醒
                        </el-checkbox>
                        <el-checkbox value="OTHER">
                          其他方式
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="通知对象">
                      <el-input v-model="formData.notificationTargets" placeholder="请输入通知对象" />
                    </el-form-item>
                    <el-form-item label="重要状态通知">
                      <el-checkbox-group v-model="formData.statusNotifications">
                        <el-checkbox value="REVIEW_START">
                          审查开始
                        </el-checkbox>
                        <el-checkbox value="REVIEW_DELAY">
                          审查延期
                        </el-checkbox>
                        <el-checkbox value="REVIEW_COMPLETE">
                          审查完成
                        </el-checkbox>
                        <el-checkbox value="RISK_ESCALATION">
                          风险升级
                        </el-checkbox>
                        <el-checkbox value="APPROVAL_PASS">
                          审批通过
                        </el-checkbox>
                        <el-checkbox value="APPROVAL_REJECT">
                          审批驳回
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="提醒设置">
                      <div class="grid grid-cols-2 gap-4">
                        <el-form-item label="开始提醒">
                          <el-input-number v-model="formData.startReminder" :min="1" :max="30" />
                          <span class="ml-2 text-gray-500">天前提醒</span>
                        </el-form-item>
                        <el-form-item label="截止提醒">
                          <el-input-number v-model="formData.deadlineReminder" :min="1" :max="30" />
                          <span class="ml-2 text-gray-500">天前提醒</span>
                        </el-form-item>
                      </div>
                    </el-form-item>
                  </div>
                <!-- 第四个 -->
                </el-card>
              </el-form>
            </div>
            <!-- 右侧辅助区 -->
            <div class="space-y-6">
              <el-card shadow="hover" class="!border-0">
                <template #header>
                  <div class="text-base font-bold">
                    审查指南
                  </div>
                </template>
                <div class="text-sm text-gray-600 space-y-2">
                  <p>1. 确保审查名称简洁明了</p>
                  <p>2. 审查对象需具体到产品或系统</p>
                  <p>3. 审查级别应根据重要性合理选择</p>
                  <el-link type="primary" class="mt-2">
                    查看详细指南
                  </el-link>
                </div>
              </el-card>
              <el-card shadow="hover" class="!border-0">
                <template #header>
                  <div class="text-base font-bold">
                    审查模板
                  </div>
                </template>
                <div class="space-y-2">
                  <el-link type="primary">
                    安全合规审查模板
                  </el-link>
                  <el-link type="primary">
                    人事合规审查模板
                  </el-link>
                  <el-link type="primary">
                    IT系统合规审查模板
                  </el-link>
                  <el-link type="primary">
                    产品发布合规模板
                  </el-link>
                </div>
              </el-card>
              <el-card shadow="hover" class="!border-0">
                <template #header>
                  <div class="text-base font-bold">
                    AI辅助
                  </div>
                </template>
                <div class="space-y-3">
                  <el-button class="!rounded-button w-full whitespace-nowrap">
                    AI智能填写
                  </el-button>
                  <el-button class="!rounded-button w-full whitespace-nowrap">
                    AI风险预分析
                  </el-button>
                  <el-button class="!rounded-button w-full whitespace-nowrap">
                    AI审查建议
                  </el-button>
                  <div class="mt-2 rounded bg-gray-50 p-3">
                    <div class="text-sm text-gray-500">
                      AI建议将显示在此处
                    </div>
                  </div>
                </div>
              </el-card>
              <el-card shadow="hover" class="!border-0">
                <template #header>
                  <div class="text-base font-bold">
                    合规参考
                  </div>
                </template>
                <div class="space-y-2">
                  <el-link type="primary">
                    《网络安全法》
                  </el-link>
                  <el-link type="primary">
                    《个人信息保护法》
                  </el-link>
                  <el-link type="primary">
                    ISO 27001标准
                  </el-link>
                  <el-link type="primary" class="mt-2 block">
                    查看相似审查
                  </el-link>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

  <style lang="scss" scoped>
  @use "@/styles/toolsCss";
  </style>
