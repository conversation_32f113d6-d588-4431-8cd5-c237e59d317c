---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 08-合同管理服务/合同审查管理

## POST 创建合同评审记录

POST /whiskerguardcontractservice/api/contract/reviews

描述：创建合同评审记录。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "employeeId": 0,
  "contractType": "string",
  "contractTitle": "string",
  "contractContent": "string",
  "reviewResult": "string",
  "status": "MODIFY",
  "overallRiskLevel": "HIGH",
  "riskScore": 100,
  "riskSummary": "string",
  "aiRequestId": 0,
  "reviewStartTime": {
    "seconds": 0,
    "nanos": 0
  },
  "reviewEndTime": {
    "seconds": 0,
    "nanos": 0
  },
  "reviewDuration": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[ContractReviewDTO](#schemacontractreviewdto)| 否 |none|

> 返回示例

> 201 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "",
  "name": "",
  "contractType": "",
  "level": "",
  "department": "",
  "auditBy": "",
  "explain": "",
  "deadlineDate": "",
  "status": "",
  "riskSelf": "",
  "riskType": "",
  "riskDesc": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "contractMessage": {
    "id": 0,
    "contractId": 0,
    "tenantId": 0,
    "firstParty": "",
    "secondParty": "",
    "thirdParty": "",
    "money": 0,
    "signDate": "",
    "performancePeriodStart": {
      "seconds": 0,
      "nanos": 0
    },
    "performancePeriodEnd": {
      "seconds": 0,
      "nanos": 0
    },
    "payWay": "",
    "solveWay": "",
    "regulationId": 0,
    "defaultResponsibility": "",
    "content": "",
    "document": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "contractAttachments": [
    {
      "id": 0,
      "contractId": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "uploadedBy": "",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|none|[ResponseEntityContractReviewDTO](#schemaresponseentitycontractreviewdto)|

# 数据模型

<h2 id="tocS_ResponseEntityContractReviewDTO">ResponseEntityContractReviewDTO</h2>

<a id="schemaresponseentitycontractreviewdto"></a>
<a id="schema_ResponseEntityContractReviewDTO"></a>
<a id="tocSresponseentitycontractreviewdto"></a>
<a id="tocsresponseentitycontractreviewdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "employeeId": 0,
  "contractType": "string",
  "contractTitle": "string",
  "contractContent": "string",
  "reviewResult": "string",
  "status": "PENDING",
  "overallRiskLevel": "HIGH",
  "riskScore": 100,
  "riskSummary": "string",
  "aiRequestId": 0,
  "reviewStartTime": {
    "seconds": 0,
    "nanos": 0
  },
  "reviewEndTime": {
    "seconds": 0,
    "nanos": 0
  },
  "reviewDuration": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|employeeId|integer(int64)|true|none||员工ID|
|contractType|string|false|none||合同类型：技术服务合同、重大决策、数据合同、采购合同|
|contractTitle|string|false|none||合同标题|
|contractContent|string|false|none||合同内容|
|reviewResult|string|false|none||审查结果（JSON格式）|
|status|string|true|none||状态：需修改、待审查、发布、审核中、已撤回|
|overallRiskLevel|string|false|none||整体风险等级|
|riskScore|integer|false|none||风险分数 (0-100)|
|riskSummary|string|false|none||风险总结|
|aiRequestId|integer(int64)|false|none||AI调用ID - 关联到ai_request表|
|reviewStartTime|[Instant](#schemainstant)|false|none||审查开始时间|
|reviewEndTime|[Instant](#schemainstant)|false|none||审查完成时间|
|reviewDuration|integer(int64)|false|none||审查耗时（毫秒）|
|metadata|string|false|none||补充字段|
|version|integer|true|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|true|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|true|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|status|PENDING|
|status|PROCESSING|
|status|COMPLETED|
|status|FAILED|
|status|CANCELLED|
|overallRiskLevel|HIGH|
|overallRiskLevel|MEDIUM|
|overallRiskLevel|LOW|

<h2 id="tocS_ContractReviewDTO">ContractReviewDTO</h2>

<a id="schemacontractreviewdto"></a>
<a id="schema_ContractReviewDTO"></a>
<a id="tocScontractreviewdto"></a>
<a id="tocscontractreviewdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "employeeId": 0,
  "contractType": "string",
  "contractTitle": "string",
  "contractContent": "string",
  "reviewResult": "string",
  "status": "MODIFY",
  "overallRiskLevel": "HIGH",
  "riskScore": 100,
  "riskSummary": "string",
  "aiRequestId": 0,
  "reviewStartTime": {
    "seconds": 0,
    "nanos": 0
  },
  "reviewEndTime": {
    "seconds": 0,
    "nanos": 0
  },
  "reviewDuration": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID - 多租户数据隔离|
|employeeId|integer(int64)|true|none||员工ID|
|contractType|string|false|none||合同类型：技术服务合同、重大决策、数据合同、采购合同|
|contractTitle|string|false|none||合同标题|
|contractContent|string|false|none||合同内容|
|reviewResult|string|false|none||审查结果（JSON格式）|
|status|string|true|none||状态：需修改、待审查、发布、审核中、已撤回|
|overallRiskLevel|string|false|none||整体风险等级|
|riskScore|integer|false|none||风险分数 (0-100)|
|riskSummary|string|false|none||风险总结|
|aiRequestId|integer(int64)|false|none||AI调用ID - 关联到ai_request表|
|reviewStartTime|[Instant](#schemainstant)|false|none||审查开始时间|
|reviewEndTime|[Instant](#schemainstant)|false|none||审查完成时间|
|reviewDuration|integer(int64)|false|none||审查耗时（毫秒）|
|metadata|string|false|none||扩展元数据|
|version|integer|true|none||乐观锁版本|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|true|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|true|none||软删除标志|

#### 枚举值

|属性|值|
|---|---|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|
|overallRiskLevel|HIGH|
|overallRiskLevel|MEDIUM|
|overallRiskLevel|LOW|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}
```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
