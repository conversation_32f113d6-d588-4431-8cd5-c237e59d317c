<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import contractApi from '@/api/review/contract'
import {
  ArrowLeft,
  Clock,
  Comment,
  Document,
  Download,
  Files,
  FullScreen,
  Printer,
  Promotion,
  User,
  VideoPlay,
  ZoomIn,
  ZoomOut,
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const contractDetail = ref<any>({})

const activeTab = ref('detail')

const contractElements = ref([
  {
    name: '合同主体',
    content: '甲方：XX科技有限公司\n乙方：阿里云计算有限公司',
    compliance: '合规',
    remark: '双方资质齐全',
  },
  {
    name: '合同金额',
    content: '人民币1,200,000元（含税）',
    compliance: '存疑',
    remark: '需确认预算审批',
  },
  {
    name: '履行期限',
    content: '2023年5月1日至2024年4月30日',
    compliance: '合规',
    remark: '符合公司年度采购周期',
  },
  {
    name: '违约责任',
    content: '服务不达标按日扣减0.1%合同金额，最高不超过合同总额10%',
    compliance: '不合规',
    remark: '违约金比例低于公司标准',
  },
  {
    name: '数据安全',
    content: '乙方需符合等保三级要求，数据不出境',
    compliance: '合规',
    remark: '已确认阿里云等保三级资质',
  },
])

// 获取详情数据
async function getDetailData() {
  if (!route.query.id) {
    ElMessage.error('缺少ID参数')
    router.push('/monitor/examination/contractReview')
    return
  }
  
  try {
    loading.value = true
    const response = await contractApi.contractReview({}, { id: route.query.id }, 'info')
    
    if (response.data) {
      contractDetail.value = response.data
      
      // 更新合同要素数据
      updateContractElements(response.data)
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 更新合同要素
function updateContractElements(data: any) {
  const elements = []
  
  if (data.contractMessage) {
    const msg = data.contractMessage
    
    // 合同主体
    if (msg.firstParty || msg.secondParty) {
      elements.push({
        name: '合同主体',
        content: `甲方：${msg.firstParty || ''}\n乙方：${msg.secondParty || ''}${msg.thirdParty ? `\n丙方：${msg.thirdParty}` : ''}`,
        compliance: '合规',
        remark: '双方资质齐全',
      })
    }
    
    // 合同金额
    if (msg.money) {
      elements.push({
        name: '合同金额',
        content: `人民币${msg.money.toLocaleString()}元`,
        compliance: '合规',
        remark: '金额符合预算',
      })
    }
    
    // 履行期限
    if (msg.performancePeriodStart && msg.performancePeriodEnd) {
      elements.push({
        name: '履行期限',
        content: `${msg.performancePeriodStart} 至 ${msg.performancePeriodEnd}`,
        compliance: '合规',
        remark: '期限合理',
      })
    }
    
    // 违约责任
    if (msg.defaultResponsibility) {
      elements.push({
        name: '违约责任',
        content: msg.defaultResponsibility,
        compliance: '合规',
        remark: '条款完善',
      })
    }
    
    // 争议解决
    if (msg.solveWay) {
      elements.push({
        name: '争议解决',
        content: msg.solveWay,
        compliance: '合规',
        remark: '解决方式明确',
      })
    }
  }
  
  if (elements.length > 0) {
    contractElements.value = elements
  }
}

// 返回列表
function handleBack() {
  router.push('/monitor/examination/contractReview')
}

// 编辑
function handleEdit() {
  router.push(`/monitor/examination/contractReview/addEdit?id=${route.query.id}`)
}

// 状态映射
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    'MODIFY': '需修改',
    'PENDING': '待审查',
    'PUBLISHED': '已发布',
    'REVIEWING': '审查中',
    'REVOKE': '已撤回',
  }
  return statusMap[status] || status
}

// 级别映射
function getLevelText(level: string) {
  const levelMap: Record<string, string> = {
    'GENERAL': '一般',
    'IMPORTANT': '重要',
    'CRITICAL': '关键',
  }
  return levelMap[level] || level
}

// 合同类型映射
function getContractTypeText(type: string) {
  const typeMap: Record<string, string> = {
    'TECHNICAL_SERVICES': '技术服务合同',
    'MAJOR_DECISIONS': '重大决策',
    'DATA': '数据合同',
    'PROCUREMENT': '采购合同',
  }
  return typeMap[type] || type
}

// 风险类型映射
function getRiskTypeText(type: string) {
  const typeMap: Record<string, string> = {
    'LAWS': '法律风险',
    'FINANCE': '财务风险',
    'OPERATION': '操作风险',
    'REPUTATION': '声誉风险',
    'OTHER': '其他风险',
  }
  return typeMap[type] || type
}

// 初始化
onMounted(() => {
  getDetailData()
})

const attachments = ref([
  {
    name: '阿里云服务报价单',
    type: 'Excel',
    size: '256 KB',
    uploadTime: '2023-04-20 10:15',
  },
  {
    name: '阿里云等保三级证书',
    type: 'PDF',
    size: '1.2 MB',
    uploadTime: '2023-04-21 14:30',
  },
  {
    name: '技术方案确认函',
    type: 'Word',
    size: '512 KB',
    uploadTime: '2023-04-22 09:45',
  },
])

const reviewSteps = ref([
  {
    step: 1,
    title: '初审完成',
    time: '2023-04-26 10:30',
    person: '合规专员：李合规',
    statusClass: 'bg-secondary text-white',
    timeClass: 'text-gray-500',
  },
  {
    step: 2,
    title: '法务审核中',
    time: '预计2023-04-28完成',
    person: '法务专员：王法务',
    statusClass: 'bg-primary text-white',
    timeClass: 'text-gray-500',
  },
  {
    step: 3,
    title: '合规审核',
    time: '待开始',
    statusClass: 'bg-gray-200 text-gray-500',
    timeClass: 'text-gray-400',
  },
  {
    step: 4,
    title: '终审',
    time: '待开始',
    statusClass: 'bg-gray-200 text-gray-500',
    timeClass: 'text-gray-400',
  },
])

const relatedLaws = ref([
  {
    name: '《中华人民共和国合同法》',
    articles: '第52条、第107条',
  },
  {
    name: '《网络安全法》',
    articles: '第21条、第31条',
  },
  {
    name: '《数据安全法》',
    articles: '第27条、第30条',
  },
  {
    name: '《个人信息保护法》',
    articles: '第28条、第51条',
  },
  {
    name: '《XX公司采购管理办法》',
    articles: '第5章第3节',
  },
])

const relatedCases = ref([
  {
    name: '2022年度云服务采购合同审查',
    desc: '审查结果：有条件通过',
  },
  {
    name: '腾讯云服务合同纠纷案例',
    desc: '案例编号：AL2021-036',
  },
  {
    name: '华为云数据安全合规案例',
    desc: '参考价值：高',
  },
])

function getTagType(status: string) {
  switch (status) {
    case '合规': return 'success'
    case '存疑': return 'warning'
    case '不合规': return 'danger'
    default: return ''
  }
}

// 获取状态标签类型
function getStatusTagType(status: string) {
  const typeMap: Record<string, string> = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'MODIFY': 'info',
    'DRAFT': '',
  }
  return typeMap[status] || ''
}

// 获取级别标签类型
function getLevelTagType(level: string) {
  const typeMap: Record<string, string> = {
    'LOW': '',
    'MEDIUM': 'warning',
    'HIGH': 'danger',
  }
  return typeMap[level] || ''
}
</script>

<template>
  <div class="absolute-container">
    <div style="height: 100%;overflow-y: auto;">
      <page-header title="" content="">
        <template #content>
          <div class="aic jcsb flex">
            <div class="mt-4 flex items-center justify-between">
              <h1 class="text-xl c-[#000000] font-bold">
                {{ contractDetail.name || '合同审查详情' }}
              </h1>
              <el-tag :type="getStatusTagType(contractDetail.status)" class="ml-4">
                {{ getStatusText(contractDetail.status) }}
              </el-tag>
            </div>
            <div class="flex items-center space-x-3">
              <el-button type="primary" class="!rounded-button">
                <el-icon class="mr-2">
                  <VideoPlay />
                </el-icon>开始审查
              </el-button>
              <el-button plain class="!rounded-button">
                <el-icon class="mr-2">
                  <Promotion />
                </el-icon>提交审查
              </el-button>
              <el-button plain class="!rounded-button">
                <el-icon class="mr-2">
                  <Document />
                </el-icon>导出报告
              </el-button>
              <el-button plain class="!rounded-button">
                <el-icon class="mr-2">
                  <Printer />
                </el-icon>打印
              </el-button>
              <el-button plain class="!rounded-button" @click="handleBack">
                <el-icon class="mr-2">
                  <ArrowLeft />
                </el-icon>返回
              </el-button>
            </div>
          </div>
        </template>
      </page-header>
      <PageMain style="background-color: transparent;">
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card class="">
              <div class="grid grid-cols-2 mb-6 gap-6">
                <div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">审查编号</label>
                    <p class="font-medium">
                      {{ contractDetail.reviewCode }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">合同名称</label>
                    <p class="font-medium">
                      {{ contractDetail.name }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">合同类型</label>
                    <p class="font-medium">
                      {{ getContractTypeText(contractDetail.contractType) }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">审查级别</label>
                    <p class="font-medium">
                      <el-tag :type="getLevelTagType(contractDetail.level)">
                        {{ getLevelText(contractDetail.level) }}
                      </el-tag>
                    </p>
                  </div>
                  <div>
                    <label class="block text-sm text-gray-500">发起部门</label>
                    <p class="font-medium">
                      {{ contractDetail.department }}
                    </p>
                  </div>
                </div>
                <div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">发起人</label>
                    <p class="font-medium">
                      {{ contractDetail.createdBy || '-' }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">发起时间</label>
                    <p class="font-medium">
                      {{ contractDetail.createdAt || '-' }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">审查人</label>
                    <p class="font-medium">
                      {{ contractDetail.auditBy || '-' }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">审查状态</label>
                    <p class="font-medium">
                      <el-tag :type="getStatusTagType(contractDetail.status)">
                        {{ getStatusText(contractDetail.status) }}
                      </el-tag>
                    </p>
                  </div>
                  <div>
                    <label class="block text-sm text-gray-500">完成时间</label>
                    <p class="font-medium">
                      {{ contractDetail.updatedAt || '-' }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="border-t border-gray-200 pt-4">
                <h3 class="mb-3 text-sm font-medium">
                  审查进度
                </h3>
                <div class="mb-2 flex items-center justify-between">
                  <div class="h-2 flex-1 overflow-hidden rounded-full bg-gray-200">
                    <div class="from-secondary to-primary h-full bg-gradient-to-r" style="width: 60%;" />
                  </div>
                </div>
                <div class="flex justify-between text-xs text-gray-500">
                  <span class="flex flex-col items-center">
                    <span
                      class="bg-secondary mb-1 h-6 w-6 flex items-center justify-center rounded-full text-white"
                    >1</span>
                    <span>初审</span>
                  </span>
                  <span class="flex flex-col items-center">
                    <span
                      class="bg-primary mb-1 h-6 w-6 flex items-center justify-center rounded-full text-white"
                    >2</span>
                    <span>法务审核</span>
                  </span>
                  <span class="flex flex-col items-center">
                    <span
                      class="mb-1 h-6 w-6 flex items-center justify-center rounded-full bg-gray-200 text-gray-500"
                    >3</span>
                    <span>合规审核</span>
                  </span>
                  <span class="flex flex-col items-center">
                    <span
                      class="mb-1 h-6 w-6 flex items-center justify-center rounded-full bg-gray-200 text-gray-500"
                    >4</span>
                    <span>终审</span>
                  </span>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="合同详情" name="detail">
                  <h3 class="mb-4 text-base font-bold">
                    合同概述
                  </h3>
                  <p class="mb-6 text-sm text-gray-700">
                    本合同为公司与阿里云签订的2023年度云服务采购合同，合同金额为人民币1,200,000元，服务期限为2023年5月1日至2024年4月30日。服务内容包括云服务器ECS、对象存储OSS、内容分发网络CDN等基础云服务，以及数据库RDS、大数据分析服务等增值服务。合同约定了服务级别协议(SLA)、数据安全条款、违约责任等关键条款。
                  </p>
                  <h3 class="mb-4 text-base font-bold">
                    合同要素
                  </h3>
                  <el-table :data="contractElements" class="mb-6">
                    <el-table-column prop="name" label="要素名称" />
                    <el-table-column prop="content" label="内容" />
                    <el-table-column prop="compliance" label="合规评估">
                      <template #default="{ row }">
                        <el-tag :type="getTagType(row.compliance)">
                          {{ row.compliance }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="remark" label="备注" />
                  </el-table>

                  <h3 class="mb-4 text-base font-bold">
                    合同文档
                  </h3>
                  <el-card class="mb-6">
                    <div class="mb-4 flex items-center justify-between">
                      <div class="flex items-center">
                        <el-icon class="mr-2 text-xl text-red-500">
                          <Document />
                        </el-icon>
                        <span class="font-medium">2023年度云服务采购合同.pdf</span>
                      </div>
                      <div class="flex space-x-2">
                        <el-button circle>
                          <el-icon><ZoomIn /></el-icon>
                        </el-button>
                        <el-button circle>
                          <el-icon><ZoomOut /></el-icon>
                        </el-button>
                        <el-button circle>
                          <el-icon><FullScreen /></el-icon>
                        </el-button>
                        <el-button circle>
                          <el-icon>
                            <Download />
                          </el-icon>
                        </el-button>
                      </div>
                    </div>
                    <div class="h-64 flex items-center justify-center border border-gray-200 rounded bg-gray-100">
                      <div class="text-center">
                        <el-icon class="mb-2 text-5xl text-red-500">
                          <Document />
                        </el-icon>
                        <p class="text-sm text-gray-500">
                          合同文档预览区域
                        </p>
                      </div>
                    </div>
                  </el-card>

                  <h3 class="mb-4 text-base font-bold">
                    合同附件
                  </h3>
                  <el-table :data="attachments">
                    <el-table-column prop="name" label="附件名称" />
                    <el-table-column prop="type" label="类型" />
                    <el-table-column prop="size" label="大小" />
                    <el-table-column prop="uploadTime" label="上传时间" />
                    <el-table-column label="操作">
                      <template #default>
                        <el-button type="text">
                          查看
                        </el-button>
                        <el-button type="text">
                          下载
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>
                <el-tab-pane label="审查记录" name="record" />
                <el-tab-pane label="法规匹配" name="law" />
                <el-tab-pane label="风险识别" name="risk" />
                <el-tab-pane label="审查意见" name="opinion" />
                <el-tab-pane label="操作历史" name="history" />
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="">
              <template #header>
                <div class="f-16 fw-600">
                  审查进度
                </div>
              </template>
              <div class="space-y-4">
                <div v-for="(step, index) in reviewSteps" :key="index" class="flex">
                  <div class="mr-3 flex flex-col items-center">
                    <div class="h-6 w-6 rounded-full" :class="step.statusClass">
                      {{ step.step }}
                    </div>
                    <div v-if="index < reviewSteps.length - 1" class="h-8 w-px bg-gray-200" />
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      {{ step.title }}
                    </p>
                    <p class="text-xs" :class="step.timeClass">
                      {{ step.time }}
                    </p>
                    <p v-if="step.person" class="mt-1 text-xs text-gray-500">
                      {{ step.person }}
                    </p>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关法规
                </div>
              </template>
              <ul class="space-y-3">
                <li v-for="(law, index) in relatedLaws" :key="index">
                  <el-link type="primary">
                    {{ law.name }}
                  </el-link>
                  <p class="mt-1 text-xs text-gray-500">
                    {{ law.articles }}
                  </p>
                </li>
              </ul>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关案例
                </div>
              </template>
              <ul class="space-y-3">
                <li v-for="(caseItem, index) in relatedCases" :key="index">
                  <el-link type="primary">
                    {{ caseItem.name }}
                  </el-link>
                  <p class="mt-1 text-xs text-gray-500">
                    {{ caseItem.desc }}
                  </p>
                </li>
              </ul>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-2">
                <div>
                  <el-button type="primary" class="!rounded-button w-full">
                    <el-icon class="mr-2">
                      <Comment />
                    </el-icon>添加意见
                  </el-button>
                </div>
                <div>
                  <el-button plain class="!rounded-button w-full">
                    <el-icon class="mr-2">
                      <Clock />
                    </el-icon>查看历史版本
                  </el-button>
                </div>
                <div>
                  <el-button plain class="!rounded-button w-full">
                    <el-icon class="mr-2">
                      <Files />
                    </el-icon>合同比对
                  </el-button>
                </div>
                <div>
                  <el-button plain class="!rounded-button w-full">
                    <el-icon class="mr-2">
                      <User />
                    </el-icon>咨询专家
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </PageMain>
    </div>
  </div>
</template>

<style scoped>
  [type="number"]::-webkit-inner-spin-button,
  [type="number"]::-webkit-outer-spin-button {
    margin: 0;
    appearance: none;
  }

  :deep(.el-card) {
    border-radius: 4px;
  }

  :deep(.el-tabs__item) {
    height: 48px;
    padding: 0 16px;
    line-height: 48px;
  }

  :deep(.el-table) {
    font-size: 14px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f9fafb;
  }

  :deep(.el-table .cell) {
    white-space: pre-line;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
