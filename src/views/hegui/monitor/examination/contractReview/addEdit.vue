<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Bell,
  Close,
  Document,
  InfoFilled,
  MagicStick,
  Notebook,
  Plus,
  Promotion,
  ScaleToOriginal,
  SuccessFilled,
  Upload,
  User,
  VideoPlay,
  Warning,
  // Lightbulb,
  // Shield,
} from '@element-plus/icons-vue'
import contractApi from '@/api/review/contract'
import dictApi from '@/api/modules/system/dict'
import DocumentUpload from '@/components/DocumentUpload/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import InitiateDialog from '@/components/initiate/index.vue'
import ReviewResultDialog from '@/components/ReviewResultDialog/index.vue'

const route = useRoute()
const router = useRouter()
const isEdit = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const contractTypeOptions = ref([])

// 响应式数据
const payWayOptions = ref([])
const solveWayOptions = ref([])
const showLawsPop = ref(false)
const selectedLaws = ref([])
const selectedLawsDisplay = ref('')

// 审查发起弹窗相关数据
const showInitiateDialog = ref(false)

// 审查结果弹窗相关数据
const showReviewResultDialog = ref(false)

const form = ref({
  id: null,
  // tenantId: 1,
  reviewCode: null,
  name: null,
  contractType: null,
  level: null,
  department: null,
  auditBy: null,
  explain: null,
  deadlineDate: null,
  riskSelf: null,
  riskType: null,
  riskDesc: null,
  status: null,
  complianceReview: null,
  reviewProcess: null,
  reviewFocus: null,
  autoSelectLaw: null,
  createdBy: null,
  createdAt: null,
  contractMessage: {
    firstParty: null,
    secondParty: null,
    thirdParty: null,
    money: null,
    signDate: null,
    performancePeriodStart: null,
    performancePeriodEnd: null,
    payWay: null,
    solveWay: null,
    regulationId: null,
    defaultResponsibility: null,
    content: null,
    document: null,
    documentUrl: null,
  },
  contractAttachments: [] as Array<{
    fileName: string
    fileUrl: string
    fileType: string
    fileSize: number
  }>,
})

// 表单验证规则
const rules = ref<FormRules>({
  'name': [
    { required: true, message: '请输入合同名称', trigger: 'blur' },
    { min: 2, max: 100, message: '合同名称长度在 2 到 100 个字符', trigger: 'blur' },
  ],
  'contractType': [
    { required: true, message: '请选择合同类型', trigger: 'change' },
  ],
  'level': [
    { required: true, message: '请选择审查级别', trigger: 'change' },
  ],
  'department': [
    { required: true, message: '请输入发起部门', trigger: 'blur' },
  ],
  'auditBy': [
    { required: true, message: '请输入审查人员', trigger: 'blur' },
  ],
  'contractMessage.firstParty': [
    { required: true, message: '请输入甲方名称', trigger: 'blur' },
  ],
  'contractMessage.secondParty': [
    { required: true, message: '请输入乙方名称', trigger: 'blur' },
  ],
  'contractMessage.money': [
    { required: true, message: '请输入合同金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '合同金额必须大于等于0', trigger: 'blur' },
  ],
})

async function generateReviewNumber() {
  try {
    const response = await dictApi.getCode('CONTRACT')
    if (response) {
      form.value.reviewCode = response
    }
  }
  catch (error) {
    // console.error('获取审查编号失败:', error)
    ElMessage.error('获取审查编号失败')
  }
}

// 获取详情数据
async function getDetailData() {
  if (!route.query.id) {
    return
  }

  try {
    loading.value = true
    const response = await contractApi.contractReview({}, { id: route.query.id }, 'info')

    if (response) {
      const data = response
      form.value = {
        ...data,
      }
    }
  }
  catch (error) {
    // console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
  finally {
    loading.value = false
  }
}
// 检查审核权限
async function checkAuditPermission() {
  if (!form.value.id) {
    return false
  }

  try {
    const res = await contractApi.getComplianceProcess({
      objectId: form.value.id,
      reviewType: 'CONTRACT',
    })
    // 根据接口返回的isAudit字段判断审核权限
    return res
  }
  catch (err) {
    // console.error('检查审核权限失败:', err)
    return false
  }
}
// 一键审核
async function startReview() {
  if (!form.value.id) {
    ElMessage.error('参数错误')
    return
  }

  // 根据complianceReview字段判断跳转页面
  if (form.value.complianceReview?.id === null) {
    // complianceReview为null，显示审查发起弹窗
    showInitiateDialog.value = true
  }
  else {
    // complianceReview不为null，先检查审核权限
    const hasAuditPermission = await checkAuditPermission()
    if (!hasAuditPermission) {
      ElMessage.error('暂无审核权限')
      return
    }

    if (hasAuditPermission.isSubmit) {
      // 显示审查发起弹窗
      showInitiateDialog.value = true
      return
    }
    else {
      if (!hasAuditPermission.isAudit) {
        ElMessage.error('暂无审核权限')
        return
      }
    }

    // 显示审查结果弹窗
    ElMessageBox.confirm(
      '确定要进行合规审查吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    ).then(() => {
      showReviewResultDialog.value = true
    }).catch(() => {
      // 用户点击了取消
      // 不进行智能审查操作
    })
  }
}
// 保存数据
async function handleSave() {
  if (!formRef.value) {
    return
  }

  try {
    // 验证表单
    await formRef.value.validate()

    loading.value = true

    const submitData = {
      ...form.value,
      // 如果 contractAttachments 数组为空，则设置为 null
      contractAttachments: form.value.contractAttachments.length === 0 ? null : form.value.contractAttachments,
    }

    let response
    if (isEdit.value && form.value.id) {
      response = await contractApi.contractReview({}, submitData, 'update')
    }
    else {
      response = await contractApi.contractReview({}, submitData, 'create')
    }

    if (response.data) {
      ElMessage.success(isEdit.value ? '保存成功' : '创建成功')
      router.push('/monitor/examination/contractReview')
    }
  }
  catch (error) {
    if (error === false) {
      ElMessage.error('请检查表单填写是否正确')
    }
    else {
      // console.error('保存失败:', error)
      ElMessage.error('保存失败')
    }
  }
  finally {
    loading.value = false
  }
}

// 提交审查
async function handleSubmit() {
  if (!formRef.value) {
    return
  }

  try {
    // 验证表单
    await formRef.value.validate()

    loading.value = true

    const submitData = {
      ...form.value,
      // 如果 contractAttachments 数组为空，则设置为 null
      contractAttachments: form.value.contractAttachments.length === 0 ? null : form.value.contractAttachments,
    }

    let response
    if (isEdit.value && form.value.id) {
      response = await contractApi.contractReview({}, submitData, 'update')
    }
    else {
      response = await contractApi.contractReview({}, submitData, 'create')
    }

    if (response.data) {
      ElMessage.success('提交成功')
      router.push('/monitor/examination/contractReview')
    }
  }
  catch (error) {
    if (error === false) {
      ElMessage.error('请检查表单填写是否正确')
    }
    else {
      // console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  }
  finally {
    loading.value = false
  }
}

// 返回列表
function handleBack() {
  router.push('/monitor/examination/contractReview')
}

// 获取合同类型选项
async function getContractTypeOptions() {
  try {
    const response = await dictApi.dictAll(56)
    if (response) {
      contractTypeOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    // console.error('获取合同类型选项失败:', error)
    ElMessage.error('获取合同类型选项失败')
  }
}

// 获取付款方式选项
async function getPayWayOptions() {
  try {
    const response = await dictApi.dictAll(60)
    if (response) {
      payWayOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    // console.error('获取付款方式选项失败:', error)
    ElMessage.error('获取付款方式选项失败')
  }
}

// 获取争议解决方式选项
async function getSolveWayOptions() {
  try {
    const response = await dictApi.dictAll(63)
    if (response) {
      solveWayOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    // console.error('获取争议解决方式选项失败:', error)
    ElMessage.error('获取争议解决方式选项失败')
  }
}

// 打开法规选择弹窗
function openLawsPop() {
  showLawsPop.value = true
}

// 确认选择法规
function handleLawsConfirm(selectedIds: string[], selectedItems: any[]) {
  // 由于要求单选，只取第一个选中的项目
  if (selectedItems.length > 0) {
    const selectedItem = selectedItems[0]
    selectedLaws.value = [selectedItem]
    selectedLawsDisplay.value = selectedItem.title
    form.value.contractMessage.regulationId = selectedItem.id
  }
  else {
    selectedLaws.value = []
    selectedLawsDisplay.value = ''
    form.value.contractMessage.regulationId = ''
  }
  showLawsPop.value = false
}

// 清除选择的法规
function clearSelectedLaws() {
  selectedLaws.value = []
  selectedLawsDisplay.value = ''
  form.value.contractMessage.regulationId = ''
}

// 处理附件上传成功
function handleAttachmentUploadSuccess(files: any[]) {
  ElMessage.success(`成功上传 ${files.length} 个附件`)
}

// 处理审查发起弹窗成功事件
function handleInitiateSuccess() {
  // 刷新页面数据
  getDetailData()
}

// 审查记录
function reviewRecord() {
  // 跳转到审查记录页面
  router.push({
    path: '/monitor/examination/contractReview/record',
    query: {
      id: route.query.id,
    },
  })
}

// 处理法规选择确认
function onLawsConfirm(selectedIds: string[], selectedItems: any[]) {
  handleLawsConfirm(selectedIds, selectedItems)
}

// 初始化
onMounted(() => {
  isEdit.value = !!route.query.id
  getContractTypeOptions()
  getPayWayOptions()
  getSolveWayOptions()
  if (isEdit.value) {
    getDetailData()
  }
  else {
    generateReviewNumber()
  }
})
</script>

<template>
  <div class="absolute-container">
    <pageHeader>
      <template #content>
        <!-- 页面标题和操作按钮 -->
        <div class="flex items-center justify-between">
          <h1 class="text-xl text-gray-800 font-bold">
            新增合同审查
          </h1>
          <div class="flex items-center space-x-3">
            <el-button :disabled="form.status === 'PUBLISHED'" plain type="primary" class="rounded-button whitespace-nowrap" :loading="loading" @click="startReview">
              <el-icon class="mr-2">
                <Promotion />
              </el-icon>一键审核
            </el-button>
            <el-button plain type="primary" class="rounded-button whitespace-nowrap" :loading="loading" @click="reviewRecord">
              <el-icon class="mr-2">
                <Promotion />
              </el-icon>审查记录
            </el-button>
            <el-button type="primary" class="rounded-button whitespace-nowrap" :loading="loading" @click="handleSave">
              <el-icon class="mr-2">
                <Upload />
              </el-icon>保存
            </el-button>
            <el-button plain type="primary" class="rounded-button whitespace-nowrap" :loading="loading" @click="handleSubmit">
              <el-icon class="mr-2">
                <Promotion />
              </el-icon>提交审查
            </el-button>
            <el-button plain class="rounded-button whitespace-nowrap" @click="handleBack">
              <el-icon class="mr-2">
                <Close />
              </el-icon>取消
            </el-button>
          </div>
        </div>
      </template>
    </pageHeader>
    <!-- 主内容区 -->
    <PageMain style="background-color: transparent;">
      <!-- 表单内容区 -->
      <div class="flex space-x-6">
        <!-- 左侧主表单区 -->
        <div class="w-3/4 space-y-6">
          <!-- 基本信息 -->
          <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
            <el-card shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                基本信息
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <el-form-item label="合同名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入合同名称" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查编号">
                    <div class="flex">
                      <el-input v-model="form.reviewCode" placeholder="自动生成" />
                      <el-button class="ml-2" @click="generateReviewNumber">
                        自动生成
                      </el-button>
                    </div>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="合同类型" prop="contractType">
                    <el-select v-model="form.contractType" placeholder="请选择合同类型">
                      <el-option
                        v-for="option in contractTypeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查级别" prop="level">
                    <el-radio-group v-model="form.level">
                      <el-radio label="GENERAL" value="GENERAL">
                        一般
                      </el-radio>
                      <el-radio label="IMPORTANT" value="IMPORTANT">
                        重要
                      </el-radio>
                      <el-radio label="CRITICAL" value="CRITICAL">
                        关键
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="发起部门" prop="department">
                    <el-input v-model="form.department" placeholder="请选择部门">
                      <template #append>
                        <el-button :icon="User" />
                      </template>
                    </el-input>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查人员" prop="auditBy">
                    <el-input v-model="form.auditBy" placeholder="请选择审查人员">
                      <template #append>
                        <el-button :icon="User" />
                      </template>
                    </el-input>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查截止日期">
                    <el-date-picker v-model="form.deadlineDate" type="date" placeholder="选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="审查说明">
                    <el-input v-model="form.explain" type="textarea" :rows="3" placeholder="请输入审查说明" />
                  </el-form-item>
                </div>
              </div>
            </el-card>

            <!-- 合同信息 -->
            <el-card shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                合同信息
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <el-form-item label="甲方" prop="contractMessage.firstParty">
                    <el-input v-model="form.contractMessage.firstParty" placeholder="请输入甲方名称" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="乙方" prop="contractMessage.secondParty">
                    <el-input v-model="form.contractMessage.secondParty" placeholder="请输入乙方名称" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="丙方（如有）">
                    <el-input v-model="form.contractMessage.thirdParty" placeholder="请输入丙方名称" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="合同金额" prop="contractMessage.money">
                    <div class="flex space-x-2">
                      <el-input-number v-model="form.contractMessage.money" :min="0" class="flex-1" />
                      <!-- <el-select v-model="form.contractMessage.currency" style="width: 120px">
                        <el-option label="人民币" value="CNY" />
                        <el-option label="美元" value="USD" />
                        <el-option label="欧元" value="EUR" />
                        <el-option label="日元" value="JPY" />
                      </el-select> -->
                    </div>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="合同签订日期">
                    <el-date-picker v-model="form.contractMessage.signDate" type="date" placeholder="选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="合同履行期限">
                    <div class="flex space-x-4">
                      <el-date-picker v-model="form.contractMessage.performancePeriodStart" type="datetime" placeholder="开始日期" class="flex-1" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
                      <el-date-picker v-model="form.contractMessage.performancePeriodEnd" type="datetime" placeholder="结束日期" class="flex-1" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
                    </div>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="付款方式">
                    <el-select v-model="form.contractMessage.payWay" placeholder="请选择付款方式">
                      <el-option
                        v-for="option in payWayOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="争议解决方式">
                    <el-select v-model="form.contractMessage.solveWay" placeholder="请选择争议解决方式">
                      <el-option
                        v-for="option in solveWayOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="适用法律">
                    <div class="flex items-center space-x-2">
                      <el-input
                        v-model="selectedLawsDisplay"
                        placeholder="请选择适用法律"
                        readonly
                        class="flex-1"
                      />
                      <el-button type="primary" @click="openLawsPop">
                        选择法规
                      </el-button>
                      <el-button
                        v-if="selectedLawsDisplay"
                        type="danger"
                        plain
                        @click="clearSelectedLaws"
                      >
                        清除
                      </el-button>
                    </div>
                  </el-form-item>
                </div>
                <!-- <div>
                  <el-form-item label="法规ID">
                    <el-input-number v-model="form.contractMessage.regulationId" :min="0" placeholder="请输入法规ID" />
                  </el-form-item>
                </div> -->
                <div class="col-span-2">
                  <el-form-item label="合同内容">
                    <el-input v-model="form.contractMessage.content" type="textarea" :rows="4" placeholder="请输入合同内容" />
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="违约责任">
                    <el-input v-model="form.contractMessage.defaultResponsibility" type="textarea" :rows="3" placeholder="请输入违约责任" />
                  </el-form-item>
                </div>
              </div>
            </el-card>

            <!-- 文档上传 -->
            <el-card shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                文档上传
              </h2>
              <div class="space-y-6">
                <div>
                  <el-form-item label="合同文档" required>
                    <div class="w-full">
                      <DocumentUpload
                        v-model="form.contractMessage.documentUrl"
                        placeholder="导入合同文档，系统自动填充信息"
                        tip-text="支持 PDF、DOC、DOCX 格式，文件大小不超过 50MB"
                        :max-size="50"
                        @on-success="(fileKey, fileInfo) => {
                          form.contractMessage.documentUrl = fileKey
                          form.contractMessage.document = fileInfo.name
                        }"
                        @on-remove="() => {
                          form.contractMessage.documentUrl = ''
                          form.contractMessage.document = ''
                        }"
                      />
                    </div>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="合同附件">
                    <UploadMbb
                      v-model="form.contractAttachments"
                      :auto-upload="true"
                      :max="10"
                      :size="20"
                      service-name="whiskerguardgeneralservice"
                      category-name="contract"
                      tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 20MB"
                      @upload-success="handleAttachmentUploadSuccess"
                    />
                  </el-form-item>
                </div>
              </div>
            </el-card>

            <!-- 风险评估 -->
            <el-card shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                风险评估
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div class="col-span-2">
                  <el-form-item label="风险自评">
                    <div class="space-y-4">
                      <div>
                        <el-form-item label="风险类型">
                          <el-select v-model="form.riskType" placeholder="请选择风险类型">
                            <el-option label="法律风险" value="LAWS" />
                            <el-option label="财务风险" value="FINANCE" />
                            <el-option label="操作风险" value="OPERATION" />
                            <el-option label="声誉风险" value="REPUTATION" />
                            <el-option label="其他" value="OTHER" />
                          </el-select>
                        </el-form-item>
                      </div>
                      <div>
                        <el-form-item label="风险等级">
                          <el-select v-model="form.riskSelf" placeholder="请选择风险等级">
                            <el-option label="一般" value="GENERAL" />
                            <el-option label="重要" value="IMPORTANT" />
                            <el-option label="关键" value="CRITICAL" />
                          </el-select>
                        </el-form-item>
                      </div>
                      <div>
                        <el-form-item label="风险描述">
                          <el-input v-model="form.riskDesc" type="textarea" :rows="3" placeholder="请输入风险描述" />
                        </el-form-item>
                      </div>
                    </div>
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="自动风险评估">
                    <div class="border border-gray-200 rounded-md bg-gray-50 p-4">
                      <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-sm text-gray-700 font-medium">
                          AI 风险评估
                        </h3>
                        <el-button type="primary" class="rounded-button whitespace-nowrap">
                          <el-icon class="mr-2">
                            <VideoPlay />
                          </el-icon>开始评估
                        </el-button>
                      </div>
                      <div class="border border-gray-200 rounded-md bg-white p-4">
                        <div class="mb-4 flex items-center space-x-4">
                          <el-tag type="success">
                            低风险
                          </el-tag>
                          <div class="text-sm text-gray-600">
                            评估结果将在此处显示
                          </div>
                        </div>
                        <div class="space-y-3">
                          <div class="flex items-start">
                            <el-icon class="mr-2 mt-1 text-yellow-500">
                              <Warning />
                            </el-icon>
                            <p class="ml-2 text-sm text-gray-600">
                              主要风险点将在此列出
                            </p>
                          </div>
                          <div class="flex items-start">
                            <el-icon class="mr-2 mt-1 text-blue-500">
                            <!-- <lightbulb /> -->
                            </el-icon>
                            <p class="ml-2 text-sm text-gray-600">
                              AI 分析建议将在此显示
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>
            </el-card>

            <!-- 审查规则 -->
            <el-card shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                审查规则
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <el-form-item label="审查流程选择">
                    <el-select v-model="form.reviewProcess" placeholder="请选择审查流程">
                      <el-option label="标准流程" value="standard" />
                      <el-option label="简化流程" value="simple" />
                      <el-option label="严格流程" value="strict" />
                      <el-option label="自定义" value="custom" />
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查重点配置">
                    <el-checkbox-group v-model="form.reviewFocus">
                      <el-checkbox label="合同主体" />
                      <el-checkbox label="合同金额" />
                      <el-checkbox label="履约条款" />
                      <el-checkbox label="违约责任" />
                      <el-checkbox label="法律适用" />
                    </el-checkbox-group>
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="法规匹配范围">
                    <el-checkbox v-model="form.autoSelectLaw">
                      按合同类型自动选择相关法规范围
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="审批流程">
                    <div class="border border-gray-200 rounded-md bg-gray-50 p-4">
                      <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-sm text-gray-700 font-medium">
                          审批流程图
                        </h3>
                        <el-button plain type="primary" class="rounded-button whitespace-nowrap">
                          <el-icon class="mr-1">
                            <Plus />
                          </el-icon>添加节点
                        </el-button>
                      </div>
                      <div class="flex items-center justify-center py-6 space-x-8">
                        <div class="flex flex-col items-center">
                          <div class="bg-primary h-12 w-12 flex items-center justify-center rounded-full text-white">
                            <el-icon>
                              <User />
                            </el-icon>
                          </div>
                          <span class="mt-2 text-xs text-gray-600">发起人</span>
                        </div>
                        <div class="h-px flex-1 bg-gray-300" />
                        <div class="flex flex-col items-center">
                          <div
                            class="border-primary text-primary h-12 w-12 flex items-center justify-center border-2 rounded-full bg-blue-100"
                          >
                            <el-icon><user-filled /></el-icon>
                          </div>
                          <span class="mt-2 text-xs text-gray-600">部门主管</span>
                        </div>
                        <div class="h-px flex-1 bg-gray-300" />
                        <div class="flex flex-col items-center">
                          <div
                            class="border-primary text-primary h-12 w-12 flex items-center justify-center border-2 rounded-full bg-blue-100"
                          >
                            <el-icon><ScaleToOriginal /></el-icon>
                          </div>
                          <span class="mt-2 text-xs text-gray-600">法务</span>
                        </div>
                        <div class="h-px flex-1 bg-gray-300" />
                        <div class="flex flex-col items-center">
                          <div
                            class="border-primary text-primary h-12 w-12 flex items-center justify-center border-2 rounded-full bg-blue-100"
                          >
                            <el-icon>
                              <Document />
                            </el-icon>
                          </div>
                          <span class="mt-2 text-xs text-gray-600">签署</span>
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>
            </el-card>
          </el-form>
        </div>

        <!-- 右侧辅助功能区 -->
        <div class="sticky-sidebar w-1/4 space-y-6">
          <!-- 审查指南 -->
          <el-card shadow="hover" class="p-6">
            <h2 class="mb-4 text-lg text-gray-800 font-bold">
              审查指南
            </h2>
            <div class="space-y-3">
              <div class="rounded-md bg-blue-50 p-3">
                <h3 class="text-primary mb-2 text-sm font-medium">
                  采购合同审查要点
                </h3>
                <ul class="text-xs text-gray-600 space-y-1">
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-green-500">
                      <SuccessFilled />
                    </el-icon>
                    <span>确认供应商资质和信用状况</span>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-green-500">
                      <SuccessFilled />
                    </el-icon>
                    <span>核对产品规格、数量和质量标准</span>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-green-500">
                      <SuccessFilled />
                    </el-icon>
                    <span>审查付款条件和交付时间</span>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-green-500">
                      <SuccessFilled />
                    </el-icon>
                    <span>检查违约责任和争议解决条款</span>
                  </li>
                </ul>
              </div>
              <el-link type="primary" :underline="false" class="inline-flex items-center">
                <el-icon class="mr-2">
                  <Notebook />
                </el-icon>查看详细指南
              </el-link>
            </div>
          </el-card>

          <!-- 审查模板 -->
          <el-card shadow="hover" class="p-6">
            <h2 class="mb-4 text-lg text-gray-800 font-bold">
              审查模板
            </h2>
            <div class="space-y-3">
              <el-button
                text
                class="hover:border-primary w-full border border-gray-200 rounded-md bg-white px-3 py-2 text-left transition-colors hover:bg-blue-50"
              >
                <div class="text-sm text-gray-800 font-medium">
                  标准采购合同模板
                </div>
                <div class="mt-1 text-xs text-gray-500">
                  适用于一般采购业务
                </div>
              </el-button>
              <el-button
                text
                class="hover:border-primary w-full border border-gray-200 rounded-md bg-white px-3 py-2 text-left transition-colors hover:bg-blue-50"
              >
                <div class="text-sm text-gray-800 font-medium">
                  技术服务合同模板
                </div>
                <div class="mt-1 text-xs text-gray-500">
                  适用于技术服务类合同
                </div>
              </el-button>
              <el-button
                text
                class="hover:border-primary w-full border border-gray-200 rounded-md bg-white px-3 py-2 text-left transition-colors hover:bg-blue-50"
              >
                <div class="text-sm text-gray-800 font-medium">
                  严格审查模板
                </div>
                <div class="mt-1 text-xs text-gray-500">
                  适用于高风险合同
                </div>
              </el-button>
            </div>
          </el-card>

          <!-- AI辅助 -->
          <el-card shadow="hover" class="p-6">
            <h2 class="mb-4 text-lg text-gray-800 font-bold">
              AI 辅助
            </h2>
            <div class="space-y-4">
              <el-button type="primary" class="rounded-button w-full whitespace-nowrap">
                <el-icon class="mr-2">
                  <MagicStick />
                </el-icon>AI 自动填写
              </el-button>
              <el-button plain type="primary" class="rounded-button w-full whitespace-nowrap">
                <el-icon class="mr-2">
                  <shield />
                </el-icon>AI 风险预分析
              </el-button>
              <div class="border border-blue-100 rounded-md bg-blue-50 p-4">
                <h3 class="text-primary mb-2 text-sm font-medium">
                  AI 建议
                </h3>
                <div class="text-xs text-gray-600 space-y-2">
                  <div class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-blue-500">
                      <InfoFilled />
                    </el-icon>
                    <p class="ml-2">
                      建议补充合同履行地条款
                    </p>
                  </div>
                  <div class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-blue-500">
                      <InfoFilled />
                    </el-icon>
                    <p class="ml-2">
                      付款条件建议增加验收条款
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </PageMain>

    <!-- 底部 -->
    <footer class="border-t border-gray-200 bg-white py-4">
      <div class="mx-auto max-w-7xl px-6 text-center text-sm text-gray-500">
        <p>© 2023 监控之翼合规管理系统. 保留所有权利.</p>
      </div>
    </footer>

    <!-- 法规弹窗 -->
    <LawsPop
      v-model="showLawsPop"
      :selected-laws="selectedLaws"
      :single-select="true"
      @confirm="onLawsConfirm"
    />

    <!-- 审查发起弹窗 -->
    <InitiateDialog
      v-model:visible="showInitiateDialog"
      :object-id="form.id"
      review-type="CONTRACT"
      :compliance-review="form.complianceReview"
      @success="handleInitiateSuccess"
    />

    <!-- 审查结果弹窗 -->
    <ReviewResultDialog
      v-model:visible="showReviewResultDialog"
      :contract-id="form.id"
      :contract-info="{
        name: form.name,
        status: form.status,
        contractType: form.contractType,
        level: form.level,
        department: form.department,
        auditBy: form.auditBy,
        createdBy: form.createdBy,
        createdAt: form.createdAt,
      }"
    />
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";
</style>
