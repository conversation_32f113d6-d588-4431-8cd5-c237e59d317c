---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 08-合同管理服务/重大决策审查管理

## POST 创建重大决策审查记录

POST /whiskerguardcontractservice/api/decision/reviews

描述： 创建一个新的重大决策审查记录。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "decisionType": "MARKET_STRATEGY",
  "level": "GENERAL",
  "department": "string",
  "auditBy": "string",
  "explain": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "background": "string",
  "purpose": "string",
  "proposal": "string",
  "plan": "string",
  "expectedResult": "string",
  "money": 0,
  "document": "string",
  "documentUrl": "string",
  "riskSelf": "string",
  "riskType": "LAWS",
  "riskAssess": "string",
  "legalImpact": "string",
  "legalImpactDegree": "LOW",
  "financialImpact": "string",
  "financialImpactDegree": "LOW",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "decisionAccordingList": [
    {}
  ],
  "decisionAttachmentList": [
    {}
  ],
  "decisionRelatedPartyList": [
    {}
  ],
  "decisionPolicyList": [
    {}
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[DecisionReviewDTO](#schemadecisionreviewdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "",
  "name": "",
  "decisionType": "",
  "level": "",
  "department": "",
  "auditBy": "",
  "explain": "",
  "deadlineDate": "",
  "status": "",
  "background": "",
  "purpose": "",
  "proposal": "",
  "plan": "",
  "expectedResult": "",
  "money": 0,
  "document": "",
  "riskSelf": "",
  "riskType": "",
  "riskAssess": "",
  "legalImpact": "",
  "legalImpactDegree": "",
  "financialImpact": "",
  "financialImpactDegree": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "decisionAccordingList": [
    {
      "id": 0,
      "tenantId": 0,
      "decisionId": 0,
      "accordingType": "",
      "name": "",
      "explain": "",
      "describe": "",
      "policyDocuments": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "decisionAttachmentList": [
    {
      "id": 0,
      "decisionId": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "uploadedBy": "",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "decisionRelatedPartyList": [
    {
      "id": 0,
      "tenantId": 0,
      "decisionId": 0,
      "accordingType": "",
      "department": "",
      "responsibleBy": "",
      "relatedName": "",
      "relatedType": "",
      "relatedExplain": "",
      "innerCommunication": "",
      "outerCommunication": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityDecisionReviewDTO](#schemaresponseentitydecisionreviewdto)|

## PATCH 部分更新重大决策审查记录

PATCH /whiskerguardcontractservice/api/decision/reviews/id

描述：部分更新一个已有的重大决策审查记录。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "decisionType": "MARKET_STRATEGY",
  "level": "GENERAL",
  "department": "string",
  "auditBy": "string",
  "explain": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "background": "string",
  "purpose": "string",
  "proposal": "string",
  "plan": "string",
  "expectedResult": "string",
  "money": 0,
  "document": "string",
  "documentUrl": "string",
  "riskSelf": "string",
  "riskType": "LAWS",
  "riskAssess": "string",
  "legalImpact": "string",
  "legalImpactDegree": "LOW",
  "financialImpact": "string",
  "financialImpactDegree": "LOW",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "decisionAccordingList": [
    {}
  ],
  "decisionAttachmentList": [
    {}
  ],
  "decisionRelatedPartyList": [
    {}
  ],
  "decisionPolicyList": [
    {}
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[DecisionReviewDTO](#schemadecisionreviewdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "",
  "name": "",
  "decisionType": "",
  "level": "",
  "department": "",
  "auditBy": "",
  "explain": "",
  "deadlineDate": "",
  "status": "",
  "background": "",
  "purpose": "",
  "proposal": "",
  "plan": "",
  "expectedResult": "",
  "money": 0,
  "document": "",
  "riskSelf": "",
  "riskType": "",
  "riskAssess": "",
  "legalImpact": "",
  "legalImpactDegree": "",
  "financialImpact": "",
  "financialImpactDegree": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "decisionAccordingList": [
    {
      "id": 0,
      "tenantId": 0,
      "decisionId": 0,
      "accordingType": "",
      "name": "",
      "explain": "",
      "describe": "",
      "policyDocuments": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "decisionAttachmentList": [
    {
      "id": 0,
      "decisionId": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "uploadedBy": "",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "decisionRelatedPartyList": [
    {
      "id": 0,
      "tenantId": 0,
      "decisionId": 0,
      "accordingType": "",
      "department": "",
      "responsibleBy": "",
      "relatedName": "",
      "relatedType": "",
      "relatedExplain": "",
      "innerCommunication": "",
      "outerCommunication": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityDecisionReviewDTO](#schemaresponseentitydecisionreviewdto)|

## GET 根据给定的 ID 获取重大决策审查记录

GET /whiskerguardcontractservice/api/decision/reviews/id

描述：根据给定的 ID 获取特定的重大决策审查记录。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "",
  "name": "",
  "decisionType": "",
  "level": "",
  "department": "",
  "auditBy": "",
  "explain": "",
  "deadlineDate": "",
  "status": "",
  "background": "",
  "purpose": "",
  "proposal": "",
  "plan": "",
  "expectedResult": "",
  "money": 0,
  "document": "",
  "riskSelf": "",
  "riskType": "",
  "riskAssess": "",
  "legalImpact": "",
  "legalImpactDegree": "",
  "financialImpact": "",
  "financialImpactDegree": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "decisionAccordingList": [
    {
      "id": 0,
      "tenantId": 0,
      "decisionId": 0,
      "accordingType": "",
      "name": "",
      "explain": "",
      "describe": "",
      "policyDocuments": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "decisionAttachmentList": [
    {
      "id": 0,
      "decisionId": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "uploadedBy": "",
      "uploadedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ],
  "decisionRelatedPartyList": [
    {
      "id": 0,
      "tenantId": 0,
      "decisionId": 0,
      "accordingType": "",
      "department": "",
      "responsibleBy": "",
      "relatedName": "",
      "relatedType": "",
      "relatedExplain": "",
      "innerCommunication": "",
      "outerCommunication": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityDecisionReviewDTO](#schemaresponseentitydecisionreviewdto)|

## DELETE 删除指定 ID 的重大决策审查记录

DELETE /whiskerguardcontractservice/api/decision/reviews/id

描述：删除指定 ID 的重大决策审查记录。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 分页获取所有重大决策审查记录

POST /whiskerguardcontractservice/api/decision/reviews/page

描述：获取所有重大决策审查记录，并支持分页。

> Body 请求参数

```json
{
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "contractType": "TECHNICAL_SERVICES",
  "level": "GENERAL",
  "department": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "createdAtStart": "string",
  "createdAtEnd": "string",
  "decisionType": "MARKET_STRATEGY",
  "reviewType": "CONTRACT"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |none|
|size|query|integer| 否 |none|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[ReviewReq](#schemareviewreq)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "reviewCode": "",
      "name": "",
      "decisionType": "",
      "level": "",
      "department": "",
      "auditBy": "",
      "explain": "",
      "deadlineDate": "",
      "status": "",
      "background": "",
      "purpose": "",
      "proposal": "",
      "plan": "",
      "expectedResult": "",
      "money": 0,
      "document": "",
      "riskSelf": "",
      "riskType": "",
      "riskAssess": "",
      "legalImpact": "",
      "legalImpactDegree": "",
      "financialImpact": "",
      "financialImpactDegree": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false,
      "decisionAccordingList": [
        {
          "id": 0,
          "tenantId": 0,
          "decisionId": 0,
          "accordingType": "",
          "name": "",
          "explain": "",
          "describe": "",
          "policyDocuments": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        }
      ],
      "decisionAttachmentList": [
        {
          "id": 0,
          "decisionId": 0,
          "fileName": "",
          "filePath": "",
          "fileType": "",
          "fileSize": "",
          "fileDesc": "",
          "metadata": "",
          "version": 0,
          "uploadedBy": "",
          "uploadedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        }
      ],
      "decisionRelatedPartyList": [
        {
          "id": 0,
          "tenantId": 0,
          "decisionId": 0,
          "accordingType": "",
          "department": "",
          "responsibleBy": "",
          "relatedName": "",
          "relatedType": "",
          "relatedExplain": "",
          "innerCommunication": "",
          "outerCommunication": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        }
      ]
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "total": 0,
  "empty": false,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "totalPages": 0,
  "totalElements": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageDecisionReviewDTO](#schemaresponseentitypagedecisionreviewdto)|

## GET 根据审核状态统计数据

GET /whiskerguardcontractservice/api/decision/reviews/count/reviewStatus/tenantId

描述：根据ReviewStatus分组统计数据。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*map 包含ReviewStatus和对应数量*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListMapObject](#schemalistmapobject)]|false|none||map 包含ReviewStatus和对应数量|
|» key|[key](#schemakey)|false|none||none|

# 数据模型

<h2 id="tocS_ReviewReq">ReviewReq</h2>

<a id="schemareviewreq"></a>
<a id="schema_ReviewReq"></a>
<a id="tocSreviewreq"></a>
<a id="tocsreviewreq"></a>

```json
{
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "contractType": "TECHNICAL_SERVICES",
  "level": "GENERAL",
  "department": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "createdAtStart": "string",
  "createdAtEnd": "string",
  "decisionType": "MARKET_STRATEGY",
  "reviewType": "CONTRACT"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|reviewCode|string|false|none||审查编号|
|name|string|false|none||合同名称|
|contractType|string|false|none||合同类型：技术服务合同、重大决策、数据合同、采购合同|
|level|string|false|none||审查级别：一般、重要、关键|
|department|string|false|none||发起部门|
|deadlineDate|string|false|none||审查截止日期|
|status|string|false|none||状态：需修改、待审查、发布、审核中、已撤回|
|createdAtStart|string|false|none||创建时间开始|
|createdAtEnd|string|false|none||创建时间结束|
|decisionType|string|false|none||决策类型：市场战略|
|reviewType|string|false|none||审查类型：合同、决策、其他|

#### 枚举值

|属性|值|
|---|---|
|contractType|TECHNICAL_SERVICES|
|contractType|MAJOR_DECISIONS|
|contractType|DATA|
|contractType|PROCUREMENT|
|level|GENERAL|
|level|IMPORTANT|
|level|CRITICAL|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|
|decisionType|MARKET_STRATEGY|
|reviewType|CONTRACT|
|reviewType|DECISION|
|reviewType|SUPPLEMENTAL|
|reviewType|VIOLATION|
|reviewType|INVESTIGATE_TASK|
|reviewType|INVESTIGATE_RECORD|
|reviewType|INVESTIGATE_REPORT|
|reviewType|RESPONSIBILITY_CORRECTION|
|reviewType|RESPONSIBILITY_DEAL|
|reviewType|CONTINUOUS_EXPERIENCE|
|reviewType|CONTINUOUS_IMPROVE|
|reviewType|CONTINUOUS_REPORT|

<h2 id="tocS_key">key</h2>

<a id="schemakey"></a>
<a id="schema_key"></a>
<a id="tocSkey"></a>
<a id="tocskey"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_ListMapObject">ListMapObject</h2>

<a id="schemalistmapobject"></a>
<a id="schema_ListMapObject"></a>
<a id="tocSlistmapobject"></a>
<a id="tocslistmapobject"></a>

```json
{
  "key": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|[key](#schemakey)|false|none||none|

<h2 id="tocS_DecisionAccordingDTO">DecisionAccordingDTO</h2>

<a id="schemadecisionaccordingdto"></a>
<a id="schema_DecisionAccordingDTO"></a>
<a id="tocSdecisionaccordingdto"></a>
<a id="tocsdecisionaccordingdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "decisionId": 0,
  "accordingType": "LAWS_REGULATIONS",
  "name": "string",
  "explain": "string",
  "describe": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|decisionId|integer(int64)|true|none||决策审查ID|
|accordingType|string|false|none||依据类型：法律法规、公司章程、内部制度、行业惯例、专家意见、其他|
|name|string|false|none||依据名称|
|explain|string|false|none||相关性说明|
|describe|string|false|none||依据描述|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||创建时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|accordingType|LAWS_REGULATIONS|
|accordingType|COMPANY_ASSOCIATION|
|accordingType|INTERNAL_SYSTEM|
|accordingType|INDUSTRY_PRACTICE|
|accordingType|EXPERT_OPINION|
|accordingType|OTHER|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_DecisionAttachmentDTO">DecisionAttachmentDTO</h2>

<a id="schemadecisionattachmentdto"></a>
<a id="schema_DecisionAttachmentDTO"></a>
<a id="tocSdecisionattachmentdto"></a>
<a id="tocsdecisionattachmentdto"></a>

```json
{
  "id": 0,
  "decisionId": 0,
  "attachmentType": 0,
  "fileName": "string",
  "filePath": "string",
  "fileType": "string",
  "fileSize": "string",
  "fileDesc": "string",
  "metadata": "string",
  "version": 0,
  "uploadedBy": "string",
  "uploadedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||附件ID|
|decisionId|integer(int64)|true|none||决策审查ID|
|attachmentType|integer|false|none||附件内容类型：1、附件 2、政策文件|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|fileType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|fileDesc|string|false|none||附件描述|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|uploadedBy|string|true|none||上传者|
|uploadedAt|[Instant](#schemainstant)|false|none||创建时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_DecisionRelatedPartyDTO">DecisionRelatedPartyDTO</h2>

<a id="schemadecisionrelatedpartydto"></a>
<a id="schema_DecisionRelatedPartyDTO"></a>
<a id="tocSdecisionrelatedpartydto"></a>
<a id="tocsdecisionrelatedpartydto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "decisionId": 0,
  "accordingType": "LAWS_REGULATIONS",
  "department": "string",
  "responsibleBy": "string",
  "relatedName": "string",
  "relatedType": "string",
  "relatedExplain": "string",
  "innerCommunication": "string",
  "outerCommunication": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|decisionId|integer(int64)|true|none||决策审查ID|
|accordingType|string|false|none||依据类型：法律法规、公司章程、内部制度、行业惯例、专家意见、其他|
|department|string|false|none||涉及部门|
|responsibleBy|string|false|none||主要责任人|
|relatedName|string|false|none||相关方名称|
|relatedType|string|false|none||相关方类型|
|relatedExplain|string|false|none||关系说明|
|innerCommunication|string|false|none||内部沟通情况|
|outerCommunication|string|false|none||外部沟通情况|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||创建时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|accordingType|LAWS_REGULATIONS|
|accordingType|COMPANY_ASSOCIATION|
|accordingType|INTERNAL_SYSTEM|
|accordingType|INDUSTRY_PRACTICE|
|accordingType|EXPERT_OPINION|
|accordingType|OTHER|

<h2 id="tocS_ResponseEntityDecisionReviewDTO">ResponseEntityDecisionReviewDTO</h2>

<a id="schemaresponseentitydecisionreviewdto"></a>
<a id="schema_ResponseEntityDecisionReviewDTO"></a>
<a id="tocSresponseentitydecisionreviewdto"></a>
<a id="tocsresponseentitydecisionreviewdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "decisionType": "MARKET_STRATEGY",
  "level": "GENERAL",
  "department": "string",
  "auditBy": "string",
  "explain": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "background": "string",
  "purpose": "string",
  "proposal": "string",
  "plan": "string",
  "expectedResult": "string",
  "money": 0,
  "document": "string",
  "documentUrl": "string",
  "riskSelf": "string",
  "riskType": "LAWS",
  "riskAssess": "string",
  "legalImpact": "string",
  "legalImpactDegree": "LOW",
  "financialImpact": "string",
  "financialImpactDegree": "LOW",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "decisionAccordingList": [
    {}
  ],
  "decisionAttachmentList": [
    {}
  ],
  "decisionRelatedPartyList": [
    {}
  ],
  "decisionPolicyList": [
    {}
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|reviewCode|string|true|none||审查编号|
|name|string|true|none||决策名称|
|decisionType|string|false|none||决策类型：市场战略|
|level|string|false|none||审查级别：一般、重要、关键|
|department|string|false|none||发起部门|
|auditBy|string|false|none||审查人员|
|explain|string|false|none||审查说明|
|deadlineDate|string|false|none||审查截止日期|
|status|string|false|none||状态：需修改、待审查、发布、审核中、已撤回|
|background|string|false|none||决策背景|
|purpose|string|false|none||决策目的|
|proposal|string|false|none||决策方案|
|plan|string|false|none||实施计划|
|expectedResult|string|false|none||预期结果|
|money|integer(int64)|false|none||决策涉及金额|
|document|string|false|none||合同文档|
|documentUrl|string|false|none||合同文档文件地址|
|riskSelf|string|false|none||风险自评|
|riskType|string|false|none||风险类型：法律风险、财务风险、操作风险、声誉风险、其他|
|riskAssess|string|false|none||风险评估|
|legalImpact|string|false|none||法律影响|
|legalImpactDegree|string|false|none||法律影响程度：低度影响、中度影响、高度影响|
|financialImpact|string|false|none||财务影响|
|financialImpactDegree|string|false|none||财务影响程度：低度影响、中度影响、高度影响|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|object|false|none||创建时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|decisionAccordingList|[object]|false|none||重大决策审查依据|
|decisionAttachmentList|[object]|false|none||重大决策附件|
|decisionRelatedPartyList|[object]|false|none||重大决策相关方|
|decisionPolicyList|[object]|false|none||相关政策附件|

#### 枚举值

|属性|值|
|---|---|
|decisionType|MARKET_STRATEGY|
|level|GENERAL|
|level|IMPORTANT|
|level|CRITICAL|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|
|riskType|LAWS|
|riskType|FINANCE|
|riskType|OPERATION|
|riskType|REPUTATION|
|riskType|OTHER|
|legalImpactDegree|LOW|
|legalImpactDegree|MIDDLE|
|legalImpactDegree|HIGH|
|financialImpactDegree|LOW|
|financialImpactDegree|MIDDLE|
|financialImpactDegree|HIGH|

<h2 id="tocS_DecisionReviewDTO">DecisionReviewDTO</h2>

<a id="schemadecisionreviewdto"></a>
<a id="schema_DecisionReviewDTO"></a>
<a id="tocSdecisionreviewdto"></a>
<a id="tocsdecisionreviewdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "reviewCode": "string",
  "name": "string",
  "decisionType": "MARKET_STRATEGY",
  "level": "GENERAL",
  "department": "string",
  "auditBy": "string",
  "explain": "string",
  "deadlineDate": "string",
  "status": "MODIFY",
  "background": "string",
  "purpose": "string",
  "proposal": "string",
  "plan": "string",
  "expectedResult": "string",
  "money": 0,
  "document": "string",
  "documentUrl": "string",
  "riskSelf": "string",
  "riskType": "LAWS",
  "riskAssess": "string",
  "legalImpact": "string",
  "legalImpactDegree": "LOW",
  "financialImpact": "string",
  "financialImpactDegree": "LOW",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "decisionAccordingList": [
    {}
  ],
  "decisionAttachmentList": [
    {}
  ],
  "decisionRelatedPartyList": [
    {}
  ],
  "decisionPolicyList": [
    {}
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|reviewCode|string|true|none||审查编号|
|name|string|true|none||决策名称|
|decisionType|string|false|none||决策类型：市场战略|
|level|string|false|none||审查级别：一般、重要、关键|
|department|string|false|none||发起部门|
|auditBy|string|false|none||审查人员|
|explain|string|false|none||审查说明|
|deadlineDate|string|false|none||审查截止日期|
|status|string|false|none||状态：需修改、待审查、发布、审核中、已撤回|
|background|string|false|none||决策背景|
|purpose|string|false|none||决策目的|
|proposal|string|false|none||决策方案|
|plan|string|false|none||实施计划|
|expectedResult|string|false|none||预期结果|
|money|integer(int64)|false|none||决策涉及金额|
|document|string|false|none||合同文档|
|documentUrl|string|false|none||合同文档文件地址|
|riskSelf|string|false|none||风险自评|
|riskType|string|false|none||风险类型：法律风险、财务风险、操作风险、声誉风险、其他|
|riskAssess|string|false|none||风险评估|
|legalImpact|string|false|none||法律影响|
|legalImpactDegree|string|false|none||法律影响程度：低度影响、中度影响、高度影响|
|financialImpact|string|false|none||财务影响|
|financialImpactDegree|string|false|none||财务影响程度：低度影响、中度影响、高度影响|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|object|false|none||创建时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|decisionAccordingList|[object]|false|none||重大决策审查依据|
|decisionAttachmentList|[object]|false|none||重大决策附件|
|decisionRelatedPartyList|[object]|false|none||重大决策相关方|
|decisionPolicyList|[object]|false|none||相关政策附件|

#### 枚举值

|属性|值|
|---|---|
|decisionType|MARKET_STRATEGY|
|level|GENERAL|
|level|IMPORTANT|
|level|CRITICAL|
|status|MODIFY|
|status|PENDING|
|status|PUBLISHED|
|status|REVIEWING|
|status|REVOKE|
|riskType|LAWS|
|riskType|FINANCE|
|riskType|OPERATION|
|riskType|REPUTATION|
|riskType|OTHER|
|legalImpactDegree|LOW|
|legalImpactDegree|MIDDLE|
|legalImpactDegree|HIGH|
|financialImpactDegree|LOW|
|financialImpactDegree|MIDDLE|
|financialImpactDegree|HIGH|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE",
  "ascending": true,
  "descending": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|
|ascending|boolean|false|none||none|
|descending|boolean|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_ResponseEntityPageDecisionReviewDTO">ResponseEntityPageDecisionReviewDTO</h2>

<a id="schemaresponseentitypagedecisionreviewdto"></a>
<a id="schema_ResponseEntityPageDecisionReviewDTO"></a>
<a id="tocSresponseentitypagedecisionreviewdto"></a>
<a id="tocsresponseentitypagedecisionreviewdto"></a>

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "reviewCode": "string",
      "name": "string",
      "decisionType": "MARKET_STRATEGY",
      "level": "GENERAL",
      "department": "string",
      "auditBy": "string",
      "explain": "string",
      "deadlineDate": "string",
      "status": "MODIFY",
      "background": "string",
      "purpose": "string",
      "proposal": "string",
      "plan": "string",
      "expectedResult": "string",
      "money": 0,
      "document": "string",
      "documentUrl": "string",
      "riskSelf": "string",
      "riskType": "LAWS",
      "riskAssess": "string",
      "legalImpact": "string",
      "legalImpactDegree": "LOW",
      "financialImpact": "string",
      "financialImpactDegree": "LOW",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "decisionAccordingList": [
        {}
      ],
      "decisionAttachmentList": [
        {}
      ],
      "decisionRelatedPartyList": [
        {}
      ],
      "decisionPolicyList": [
        {}
      ]
    }
  ],
  "pageable": {},
  "total": 0,
  "empty": true,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "totalPages": 0,
  "totalElements": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|content|[[DecisionReviewDTO](#schemadecisionreviewdto)]|false|none||none|
|pageable|object|false|none||none|
|total|integer(int64)|false|none||none|
|empty|boolean|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

