<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import {
  ArrowDown as ElIconArrowDown,
  DataAnalysis as ElIconDataAnalysis,
  Document as ElIconDocument,
  Download as ElIconDownload,
  Plus as ElIconPlus,
  SetUp as ElIconSetUp,
  Tickets as ElIconTickets,
} from '@element-plus/icons-vue'
import decisionApi from '@/api/review/decision'

const router = useRouter()
// 标签页
const activeTab = ref('decision')
// 筛选条件
const filter = ref({
  status: '',
  level: '',
  department: '',
  dateRange: [],
  keyword: '',
  reviewer: '',
  result: '',
  risk: '',
  complianceTypes: [],
})
// 表格数据
const contractData = [
  {
    id: 'REV-2023-001',
    name: '2023年度服务器采购合同',
    type: '采购合同',
    amount: 1250000,
    counterparty: '阿里云',
    department: '技术部',
    level: 2,
    status: '审查中',
    startTime: '2023-05-10',
    reviewer: '李四',
    endTime: '',
  },
  {
    id: 'REV-2023-002',
    name: '华东地区销售代理协议',
    type: '销售合同',
    amount: 800000,
    counterparty: '上海代理公司',
    department: '市场部',
    level: 1,
    status: '待审查',
    startTime: '2023-05-12',
    reviewer: '',
    endTime: '',
  },
  {
    id: 'REV-2023-003',
    name: '法律顾问服务合同',
    type: '服务合同',
    amount: 300000,
    counterparty: '大成律师事务所',
    department: '法务部',
    level: 3,
    status: '已完成',
    startTime: '2023-04-28',
    reviewer: '王五',
    endTime: '2023-05-15',
  },
  {
    id: 'REV-2023-004',
    name: '办公室租赁合同',
    type: '租赁合同',
    amount: 1500000,
    counterparty: 'SOHO中国',
    department: '行政部',
    level: 2,
    status: '需修改',
    startTime: '2023-05-05',
    reviewer: '张三',
    endTime: '',
  },
  {
    id: 'REV-2023-005',
    name: '员工培训合作协议',
    type: '其他合同',
    amount: 250000,
    counterparty: '北大青鸟',
    department: '人力资源部',
    level: 1,
    status: '已撤回',
    startTime: '2023-05-08',
    reviewer: '李四',
    endTime: '2023-05-09',
  },
]
// 重大决策数据
const decisionData = ref([])
const loading = ref(false)
const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})
// 分页
const currentPage = ref(1)
const decisionCurrentPage = ref(1)
const otherCurrentPage = ref(1)
const pageSize = 10
const totalItems = computed(() => contractData.length)
const decisionTotalItems = computed(() => paging.value.total)
const otherTotalItems = computed(() => otherData.length)
// 选择项
const selectedItems = ref([])
const selectedDecisionItems = ref([])
const selectedOtherItems = ref([])
const selectAll = ref(false)
const selectAllDecision = ref(false)
const selectAllOther = ref(false)
// 我的审查
const myReviews = [
  { id: 1, title: '数据隐私政策审查', status: '需修改', deadline: '剩余1天' },
  { id: 2, title: '年度环保技术升级投资决策', status: '审查中', deadline: '剩余2天' },
  { id: 3, title: '供应商环保合规评估', status: '已完成', deadline: '2024-05-01' },
  { id: 4, title: '设备采购合同', status: '审查中', deadline: '剩余3天' },
  { id: 5, title: '软件许可协议', status: '待审查', deadline: '剩余5天' },
]
// 审查趋势数据
const trendData = [
  { month: '2023-12', count: 18 },
  { month: '2024-01', count: 22 },
  { month: '2024-02', count: 19 },
  { month: '2024-03', count: 25 },
  { month: '2024-04', count: 28 },
  { month: '2024-05', count: 8 },
]
// 初始化图表
const trendChart = ref(null)
const pieChart = ref(null)
function initCharts() {
  if (trendChart.value) {
    const trendChartInstance = echarts.init(trendChart.value)
    const trendOption = {
      animation: false,
      xAxis: {
        type: 'category',
        data: trendData.map(item => item.month),
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: trendData.map(item => item.count),
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#409EFF',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.1)',
              },
            ]),
          },
        },
      ],
    }
    trendChartInstance.setOption(trendOption)
    if (pieChart.value) {
      const pieChartInstance = echarts.init(pieChart.value)
      const pieOption = {
        animation: false,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data:
              activeTab.value === 'contract'
                ? ['采购合同', '销售合同', '服务合同', '劳动合同', '技术许可合同', '其他合同']
                : activeTab.value === 'decision'
                  ? ['投资决策', '战略规划', '重组决策', '融资决策', '重大采购']
                  : ['安全合规', '品质合规', '环保合规', 'IT合规', '数据合规', '人事合规', '其他'],
        },
        series: [
          {
            name: '分布比例',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data:
                activeTab.value === 'contract'
                  ? [
                      { value: 32, name: '采购合同' },
                      { value: 28, name: '销售合同' },
                      { value: 18, name: '服务合同' },
                      { value: 10, name: '劳动合同' },
                      { value: 8, name: '技术许可合同' },
                      { value: 4, name: '其他合同' },
                    ]
                  : activeTab.value === 'decision'
                    ? [
                        { value: 38, name: '投资决策' },
                        { value: 24, name: '战略规划' },
                        { value: 18, name: '重组决策' },
                        { value: 12, name: '融资决策' },
                        { value: 8, name: '重大采购' },
                      ]
                    : [
                        { value: 26, name: '安全合规' },
                        { value: 20, name: '品质合规' },
                        { value: 16, name: '环保合规' },
                        { value: 14, name: 'IT合规' },
                        { value: 12, name: '数据合规' },
                        { value: 8, name: '人事合规' },
                        { value: 4, name: '其他' },
                      ],
          },
        ],
      }
      pieChartInstance.setOption(pieOption)
    }
    window.addEventListener('resize', () => {
      trendChartInstance.resize()
      if (pieChart.value && pieChartInstance) {
        pieChartInstance.resize()
      }
    })
  }
}
onMounted(() => {
  initCharts()
  getDecisionList()
})
watch(activeTab, () => {
  nextTick(() => {
    initCharts()
  })
})
function searchList() {
  getDecisionList()
}
// API方法
async function getDecisionList() {
  try {
    loading.value = true
    const params = {
      tenantId: 1,
      // ...filter.value,
    }
    const response = await decisionApi.decisionReview(paging.value, params)
    if (response && response.content) {
      decisionData.value = response.content
      paging.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取决策审查列表失败:', error)
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

// 方法
function getTagType(type) {
  const types = {
    采购合同: '',
    销售合同: 'success',
    服务合同: 'info',
    租赁合同: 'warning',
    其他合同: 'danger',
  }
  return types[type] || ''
}
function getDecisionTagType(type) {
  const types = {
    投资决策: 'success',
    融资决策: 'danger',
    重组决策: 'warning',
    战略规划: 'info',
    重大采购: '',
  }
  return types[type] || ''
}
function getOtherTagType(type) {
  const types = {
    安全合规: 'danger',
    IT合规: 'warning',
    财务合规: 'success',
    数据合规: 'info',
    业务合规: '',
  }
  return types[type] || ''
}
function getStatusTagType(status) {
  const statusTypes = {
    待审查: 'info',
    审查中: '',
    已完成: 'success',
    已撤回: 'info',
    需修改: 'warning',
  }
  return statusTypes[status] || ''
}
function formatAmount(amount) {
  return `¥${amount.toLocaleString()}`
}
function handleSelectionChange(val) {
  selectedItems.value = val
  selectAll.value = val.length === contractData.length
}
function handleDecisionSelectionChange(val) {
  selectedDecisionItems.value = val
  selectAllDecision.value = val.length === decisionData.value.length
}
function handleOtherSelectionChange(val) {
  selectedOtherItems.value = val
  selectAllOther.value = val.length === otherData.length
}
function toggleSelectAll() {
  if (selectAll.value) {
    selectedItems.value = [...contractData]
  }
  else {
    selectedItems.value = []
  }
}
function resetFilter() {
  filter.value = {
    status: '',
    level: '',
    department: '',
    dateRange: [],
    keyword: '',
    reviewer: '',
    result: '',
    risk: '',
    complianceTypes: [],
  }
}
function applyFilter() {
  paging.value.page = 1
  getDecisionList()
}

// 分页变化处理
function handlePageChange(page: number) {
  paging.value.page = page
  decisionCurrentPage.value = page
  getDecisionList()
}

// 页面大小变化处理
function handleSizeChange(size: number) {
  paging.value.limit = size
  paging.value.page = 1
  decisionCurrentPage.value = 1
  getDecisionList()
}
function viewDetail(row) {
  console.log('查看详情:', row)
}
function editItem(row) {
  console.log('编辑:', row)
}
function deleteItem(row) {
  console.log('删除:', row)
}
function exportItem(row) {
  console.log('导出:', row)
}
function withdrawItem(row) {
  console.log('撤回:', row)
}
function viewDecisionDetail(row: any) {
  router.push({
    path: '/monitor/examination/decisionReview/detail',
    query: { id: row.id },
  })
}

function editDecisionItem(row: any) {
  router.push({
    path: '/monitor/examination/decisionReview/addEdit',
    query: { id: row.id },
  })
}

function addDecisionItem() {
  router.push({
    path: '/monitor/examination/decisionReview/addEdit',
  })
}

async function deleteDecisionItem(row: any) {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条决策审查记录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await decisionApi.decisionReview(null, { id: row.id }, 'delete')
    ElMessage.success('删除成功')
    getDecisionList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

function exportDecisionItem(row: any) {
  // 导出功能实现
  ElMessage.info('导出功能开发中')
}

async function withdrawDecisionItem(row: any) {
  try {
    await ElMessageBox.confirm(
      '确定要撤回这条决策审查记录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 这里需要根据实际API调整
    ElMessage.success('撤回成功')
    getDecisionList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('撤回失败:', error)
      ElMessage.error('撤回失败')
    }
  }
}
// 其他审查数据
const otherData = [
  {
    id: 'OR20240502001',
    name: '产品安全性合规审查',
    type: '安全合规',
    object: '新款智能手表',
    department: '产品部',
    level: '高',
    status: '审查中',
    startTime: '2024-05-02',
    reviewer: '刘芳',
    endTime: '',
  },
  {
    id: 'OR20240429002',
    name: '年度IT安全审计',
    type: 'IT合规',
    object: '企业信息系统',
    department: 'IT部',
    level: '中',
    status: '待审查',
    startTime: '2024-04-29',
    reviewer: '待分配',
    endTime: '',
  },
  {
    id: 'OR20240425003',
    name: '财务季度合规审查',
    type: '财务合规',
    object: 'Q1财务报表',
    department: '财务部',
    level: '高',
    status: '已完成',
    startTime: '2024-04-25',
    reviewer: '王强',
    endTime: '2024-04-30',
  },
  {
    id: 'OR20240418004',
    name: '用户数据隐私审查',
    type: '数据合规',
    object: '用户数据库',
    department: '数据部',
    level: '中',
    status: '需修改',
    startTime: '2024-04-18',
    reviewer: '李明',
    endTime: '',
  },
  {
    id: 'OR20240410005',
    name: '新业务线合规评估',
    type: '业务合规',
    object: '新零售业务',
    department: '业务部',
    level: '高',
    status: '已撤回',
    startTime: '2024-04-10',
    reviewer: '张华',
    endTime: '2024-04-12',
  },
]
function viewOtherDetail(row) {
  console.log('查看其他审查详情:', row)
}
function editOtherItem(row) {
  console.log('编辑其他审查:', row)
}
function deleteOtherItem(row) {
  console.log('删除其他审查:', row)
}
function exportOtherItem(row) {
  console.log('导出其他审查:', row)
}
function withdrawOtherItem(row) {
  console.log('撤回其他审查:', row)
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              重大决策审查
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button
              @click="searchList"
            >
              <el-icon class="mr-2">
                <ElIconPlus />
              </el-icon>
              查询
            </el-button>
            <el-button
              @click="addDecisionItem"
            >
              <el-icon class="mr-2">
                <ElIconPlus />
              </el-icon>
              新增审查
            </el-button>
            <el-button>
              <el-icon class="mr-2">
                <ElIconDownload />
              </el-icon>
              导出数据
            </el-button>
            <el-button>
              <el-icon class="mr-2">
                <ElIconDataAnalysis />
              </el-icon>
              审查统计
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template> -->
              <div class="p-6">
                <!-- 表格 -->
                <el-table
                  v-loading="loading"
                  :data="decisionData"
                  style="width: 100%;"
                  @selection-change="handleDecisionSelectionChange"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="reviewCode" label="审查编号" width="150" />
                  <el-table-column prop="name" label="决策名称" width="200" />
                  <el-table-column prop="decisionType" label="决策类型" width="120">
                    <template #default="{ row }">
                      <el-tag :type="getDecisionTagType(row.decisionType)" size="small">
                        {{ row.decisionType === 'MARKET_STRATEGY' ? '市场战略'
                          : row.decisionType === 'INVESTMENT' ? '投资决策'
                            : row.decisionType === 'FINANCING' ? '融资决策'
                              : row.decisionType === 'REORGANIZATION' ? '重组决策'
                                : row.decisionType === 'MAJOR_PURCHASE' ? '重大采购'
                                  : row.decisionType }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="level" label="决策级别" width="100">
                    <template #default="{ row }">
                      <el-tag
                        :type="row.level === 'CRITICAL' ? 'danger' : row.level === 'IMPORTANT' ? 'warning' : 'info'"
                        size="small"
                      >
                        {{ row.level === 'CRITICAL' ? '重大'
                          : row.level === 'IMPORTANT' ? '重要'
                            : row.level === 'GENERAL' ? '一般' : row.level }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="department" label="发起部门" width="120" />
                  <el-table-column prop="status" label="状态" width="120">
                    <template #default="{ row }">
                      <el-tag :type="getStatusTagType(row.status)" size="small">
                        {{ row.status === 'MODIFY' ? '需修改'
                          : row.status === 'PENDING' ? '待审查'
                            : row.status === 'PUBLISHED' ? '已发布'
                              : row.status === 'REVIEWING' ? '审查中'
                                : row.status === 'REVOKE' ? '已撤回' : row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createdAt" label="发起时间" width="150" />
                  <el-table-column prop="auditBy" label="审查人" width="120" />
                  <el-table-column prop="updatedAt" label="更新时间" width="150" />
                  <el-table-column label="操作" width="180" fixed="right">
                    <template #default="{ row }">
                      <el-button type="text" size="small" @click="viewDecisionDetail(row)">
                        查看
                      </el-button>
                      <el-button type="text" size="small" @click="editDecisionItem(row)">
                        编辑
                      </el-button>
                      <el-dropdown>
                        <el-button type="text" size="small">
                          更多
                          <el-icon class="el-icon--right">
                            <ElIconArrowDown />
                          </el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item @click="deleteDecisionItem(row)">
                              删除
                            </el-dropdown-item>
                            <el-dropdown-item @click="exportDecisionItem(row)">
                              导出
                            </el-dropdown-item>
                            <el-dropdown-item @click="withdrawDecisionItem(row)">
                              撤回
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="mt-4 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    共 {{ decisionTotalItems }} 条记录
                  </div>
                  <el-pagination
                    v-model:current-page="decisionCurrentPage"
                    :page-size="pageSize"
                    :total="decisionTotalItems"
                    layout="sizes, prev, pager, next, jumper"
                    :page-sizes="[10, 20, 50, 100]"
                    @current-change="handlePageChange"
                    @size-change="handleSizeChange"
                  />
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  审查统计
                </div>
              </template>
              <div class="grid grid-cols-2 mb-6 gap-4">
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    总审查数
                  </div>
                  <div class="text-2xl font-bold">
                    {{ activeTab === 'contract' ? '42' : activeTab === 'decision' ? '28' : '35' }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    待审查
                  </div>
                  <div class="text-2xl text-blue-500 font-bold">
                    {{ activeTab === 'contract' ? '9' : activeTab === 'decision' ? '6' : '7' }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    审查中
                  </div>
                  <div class="text-2xl text-yellow-500 font-bold">
                    {{ activeTab === 'contract' ? '15' : activeTab === 'decision' ? '10' : '12' }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    已完成
                  </div>
                  <div class="text-2xl text-green-500 font-bold">
                    {{ activeTab === 'contract' ? '16' : activeTab === 'decision' ? '11' : '14' }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    需修改
                  </div>
                  <div class="text-2xl text-red-500 font-bold">
                    {{ activeTab === 'contract' ? '2' : activeTab === 'decision' ? '1' : '2' }}
                  </div>
                </div>
              </div>
              <!-- 审查分布图 -->
              <div class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    审查分布
                  </div>
                  <el-link type="primary" :underline="false">
                    查看更多
                  </el-link>
                </div>
                <div class="h-40">
                  <div ref="pieChart" class="h-full w-full" />
                </div>
              </div>
              <!-- 审查趋势图 -->
              <div class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    审查趋势
                  </div>
                  <el-link type="primary" :underline="false">
                    查看更多
                  </el-link>
                </div>
                <div class="h-40">
                  <!-- 折线图 -->
                  <div ref="trendChart" class="h-full w-full" />
                </div>
              </div>
              <!-- 我的审查 -->
              <div class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    我的审查
                  </div>
                  <el-link type="primary" :underline="false">
                    查看全部
                  </el-link>
                </div>
                <div class="space-y-3">
                  <div v-for="item in myReviews" :key="item.id" class="border-b pb-3 last:border-b-0 last:pb-0">
                    <div class="flex justify-between">
                      <div class="font-medium">
                        {{ item.title }}
                      </div>
                      <el-tag :type="getStatusTagType(item.status)" size="small">
                        {{ item.status }}
                      </el-tag>
                    </div>
                    <div class="text-sm text-gray-500">
                      截止: {{ item.deadline }}
                    </div>
                  </div>
                </div>
              </div>
              <!-- 快捷功能 -->
              <div class="border rounded-lg p-4">
                <div class="mb-3 font-medium">
                  快捷功能
                </div>
                <div class="grid grid-cols-2 gap-3">
                  <button
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconDocument />
                    </el-icon>
                    模板管理
                  </button>
                  <button
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconSetUp />
                    </el-icon>
                    流程配置
                  </button>
                  <button
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconTickets />
                    </el-icon>
                    报告生成
                  </button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义样式 */
  :deep(.el-tabs__header) {
    margin: 0;
  }

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    background-color: #e4e7ed;
  }

  :deep(.el-tree) {
    background: transparent;
  }

  :deep(.el-tree-node__content) {
    height: 36px;
  }

  :deep(.el-table) {
    --el-table-border-color: #f0f0f0;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f8f8f8;
  }

  :deep(.el-table .el-table__cell) {
    padding: 12px 0;
  }

  :deep(.el-table .cell) {
    padding-right: 16px;
    padding-left: 16px;
  }
</style>
