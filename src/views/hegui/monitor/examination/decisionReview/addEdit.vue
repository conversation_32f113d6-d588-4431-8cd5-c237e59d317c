<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  ArrowDown,
  DataAnalysis,
  Document,
  Download,
  Plus,
  SetUp,
  Tickets,
} from '@element-plus/icons-vue'
import decisionApi from '@/api/review/decision'
import dictApi from '@/api/modules/system/dict'
import DocumentUpload from '@/components/DocumentUpload/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'

interface DecisionTypeOption {
  label: string
  value: string
}

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()
const loading = ref(false)
const isEdit = ref(false)
const decisionTypeOptions = ref<DecisionTypeOption[]>([]) // 决策类型选项
const accordingTypeOptions = ref<DecisionTypeOption[]>([]) // 依据类型选项

// 定义类型接口
interface DecisionAccording {
  accordingType: string
  name: string
  explain: string
  describe: string
}

interface DecisionRelatedParty {
  accordingType: string | null
  department: string
  responsibleBy: string
  relatedName: string
  relatedType: string
  relatedExplain: string
  innerCommunication: string
  outerCommunication: string
}

interface DecisionAttachment {
  attachmentType: number
  fileName: string
  filePath: string
  fileType: string
  fileSize: string
  fileDesc: string
}

interface DecisionPolicy {
  attachmentType: number
  fileName: string
  filePath: string
  fileType: string
  fileSize: string
  fileDesc: string
}

// 表单数据
const formData = reactive({
  reviewCode: '',
  name: '',
  decisionType: '',
  level: 'GENERAL',
  department: '',
  auditBy: '',
  explain: '',
  deadlineDate: '',
  status: 'DRAFT',
  background: '',
  purpose: '',
  proposal: '',
  plan: '',
  expectedResult: '',
  money: 0,
  document: '',
  documentUrl: '',
  riskSelf: '',
  riskType: 'LAWS',
  riskAssess: '',
  legalImpact: '',
  legalImpactDegree: 'LOW',
  financialImpact: '',
  financialImpactDegree: 'LOW',
  decisionAccordingList: [] as DecisionAccording[],
  decisionAttachmentList: [] as DecisionAttachment[],
  decisionRelatedPartyList: [] as DecisionRelatedParty[],
  decisionPolicyList: [] as DecisionPolicy[],
})

// 决策主体数据
const decisionSubject = reactive({
  department: '',
  responsibleBy: '',
})

// 内外部沟通数据
const communicationData = reactive({
  innerCommunication: '',
  outerCommunication: '',
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入决策名称', trigger: 'blur' },
  ],
  decisionType: [
    { required: true, message: '请选择决策类型', trigger: 'change' },
  ],
  level: [
    { required: true, message: '请选择决策级别', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
  department: [
    { required: true, message: '请选择发起部门', trigger: 'change' },
  ],
  auditBy: [
    { required: true, message: '请选择审查人员', trigger: 'change' },
  ],
  background: [
    { required: true, message: '请输入决策背景', trigger: 'blur' },
  ],
  purpose: [
    { required: true, message: '请输入决策目的', trigger: 'blur' },
  ],
  proposal: [
    { required: true, message: '请输入决策方案', trigger: 'blur' },
  ],
  document: [
    { required: true, message: '请输入决策文档', trigger: 'blur' },
  ],
}

// 决策依据操作
function addAccording() {
  formData.decisionAccordingList.push({
    accordingType: '',
    name: '',
    explain: '',
    describe: '',
  })
}

function removeAccording(index: number) {
  formData.decisionAccordingList.splice(index, 1)
}

// 利益相关方操作
function addRelatedParty() {
  formData.decisionRelatedPartyList.push({
    accordingType: null,
    department: '',
    responsibleBy: '',
    relatedName: '',
    relatedType: '',
    relatedExplain: '',
    innerCommunication: '',
    outerCommunication: '',
  })
}

function removeRelatedParty(index: number) {
  formData.decisionRelatedPartyList.splice(index, 1)
}

// 生成审查编号
async function generateReviewNumber() {
  try {
    const response = await dictApi.getCode('DECISION')
    if (response) {
      formData.reviewCode = response
    }
  }
  catch (error) {
    console.error('获取审查编号失败:', error)
    ElMessage.error('获取审查编号失败')
  }
}

// 获取决策类型选项
async function getDecisionTypeOptions() {
  try {
    const response = await dictApi.dictAll(57)
    if (response) {
      decisionTypeOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取决策类型选项失败:', error)
    ElMessage.error('获取决策类型选项失败')
  }
}

// 获取依据类型选项
async function getAccordingTypeOptions() {
  try {
    const response = await dictApi.dictAll(55)
    if (response) {
      accordingTypeOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取依据类型选项失败:', error)
    ElMessage.error('获取依据类型选项失败')
  }
}

// 政策文件上传成功处理
function handlePolicyUploadSuccess(_fileInfo: any) {
  // 政策文件上传成功，循环数据添加attachmentType字段
  formData.decisionPolicyList.forEach((item) => {
    if (!item.attachmentType) {
      item.attachmentType = 2
    }
  })
}

// 决策附件上传成功处理
function handleAttachmentUploadSuccess(_fileInfo: any) {
  // 决策附件上传成功，循环数据添加attachmentType字段
  formData.decisionAttachmentList.forEach((item) => {
    if (!item.attachmentType) {
      item.attachmentType = 1
    }
  })
}

// 初始化数据
function initData() {
  // 添加一个默认的决策依据
  if (formData.decisionAccordingList.length === 0) {
    addAccording()
  }
  // 添加一个默认的利益相关方
  if (formData.decisionRelatedPartyList.length === 0) {
    addRelatedParty()
  }
}

// 保存表单
async function handleSave() {
  if (!formRef.value) {
    return
  }

  try {
    await formRef.value.validate()
    loading.value = true

    // 更新利益相关方数据中的决策主体信息
    formData.decisionRelatedPartyList.forEach((item) => {
      if (!item.department) {
        item.department = decisionSubject.department
      }
      if (!item.responsibleBy) {
        item.responsibleBy = decisionSubject.responsibleBy
      }
      if (!item.innerCommunication) {
        item.innerCommunication = communicationData.innerCommunication
      }
      if (!item.outerCommunication) {
        item.outerCommunication = communicationData.outerCommunication
      }
    })

    const params = {
      ...formData,
      // 确保日期格式正确
      deadlineDate: formData.deadlineDate ? new Date(formData.deadlineDate).toISOString() : null,
      // 确保决策主体信息被包含
      department: decisionSubject.department || formData.department,
      auditBy: decisionSubject.responsibleBy || formData.auditBy,
    }

    const response = await decisionApi.decisionReview({}, params, isEdit.value ? 'update' : 'create')

    if (response.data) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      router.push('/monitor/examination/decision')
    }
  }
  catch (error: any) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败，请检查表单信息')
  }
  finally {
    loading.value = false
  }
}

// 提交审查
async function handleSubmit() {
  if (!formRef.value) {
    return
  }

  try {
    await formRef.value.validate()

    await ElMessageBox.confirm(
      '确定要提交审查吗？提交后将无法修改。',
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true

    // 更新利益相关方数据中的决策主体信息
    formData.decisionRelatedPartyList.forEach((item) => {
      if (!item.department) {
        item.department = decisionSubject.department
      }
      if (!item.responsibleBy) {
        item.responsibleBy = decisionSubject.responsibleBy
      }
      if (!item.innerCommunication) {
        item.innerCommunication = communicationData.innerCommunication
      }
      if (!item.outerCommunication) {
        item.outerCommunication = communicationData.outerCommunication
      }
    })

    const params = {
      ...formData,
      status: 'PENDING',
      // 确保日期格式正确
      deadlineDate: formData.deadlineDate ? new Date(formData.deadlineDate).toISOString() : null,
      // 确保决策主体信息被包含
      department: decisionSubject.department || formData.department,
      auditBy: decisionSubject.responsibleBy || formData.auditBy,
    }

    const response = await decisionApi.decisionReview({}, params, isEdit.value ? 'update' : 'create')

    if (response.data) {
      ElMessage.success('提交成功')
      router.push('/monitor/examination/decision')
    }
  }
  catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error(error.message || '提交失败，请检查表单信息')
    }
  }
  finally {
    loading.value = false
  }
}

// 取消操作
function handleCancel() {
  router.push('/monitor/examination/decision')
}

// 预览功能
function handlePreview() {
  ElMessage.info('预览功能开发中')
}

// 获取详情数据
async function getDetailData(id: string | number) {
  try {
    loading.value = true
    const response = await decisionApi.decisionReview({}, { id }, 'info')

    if (response) {
      // 映射API返回的数据到表单数据
      Object.assign(formData, {
        ...response,
        // 确保日期格式正确
        deadlineDate: response.deadlineDate ? response.deadlineDate.split('T')[0] : '',
      })

      // 设置决策主体数据
      if (response.department) {
        decisionSubject.department = response.department
      }
      if (response.auditBy) {
        decisionSubject.responsibleBy = response.auditBy
      }

      // 设置沟通数据（从利益相关方中获取）
      if (response.decisionRelatedPartyList && response.decisionRelatedPartyList.length > 0) {
        const firstParty = response.decisionRelatedPartyList[0]
        communicationData.innerCommunication = firstParty.innerCommunication || ''
        communicationData.outerCommunication = firstParty.outerCommunication || ''
      }
    }
  }
  catch (error: any) {
    console.error('获取详情失败:', error)
    ElMessage.error(error.message || '获取详情失败')
  }
  finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  const id = route.query.id as string
  getDecisionTypeOptions()
  getAccordingTypeOptions()
  if (id) {
    isEdit.value = true
    getDetailData(id)
  }
  else {
    generateReviewNumber()
    initData()
  }
})
</script>

<template>
  <div class="absolute-container">
    <pageHeader>
      <template #content>
        <!-- 页面标题和按钮组 -->
        <div class="mb-6 flex items-center justify-between">
          <h1 class="text-xl font-bold">
            新增重大决策审查
          </h1>
          <div class="space-x-4">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="handleSave">
              <el-icon class="mr-2">
                <Plus />
                <i class="fas fa-save" />
              </el-icon>
              保存
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap" :loading="loading" @click="handleSubmit">
              <el-icon class="mr-2">
                <i class="fas fa-paper-plane" />
              </el-icon>
              提交审查
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap" @click="handleCancel">
              <el-icon class="mr-2">
                <i class="fas fa-times" />
              </el-icon>
              取消
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap" @click="handlePreview">
              <el-icon class="mr-2">
                <i class="fas fa-eye" />
              </el-icon>
              预览
            </el-button>
          </div>
        </div>
      </template>
    </pageHeader>
    <PageMain style="background-color: transparent;">
      <div class="mx-auto px-4 py-6 container">
        <div class="flex gap-6">
          <!-- 主要内容区 -->
          <div class="flex-grow">
            <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
              <!-- 基本信息 -->
              <el-card class="mb-6">
                <template #header>
                  <h2 class="text-lg font-bold">
                    基本信息
                  </h2>
                </template>
                <div class="space-y-6">
                  <div class="grid grid-cols-2 gap-6">
                    <el-form-item label="决策名称" prop="name" required>
                      <el-input v-model="formData.name" placeholder="请输入决策名称" />
                    </el-form-item>
                    <el-form-item label="审查编号">
                      <el-input v-model="formData.reviewCode" placeholder="自动生成" readonly />
                    </el-form-item>
                  </div>

                  <div class="grid grid-cols-2 gap-6">
                    <el-form-item label="决策类型" prop="decisionType" required>
                      <el-select v-model="formData.decisionType" placeholder="请选择决策类型" class="w-full">
                        <el-option
                          v-for="option in decisionTypeOptions"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        />
                      </el-select>
                    </el-form-item>

                    <el-form-item label="决策级别" prop="level" required>
                      <el-radio-group v-model="formData.level">
                        <el-radio-button label="GENERAL">
                          一般
                        </el-radio-button>
                        <el-radio-button label="IMPORTANT">
                          重要
                        </el-radio-button>
                        <el-radio-button label="CRITICAL">
                          关键
                        </el-radio-button>
                      </el-radio-group>
                    </el-form-item>

                    <el-form-item label="状态" prop="status" required>
                      <el-select v-model="formData.status" placeholder="请选择状态" class="w-full">
                        <el-option label="草稿" value="DRAFT" />
                        <el-option label="待审查" value="PENDING" />
                        <el-option label="发布" value="PUBLISHED" />
                        <el-option label="审核中" value="REVIEWING" />
                        <el-option label="已撤回" value="REVOKE" />
                      </el-select>
                    </el-form-item>
                  </div>

                  <div class="grid grid-cols-2 gap-6">
                    <el-form-item label="发起部门" prop="department" required>
                      <DepartmentTreeSelect v-model="formData.department" placeholder="请选择部门" />
                    </el-form-item>

                    <el-form-item label="审查人员" prop="auditBy" required>
                      <el-input v-model="formData.auditBy" placeholder="请输入审查人员" />
                    </el-form-item>
                  </div>

                  <div>
                    <el-form-item label="审查截止日期">
                      <el-date-picker
                        v-model="formData.deadlineDate"
                        type="date"
                        placeholder="选择日期"
                        class="w-full"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                      />
                    </el-form-item>

                    <el-form-item label="审查说明">
                      <el-input
                        v-model="formData.explain"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入审查说明"
                      />
                    </el-form-item>
                  </div>
                </div>
              </el-card>

              <!-- 决策内容 -->
              <el-card class="mb-6">
                <template #header>
                  <h2 class="text-lg font-bold">
                    决策内容
                  </h2>
                </template>
                <div class="space-y-6">
                  <el-form-item label="决策背景" prop="background" required>
                    <el-input
                      v-model="formData.background"
                      type="textarea"
                      :rows="5"
                      placeholder="请输入决策背景"
                    />
                  </el-form-item>

                  <el-form-item label="决策目的" prop="purpose" required>
                    <el-input
                      v-model="formData.purpose"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入决策目的"
                    />
                  </el-form-item>

                  <el-form-item label="决策方案" prop="proposal" required>
                    <el-input
                      v-model="formData.proposal"
                      type="textarea"
                      :rows="5"
                      placeholder="请输入决策方案"
                    />
                  </el-form-item>

                  <el-form-item label="实施计划">
                    <el-input
                      v-model="formData.plan"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入实施计划"
                    />
                  </el-form-item>

                  <el-form-item label="预期结果">
                    <el-input
                      v-model="formData.expectedResult"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入预期结果"
                    />
                  </el-form-item>

                  <div class="grid grid-cols-2 gap-6">
                    <el-form-item label="决策涉及金额">
                      <div class="flex">
                        <el-input-number v-model="formData.money" class="flex-1" :min="0" :precision="2" />
                        <el-select class="ml-2 w-24">
                          <el-option label="人民币" value="CNY" />
                          <el-option label="美元" value="USD" />
                          <el-option label="欧元" value="EUR" />
                          <el-option label="日元" value="JPY" />
                        </el-select>
                      </div>
                    </el-form-item>
                  </div>
                </div>
              </el-card>

              <!-- 决策依据 -->
              <el-card class="mb-6">
                <template #header>
                  <h2 class="text-lg font-bold">
                    决策依据
                  </h2>
                </template>
                <div class="space-y-6">
                  <!-- <div>
                    <label class="mb-2 block text-gray-600">
                      依据类型：
                    </label>
                    <el-checkbox-group>
                      <el-checkbox label="法律法规" />
                      <el-checkbox label="公司章程" />
                      <el-checkbox label="内部制度" />
                      <el-checkbox label="行业惯例" />
                      <el-checkbox label="专家意见" />
                      <el-checkbox label="其他" />
                    </el-checkbox-group>
                  </div> -->
                  <div>
                    <label class="mb-2 block text-gray-600">
                      具体依据：
                    </label>
                    <el-table :data="formData.decisionAccordingList" border>
                      <el-table-column prop="accordingType" label="依据类型" width="120">
                        <template #default="scope">
                          <el-select v-model="scope.row.accordingType" placeholder="请选择依据类型" class="w-full">
                            <el-option
                              v-for="item in accordingTypeOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column prop="name" label="依据名称" width="200">
                        <template #default="scope">
                          <el-input v-model="scope.row.name" placeholder="请输入依据名称" />
                        </template>
                      </el-table-column>
                      <el-table-column prop="explain" label="具体条款">
                        <template #default="scope">
                          <el-input v-model="scope.row.explain" placeholder="请输入具体条款" />
                        </template>
                      </el-table-column>
                      <el-table-column prop="describe" label="适用说明">
                        <template #default="scope">
                          <el-input v-model="scope.row.describe" placeholder="请输入适用说明" />
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80">
                        <template #default="scope">
                          <el-button type="danger" size="small" @click="removeAccording(scope.$index)">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button class="mt-2" type="primary" plain @click="addAccording">
                      <el-icon class="mr-1">
                        <i class="fas fa-plus" />
                      </el-icon>
                      添加依据
                    </el-button>
                  </div>

                  <div>
                    <label class="mb-2 block text-gray-600">
                      相关政策文件：
                    </label>
                    <UploadMbb
                      v-model="formData.decisionPolicyList"
                      :auto-upload="true"
                      :multiple="true"
                      category-name="policy"
                      tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 20MB"
                      @upload-success="handlePolicyUploadSuccess"
                    />
                  </div>
                </div>
              </el-card>

              <!-- 文档上传 -->
              <el-card class="mb-6">
                <template #header>
                  <h2 class="text-lg font-bold">
                    文档上传
                  </h2>
                </template>
                <div class="space-y-6">
                  <el-form-item label-position="top" label="决策文档" prop="document" required>
                    <div class="w-full">
                      <DocumentUpload
                        v-model="formData.documentUrl"
                        placeholder="导入决策文档，系统自动填充信息"
                        tip-text="支持 PDF、DOC、DOCX 格式，文件大小不超过 50MB"
                        :max-size="50"
                        @on-success="(fileKey, fileInfo) => {
                          formData.documentUrl = fileKey
                          formData.document = fileInfo.name
                        }"
                        @on-remove="() => {
                          formData.documentUrl = ''
                          formData.document = ''
                        }"
                      />
                    </div>
                  </el-form-item>

                  <div>
                    <label class="mb-2 block text-gray-600">
                      决策附件：
                    </label>
                    <UploadMbb
                      v-model="formData.decisionAttachmentList"
                      :auto-upload="true"
                      :multiple="true"
                      category-name="attachment"
                      tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 20MB"
                      @upload-success="handleAttachmentUploadSuccess"
                    />
                  </div>
                </div>
              </el-card>
            </el-form>

            <!-- 利益相关方 -->
            <el-card class="mb-6">
              <template #header>
                <h2 class="text-lg font-bold">
                  利益相关方
                </h2>
              </template>
              <div class="space-y-6">
                <div>
                  <label class="mb-2 block text-gray-600">
                    <span class="text-red-500">*</span>
                    决策主体：
                  </label>
                  <div class="grid grid-cols-2 gap-6">
                    <div>
                      <label class="mb-1 block text-xs text-gray-500">涉及部门</label>
                      <el-select v-model="decisionSubject.department" class="w-full" placeholder="请选择部门">
                        <el-option label="财务部" value="财务部" />
                        <el-option label="市场部" value="市场部" />
                        <el-option label="研发部" value="研发部" />
                        <el-option label="人力资源部" value="人力资源部" />
                        <el-option label="法务部" value="法务部" />
                      </el-select>
                    </div>
                    <div>
                      <label class="mb-1 block text-xs text-gray-500">主要责任人</label>
                      <el-select v-model="decisionSubject.responsibleBy" class="w-full" placeholder="请选择人员">
                        <el-option label="张明远 (法务总监)" value="张明远" />
                        <el-option label="李思琪 (财务总监)" value="李思琪" />
                        <el-option label="王建国 (CEO)" value="王建国" />
                        <el-option label="陈晓雯 (COO)" value="陈晓雯" />
                      </el-select>
                    </div>
                  </div>
                </div>

                <div>
                  <label class="mb-2 block text-gray-600">
                    外部相关方：
                  </label>
                  <el-table :data="formData.decisionRelatedPartyList" border>
                    <el-table-column prop="relatedName" label="相关方名称">
                      <template #default="scope">
                        <el-input v-model="scope.row.relatedName" placeholder="请输入相关方名称" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="relatedType" label="相关方类型">
                      <template #default="scope">
                        <el-select v-model="scope.row.relatedType" placeholder="请选择类型">
                          <el-option label="供应商" value="SUPPLIER" />
                          <el-option label="客户" value="CUSTOMER" />
                          <el-option label="合作伙伴" value="PARTNER" />
                          <el-option label="监管机构" value="REGULATOR" />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column prop="relatedExplain" label="关系说明">
                      <template #default="scope">
                        <el-input v-model="scope.row.relatedExplain" type="textarea" :rows="2" placeholder="请输入关系说明" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80">
                      <template #default="scope">
                        <el-button type="danger" size="small" @click="removeRelatedParty(scope.$index)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button class="mt-2" type="primary" plain @click="addRelatedParty">
                    <el-icon class="mr-1">
                      <i class="fas fa-plus" />
                    </el-icon>
                    添加相关方
                  </el-button>
                </div>

                <div>
                  <label class="mb-2 block text-gray-600">
                    内部沟通情况：
                  </label>
                  <el-input
                    v-model="communicationData.innerCommunication"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入内部沟通情况"
                  />
                </div>

                <div>
                  <label class="mb-2 block text-gray-600">
                    外部沟通情况：
                  </label>
                  <el-input
                    v-model="communicationData.outerCommunication"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入外部沟通情况"
                  />
                </div>
              </div>
            </el-card>

            <!-- 风险评估 -->
            <el-card>
              <template #header>
                <h2 class="text-lg font-bold">
                  风险评估
                </h2>
              </template>
              <div class="space-y-6">
                <div>
                  <label class="mb-2 block text-gray-600">
                    风险自评：
                  </label>
                  <el-checkbox-group>
                    <el-checkbox label="法律风险" />
                    <el-checkbox label="财务风险" />
                    <el-checkbox label="运营风险" />
                    <el-checkbox label="声誉风险" />
                    <el-checkbox label="合规风险" />
                    <el-checkbox label="其他" />
                  </el-checkbox-group>
                  <el-input
                    v-model="formData.riskSelf"
                    class="mt-2"
                    type="textarea"
                    :rows="3"
                    placeholder="请描述风险情况..."
                  />
                </div>

                <div>
                  <label class="mb-2 block text-gray-600">
                    风险类型：
                  </label>
                  <el-select v-model="formData.riskType" placeholder="请选择风险类型" class="w-full">
                    <el-option label="法律风险" value="LAWS" />
                    <el-option label="财务风险" value="FINANCE" />
                    <el-option label="运营风险" value="OPERATION" />
                    <el-option label="声誉风险" value="REPUTATION" />
                    <el-option label="合规风险" value="COMPLIANCE" />
                  </el-select>
                </div>

                <div>
                  <label class="mb-2 block text-gray-600">
                    风险评估：
                  </label>
                  <el-input
                    v-model="formData.riskAssess"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入风险评估"
                  />
                </div>

                <div>
                  <label class="mb-2 block text-gray-600">
                    自动风险评估：
                  </label>
                  <el-card shadow="never" class="border border-gray-200">
                    <el-button type="primary" class="!rounded-button mb-4 whitespace-nowrap">
                      <el-icon class="mr-2">
                        <i class="fas fa-bolt" />
                      </el-icon>
                      开始评估
                    </el-button>

                    <el-alert
                      title="风险等级: 中"
                      type="warning"
                      description="建议加强风险控制措施"
                      show-icon
                      class="mb-4"
                    />

                    <div class="mb-4 text-sm">
                      <div class="mb-2 font-medium">
                        主要风险点:
                      </div>
                      <ul class="list-disc pl-5 space-y-1">
                        <li>投资金额较大，可能影响公司现金流</li>
                        <li>目标公司存在未决诉讼风险</li>
                        <li>行业政策变化可能影响预期收益</li>
                      </ul>
                    </div>

                    <div class="text-sm">
                      <div class="mb-2 font-medium">
                        AI分析建议:
                      </div>
                      <p>建议增加投资回报率分析，完善退出机制条款，加强法律尽职调查，特别是针对目标公司的诉讼风险。</p>
                    </div>
                  </el-card>
                </div>

                <div>
                  <label class="mb-2 block text-gray-600">
                    影响分析：
                  </label>
                  <div class="grid grid-cols-2 gap-6">
                    <div>
                      <label class="mb-1 block text-xs text-gray-500">法律影响</label>
                      <el-select v-model="formData.legalImpactDegree" class="w-full">
                        <el-option label="无影响" value="LOW" />
                        <el-option label="轻微影响" value="LOW" />
                        <el-option label="中度影响" value="MEDIUM" />
                        <el-option label="重大影响" value="HIGH" />
                      </el-select>
                      <el-input
                        v-model="formData.legalImpact"
                        class="mt-2"
                        type="textarea"
                        :rows="2"
                        placeholder="说明..."
                      />
                    </div>
                    <div>
                      <label class="mb-1 block text-xs text-gray-500">财务影响</label>
                      <el-select v-model="formData.financialImpactDegree" class="w-full">
                        <el-option label="无影响" value="LOW" />
                        <el-option label="轻微影响" value="LOW" />
                        <el-option label="中度影响" value="MEDIUM" />
                        <el-option label="重大影响" value="HIGH" />
                      </el-select>
                      <el-input
                        v-model="formData.financialImpact"
                        class="mt-2"
                        type="textarea"
                        :rows="2"
                        placeholder="说明..."
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧辅助区 -->
          <div v-if="false" class="w-80">
            <el-card class="mb-4">
              <template #header>
                <h3 class="flex items-center text-lg font-bold">
                  <el-icon class="mr-2">
                    <i class="fas fa-info-circle" />
                  </el-icon>
                  审查指南
                </h3>
              </template>
              <div class="text-sm text-gray-600">
                <p class="mb-2">
                  1. 请确保填写所有标有 <span class="text-red-500">*</span> 的必填项
                </p>
                <p class="mb-2">
                  2. 决策名称应简明扼要，反映决策核心内容
                </p>
                <p class="mb-2">
                  3. 决策方案需详细描述实施步骤和时间节点
                </p>
                <p class="mb-2">
                  4. 上传的文档需为最终版本，避免多次修改
                </p>
                <p class="mb-2">
                  5. 风险评估需客观全面，不回避潜在问题
                </p>
                <p>6. 提交前请仔细检查所有信息，确保准确无误</p>
              </div>
            </el-card>

            <el-card class="mb-4">
              <template #header>
                <h3 class="flex items-center text-lg font-bold">
                  <el-icon class="mr-2">
                    <i class="fas fa-history" />
                  </el-icon>
                  审查进度
                </h3>
              </template>
              <el-steps direction="vertical" :active="1">
                <el-step title="创建审查" description="2023-06-15 14:30" />
                <el-step title="填写基本信息" description="进行中" />
                <el-step title="填写决策内容" description="待完成" />
                <el-step title="上传文档" description="待完成" />
                <el-step title="风险评估" description="待完成" />
                <el-step title="提交审查" description="待完成" />
              </el-steps>
            </el-card>

            <el-card>
              <template #header>
                <h3 class="flex items-center text-lg font-bold">
                  <el-icon class="mr-2">
                    <i class="fas fa-users" />
                  </el-icon>
                  审查人员
                </h3>
              </template>
              <div class="space-y-3">
                <div class="flex items-center">
                  <el-avatar :size="40" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
                  <div class="ml-3">
                    <div class="text-sm font-medium">
                      张明远
                    </div>
                    <div class="text-xs text-gray-500">
                      法务总监
                    </div>
                  </div>
                </div>
                <div class="flex items-center">
                  <el-avatar :size="40" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
                  <div class="ml-3">
                    <div class="text-sm font-medium">
                      李思琪
                    </div>
                    <div class="text-xs text-gray-500">
                      财务总监
                    </div>
                  </div>
                </div>
                <div class="flex items-center">
                  <el-avatar :size="40" src="https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png" />
                  <div class="ml-3">
                    <div class="text-sm font-medium">
                      王建国
                    </div>
                    <div class="text-xs text-gray-500">
                      CEO
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";
.el-card {
  margin-bottom: 24px;
}

.el-card :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.el-card :deep(.el-card__body) {
  padding: 20px;
}

.el-upload {
  width: 100%;
}

.el-upload-dragger {
  width: 100%;
  padding: 30px 0;
}

.el-steps {
  padding: 0 10px;
}

.el-step :deep(.el-step__title) {
  font-size: 13px;
  line-height: 1.5;
}

.el-step :deep(.el-step__description) {
  font-size: 12px;
  line-height: 1.5;
}
</style>
