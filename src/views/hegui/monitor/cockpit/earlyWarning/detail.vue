<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { ref } from 'vue'
import {
  ArrowLeft,
  Check,
  Close,
  Document,
  Download,
  Edit,
  Search,
  User,
} from '@element-plus/icons-vue'

const activeTab = ref('detail')
</script>

<template>
  <div class="absolute-container">
    <div style="height: 100%;overflow-y: auto;">
      <page-header title="" content="">
        <template #content>
          <div class="mb-6 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <h1 class="text-xl c-[#000000] font-bold">
                预警详情
              </h1>
              <span class="status-badge unprocessed">未处理</span>
            </div>
            <div class="flex space-x-3">
              <el-button type="primary" class="rounded-button whitespace-nowrap">
                <el-icon class="mr-2">
                  <Check />
                </el-icon>处理预警
              </el-button>
              <el-button type="danger" class="rounded-button whitespace-nowrap">
                <el-icon class="mr-2">
                  <Close />
                </el-icon>忽略预警
              </el-button>
              <el-button plain class="rounded-button whitespace-nowrap">
                <el-icon class="mr-2">
                  <Download />
                </el-icon>导出
              </el-button>
              <el-button plain class="rounded-button whitespace-nowrap">
                <el-icon class="mr-2">
                  <ArrowLeft />
                </el-icon>返回
              </el-button>
            </div>
          </div>
        </template>
      </page-header>
      <PageMain style="background-color: transparent;">
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card class="">
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <div class="grid grid-cols-2 mb-6 gap-4">
                  <div class="flex">
                    <span class="w-24 text-gray-500">预警ID：</span>
                    <span class="font-medium">AL20230512-001</span>
                  </div>
                  <div class="flex">
                    <span class="w-24 text-gray-500">预警级别：</span>
                    <span class="text-red-500 font-medium">高</span>
                  </div>
                  <div class="flex">
                    <span class="w-24 text-gray-500">预警类型：</span>
                    <span class="font-medium">数据异常</span>
                  </div>
                  <div class="flex">
                    <span class="w-24 text-gray-500">触发时间：</span>
                    <span class="font-medium">2023-05-12 14:23:45</span>
                  </div>
                  <div class="flex">
                    <span class="w-24 text-gray-500">触发规则：</span>
                    <span class="font-medium">销售数据异常波动</span>
                  </div>
                  <div class="flex">
                    <span class="w-24 text-gray-500">影响范围：</span>
                    <span class="font-medium">销售部/订单处理流程</span>
                  </div>
                  <div class="flex">
                    <span class="w-24 text-gray-500">当前状态：</span>
                    <span class="font-medium">未处理</span>
                  </div>
                  <div class="flex">
                    <span class="w-24 text-gray-500">处理人：</span>
                    <span class="font-medium">-</span>
                  </div>
                </div>

                <div>
                  <h3 class="mb-3 text-lg font-bold">
                    预警描述
                  </h3>
                  <p class="text-gray-700 leading-relaxed">
                    系统检测到销售部在2023年5月12日下午14:00至14:30期间，订单金额出现异常波动，30分钟内订单总额达到1,250,000元，远超该时段历史平均值的3倍标准差。此异常主要集中在华东区域的大客户订单，需要立即核查是否存在数据异常或违规操作。
                  </p>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <!-- 内容标签页区 -->
              <div class="rounded-lg bg-white shadow-sm">
                <!-- 标签页导航 -->
                <el-tabs v-model="activeTab" class="border-b border-gray-200">
                  <el-tab-pane label="详细信息" name="detail" />
                  <el-tab-pane label="处理进度" name="progress" />
                  <el-tab-pane label="相关预警" name="related" />
                  <el-tab-pane label="历史记录" name="history" />
                </el-tabs>

                <!-- 标签页内容 -->
                <div class="p-6">
                  <!-- 详细信息标签页 -->
                  <div v-if="activeTab === 'detail'">
                    <!-- 触发详情 -->
                    <div class="mb-8">
                      <h3 class="mb-4 text-lg font-bold">
                        触发详情
                      </h3>
                      <div class="rounded-lg bg-gray-50 p-4">
                        <div class="grid grid-cols-3 gap-4">
                          <div>
                            <p class="mb-1 text-sm text-gray-500">
                              触发条件
                            </p>
                            <p class="font-medium">
                              30分钟内订单总额 > 历史平均值 + 3σ
                            </p>
                          </div>
                          <div>
                            <p class="mb-1 text-sm text-gray-500">
                              触发数据
                            </p>
                            <p class="font-medium">
                              1,250,000元
                            </p>
                          </div>
                          <div>
                            <p class="mb-1 text-sm text-gray-500">
                              阈值信息
                            </p>
                            <p class="font-medium">
                              历史平均值: 420,000元 (σ=150,000)
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 影响分析 -->
                    <div class="mb-8">
                      <h3 class="mb-4 text-lg font-bold">
                        影响分析
                      </h3>
                      <div class="rounded-lg bg-gray-50 p-4">
                        <div class="grid grid-cols-3 gap-4">
                          <div>
                            <p class="mb-1 text-sm text-gray-500">
                              潜在影响
                            </p>
                            <p class="font-medium">
                              可能导致财务数据失真，影响季度报表准确性
                            </p>
                          </div>
                          <div>
                            <p class="mb-1 text-sm text-gray-500">
                              风险等级
                            </p>
                            <p class="text-red-500 font-medium">
                              高风险
                            </p>
                          </div>
                          <div>
                            <p class="mb-1 text-sm text-gray-500">
                              影响范围
                            </p>
                            <p class="font-medium">
                              销售部、财务部、审计部
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 相关资源 -->
                    <div>
                      <h3 class="mb-4 text-lg font-bold">
                        相关资源
                      </h3>
                      <div class="rounded-lg bg-gray-50 p-4">
                        <div class="grid grid-cols-3 gap-4">
                          <div>
                            <p class="mb-1 text-sm text-gray-500">
                              相关制度
                            </p>
                            <el-link type="primary" :underline="false" class="font-medium">
                              销售数据管理规定
                            </el-link>
                          </div>
                          <div>
                            <p class="mb-1 text-sm text-gray-500">
                              相关法规
                            </p>
                            <el-link type="primary" :underline="false" class="font-medium">
                              企业会计准则第14号
                            </el-link>
                          </div>
                          <div>
                            <p class="mb-1 text-sm text-gray-500">
                              处理指南
                            </p>
                            <el-link type="primary" :underline="false" class="font-medium">
                              数据异常处理流程
                            </el-link>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="">
              <template #header>
                <div class="f-16 fw-600">
                  触发规则
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <p class="text-sm text-gray-500">
                    规则名称
                  </p>
                  <p class="font-medium">
                    销售数据异常波动
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    规则类型
                  </p>
                  <p class="font-medium">
                    数据异常监控
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    创建时间
                  </p>
                  <p class="font-medium">
                    2023-01-15
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    最后修改
                  </p>
                  <p class="font-medium">
                    2023-04-20
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    规则状态
                  </p>
                  <p class="text-green-500 font-medium">
                    启用中
                  </p>
                </div>
                <el-link type="primary" :underline="false" class="mt-2 inline-block text-sm">
                  <el-icon class="mr-1">
                    <Edit />
                  </el-icon>编辑规则
                </el-link>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  处理建议
                </div>
              </template>
              <div class="space-y-4">
                <div class="rounded-lg bg-blue-50 p-3">
                  <p class="text-blue-700 font-medium">
                    推荐处理方式
                  </p>
                  <p class="mt-1 text-sm">
                    1. 核查订单数据来源<br>2. 联系销售负责人确认<br>3. 必要时发起内部审计
                  </p>
                </div>
                <div>
                  <p class="mb-1 text-sm text-gray-500">
                    历史处理案例
                  </p>
                  <p class="text-sm">
                    2023-03-08 类似预警，经核查为系统数据同步延迟导致
                  </p>
                </div>
                <div>
                  <p class="mb-1 text-sm text-gray-500">
                    最佳实践
                  </p>
                  <p class="text-sm">
                    建议在24小时内完成初步核查，72小时内提交处理报告
                  </p>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <el-button plain class="rounded-button w-full">
                  <el-icon class="mr-2">
                    <User />
                  </el-icon>分派任务
                </el-button>
                <el-button plain class="rounded-button w-full" style="margin-left: 0;">
                  <el-icon class="mr-2">
                    <Search />
                  </el-icon>相关审查
                </el-button>
                <el-button plain class="rounded-button w-full" style="margin-left: 0;">
                  <el-icon class="mr-2">
                    <Document />
                  </el-icon>创建报告
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </PageMain>
    </div>
  </div>
</template>

<style scoped>
  .font-pacifico {
    font-family: Pacifico, serif;
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 12px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 12px;
  }

  .status-badge.unprocessed {
    color: #dc2626;
    background-color: #fee2e2;
  }

  .status-badge.processing {
    color: #d97706;
    background-color: #fef3c7;
  }

  .status-badge.processed {
    color: #059669;
    background-color: #d1fae5;
  }

  .status-badge.ignored {
    color: #4b5563;
    background-color: #e5e7eb;
  }

  .tab-active {
    position: relative;
    font-weight: 600;
    color: #3b82f6;
  }

  .tab-active::after {
    position: absolute;
    right: 0;
    bottom: -1px;
    left: 0;
    height: 2px;
    content: "";
    background-color: #3b82f6;
  }

  .timeline-item {
    position: relative;
    padding-left: 24px;
    margin-bottom: 16px;
  }

  .timeline-item::before {
    position: absolute;
    top: 4px;
    left: 0;
    width: 12px;
    height: 12px;
    content: "";
    background-color: #3b82f6;
    border-radius: 50%;
  }

  .timeline-item::after {
    position: absolute;
    top: 16px;
    bottom: -16px;
    left: 5px;
    width: 2px;
    content: "";
    background-color: #e5e7eb;
  }

  .timeline-item:last-child::after {
    display: none;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
