<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { ref } from 'vue'
import {
  Clock,
  Delete,
  DocumentCopy,
  Download,
  Edit,
  Plus,
  Search,
  TopRight,
  Upload,
} from '@element-plus/icons-vue'

const paging = ref({
  page: 1,
  limit: 1,
  total: 1,
})

const tableData = ref([
  {
    name: '财务数据完整性检查',
    type: '数据完整性',
    scope: '财务系统',
    condition: '总账科目余额 ≠ 明细账合计',
    priority: '高',
    priorityClass: 'high',
    status: true,
    lastModified: '2023-05-15 14:30',
  },
  {
    name: '采购订单超期未审批',
    type: '时效性',
    scope: '采购系统',
    condition: '创建时间 > 24小时且状态=待审批',
    priority: '中',
    priorityClass: 'medium',
    status: true,
    lastModified: '2023-05-10 09:15',
  },
  {
    name: '员工信息变更审计',
    type: '合规性',
    scope: 'HR系统',
    condition: '关键字段变更且无审批记录',
    priority: '高',
    priorityClass: 'high',
    status: false,
    lastModified: '2023-05-08 16:45',
  },
  {
    name: '销售合同到期提醒',
    type: '时效性',
    scope: '销售系统',
    condition: '到期日期 - 当前日期 ≤ 30天',
    priority: '低',
    priorityClass: 'low',
    status: true,
    lastModified: '2023-05-05 11:20',
  },
  {
    name: '系统登录异常检测',
    type: '安全性',
    scope: '所有系统',
    condition: '同一IP短时间多次失败登录',
    priority: '高',
    priorityClass: 'high',
    status: true,
    lastModified: '2023-05-02 10:10',
  },
])

const templates = ref([
  {
    name: '财务数据完整性检查',
    desc: '检查总账与明细账数据一致性',
  },
  {
    name: '审批流程超时提醒',
    desc: '监控审批流程时效性',
  },
  {
    name: '敏感数据变更审计',
    desc: '跟踪关键数据变更记录',
  },
])

const router = useRouter()
function goDeatil(item: Object) {
  console.log(item)
  router.push({
    name: '/monitor/cockpit/3/detail',
    query: {},
  })
}
</script>

<template>
  <div class="absolute-container">
    <div style="height: 100%;overflow-y: auto;">
      <page-header title="" content="">
        <template #content>
          <div class="aic jcsb flex c-[#000000]">
            <div class="flex items-center">
              <h1 class="f-24 fw-600">
                预警配置
              </h1>
              <!-- <span class="text-base text-gray-600">监控对象：华东区销售部</span> -->
            </div>
            <div>
              <div class="flex space-x-3">
                <el-button type="primary" class="rounded-button whitespace-nowrap">
                  <el-icon class="mr-2">
                    <Plus />
                  </el-icon>新增规则
                </el-button>
                <el-button plain class="rounded-button whitespace-nowrap">
                  <el-icon class="mr-2">
                    <Upload />
                  </el-icon>批量导入
                </el-button>
                <el-button plain class="rounded-button whitespace-nowrap">
                  <el-icon class="mr-2">
                    <Download />
                  </el-icon>导出
                </el-button>
              </div>
            </div>
          </div>
        </template>
      </page-header>
      <PageMain style="background-color: transparent;">
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card class="">
              <!--              <template #header>
                <div class="f-16 fw-600">相关资源</div>
              </template> -->
              <!-- 筛选区域 -->
              <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
                <div class="grid grid-cols-4 gap-4">
                  <div>
                    <label class="mb-1 block text-sm text-gray-700 font-medium">规则类型</label>
                    <el-select class="w-full" placeholder="全部">
                      <el-option label="全部" value="all" />
                      <el-option label="数据完整性" value="integrity" />
                      <el-option label="时效性" value="timeliness" />
                      <el-option label="合规性" value="compliance" />
                      <el-option label="安全性" value="security" />
                    </el-select>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-700 font-medium">规则状态</label>
                    <el-select class="w-full" placeholder="全部">
                      <el-option label="全部" value="all" />
                      <el-option label="已启用" value="enabled" />
                      <el-option label="已禁用" value="disabled" />
                    </el-select>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-700 font-medium">应用范围</label>
                    <el-select class="w-full" placeholder="全部">
                      <el-option label="全部" value="all" />
                      <el-option label="财务系统" value="finance" />
                      <el-option label="HR系统" value="hr" />
                      <el-option label="采购系统" value="purchase" />
                      <el-option label="销售系统" value="sales" />
                    </el-select>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-700 font-medium">关键词搜索</label>
                    <el-input placeholder="输入规则名称或描述">
                      <template #suffix>
                        <el-icon class="text-gray-400">
                          <Search />
                        </el-icon>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>

              <el-table :data="tableData" style="width: 100%;">
                <el-table-column prop="name" label="规则名称" />
                <el-table-column prop="type" label="规则类型" />
                <el-table-column prop="scope" label="应用范围" />
                <el-table-column prop="condition" label="触发条件摘要" />
                <el-table-column prop="priority" label="优先级">
                  <template #default="{ row }">
                    <span :class="`priority-${row.priorityClass}`">{{ row.priority }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态">
                  <template #default="{ row }">
                    <el-switch v-model="row.status" />
                  </template>
                </el-table-column>
                <el-table-column prop="lastModified" label="最后修改时间" />
                <el-table-column label="操作" width="180">
                  <template #default>
                    <el-button type="text" size="small">
                      <el-icon @click="goDeatil()">
                        <Edit />
                      </el-icon>
                    </el-button>
                    <el-button type="text" size="small">
                      <el-icon>
                        <DocumentCopy />
                      </el-icon>
                    </el-button>
                    <el-button type="text" size="small">
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </el-button>
                    <el-button type="text" size="small">
                      <el-icon>
                        <Clock />
                      </el-icon>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <page-compon
                :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
                @pag-change="pagChange"
              />
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="">
              <template #header>
                <div class="f-16 fw-600">
                  配置指南
                </div>
              </template>
              <ul class="text-sm text-gray-600 space-y-2">
                <li>1. 点击"新增规则"创建新预警规则</li>
                <li>2. 设置规则类型和应用范围</li>
                <li>3. 配置触发条件和动作</li>
                <li>4. 测试并保存规则</li>
              </ul>
              <el-link type="primary" class="mt-3 text-sm" :underline="false">
                查看详细文档
                <el-icon class="ml-1">
                  <TopRight />
                </el-icon>
              </el-link>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  预设规则模板
                </div>
              </template>
              <div class="space-y-3">
                <div
                  v-for="(template, index) in templates" :key="index"
                  class="cursor-pointer border border-gray-200 rounded p-2 hover:bg-gray-50"
                >
                  <div class="text-sm font-medium">
                    {{ template.name }}
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    {{ template.desc }}
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  配置统计
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600">当前规则总数</span>
                    <span class="font-medium">24</span>
                  </div>
                  <el-progress :percentage="100" :show-text="false" :stroke-width="6" color="#2563eb" />
                </div>
                <div>
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600">已启用规则数</span>
                    <span class="font-medium">18</span>
                  </div>
                  <el-progress :percentage="75" :show-text="false" :stroke-width="6" color="#67C23A" />
                </div>
                <div>
                  <div class="mb-1 text-sm text-gray-600">
                    优先级分布
                  </div>
                  <div class="flex text-xs space-x-2">
                    <div class="flex items-center">
                      <div class="mr-1 h-2 w-2 rounded-full bg-red-500" />
                      <span>高: 8</span>
                    </div>
                    <div class="flex items-center">
                      <div class="mr-1 h-2 w-2 rounded-full bg-orange-500" />
                      <span>中: 10</span>
                    </div>
                    <div class="flex items-center">
                      <div class="mr-1 h-2 w-2 rounded-full bg-yellow-500" />
                      <span>低: 6</span>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600">最近30天触发次数</span>
                    <span class="font-medium">156</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </PageMain>
    </div>
  </div>
</template>

<style scoped>
  .priority-high {
    color: #ef4444;
  }

  .priority-medium {
    color: #f97316;
  }

  .priority-low {
    color: #eab308;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
