<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { ref } from 'vue'
import {
  Bell,
  Bottom,
  Document,
  Download,
  Operation,
  Refresh,
  Search,
  Top,
} from '@element-plus/icons-vue'

const timeRange = ref('today')
const activeTab = ref('metrics')
const metricType = ref('all')
const metricStatus = ref('all')
const searchMetric = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(4)
const paging = ref({
  page: 1,
  limit: 1,
  total: 1,
})

const metricsData = ref([
  {
    name: '合同合规率',
    currentValue: '98.7%',
    targetValue: '≥95%',
    status: '正常',
    trendIcon: 'Top',
    trendValue: '2.1%',
    updateTime: '2023-11-15 14:30:22',
  },
  {
    name: '审批时效',
    currentValue: '3.2天',
    targetValue: '≤2天',
    status: '预警',
    trendIcon: 'Top',
    trendValue: '0.5天',
    updateTime: '2023-11-15 14:30:22',
  },
  {
    name: '客户投诉率',
    currentValue: '0.8%',
    targetValue: '≤1%',
    status: '正常',
    trendIcon: 'Bottom',
    trendValue: '0.3%',
    updateTime: '2023-11-15 14:30:22',
  },
  {
    name: '违规操作次数',
    currentValue: '5次',
    targetValue: '≤2次',
    status: '异常',
    trendIcon: 'Top',
    trendValue: '3次',
    updateTime: '2023-11-15 14:30:22',
  },
])

function getStatusColor(status: string) {
  switch (status) {
    case '正常': return 'green'
    case '预警': return 'yellow'
    case '异常': return 'red'
    default: return 'gray'
  }
}
</script>

<template>
  <div class="absolute-container">
    <div style="height: 100%;overflow-y: auto;">
      <page-header title="" content="">
        <template #content>
          <div class="aic jcsb flex c-[#000000]">
            <div class="flex items-center">
              <h1 class="mr-4 text-xl font-bold">
                实时监控详情
              </h1>
              <span class="text-base text-gray-600">监控对象：华东区销售部</span>
            </div>
            <div>
              <div class="flex items-center space-x-3">
                <el-select v-model="timeRange" class="w-32" placeholder="时间范围">
                  <el-option label="今日" value="today" />
                  <el-option label="本周" value="week" />
                  <el-option label="本月" value="month" />
                  <el-option label="自定义" value="custom" />
                </el-select>
                <el-button class="!rounded-button whitespace-nowrap">
                  <el-icon>
                    <Refresh />
                  </el-icon>
                  <span>刷新</span>
                </el-button>
                <el-button type="primary" class="!rounded-button whitespace-nowrap">
                  <el-icon>
                    <Download />
                  </el-icon>
                  <span>导出报告</span>
                </el-button>
              </div>
            </div>
          </div>
        </template>
      </page-header>

      <PageMain style="background-color: transparent;">
        <el-row :gutter="20" class="">
          <el-col :span="16">
            <!-- 左侧区域 -->
            <el-card class="mb-4">
              <template #header>
                <h2 class="text-lg font-semibold">
                  监控概览
                </h2>
              </template>
              <div class="grid grid-cols-2 mb-6 gap-4">
                <div>
                  <div class="grid grid-cols-2 gap-y-3 text-sm">
                    <div class="text-gray-500">
                      监控对象：
                    </div>
                    <div>华东区销售部</div>
                    <div class="text-gray-500">
                      监控类型：
                    </div>
                    <div>实时监控</div>
                    <div class="text-gray-500">
                      风险等级：
                    </div>
                    <div class="text-yellow-500">
                      中
                    </div>
                  </div>
                </div>
                <div>
                  <div class="grid grid-cols-2 gap-y-3 text-sm">
                    <div class="text-gray-500">
                      责任人：
                    </div>
                    <div>张明远</div>
                    <div class="text-gray-500">
                      最近更新：
                    </div>
                    <div>2023-11-15 14:30:22</div>
                    <div class="text-gray-500">
                      监控周期：
                    </div>
                    <div>每日</div>
                  </div>
                </div>
              </div>
              <div class="grid grid-cols-4 gap-4">
                <el-card shadow="never" class="bg-green-50">
                  <div class="mb-1 text-sm text-gray-500">
                    合同合规率
                  </div>
                  <div class="flex items-end justify-between">
                    <div class="text-2xl text-green-500 font-bold">
                      98.7%
                    </div>
                    <div class="text-green-500">
                      <el-icon>
                        <Top />
                      </el-icon>
                      <span class="text-xs">2.1%</span>
                    </div>
                  </div>
                </el-card>
                <el-card shadow="never" class="bg-yellow-50">
                  <div class="mb-1 text-sm text-gray-500">
                    审批时效
                  </div>
                  <div class="flex items-end justify-between">
                    <div class="text-2xl text-yellow-500 font-bold">
                      3.2天
                    </div>
                    <div class="text-yellow-500">
                      <el-icon>
                        <Top />
                      </el-icon>
                      <span class="text-xs">0.5天</span>
                    </div>
                  </div>
                </el-card>
                <el-card shadow="never" class="bg-green-50">
                  <div class="mb-1 text-sm text-gray-500">
                    客户投诉率
                  </div>
                  <div class="flex items-end justify-between">
                    <div class="text-2xl text-green-500 font-bold">
                      0.8%
                    </div>
                    <div class="text-green-500">
                      <el-icon>
                        <Bottom />
                      </el-icon>
                      <span class="text-xs">0.3%</span>
                    </div>
                  </div>
                </el-card>
                <el-card shadow="never" class="bg-red-50">
                  <div class="mb-1 text-sm text-gray-500">
                    违规操作
                  </div>
                  <div class="flex items-end justify-between">
                    <div class="text-2xl text-red-500 font-bold">
                      5次
                    </div>
                    <div class="text-red-500">
                      <el-icon>
                        <Top />
                      </el-icon>
                      <span class="text-xs">3次</span>
                    </div>
                  </div>
                </el-card>
              </div>
            </el-card>
            <!-- 内容标签页区 -->
            <el-card class="mt-20">
              <template #header>
                <el-tabs v-model="activeTab">
                  <el-tab-pane label="详细指标" name="metrics" />
                  <el-tab-pane label="异常记录" name="exceptions" />
                  <el-tab-pane label="监控规则" name="rules" />
                  <el-tab-pane label="操作日志" name="logs" />
                </el-tabs>
              </template>
              <!-- 详细指标标签页内容 -->
              <div v-if="activeTab === 'metrics'">
                <div class="mb-4 flex items-center justify-between">
                  <div class="flex space-x-2">
                    <el-select v-model="metricType" placeholder="全部指标类型" class="w-36">
                      <el-option label="全部指标类型" value="all" />
                      <el-option label="合同类" value="contract" />
                      <el-option label="审批类" value="approval" />
                      <el-option label="客户类" value="customer" />
                      <el-option label="操作类" value="operation" />
                    </el-select>
                    <el-select v-model="metricStatus" placeholder="全部状态" class="w-32">
                      <el-option label="全部状态" value="all" />
                      <el-option label="正常" value="normal" />
                      <el-option label="预警" value="warning" />
                      <el-option label="异常" value="error" />
                    </el-select>
                  </div>
                  <el-input v-model="searchMetric" placeholder="搜索指标名称" class="w-64" :prefix-icon="Search" />
                </div>

                <el-table :data="metricsData" class="w-full">
                  <el-table-column prop="name" label="指标名称" width="150" />
                  <el-table-column prop="currentValue" label="当前值" width="100" />
                  <el-table-column prop="targetValue" label="目标值" width="100" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <span :class="`text-${getStatusColor(row.status)}-500`">{{ row.status }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="trend" label="变化趋势" width="120">
                    <template #default="{ row }">
                      <div :class="`text-${getStatusColor(row.status)}-500 flex items-center`">
                        <el-icon>
                          <component :is="row.trendIcon" />
                        </el-icon>
                        <span class="ml-1">{{ row.trendValue }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="updateTime" label="更新时间" width="180" />
                  <el-table-column label="操作" width="180">
                    <template #default>
                      <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap">
                        查看趋势
                      </el-button>
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        设置规则
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- <div class="mt-4 flex justify-between items-center"> -->
                <page-compon
                  :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
                  @pag-change="pagChange"
                />
                <!-- <div class="text-sm text-gray-500">显示 1-4 条，共 4 条</div>
                  <el-pagination :current-page="currentPage" :page-size="pageSize" :total="total"
                    layout="prev, pager, next" small /> -->
                <!-- </div> -->
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <!-- 右侧区域 -->
            <el-card class="">
              <template #header>
                <h2 class="text-base font-semibold">
                  最近预警
                </h2>
              </template>
              <div class="space-y-3">
                <div class="border-l-2 border-yellow-500 py-1 pl-3">
                  <div class="mb-1 text-xs text-gray-500">
                    2023-11-15 10:22:15
                  </div>
                  <div class="text-sm">
                    审批时效超过阈值 (3.2天 > 2天)
                  </div>
                  <div class="mt-1 text-xs text-yellow-500">
                    未处理
                  </div>
                </div>
                <div class="border-l-2 border-red-500 py-1 pl-3">
                  <div class="mb-1 text-xs text-gray-500">
                    2023-11-14 16:45:30
                  </div>
                  <div class="text-sm">
                    检测到异常合同审批流程
                  </div>
                  <div class="mt-1 text-xs text-green-500">
                    已处理
                  </div>
                </div>
                <div class="border-l-2 border-yellow-500 py-1 pl-3">
                  <div class="mb-1 text-xs text-gray-500">
                    2023-11-14 09:12:45
                  </div>
                  <div class="text-sm">
                    客户投诉率上升趋势
                  </div>
                  <div class="mt-1 text-xs text-blue-500">
                    处理中
                  </div>
                </div>
              </div>
            </el-card>

            <el-card class="mt-20">
              <template #header>
                <h2 class="text-base font-semibold">
                  相关分析报告
                </h2>
              </template>
              <div class="space-y-3">
                <div class="border-l-2 border-gray-100 py-1 pl-3">
                  <div class="text-primary mb-1 text-sm">
                    华东区销售部11月合规分析
                  </div>
                  <div class="text-xs text-gray-500">
                    2023-11-10
                  </div>
                </div>
                <div class="border-l-2 border-gray-100 py-1 pl-3">
                  <div class="text-primary mb-1 text-sm">
                    合同审批流程优化建议
                  </div>
                  <div class="text-xs text-gray-500">
                    2023-10-28
                  </div>
                </div>
                <div class="border-l-2 border-gray-100 py-1 pl-3">
                  <div class="text-primary mb-1 text-sm">
                    客户投诉处理指南
                  </div>
                  <div class="text-xs text-gray-500">
                    2023-10-15
                  </div>
                </div>
              </div>
            </el-card>

            <el-card class="mt-20">
              <template #header>
                <h2 class="text-base font-semibold">
                  快捷操作
                </h2>
              </template>
              <div class="space-y-2">
                <el-button type="primary" plain class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Operation />
                  </el-icon>
                  <span>调整监控规则</span>
                </el-button>
                <el-button type="primary" plain class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span>生成分析报告</span>
                </el-button>
                <el-button type="primary" plain class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Bell />
                  </el-icon>
                  <span>发送通知</span>
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </PageMain>
    </div>
  </div>
</template>

<style scoped>
  .border-l-2 {
    border-left-width: 2px;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
