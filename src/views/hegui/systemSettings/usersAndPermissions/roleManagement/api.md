---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 03-企业组织架构服务/角色

## POST 创建新的角色

POST /whiskerguardorgservice/api/roles

创建新的角色

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "description": "string",
  "status": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parent": {
    "id": 0,
    "tenantId": 0,
    "name": "string",
    "code": "string",
    "description": "string",
    "status": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {},
    "updatedBy": "string",
    "updatedAt": {},
    "isDeleted": true,
    "parent": {
      "id": 0,
      "tenantId": 0,
      "name": "string",
      "code": "string",
      "description": "string",
      "status": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "parent": {
        "id": 0,
        "tenantId": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {},
        "updatedBy": "string",
        "updatedAt": {},
        "isDeleted": true,
        "parent": {
          "id": null,
          "tenantId": null,
          "name": null,
          "code": null,
          "description": null,
          "status": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parent": null
        }
      }
    }
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[RoleDTO](#schemaroledto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "code": "",
  "description": "",
  "status": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parent": {
    "id": 0,
    "tenantId": 0,
    "name": "",
    "code": "",
    "description": "",
    "status": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parent": {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "code": "",
      "description": "",
      "status": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parent": {}
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityRoleDTO](#schemaresponseentityroledto)|

## GET 获取所有角色

GET /whiskerguardorgservice/api/roles

获取所有角色

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |none|
|size|query|integer| 否 |none|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "tenantId": 0,
    "name": "",
    "code": "",
    "description": "",
    "status": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parent": {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "code": "",
      "description": "",
      "status": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parent": {
        "id": 0,
        "tenantId": 0,
        "name": "",
        "code": "",
        "description": "",
        "status": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "parent": {}
      }
    }
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*the [Extension of{@link HttpEntity} that adds an{@link HttpStatusCode} status code.
Used in{@code RestTemplate} as well as in{@code @Controller} methods.

<p>In{@code RestTemplate}, this class is returned by
{@link org.springframework.web.client.RestTemplate#getForEntity getForEntity()} and
{@link org.springframework.web.client.RestTemplate#exchange exchange()}:
<pre class="code">
ResponseEntity&lt;String&gt; entity = template.getForEntity("https://example.com", String.class);
String body = entity.getBody();
MediaType contentType = entity.getHeaders().getContentType();
HttpStatus statusCode = entity.getStatusCode();
</pre>

<p>This can also be used in Spring MVC as the return value from an
{@code @Controller} method:
<pre class="code">
&#64;RequestMapping("/handle")
public ResponseEntity&lt;String&gt; handle(){
  URI location = ...;
  HttpHeaders responseHeaders = new HttpHeaders();
  responseHeaders.setLocation(location);
  responseHeaders.set("MyResponseHeader", "MyValue");
  return new ResponseEntity&lt;String&gt;("Hello World", responseHeaders, HttpStatus.CREATED);
}
</pre>

Or, by using a builder accessible via static methods:
<pre class="code">
&#64;RequestMapping("/handle")
public ResponseEntity&lt;String&gt; handle(){
  URI location = ...;
  return ResponseEntity.created(location).header("MyResponseHeader", "MyValue").body("Hello World");
}
</pre>] with status {@code 200 (OK)} and the list of roles in body.
         返回状态码为200的响应和角色列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListRoleDTO](#schemalistroledto)]|false|none||the [Extension of{@link HttpEntity} that adds an{@link HttpStatusCode} status code.<br />Used in{@code RestTemplate} as well as in{@code @Controller} methods.<br /><br /><p>In{@code RestTemplate}, this class is returned by<br />{@link org.springframework.web.client.RestTemplate#getForEntity getForEntity()} and<br />{@link org.springframework.web.client.RestTemplate#exchange exchange()}:<br /><pre class="code"><br />ResponseEntity&lt;String&gt; entity = template.getForEntity("https://example.com", String.class);<br />String body = entity.getBody();<br />MediaType contentType = entity.getHeaders().getContentType();<br />HttpStatus statusCode = entity.getStatusCode();<br /></pre><br /><br /><p>This can also be used in Spring MVC as the return value from an<br />{@code @Controller} method:<br /><pre class="code"><br />&#64;RequestMapping("/handle")<br />public ResponseEntity&lt;String&gt; handle(){<br />  URI location = ...;<br />  HttpHeaders responseHeaders = new HttpHeaders();<br />  responseHeaders.setLocation(location);<br />  responseHeaders.set("MyResponseHeader", "MyValue");<br />  return new ResponseEntity&lt;String&gt;("Hello World", responseHeaders, HttpStatus.CREATED);<br />}<br /></pre><br /><br />Or, by using a builder accessible via static methods:<br /><pre class="code"><br />&#64;RequestMapping("/handle")<br />public ResponseEntity&lt;String&gt; handle(){<br />  URI location = ...;<br />  return ResponseEntity.created(location).header("MyResponseHeader", "MyValue").body("Hello World");<br />}<br /></pre>] with status {@code 200 (OK)} and the list of roles in body.<br />         返回状态码为200的响应和角色列表|
|» id|integer(int64)|false|none||主键 ID|
|» tenantId|integer(int64)|true|none||租户 ID|
|» name|string|true|none||角色名称|
|» code|string|true|none||角色编码|
|» description|string|false|none||描述|
|» status|integer|true|none||状态|
|» metadata|string|false|none||扩展元数据|
|» version|integer|true|none||乐观锁版本|
|» createdBy|string|false|none||创建者|
|» createdAt|object|true|none||创建时间|
|» updatedBy|string|false|none||更新者|
|» updatedAt|object|true|none||创建时间|
|» isDeleted|boolean|true|none||软删除标志|
|» parent|[RoleDTO](#schemaroledto)|false|none||none|
|»» id|integer(int64)|false|none||主键 ID|
|»» tenantId|integer(int64)|true|none||租户 ID|
|»» name|string|true|none||角色名称|
|»» code|string|true|none||角色编码|
|»» description|string|false|none||描述|
|»» status|integer|true|none||状态|
|»» metadata|string|false|none||扩展元数据|
|»» version|integer|false|none||乐观锁版本|
|»» createdBy|string|false|none||创建者|
|»» createdAt|object|false|none||创建时间|
|»» updatedBy|string|false|none||更新者|
|»» updatedAt|object|false|none||创建时间|
|»» isDeleted|boolean|false|none||软删除标志|
|»» parent|[RoleDTO](#schemaroledto)|false|none||none|
|»»» id|integer(int64)|false|none||主键 ID|
|»»» tenantId|integer(int64)|true|none||租户 ID|
|»»» name|string|true|none||角色名称|
|»»» code|string|true|none||角色编码|
|»»» description|string|false|none||描述|
|»»» status|integer|true|none||状态|
|»»» metadata|string|false|none||扩展元数据|
|»»» version|integer|false|none||乐观锁版本|
|»»» createdBy|string|false|none||创建者|
|»»» createdAt|object|false|none||创建时间|
|»»» updatedBy|string|false|none||更新者|
|»»» updatedAt|object|false|none||创建时间|
|»»» isDeleted|boolean|false|none||软删除标志|
|»»» parent|[RoleDTO](#schemaroledto)|false|none||none|

## PATCH 部分更新现有角色

PATCH /whiskerguardorgservice/api/roles/{id}

部分更新现有角色的指定字段，如果字段为null则忽略

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "description": "string",
  "status": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parent": {
    "id": 0,
    "tenantId": 0,
    "name": "string",
    "code": "string",
    "description": "string",
    "status": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {},
    "updatedBy": "string",
    "updatedAt": {},
    "isDeleted": true,
    "parent": {
      "id": 0,
      "tenantId": 0,
      "name": "string",
      "code": "string",
      "description": "string",
      "status": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "parent": {
        "id": 0,
        "tenantId": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {},
        "updatedBy": "string",
        "updatedAt": {},
        "isDeleted": true,
        "parent": {
          "id": null,
          "tenantId": null,
          "name": null,
          "code": null,
          "description": null,
          "status": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parent": null
        }
      }
    }
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |the id of the roleDTO to save. 要保存的角色DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[RoleDTO](#schemaroledto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "code": "",
  "description": "",
  "status": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parent": {
    "id": 0,
    "tenantId": 0,
    "name": "",
    "code": "",
    "description": "",
    "status": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parent": {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "code": "",
      "description": "",
      "status": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parent": {}
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityRoleDTO](#schemaresponseentityroledto)|

## GET 获取指定ID的角色

GET /whiskerguardorgservice/api/roles/{id}

获取指定ID的角色

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |the id of the roleDTO to retrieve. 要检索的角色DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "code": "",
  "description": "",
  "status": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parent": {
    "id": 0,
    "tenantId": 0,
    "name": "",
    "code": "",
    "description": "",
    "status": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parent": {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "code": "",
      "description": "",
      "status": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parent": {}
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityRoleDTO](#schemaresponseentityroledto)|

## DELETE 删除指定ID的角色

DELETE /whiskerguardorgservice/api/roles/{id}

删除指定ID的角色

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |the id of the roleDTO to delete. 要删除的角色DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_RoleDTO">RoleDTO</h2>

<a id="schemaroledto"></a>
<a id="schema_RoleDTO"></a>
<a id="tocSroledto"></a>
<a id="tocsroledto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "description": "string",
  "status": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parent": {
    "id": 0,
    "tenantId": 0,
    "name": "string",
    "code": "string",
    "description": "string",
    "status": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {},
    "updatedBy": "string",
    "updatedAt": {},
    "isDeleted": true,
    "parent": {
      "id": 0,
      "tenantId": 0,
      "name": "string",
      "code": "string",
      "description": "string",
      "status": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "parent": {
        "id": 0,
        "tenantId": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {},
        "updatedBy": "string",
        "updatedAt": {},
        "isDeleted": true,
        "parent": {
          "id": null,
          "tenantId": null,
          "name": null,
          "code": null,
          "description": null,
          "status": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parent": null
        }
      }
    }
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|tenantId|integer(int64)|true|none||租户 ID|
|name|string|true|none||角色名称|
|code|string|true|none||角色编码|
|description|string|false|none||描述|
|status|integer|true|none||状态|
|metadata|string|false|none||扩展元数据|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|object|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|object|false|none||创建时间|
|isDeleted|boolean|false|none||软删除标志|
|parent|[RoleDTO](#schemaroledto)|false|none||none|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ResponseEntityRoleDTO">ResponseEntityRoleDTO</h2>

<a id="schemaresponseentityroledto"></a>
<a id="schema_ResponseEntityRoleDTO"></a>
<a id="tocSresponseentityroledto"></a>
<a id="tocsresponseentityroledto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "description": "string",
  "status": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parent": {
    "id": 0,
    "tenantId": 0,
    "name": "string",
    "code": "string",
    "description": "string",
    "status": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {},
    "updatedBy": "string",
    "updatedAt": {},
    "isDeleted": true,
    "parent": {
      "id": 0,
      "tenantId": 0,
      "name": "string",
      "code": "string",
      "description": "string",
      "status": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "parent": {
        "id": 0,
        "tenantId": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {},
        "updatedBy": "string",
        "updatedAt": {},
        "isDeleted": true,
        "parent": {
          "id": null,
          "tenantId": null,
          "name": null,
          "code": null,
          "description": null,
          "status": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parent": null
        }
      }
    }
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|tenantId|integer(int64)|true|none||租户 ID|
|name|string|true|none||角色名称|
|code|string|true|none||角色编码|
|description|string|false|none||描述|
|status|integer|true|none||状态|
|metadata|string|false|none||扩展元数据|
|version|integer|true|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|object|true|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|object|true|none||创建时间|
|isDeleted|boolean|true|none||软删除标志|
|parent|[RoleDTO](#schemaroledto)|false|none||none|

<h2 id="tocS_ListRoleDTO">ListRoleDTO</h2>

<a id="schemalistroledto"></a>
<a id="schema_ListRoleDTO"></a>
<a id="tocSlistroledto"></a>
<a id="tocslistroledto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "description": "string",
  "status": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parent": {
    "id": 0,
    "tenantId": 0,
    "name": "string",
    "code": "string",
    "description": "string",
    "status": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {},
    "updatedBy": "string",
    "updatedAt": {},
    "isDeleted": true,
    "parent": {
      "id": 0,
      "tenantId": 0,
      "name": "string",
      "code": "string",
      "description": "string",
      "status": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "parent": {
        "id": 0,
        "tenantId": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {},
        "updatedBy": "string",
        "updatedAt": {},
        "isDeleted": true,
        "parent": {
          "id": null,
          "tenantId": null,
          "name": null,
          "code": null,
          "description": null,
          "status": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parent": null
        }
      }
    }
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|tenantId|integer(int64)|true|none||租户 ID|
|name|string|true|none||角色名称|
|code|string|true|none||角色编码|
|description|string|false|none||描述|
|status|integer|true|none||状态|
|metadata|string|false|none||扩展元数据|
|version|integer|true|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|object|true|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|object|true|none||创建时间|
|isDeleted|boolean|true|none||软删除标志|
|parent|[RoleDTO](#schemaroledto)|false|none||none|

