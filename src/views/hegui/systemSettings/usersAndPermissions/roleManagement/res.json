[{"id": 582, "parentId": 0, "path": "/one", "redirect": null, "component": "", "name": "/one", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"一体\",\"icon\":\"ep:user\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 583, "parentId": 582, "path": "/one/systemManagement", "redirect": null, "component": "Layout", "name": "/one/systemManagement", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"制度管理\",\"icon\":\"i-heroicons-solid:menu-alt-3\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 584, "parentId": 583, "path": "/one/systemManagement/index", "redirect": null, "component": "hegui/one/system/systemManagement/index.vue", "name": "/one/systemManagement/index", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"制度库管理\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 585, "parentId": 584, "path": "/one/systemManagement/detail", "redirect": null, "component": "hegui/one/system/systemManagement/detail.vue", "name": "/one/systemManagement/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"制度详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/one/systemManagement/index\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}, {"id": 586, "parentId": 584, "path": "/one/systemManagement/addEdit", "redirect": null, "component": "hegui/one/system/systemManagement/addEdit.vue", "name": "/one/systemManagement/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"新增制度\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/one/systemManagement/index\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}, {"id": 587, "parentId": 583, "path": "/one/regulatoryConversion/index", "redirect": null, "component": "hegui/one/system/regulatoryConversion/index.vue", "name": "/one/regulatoryConversion/index", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"法规转化\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 588, "parentId": 587, "path": "/one/regulatoryConversion/detail", "redirect": null, "component": "hegui/one/system/regulatoryConversion/detail.vue", "name": "/one/regulatoryConversion/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"制度详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/one/regulatoryConversion/index\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}, {"id": 589, "parentId": 587, "path": "/one/regulatoryConversion/addEdit", "redirect": null, "component": "hegui/one/system/regulatoryConversion/addEdit.vue", "name": "/one/regulatoryConversion/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"新增制度\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/one/regulatoryConversion/index\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}, {"id": 590, "parentId": 583, "path": "/system/review", "redirect": null, "component": "hegui/one/system/systemAdditionAndReview.vue", "name": "/system/review", "realName": null, "isMenu": true, "sort": 3, "mark": 1, "meta": "{\"title\":\"审查记录\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 591, "parentId": 590, "path": "/system/review/detail", "redirect": null, "component": "hegui/one/system/systemAdditionAndReview/detail.vue", "name": "/system/review/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"制度审查详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/system/review\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}, {"id": 592, "parentId": 590, "path": "/system/review/report", "redirect": null, "component": "hegui/one/system/systemAdditionAndReview/report.vue", "name": "/system/review/report", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"制度审查报告\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/system/review\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}, {"id": 593, "parentId": 590, "path": "/system/review/addEdit", "redirect": null, "component": "hegui/one/system/systemAdditionAndReview/addEdit.vue", "name": "/system/review/addEdit", "realName": null, "isMenu": true, "sort": 3, "mark": 1, "meta": "{\"title\":\"新增审核记录\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/system/review\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}, {"id": 594, "parentId": 582, "path": "/database", "redirect": null, "component": "Layout", "name": "database", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"合规数据库\",\"icon\":\"i-heroicons-solid:menu-alt-3\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 595, "parentId": 594, "path": "/database/laws", "redirect": null, "component": "hegui/one/database/regulatoryDatabase.vue", "name": "database/laws", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"法规库\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 596, "parentId": 595, "path": "/database/laws/detail", "redirect": null, "component": "hegui/one/database/regulatoryDatabaseMode/detail.vue", "name": "/database/laws/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"法规详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/database/laws\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}, {"id": 597, "parentId": 595, "path": "/database/laws/addEdit", "redirect": null, "component": "hegui/one/database/regulatoryDatabaseMode/addEdit.vue", "name": "/database/laws/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"新增法规\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/database/laws\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}, {"id": 598, "parentId": 594, "path": "/database/cases", "redirect": null, "component": "hegui/one/database/complianceCaseLibrary.vue", "name": "database/cases", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"合规案例库\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 599, "parentId": 598, "path": "/database/cases/detail", "redirect": null, "component": "hegui/one/database/complianceCaseLibraryMode/detail.vue", "name": "/database/cases/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"案例详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/database/cases\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}, {"id": 600, "parentId": 598, "path": "/database/cases/addEdit", "redirect": null, "component": "hegui/one/database/complianceCaseLibraryMode/addEdit.vue", "name": "/database/cases/addEdit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"新增案例\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/database/cases\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}, {"id": 601, "parentId": 0, "path": "/", "redirect": null, "component": "", "name": "/two", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"预防之翼\",\"icon\":\"i-heroicons-solid:home\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 602, "parentId": 601, "path": "/threeListManagement", "redirect": null, "component": "Layout", "name": "/threeListManagement", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"三张清单管理\",\"icon\":\"i-heroicons-solid:menu-alt-3\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 603, "parentId": 602, "path": "/threeListManagement/complianceRisk", "redirect": null, "component": "hegui/prevention/threeListManagement/complianceRisk.vue", "name": "threeListManagement/complianceRisk", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"合规风险识别清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 604, "parentId": 603, "path": "/threeListManagement/complianceRisk/edit", "redirect": null, "component": "hegui/prevention/threeListManagement/complianceRisk_edit.vue", "name": "/threeListManagement/complianceRisk/edit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增合规清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/complianceRisk\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}, {"id": 605, "parentId": 603, "path": "/threeListManagement/complianceRisk/detail", "redirect": null, "component": "hegui/prevention/threeListManagement/complianceRisk_detail.vue", "name": "/threeListManagement/complianceRisk/detail", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"合规清单详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/complianceRisk\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}, {"id": 606, "parentId": 602, "path": "/threeListManagement/jobSpecifications", "redirect": null, "component": "hegui/prevention/threeListManagement/jobSpecifications.vue", "name": "threeListManagement/jobSpecifications", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"重点岗位合规职责清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 607, "parentId": 606, "path": "/threeListManagement/jobSpecifications/edit", "redirect": null, "component": "hegui/prevention/threeListManagement/jobSpecifications_edit.vue", "name": "/threeListManagement/jobSpecifications/edit", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"新增重点岗位合规职责清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/jobSpecifications\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}, {"id": 608, "parentId": 606, "path": "/threeListManagement/jobSpecifications/detail", "redirect": null, "component": "hegui/prevention/threeListManagement/jobSpecifications_detail.vue", "name": "/threeListManagement/jobSpecifications/detail", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"重点岗位合规职责清单详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/jobSpecifications\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}, {"id": 609, "parentId": 602, "path": "/threeListManagement/operationFlow", "redirect": null, "component": "hegui/prevention/threeListManagement/operationFlow.vue", "name": "threeListManagement/operationFlow", "realName": null, "isMenu": true, "sort": 3, "mark": 1, "meta": "{\"title\":\"关键业务流程管控清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":true,\"breadcrumb\":true,\"activeMenu\":\"\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":true}", "children": [{"id": 610, "parentId": 609, "path": "/threeListManagement/operationFlow/detail", "redirect": null, "component": "hegui/prevention/threeListManagement/operationFlow_detail.vue", "name": "/threeListManagement/operationFlow/detail", "realName": null, "isMenu": true, "sort": 1, "mark": 1, "meta": "{\"title\":\"关键业务流程管控清单详情\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/operationFlow\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}, {"id": 611, "parentId": 609, "path": "/threeListManagement/operationFlow/edit", "redirect": null, "component": "hegui/prevention/threeListManagement/operationFlow_edit.vue", "name": "/threeListManagement/operationFlow/edit", "realName": null, "isMenu": true, "sort": 2, "mark": 1, "meta": "{\"title\":\"新增关键业务流程管控清单\",\"icon\":\"\",\"activeIcon\":\"\",\"defaultOpened\":false,\"permanent\":false,\"auth\":[],\"menu\":false,\"breadcrumb\":false,\"activeMenu\":\"/threeListManagement/operationFlow\",\"cache\":[],\"noCache\":[],\"badge\":\"\",\"link\":\"\",\"iframe\":\"\",\"copyright\":false,\"paddingBottom\":\"0px\",\"sidebar\":false}", "children": [], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}], "auths": [], "isAvailable": null}]