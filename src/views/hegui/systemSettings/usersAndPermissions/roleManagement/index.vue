<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import {
  Check, Close, Delete, Document,
  Download, Edit, Key, Menu, Plus, Refresh, Search, User, View,
} from '@element-plus/icons-vue'
import organizationalApi from '@/api/organizational/index'
import roleTree from '@/api/permissions/role'

// 角色数据
const roleData = ref([])
const loading = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 批量选择
const multipleSelection = ref([])
const selectedCount = ref(0)

// 弹窗相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formRef = ref()

// 权限分配弹窗相关
const permissionDialogVisible = ref(false)
const permissionLoading = ref(false)
const permissionTreeData = ref([])
const checkedPermissions = ref([])
const currentRoleForPermission = ref(null)
const permissionTreeRef = ref()

// 表单数据
const formData = ref({
  id: null,
  tenantId: 1,
  name: '',
  code: '',
  description: '',
  status: 1,
  metadata: '',
  version: 0,
  parent: null,
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
}

const chartRef = ref()
const roleUsage = ref([
  { name: '系统管理员', count: 3, color: 'bg-blue-500' },
  { name: '合规管理员', count: 8, color: 'bg-green-500' },
  { name: '部门管理员', count: 15, color: 'bg-purple-500' },
  { name: '审计员', count: 5, color: 'bg-yellow-500' },
  { name: '报表查看员', count: 12, color: 'bg-red-500' },
])

// 获取角色列表
async function fetchRoleData() {
  try {
    loading.value = true
    const arr = await organizationalApi.roleApi(
      { page: currentPage.value - 1, size: pageSize.value },
      { keyword: searchQuery.value },
      null,
    )
    const response = arr.content
    if (response && Array.isArray(response)) {
      roleData.value = response.map(item => ({
        ...item,
        statusText: item.status === 1 ? '启用' : '禁用',
        createTime: formatTime(item.createdAt),
      }))
      total.value = arr.totalElements
    }
    else {
      roleData.value = []
      total.value = 0
    }
  }
  catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
    roleData.value = []
    total.value = 0
  }
  finally {
    loading.value = false
  }
}

// 搜索角色
function handleSearch() {
  currentPage.value = 1
  fetchRoleData()
}

// 重置搜索
function handleReset() {
  searchQuery.value = ''
  currentPage.value = 1
  fetchRoleData()
}

// 分页处理
function handleSizeChange(val) {
  pageSize.value = val
  fetchRoleData()
}

function handleCurrentChange(val) {
  currentPage.value = val
  fetchRoleData()
}

// 选择处理
function handleSelectionChange(val) {
  multipleSelection.value = val
  selectedCount.value = val.length
}

function cancelSelection() {
  multipleSelection.value = []
  selectedCount.value = 0
}

// 重置表单
function resetForm() {
  formData.value = {
    id: null,
    tenantId: 1,
    name: '',
    code: '',
    description: '',
    status: 1,
    metadata: '',
    version: 0,
    parent: null,
  }
  formRef.value?.clearValidate()
}

// 新增角色
function handleAdd() {
  resetForm()
  dialogTitle.value = '新增角色'
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑角色
function handleEdit(row) {
  resetForm()
  formData.value = {
    id: row.id,
    tenantId: row.tenantId || 1,
    name: row.name,
    code: row.code,
    description: row.description || '',
    status: row.status,
    metadata: row.metadata || '',
    version: row.version,
    parent: row.parent,
  }
  dialogTitle.value = '编辑角色'
  isEdit.value = true
  dialogVisible.value = true
}

// 保存角色
async function saveRole() {
  if (!formRef.value) { return }

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      // 更新角色
      await organizationalApi.roleApi(
        { id: formData.value.id },
        formData.value,
        'update',
      )
      ElMessage.success('更新角色成功')
    }
    else {
      // 创建角色
      await organizationalApi.roleApi(
        {},
        formData.value,
        'create',
      )
      ElMessage.success('创建角色成功')
    }

    dialogVisible.value = false
    await fetchRoleData()
  }
  catch (error) {
    console.error('保存角色失败:', error)
    ElMessage.error('保存角色失败')
  }
  finally {
    loading.value = false
  }
}

// 删除角色
async function handleDelete(row) {
  try {
    await ElMessageBox.confirm('确定要删除这个角色吗？删除后不可恢复！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await organizationalApi.roleApi(
      { },
      { id: row.id },
      'delete',
    )
    ElMessage.success('删除成功')
    await fetchRoleData()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 切换状态
async function handleToggleStatus(row) {
  try {
    const newStatus = row.status === 1 ? 0 : 1
    await organizationalApi.roleApi(
      { id: row.id },
      { ...row, status: newStatus },
      'update',
    )
    row.status = newStatus
    row.statusText = newStatus === 1 ? '启用' : '禁用'
    ElMessage.success(`${newStatus === 1 ? '启用' : '禁用'}成功`)
  }
  catch (error) {
    console.error('切换状态失败:', error)
    ElMessage.error('操作失败')
  }
}

// 转换权限数据为树形结构
function transformPermissionData(data) {
  if (!Array.isArray(data)) { return [] }

  return data.map((item) => {
    const node = {
      id: `menu_${item.id}`,
      label: item.name,
      type: 'menu',
      originalId: item.id,
      isMenu: item.isMenu,
      children: [],
    }

    // 添加按钮权限作为子节点
    if (item.auths && Array.isArray(item.auths)) {
      item.auths.forEach((auth) => {
        node.children.push({
          id: `auth_${auth.id}`,
          label: auth.name,
          type: 'auth',
          originalId: auth.id,
          value: auth.value,
          parentMenuId: item.id,
        })
      })
    }

    // 递归处理子菜单
    if (item.children && Array.isArray(item.children)) {
      const childMenus = transformPermissionData(item.children)
      node.children.push(...childMenus)
    }

    return node
  })
}

// 分配权限
async function handleAssignPermissions(row) {
  try {
    currentRoleForPermission.value = row
    permissionLoading.value = true
    permissionDialogVisible.value = true

    // 先获取所有权限树数据
    const allPermissions = await roleTree.rolePermissionsTree()
    if (allPermissions && Array.isArray(allPermissions)) {
      // 转换权限数据为树形结构
      permissionTreeData.value = transformPermissionData(allPermissions)
      console.log('所有权限树数据:', permissionTreeData.value)
    }

    // 获取角色已有权限并设置选中状态
    const rolePermissions = await roleTree.rolePermissionsApi(row.id)
    if (rolePermissions && Array.isArray(rolePermissions)) {
      console.log('角色已有权限:', rolePermissions)
      
      // 创建角色权限ID集合，用于快速查找
      const rolePermissionIds = new Set()
      rolePermissions.forEach((permission) => {
        rolePermissionIds.add(permission.id)
      })
      
      // 递归遍历权限树，对比ID并设置选中状态
      const checkedKeys = []
      
      function checkPermissionInTree(treeData) {
        treeData.forEach((node) => {
          // 检查当前节点是否在角色权限中
          if (rolePermissionIds.has(node.originalId)) {
            checkedKeys.push(node.id)
          }
          
          // 递归检查子节点
          if (node.children && node.children.length > 0) {
            checkPermissionInTree(node.children)
          }
        })
      }
      
      // 开始检查权限树
      checkPermissionInTree(permissionTreeData.value)
      
      console.log('匹配到的权限节点:', checkedKeys)

      // 等待下一个tick确保树组件已渲染
      await nextTick()
      if (permissionTreeRef.value) {
        permissionTreeRef.value.setCheckedKeys(checkedKeys)
      }
    }
  }
  catch (error) {
    console.error('获取权限数据失败:', error)
    ElMessage.error('获取权限数据失败')
    permissionDialogVisible.value = false
  }
  finally {
    permissionLoading.value = false
  }
}

// 保存权限分配
async function savePermissions() {
  try {
    if (!currentRoleForPermission.value) {
      ElMessage.error('未选择角色')
      return
    }

    permissionLoading.value = true

    // 获取选中的权限节点（包括完全选中和半选中的节点）
    const checkedKeys = permissionTreeRef.value?.getCheckedKeys() || []
    const halfCheckedKeys = permissionTreeRef.value?.getHalfCheckedKeys() || []
    const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]

    // 构造权限数据，按照要求的格式
    const permissionData = []

    allCheckedKeys.forEach((key) => {
      // 提取原始ID
      let originalId
      if (key.startsWith('menu_')) {
        originalId = key.replace('menu_', '')
      } else if (key.startsWith('auth_')) {
        originalId = key.replace('auth_', '')
      }

      if (originalId) {
        permissionData.push({
          role: {
            id: currentRoleForPermission.value.id,
          },
          permission: {
            id: parseInt(originalId),
          },
        })
      }
    })

    console.log('保存权限数据:', permissionData)

    // 调用保存接口
    await roleTree.roleAndPermissions(permissionData)

    ElMessage.success(`权限分配成功！共分配 ${permissionData.length} 个权限`)
    permissionDialogVisible.value = false
  }
  catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('保存权限失败')
  }
  finally {
    permissionLoading.value = false
  }
}

// 格式化时间
function formatTime(timeObj) {
  if (!timeObj || !timeObj.seconds) { return '-' }
  const date = new Date(timeObj.seconds * 1000)
  return date.toLocaleDateString('zh-CN')
}

// 批量操作
function batchEnable() {
  console.log('批量启用:', multipleSelection.value)
}

function batchDisable() {
  console.log('批量禁用:', multipleSelection.value)
}

function batchDelete() {
  console.log('批量删除:', multipleSelection.value)
}

onMounted(() => {
  fetchRoleData()
  nextTick(() => {
    if (chartRef.value) {
      const chart = echarts.init(chartRef.value)
      const option = {
        animation: false,
        tooltip: {
          trigger: 'item',
        },
        legend: {
          top: '5%',
          left: 'center',
        },
        series: [
          {
            name: '角色分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 3, name: '系统角色' },
              { value: 12, name: '自定义角色' },
            ],
            color: ['#1E88E5', '#7460EE'],
          },
        ],
      }
      chart.setOption(option)
      window.addEventListener('resize', () => {
        chart.resize()
      })
    }
  })
})
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              角色管理
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" @click="handleAdd">
              <el-icon>
                <Plus />
              </el-icon>
              <span>新增角色</span>
            </el-button>
            <el-button>
              <el-icon>
                <Download />
              </el-icon>
              <span>导出</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="24">
            <el-card shadow="hover" class="">
              <!--              <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template> -->
              <div class="flex items-center justify-between border-b p-4">
                <div class="flex items-center gap-2">
                  <el-input
                    v-model="searchQuery"
                    placeholder="搜索角色名称或编码"
                    class="w-64"
                    clearable
                    @keyup.enter="handleSearch"
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <el-button type="primary" @click="handleSearch">
                    查询
                  </el-button>
                  <el-button @click="handleReset">
                    重置
                  </el-button>
                </div>
              </div>
              <el-table
                v-loading="loading" :data="roleData"
                style="width: 100%;"
                :header-cell-style="{ background: '#F5F7FA', color: '#606266' }"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="50" />
                <el-table-column prop="code" label="角色编码" width="120" />
                <el-table-column prop="name" label="角色名称" width="150" />
                <el-table-column prop="description" label="角色描述" show-overflow-tooltip />
                <el-table-column prop="createdAt" label="创建时间" width="150" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                      {{ row.statusText }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="280">
                  <template #default="{ row }">
                    <div class="flex space-x-1">
                      <el-button size="small" type="primary" plain @click="handleEdit(row)">
                        编辑
                      </el-button>
                      <el-button size="small" :type="row.status === 1 ? 'warning' : 'success'" plain @click="handleToggleStatus(row)">
                        {{ row.status === 1 ? '禁用' : '启用' }}
                      </el-button>
                      <el-button size="small" type="info" plain @click="handleAssignPermissions(row)">
                        分配权限
                      </el-button>
                      <el-button size="small" type="danger" plain @click="handleDelete(row)">
                        删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="flex items-center justify-between border-t p-4">
                <div class="flex items-center space-x-3">
                  <div class="text-sm text-gray-500">
                    <span>已选择 {{ selectedCount }} 项</span>
                  </div>
                  <el-button size="small" class="!rounded-button whitespace-nowrap" type="success" plain @click="batchEnable">
                    <el-icon>
                      <Check />
                    </el-icon>
                    <span>批量启用</span>
                  </el-button>
                  <el-button size="small" class="!rounded-button whitespace-nowrap" type="warning" plain @click="batchDisable">
                    <el-icon>
                      <Close />
                    </el-icon>
                    <span>批量禁用</span>
                  </el-button>
                  <el-button size="small" class="!rounded-button whitespace-nowrap" type="danger" plain @click="batchDelete">
                    <el-icon>
                      <Delete />
                    </el-icon>
                    <span>批量删除</span>
                  </el-button>
                  <el-button size="small" class="!rounded-button whitespace-nowrap" text @click="cancelSelection">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    <span>取消选择</span>
                  </el-button>
                </div>
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="total"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </el-col>
          <el-col v-if="false" :span="0">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  角色概览
                </div>
              </template>
              <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                  <div class="rounded bg-blue-50 p-3">
                    <div class="text-sm text-gray-500">
                      总角色数
                    </div>
                    <div class="mt-1 text-xl font-bold">
                      15
                    </div>
                  </div>
                  <div class="rounded bg-green-50 p-3">
                    <div class="text-sm text-gray-500">
                      系统角色
                    </div>
                    <div class="mt-1 text-xl font-bold">
                      3
                    </div>
                  </div>
                  <div class="rounded bg-purple-50 p-3">
                    <div class="text-sm text-gray-500">
                      自定义角色
                    </div>
                    <div class="mt-1 text-xl font-bold">
                      12
                    </div>
                  </div>
                  <div class="rounded bg-red-50 p-3">
                    <div class="text-sm text-gray-500">
                      禁用角色
                    </div>
                    <div class="mt-1 text-xl font-bold">
                      2
                    </div>
                  </div>
                </div>
                <div ref="chartRef" class="h-60" />
                <div>
                  <h3 class="mb-2 text-sm font-bold">
                    角色使用情况
                  </h3>
                  <div class="space-y-2">
                    <div v-for="item in roleUsage" :key="item.name" class="flex items-center justify-between text-sm">
                      <div class="flex items-center">
                        <div class="mr-2 h-2 w-2 rounded-full" :class="item.color" />
                        <span>{{ item.name }}</span>
                      </div>
                      <span>{{ item.count }}人</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <!-- <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">角色使用情况</div>
              </template>
            </el-card>
          </el-col> -->
          </el-col>
        </el-row>
      </div>
    </PageMain>

    <!-- 新增/编辑角色弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入角色名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="角色编码" prop="code">
          <el-input
            v-model="formData.code"
            placeholder="请输入角色编码"
            maxlength="20"
            show-word-limit
            :disabled="isEdit"
          />
        </el-form-item>

        <el-form-item label="角色描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入角色描述"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">
              启用
            </el-radio>
            <el-radio :label="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" :loading="loading" @click="saveRole">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限分配弹窗 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="分配权限"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentRoleForPermission" class="mb-4">
        <el-alert
          :title="`正在为角色${currentRoleForPermission.name}分配权限`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <div v-loading="permissionLoading" class="permission-tree-container">
        <div class="mb-4">
          <el-alert
            title="权限说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="text-sm">
                <div class="mb-1 flex items-center">
                  <el-icon class="mr-1 text-blue-500">
                    <Menu />
                  </el-icon>
                  <span>菜单权限：控制页面访问权限</span>
                </div>
                <div class="flex items-center">
                  <el-icon class="mr-1 text-green-500">
                    <Key />
                  </el-icon>
                  <span>按钮权限：控制页面内操作按钮权限</span>
                </div>
              </div>
            </template>
          </el-alert>
        </div>

        <el-tree
          ref="permissionTreeRef"
          :data="permissionTreeData"
          show-checkbox
          node-key="id"
          :default-expand-all="true"
          :expand-on-click-node="false"
          :check-strictly="false"
          :props="{
            children: 'children',
            label: 'label',
          }"
          class="permission-tree"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <el-icon
                v-if="data.type === 'menu'"
                class="mr-2 text-blue-500"
                :size="16"
              >
                <Menu />
              </el-icon>
              <el-icon
                v-else-if="data.type === 'auth'"
                class="mr-2 text-green-500"
                :size="14"
              >
                <Key />
              </el-icon>
              <span
                :class="{
                  'font-medium text-gray-800': data.type === 'menu',
                  'text-gray-600 text-sm': data.type === 'auth',
                }"
              >
                {{ node.label }}
              </span>
              <el-tag
                v-if="data.type === 'auth' && data.value"
                size="small"
                type="info"
                class="ml-2"
              >
                {{ data.value }}
              </el-tag>
            </span>
          </template>
        </el-tree>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" :loading="permissionLoading" @click="savePermissions">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  :deep(.el-table .cell) {
    padding-right: 10px;
    padding-left: 10px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f5f7fa;
  }

  :deep(.el-table--border .el-table__cell) {
    border-right: none;
  }

  :deep(.el-table__inner-wrapper::before) {
    display: none;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
    background-color: #1e88e5;
  }

  .permission-tree-container {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;
  }

  .permission-tree {
    background-color: transparent;

    :deep(.el-tree-node) {
      margin-bottom: 2px;
    }

    :deep(.el-tree-node__content) {
      height: auto;
      line-height: 1.5;
      padding: 10px 8px;
      border-radius: 6px;
      transition: all 0.2s ease;
      border: 1px solid transparent;
    }

    :deep(.el-tree-node__content:hover) {
      background-color: #f0f9ff;
      border-color: #e0f2fe;
    }

    :deep(.el-tree-node__expand-icon) {
      color: #409eff;
    }

    :deep(.el-checkbox) {
      margin-right: 8px;
    }

    :deep(.el-tree-node__label) {
      font-size: 14px;
    }
  }

  .custom-tree-node {
    display: flex;
    align-items: center;
    width: 100%;
    font-size: 14px;
  }
</style>
