<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import {
  Menu as ElIconMenu,
  User as ElIconUser,
  Search as ElIconSearch,
  Bell as ElIconBell,
  ArrowDown as ElIconArrowDown,
  Right as <PERSON><PERSON>conR<PERSON>,
  FolderOpened as ElIconFolderOpened,
  // Date as ElIconDate,
  Check as ElIconChe<PERSON>,
  Close as ElIconClose,
  Minus as ElIconMinus,
} from '@element-plus/icons-vue'
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              用户管理
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <plus />
              </el-icon>
              新增用户
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <upload />
              </el-icon>
              导入用户
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <download />
              </el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div class="min-h-screen flex bg-gray-50">
          <!-- 左侧导航菜单 -->
          <div class="w-64 bg-[#1A1F37] text-white">
            <div class="border-b border-gray-700 p-4">
              <div class="flex items-center space-x-2">
                <el-icon class="text-xl">
                  <ElIconMenu />
                </el-icon>
                <span>系统导航</span>
              </div>
            </div>
            <div class="p-4 space-y-2">
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-700">
                仪表盘
              </div>
              <div class="cursor-pointer rounded bg-gray-700 px-3 py-2">
                系统管理
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-700">
                用户管理
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-700">
                角色管理
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-700">
                权限管理
              </div>
            </div>
          </div>

          <!-- 主内容区 -->
          <div class="flex flex-1 flex-col">
            <!-- 顶部标题栏 -->
            <div class="h-16 flex items-center justify-between bg-white px-6 shadow-sm">
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                  <el-icon class="text-2xl text-blue-500">
                    <ElIconUser />
                  </el-icon>
                  <span class="text-lg font-medium">猫伯伯合规管家</span>
                </div>
                <div class="relative w-64">
                  <input
                    type="text" placeholder="搜索..."
                    class="w-full rounded-full border-none bg-gray-100 py-2 pl-8 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-200"
                  >
                  <el-icon class="absolute left-3 top-2.5 text-gray-400">
                    <ElIconSearch />
                  </el-icon>
                </div>
              </div>
              <div class="flex items-center space-x-4">
                <el-icon class="cursor-pointer text-xl">
                  <ElIconBell />
                </el-icon>
                <div class="flex cursor-pointer items-center space-x-2">
                  <div class="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500 text-white">
                    管
                  </div>
                  <span>管理员</span>
                  <el-icon><ElIconArrowDown /></el-icon>
                </div>
              </div>
            </div>

            <!-- 页面内容 -->
            <div class="flex-1 overflow-auto p-6">
              <!-- 面包屑和操作按钮 -->
              <div class="mb-6 flex items-center justify-between">
                <div class="text-sm text-gray-500">
                  <span>系统管理</span>
                  <el-icon class="mx-1">
                    <ElIconRight />
                  </el-icon>
                  <span>用户管理</span>
                  <el-icon class="mx-1">
                    <ElIconRight />
                  </el-icon>
                  <span>用户列表</span>
                  <el-icon class="mx-1">
                    <ElIconRight />
                  </el-icon>
                  <span class="text-gray-800">新增用户</span>
                </div>
                <div class="flex space-x-3">
                  <button class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">
                    保存
                  </button>
                  <button
                    class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50"
                  >
                    保存并新增
                  </button>
                  <button
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50"
                  >
                    取消
                  </button>
                </div>
              </div>

              <div class="flex space-x-6">
                <!-- 左侧表单区 -->
                <div class="flex-1 space-y-6">
                  <!-- 基本信息 -->
                  <div class="rounded-lg bg-white p-6 shadow-sm">
                    <h3 class="mb-6 text-lg font-bold">
                      基本信息
                    </h3>
                    <div class="grid grid-cols-2 gap-6">
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          <span class="mr-1 text-red-500">*</span>
                          用户名
                        </label>
                        <input
                          type="text"
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入用户名"
                        >
                        <p class="mt-1 text-xs text-gray-400">
                          用于登录的账号，创建后不可修改
                        </p>
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          <span class="mr-1 text-red-500">*</span>
                          姓名
                        </label>
                        <input
                          type="text"
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入姓名"
                        >
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          <span class="mr-1 text-red-500">*</span>
                          手机号码
                        </label>
                        <input
                          type="tel"
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入手机号码"
                        >
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          邮箱地址
                        </label>
                        <input
                          type="email"
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入邮箱地址"
                        >
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          性别
                        </label>
                        <div class="flex space-x-4">
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="gender" checked>
                            <span>男</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="gender">
                            <span>女</span>
                          </label>
                        </div>
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          状态
                        </label>
                        <div class="flex space-x-4">
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="status" checked>
                            <span>启用</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="status">
                            <span>禁用</span>
                          </label>
                        </div>
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          员工编号
                        </label>
                        <input
                          type="text"
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入员工编号"
                        >
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          排序号
                        </label>
                        <input
                          type="number"
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入排序号"
                        >
                        <p class="mt-1 text-xs text-gray-400">
                          用于显示顺序，数字越小越靠前
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- 组织与岗位 -->
                  <div class="rounded-lg bg-white p-6 shadow-sm">
                    <h3 class="mb-6 text-lg font-bold">
                      组织与岗位
                    </h3>
                    <div class="grid grid-cols-2 gap-6">
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          <span class="mr-1 text-red-500">*</span>
                          所属部门
                        </label>
                        <div class="flex">
                          <input
                            type="text"
                            class="h-8 flex-1 border border-gray-200 rounded-l px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                            placeholder="请选择部门" readonly
                          >
                          <button
                            class="h-8 border border-l-0 border-gray-200 rounded-r bg-gray-50 px-3 hover:bg-gray-100"
                          >
                            <el-icon><ElIconFolderOpened /></el-icon>
                          </button>
                        </div>
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          <span class="mr-1 text-red-500">*</span>
                          岗位
                        </label>
                        <select
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="">
                            请选择岗位
                          </option>
                          <option value="1">
                            部门经理
                          </option>
                          <option value="2">
                            项目经理
                          </option>
                          <option value="3">
                            普通员工
                          </option>
                        </select>
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          直属上级
                        </label>
                        <div class="flex">
                          <input
                            type="text"
                            class="h-8 flex-1 border border-gray-200 rounded-l px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                            placeholder="请选择直属上级" readonly
                          >
                          <button
                            class="h-8 border border-l-0 border-gray-200 rounded-r bg-gray-50 px-3 hover:bg-gray-100"
                          >
                            <el-icon><ElIconUser /></el-icon>
                          </button>
                        </div>
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          入职日期
                        </label>
                        <div class="flex">
                          <input
                            type="text"
                            class="h-8 flex-1 border border-gray-200 rounded-l px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                            placeholder="请选择日期" readonly
                          >
                          <button
                            class="h-8 border border-l-0 border-gray-200 rounded-r bg-gray-50 px-3 hover:bg-gray-100"
                          >
                            <el-icon><el-icon-date /></el-icon>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 角色与权限 -->
                  <div class="rounded-lg bg-white p-6 shadow-sm">
                    <h3 class="mb-6 text-lg font-bold">
                      角色与权限
                    </h3>
                    <div class="grid grid-cols-2 gap-6">
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          用户类型
                        </label>
                        <div class="flex space-x-4">
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="userType" checked>
                            <span>普通用户</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="userType">
                            <span>管理员</span>
                          </label>
                        </div>
                      </div>
                      <div class="col-span-2 space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          角色分配
                        </label>
                        <div class="border border-gray-200 rounded p-3">
                          <div class="space-y-3">
                            <label class="flex items-start space-x-2">
                              <input type="checkbox" class="mt-1">
                              <div>
                                <div class="font-medium">系统管理员</div>
                                <div class="text-xs text-gray-500">拥有系统所有权限</div>
                              </div>
                            </label>
                            <label class="flex items-start space-x-2">
                              <input type="checkbox" class="mt-1">
                              <div>
                                <div class="font-medium">部门管理员</div>
                                <div class="text-xs text-gray-500">拥有部门管理权限</div>
                              </div>
                            </label>
                            <label class="flex items-start space-x-2">
                              <input type="checkbox" class="mt-1" checked>
                              <div>
                                <div class="font-medium">普通用户</div>
                                <div class="text-xs text-gray-500">基础权限</div>
                              </div>
                            </label>
                          </div>
                        </div>
                      </div>
                      <div class="col-span-2 space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          数据权限
                        </label>
                        <div class="flex space-x-4">
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="dataPermission" checked>
                            <span>全部数据</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="dataPermission">
                            <span>本部门数据</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="dataPermission">
                            <span>本部门及下级部门数据</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="dataPermission">
                            <span>仅本人数据</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="dataPermission">
                            <span>自定义</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 账号安全 -->
                  <div class="rounded-lg bg-white p-6 shadow-sm">
                    <h3 class="mb-6 text-lg font-bold">
                      账号安全
                    </h3>
                    <div class="grid grid-cols-2 gap-6">
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          设置初始密码
                        </label>
                        <div class="flex space-x-4">
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="passwordType" checked>
                            <span>系统随机生成</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="passwordType">
                            <span>手动设置</span>
                          </label>
                        </div>
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          密码过期时间
                        </label>
                        <div class="flex space-x-4">
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="passwordExpire" checked>
                            <span>永不过期</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="passwordExpire">
                            <span>30天</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="passwordExpire">
                            <span>60天</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="radio" name="passwordExpire">
                            <span>90天</span>
                          </label>
                        </div>
                      </div>
                      <div class="col-span-2 space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          登录限制
                        </label>
                        <div class="flex space-x-6">
                          <label class="flex items-center space-x-1">
                            <input type="checkbox">
                            <span>IP限制</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="checkbox">
                            <span>时间限制</span>
                          </label>
                          <label class="flex items-center space-x-1">
                            <input type="checkbox">
                            <span>设备限制</span>
                          </label>
                        </div>
                      </div>
                      <div class="col-span-2 space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          IP白名单
                        </label>
                        <div class="space-y-2">
                          <div class="flex space-x-2">
                            <input
                              type="text"
                              class="h-8 flex-1 border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                              placeholder="请输入IP地址"
                            >
                            <button
                              class="!rounded-button h-8 whitespace-nowrap bg-blue-500 px-3 text-white hover:bg-blue-600"
                            >
                              添加
                            </button>
                          </div>
                          <div class="text-xs text-gray-500">
                            多个IP用逗号分隔
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 联系信息 -->
                  <div class="rounded-lg bg-white p-6 shadow-sm">
                    <h3 class="mb-6 text-lg font-bold">
                      联系信息
                    </h3>
                    <div class="grid grid-cols-2 gap-6">
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          办公电话
                        </label>
                        <input
                          type="tel"
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入办公电话"
                        >
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          即时通讯
                        </label>
                        <input
                          type="text"
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入即时通讯账号"
                        >
                      </div>
                      <div class="space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          办公地点
                        </label>
                        <input
                          type="text"
                          class="h-8 w-full border border-gray-200 rounded px-3 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入办公地点"
                        >
                      </div>
                      <div class="col-span-2 space-y-1">
                        <label class="flex items-center justify-end text-sm text-gray-500">
                          备注信息
                        </label>
                        <textarea
                          class="h-24 w-full border border-gray-200 rounded px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="请输入备注信息"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 右侧辅助区 -->
                <div class="w-80 space-y-6">
                  <!-- 创建向导 -->
                  <div class="rounded-lg bg-white p-6 shadow-sm">
                    <h3 class="mb-4 text-lg font-bold">
                      创建向导
                    </h3>
                    <div class="space-y-4">
                      <div class="flex items-start space-x-3">
                        <div class="h-6 w-6 flex items-center justify-center rounded-full bg-blue-500 text-white">
                          1
                        </div>
                        <div>
                          <div class="text-blue-500 font-medium">
                            基本信息
                          </div>
                          <div class="text-xs text-gray-500">
                            填写用户基本信息
                          </div>
                        </div>
                      </div>
                      <div class="flex items-start space-x-3">
                        <div class="h-6 w-6 flex items-center justify-center rounded-full bg-blue-500 text-white">
                          2
                        </div>
                        <div>
                          <div class="text-blue-500 font-medium">
                            组织与岗位
                          </div>
                          <div class="text-xs text-gray-500">
                            设置用户所属组织和岗位
                          </div>
                        </div>
                      </div>
                      <div class="flex items-start space-x-3">
                        <div class="h-6 w-6 flex items-center justify-center rounded-full bg-gray-200 text-gray-500">
                          3
                        </div>
                        <div>
                          <div class="font-medium">
                            角色与权限
                          </div>
                          <div class="text-xs text-gray-500">
                            分配用户角色和权限
                          </div>
                        </div>
                      </div>
                      <div class="flex items-start space-x-3">
                        <div class="h-6 w-6 flex items-center justify-center rounded-full bg-gray-200 text-gray-500">
                          4
                        </div>
                        <div>
                          <div class="font-medium">
                            账号安全
                          </div>
                          <div class="text-xs text-gray-500">
                            设置账号安全选项
                          </div>
                        </div>
                      </div>
                      <div class="flex items-start space-x-3">
                        <div class="h-6 w-6 flex items-center justify-center rounded-full bg-gray-200 text-gray-500">
                          5
                        </div>
                        <div>
                          <div class="font-medium">
                            联系信息
                          </div>
                          <div class="text-xs text-gray-500">
                            填写联系方式
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 模板应用 -->
                  <div class="rounded-lg bg-white p-6 shadow-sm">
                    <h3 class="mb-4 text-lg font-bold">
                      模板应用
                    </h3>
                    <button
                      class="!rounded-button mb-4 w-full whitespace-nowrap bg-blue-500 py-2 text-white hover:bg-blue-600"
                    >
                      应用模板
                    </button>
                    <div class="space-y-3">
                      <div class="cursor-pointer border border-gray-200 rounded p-3 hover:bg-blue-50">
                        <div class="font-medium">
                          新员工
                        </div>
                        <div class="text-xs text-gray-500">
                          基础权限，适用于新入职员工
                        </div>
                      </div>
                      <div class="cursor-pointer border border-gray-200 rounded p-3 hover:bg-blue-50">
                        <div class="font-medium">
                          管理员
                        </div>
                        <div class="text-xs text-gray-500">
                          系统管理权限
                        </div>
                      </div>
                      <div class="cursor-pointer border border-gray-200 rounded p-3 hover:bg-blue-50">
                        <div class="font-medium">
                          部门负责人
                        </div>
                        <div class="text-xs text-gray-500">
                          部门管理权限
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 表单验证 -->
                  <div class="rounded-lg bg-white p-6 shadow-sm">
                    <h3 class="mb-4 text-lg font-bold">
                      表单验证
                    </h3>
                    <div class="space-y-3">
                      <div class="flex items-center justify-between">
                        <span class="text-sm">基本信息</span>
                        <el-icon class="text-green-500">
                          <ElIconCheck />
                        </el-icon>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm">组织与岗位</span>
                        <el-icon class="text-red-500">
                          <ElIconClose />
                        </el-icon>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm">角色与权限</span>
                        <el-icon class="text-gray-400">
                          <ElIconMinus />
                        </el-icon>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm">账号安全</span>
                        <el-icon class="text-gray-400">
                          <ElIconMinus />
                        </el-icon>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm">联系信息</span>
                        <el-icon class="text-gray-400">
                          <ElIconMinus />
                        </el-icon>
                      </div>
                    </div>
                    <div class="mt-4 rounded bg-red-50 p-3">
                      <div class="text-sm text-red-600 font-medium">
                        未通过验证的项目
                      </div>
                      <ul class="mt-1 text-xs text-red-500 space-y-1">
                        <li class="cursor-pointer hover:underline">
                          所属部门不能为空
                        </li>
                        <li class="cursor-pointer hover:underline">
                          岗位不能为空
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
</style>
