---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 03-企业组织架构服务/组织单元

## POST 创建新的组织单元

POST /whiskerguardorgservice/api/org/units

创建新的组织单元

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "type": "COMPANY",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentId": 0,
  "children": "new ArrayList<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[OrgUnitDTO](#schemaorgunitdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "code": "",
  "type": "",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parentId": 0,
  "children": [
    {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "code": "",
      "type": "",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentId": 0,
      "children": [
        {
          "id": 0,
          "tenantId": 0,
          "name": "",
          "code": "",
          "type": "",
          "level": 0,
          "status": 0,
          "sortOrder": 0,
          "description": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "parentId": 0,
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityOrgUnitDTO](#schemaresponseentityorgunitdto)|

## GET 获取所有组织单元

GET /whiskerguardorgservice/api/org/units

获取所有组织单元

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |none|
|size|query|integer| 否 |none|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "tenantId": 0,
    "name": "",
    "code": "",
    "type": "",
    "level": 0,
    "status": 0,
    "sortOrder": 0,
    "description": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parentId": 0,
    "children": [
      {
        "id": 0,
        "tenantId": 0,
        "name": "",
        "code": "",
        "type": "",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "parentId": 0,
        "children": [
          {
            "id": 0,
            "tenantId": 0,
            "name": "",
            "code": "",
            "type": "",
            "level": 0,
            "status": 0,
            "sortOrder": 0,
            "description": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "parentId": 0,
            "children": []
          }
        ]
      }
    ]
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*the [Extension of{@link HttpEntity} that adds an{@link HttpStatusCode} status code.
Used in{@code RestTemplate} as well as in{@code @Controller} methods.

<p>In{@code RestTemplate}, this class is returned by
{@link org.springframework.web.client.RestTemplate#getForEntity getForEntity()} and
{@link org.springframework.web.client.RestTemplate#exchange exchange()}:
<pre class="code">
ResponseEntity&lt;String&gt; entity = template.getForEntity("https://example.com", String.class);
String body = entity.getBody();
MediaType contentType = entity.getHeaders().getContentType();
HttpStatus statusCode = entity.getStatusCode();
</pre>

<p>This can also be used in Spring MVC as the return value from an
{@code @Controller} method:
<pre class="code">
&#64;RequestMapping("/handle")
public ResponseEntity&lt;String&gt; handle(){
  URI location = ...;
  HttpHeaders responseHeaders = new HttpHeaders();
  responseHeaders.setLocation(location);
  responseHeaders.set("MyResponseHeader", "MyValue");
  return new ResponseEntity&lt;String&gt;("Hello World", responseHeaders, HttpStatus.CREATED);
}
</pre>

Or, by using a builder accessible via static methods:
<pre class="code">
&#64;RequestMapping("/handle")
public ResponseEntity&lt;String&gt; handle(){
  URI location = ...;
  return ResponseEntity.created(location).header("MyResponseHeader", "MyValue").body("Hello World");
}
</pre>] with status {@code 200 (OK)} and the list of orgUnits in body.
         返回状态码为200的响应和组织单元列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListOrgUnitDTO](#schemalistorgunitdto)]|false|none||the [Extension of{@link HttpEntity} that adds an{@link HttpStatusCode} status code.<br />Used in{@code RestTemplate} as well as in{@code @Controller} methods.<br /><br /><p>In{@code RestTemplate}, this class is returned by<br />{@link org.springframework.web.client.RestTemplate#getForEntity getForEntity()} and<br />{@link org.springframework.web.client.RestTemplate#exchange exchange()}:<br /><pre class="code"><br />ResponseEntity&lt;String&gt; entity = template.getForEntity("https://example.com", String.class);<br />String body = entity.getBody();<br />MediaType contentType = entity.getHeaders().getContentType();<br />HttpStatus statusCode = entity.getStatusCode();<br /></pre><br /><br /><p>This can also be used in Spring MVC as the return value from an<br />{@code @Controller} method:<br /><pre class="code"><br />&#64;RequestMapping("/handle")<br />public ResponseEntity&lt;String&gt; handle(){<br />  URI location = ...;<br />  HttpHeaders responseHeaders = new HttpHeaders();<br />  responseHeaders.setLocation(location);<br />  responseHeaders.set("MyResponseHeader", "MyValue");<br />  return new ResponseEntity&lt;String&gt;("Hello World", responseHeaders, HttpStatus.CREATED);<br />}<br /></pre><br /><br />Or, by using a builder accessible via static methods:<br /><pre class="code"><br />&#64;RequestMapping("/handle")<br />public ResponseEntity&lt;String&gt; handle(){<br />  URI location = ...;<br />  return ResponseEntity.created(location).header("MyResponseHeader", "MyValue").body("Hello World");<br />}<br /></pre>] with status {@code 200 (OK)} and the list of orgUnits in body.<br />         返回状态码为200的响应和组织单元列表|
|» id|integer(int64)|false|none||主键 ID|
|» tenantId|integer(int64)|true|none||租户 ID，标识不同租户的数据隔离|
|» name|string|true|none||组织单位名称|
|» code|string|true|none||唯一编码，用于外部系统对接或导入映射|
|» type|string|true|none||组织单元类型|
|» level|integer|true|none||层级深度，根节点为 1|
|» status|integer|true|none||状态：1=启用，0=禁用|
|» sortOrder|integer|false|none||排序序号|
|» description|string|false|none||描述信息|
|» metadata|string|false|none||扩展元数据（JSON）|
|» version|integer|true|none||乐观锁版本|
|» createdBy|string|false|none||创建者|
|» createdAt|object|true|none||创建时间|
|» updatedBy|string|false|none||更新者|
|» updatedAt|object|true|none||创建时间|
|» isDeleted|boolean|true|none||软删除标志|
|» parentId|integer(int64)|false|none||父节点|
|» children|[object]|false|none||子节点|

#### 枚举值

|属性|值|
|---|---|
|type|COMPANY|
|type|SUBSIDIARY|
|type|BUSINESS_GROUP|
|type|DEPARTMENT|
|type|TEAM|

## PATCH 部分更新现有组织单元

PATCH /whiskerguardorgservice/api/org/units/{id}

部分更新现有组织单元的指定字段，如果字段为null则忽略

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "type": "COMPANY",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentId": 0,
  "children": "new ArrayList<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |the id of the orgUnitDTO to save. 要保存的组织单元DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[OrgUnitDTO](#schemaorgunitdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "code": "",
  "type": "",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parentId": 0,
  "children": [
    {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "code": "",
      "type": "",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentId": 0,
      "children": [
        {
          "id": 0,
          "tenantId": 0,
          "name": "",
          "code": "",
          "type": "",
          "level": 0,
          "status": 0,
          "sortOrder": 0,
          "description": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "parentId": 0,
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityOrgUnitDTO](#schemaresponseentityorgunitdto)|

## GET 获取指定ID的组织单元

GET /whiskerguardorgservice/api/org/units/{id}

获取指定ID的组织单元

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |the id of the orgUnitDTO to retrieve. 要检索的组织单元DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "code": "",
  "type": "",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parentId": 0,
  "children": [
    {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "code": "",
      "type": "",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentId": 0,
      "children": [
        {
          "id": 0,
          "tenantId": 0,
          "name": "",
          "code": "",
          "type": "",
          "level": 0,
          "status": 0,
          "sortOrder": 0,
          "description": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "parentId": 0,
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityOrgUnitDTO](#schemaresponseentityorgunitdto)|

## DELETE 删除指定ID的组织单元

DELETE /whiskerguardorgservice/api/org/units/{id}

删除指定ID的组织单元

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |the id of the orgUnitDTO to delete. 要删除的组织单元DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获取组织单元树形结构

GET /whiskerguardorgservice/api/org/units/tree

获取组织单元树形结构

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenantId|query|integer| 是 |the tenant id. 租户ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "tenantId": 0,
    "name": "",
    "code": "",
    "type": "",
    "level": 0,
    "status": 0,
    "sortOrder": 0,
    "description": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parentId": 0,
    "children": [
      {
        "id": 0,
        "tenantId": 0,
        "name": "",
        "code": "",
        "type": "",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "parentId": 0,
        "children": [
          {
            "id": 0,
            "tenantId": 0,
            "name": "",
            "code": "",
            "type": "",
            "level": 0,
            "status": 0,
            "sortOrder": 0,
            "description": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "parentId": 0,
            "children": []
          }
        ]
      }
    ]
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*the [Extension of{@link HttpEntity} that adds an{@link HttpStatusCode} status code.
Used in{@code RestTemplate} as well as in{@code @Controller} methods.

<p>In{@code RestTemplate}, this class is returned by
{@link org.springframework.web.client.RestTemplate#getForEntity getForEntity()} and
{@link org.springframework.web.client.RestTemplate#exchange exchange()}:
<pre class="code">
ResponseEntity&lt;String&gt; entity = template.getForEntity("https://example.com", String.class);
String body = entity.getBody();
MediaType contentType = entity.getHeaders().getContentType();
HttpStatus statusCode = entity.getStatusCode();
</pre>

<p>This can also be used in Spring MVC as the return value from an
{@code @Controller} method:
<pre class="code">
&#64;RequestMapping("/handle")
public ResponseEntity&lt;String&gt; handle(){
  URI location = ...;
  HttpHeaders responseHeaders = new HttpHeaders();
  responseHeaders.setLocation(location);
  responseHeaders.set("MyResponseHeader", "MyValue");
  return new ResponseEntity&lt;String&gt;("Hello World", responseHeaders, HttpStatus.CREATED);
}
</pre>

Or, by using a builder accessible via static methods:
<pre class="code">
&#64;RequestMapping("/handle")
public ResponseEntity&lt;String&gt; handle(){
  URI location = ...;
  return ResponseEntity.created(location).header("MyResponseHeader", "MyValue").body("Hello World");
}
</pre>] with status {@code 200 (OK)} and with body the org unit tree.
         返回状态码为200的响应和组织单元树形结构*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListOrgUnitDTO](#schemalistorgunitdto)]|false|none||the [Extension of{@link HttpEntity} that adds an{@link HttpStatusCode} status code.<br />Used in{@code RestTemplate} as well as in{@code @Controller} methods.<br /><br /><p>In{@code RestTemplate}, this class is returned by<br />{@link org.springframework.web.client.RestTemplate#getForEntity getForEntity()} and<br />{@link org.springframework.web.client.RestTemplate#exchange exchange()}:<br /><pre class="code"><br />ResponseEntity&lt;String&gt; entity = template.getForEntity("https://example.com", String.class);<br />String body = entity.getBody();<br />MediaType contentType = entity.getHeaders().getContentType();<br />HttpStatus statusCode = entity.getStatusCode();<br /></pre><br /><br /><p>This can also be used in Spring MVC as the return value from an<br />{@code @Controller} method:<br /><pre class="code"><br />&#64;RequestMapping("/handle")<br />public ResponseEntity&lt;String&gt; handle(){<br />  URI location = ...;<br />  HttpHeaders responseHeaders = new HttpHeaders();<br />  responseHeaders.setLocation(location);<br />  responseHeaders.set("MyResponseHeader", "MyValue");<br />  return new ResponseEntity&lt;String&gt;("Hello World", responseHeaders, HttpStatus.CREATED);<br />}<br /></pre><br /><br />Or, by using a builder accessible via static methods:<br /><pre class="code"><br />&#64;RequestMapping("/handle")<br />public ResponseEntity&lt;String&gt; handle(){<br />  URI location = ...;<br />  return ResponseEntity.created(location).header("MyResponseHeader", "MyValue").body("Hello World");<br />}<br /></pre>] with status {@code 200 (OK)} and with body the org unit tree.<br />         返回状态码为200的响应和组织单元树形结构|
|» id|integer(int64)|false|none||主键 ID|
|» tenantId|integer(int64)|true|none||租户 ID，标识不同租户的数据隔离|
|» name|string|true|none||组织单位名称|
|» code|string|true|none||唯一编码，用于外部系统对接或导入映射|
|» type|string|true|none||组织单元类型|
|» level|integer|true|none||层级深度，根节点为 1|
|» status|integer|true|none||状态：1=启用，0=禁用|
|» sortOrder|integer|false|none||排序序号|
|» description|string|false|none||描述信息|
|» metadata|string|false|none||扩展元数据（JSON）|
|» version|integer|true|none||乐观锁版本|
|» createdBy|string|false|none||创建者|
|» createdAt|object|true|none||创建时间|
|» updatedBy|string|false|none||更新者|
|» updatedAt|object|true|none||创建时间|
|» isDeleted|boolean|true|none||软删除标志|
|» parentId|integer(int64)|false|none||父节点|
|» children|[object]|false|none||子节点|

#### 枚举值

|属性|值|
|---|---|
|type|COMPANY|
|type|SUBSIDIARY|
|type|BUSINESS_GROUP|
|type|DEPARTMENT|
|type|TEAM|

## PATCH 变更现有组织单元的状态

PATCH /whiskerguardorgservice/api/org/units/{id}/status

变更现有组织单元的状态

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |the id of the org unit to change status. 要变更状态的组织单元ID|
|status|query|integer| 是 |the target status. 目标状态|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "code": "",
  "type": "",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parentId": 0,
  "children": [
    {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "code": "",
      "type": "",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentId": 0,
      "children": [
        {
          "id": 0,
          "tenantId": 0,
          "name": "",
          "code": "",
          "type": "",
          "level": 0,
          "status": 0,
          "sortOrder": 0,
          "description": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "parentId": 0,
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityOrgUnitDTO](#schemaresponseentityorgunitdto)|

## PUT 更新组织单元的排序顺序

PUT /whiskerguardorgservice/api/org/units/sort

更新组织单元的排序顺序

> Body 请求参数

```json
[
  0
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|array[integer]| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_OrgUnitDTO">OrgUnitDTO</h2>

<a id="schemaorgunitdto"></a>
<a id="schema_OrgUnitDTO"></a>
<a id="tocSorgunitdto"></a>
<a id="tocsorgunitdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "type": "COMPANY",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentId": 0,
  "children": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|tenantId|integer(int64)|true|none||租户 ID，标识不同租户的数据隔离|
|name|string|true|none||组织单位名称|
|code|string|true|none||唯一编码，用于外部系统对接或导入映射|
|type|string|true|none||组织单元类型|
|level|integer|true|none||层级深度，根节点为 1|
|status|integer|true|none||状态：1=启用，0=禁用|
|sortOrder|integer|false|none||排序序号|
|description|string|false|none||描述信息|
|metadata|string|false|none||扩展元数据（JSON）|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|object|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|object|false|none||创建时间|
|isDeleted|boolean|false|none||软删除标志|
|parentId|integer(int64)|false|none||父节点|
|children|[[OrgUnitDTO](#schemaorgunitdto)]|false|none||子节点|

#### 枚举值

|属性|值|
|---|---|
|type|COMPANY|
|type|SUBSIDIARY|
|type|BUSINESS_GROUP|
|type|DEPARTMENT|
|type|TEAM|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ResponseEntityOrgUnitDTO">ResponseEntityOrgUnitDTO</h2>

<a id="schemaresponseentityorgunitdto"></a>
<a id="schema_ResponseEntityOrgUnitDTO"></a>
<a id="tocSresponseentityorgunitdto"></a>
<a id="tocsresponseentityorgunitdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "type": "COMPANY",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentId": 0,
  "children": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|tenantId|integer(int64)|true|none||租户 ID，标识不同租户的数据隔离|
|name|string|true|none||组织单位名称|
|code|string|true|none||唯一编码，用于外部系统对接或导入映射|
|type|string|true|none||组织单元类型|
|level|integer|true|none||层级深度，根节点为 1|
|status|integer|true|none||状态：1=启用，0=禁用|
|sortOrder|integer|false|none||排序序号|
|description|string|false|none||描述信息|
|metadata|string|false|none||扩展元数据（JSON）|
|version|integer|true|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|object|true|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|object|true|none||创建时间|
|isDeleted|boolean|true|none||软删除标志|
|parentId|integer(int64)|false|none||父节点|
|children|[object]|false|none||子节点|

#### 枚举值

|属性|值|
|---|---|
|type|COMPANY|
|type|SUBSIDIARY|
|type|BUSINESS_GROUP|
|type|DEPARTMENT|
|type|TEAM|

<h2 id="tocS_ListOrgUnitDTO">ListOrgUnitDTO</h2>

<a id="schemalistorgunitdto"></a>
<a id="schema_ListOrgUnitDTO"></a>
<a id="tocSlistorgunitdto"></a>
<a id="tocslistorgunitdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "code": "string",
  "type": "COMPANY",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentId": 0,
  "children": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|tenantId|integer(int64)|true|none||租户 ID，标识不同租户的数据隔离|
|name|string|true|none||组织单位名称|
|code|string|true|none||唯一编码，用于外部系统对接或导入映射|
|type|string|true|none||组织单元类型|
|level|integer|true|none||层级深度，根节点为 1|
|status|integer|true|none||状态：1=启用，0=禁用|
|sortOrder|integer|false|none||排序序号|
|description|string|false|none||描述信息|
|metadata|string|false|none||扩展元数据（JSON）|
|version|integer|true|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|object|true|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|object|true|none||创建时间|
|isDeleted|boolean|true|none||软删除标志|
|parentId|integer(int64)|false|none||父节点|
|children|[object]|false|none||子节点|

#### 枚举值

|属性|值|
|---|---|
|type|COMPANY|
|type|SUBSIDIARY|
|type|BUSINESS_GROUP|
|type|DEPARTMENT|
|type|TEAM|

