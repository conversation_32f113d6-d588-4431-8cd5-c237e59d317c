<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { ref } from 'vue'
import {
  Lock,
  Plus,
  User,
} from '@element-plus/icons-vue'

const activeMenu = ref('2-1')

const form = ref({
  companyName: '北京猫伯伯科技有限公司',
  companyType: 'private',
  industry: 'internet',
  scale: '50-200',
  establishDate: '2018-06-15',
  legalRepresentative: '李明',
  creditCode: '91110108MA01ABCDEF',
  description: '猫伯伯科技是一家专注于企业合规管理解决方案的高新技术企业，致力于为企业提供全方位的合规风险管理和智能化合规服务。',
  contactPerson: '张伟',
  contactPhone: '13800138000',
  email: '<EMAIL>',
  website: 'https://www.maobobo.com',
  address: {
    region: ['beijing', 'beijing', 'haidian'],
    detail: '中关村软件园12号楼',
  },
  logo: 'https://ai-public.mastergo.com/ai/img_res/f3b56cc344b328a50ac0b20befb66d23.jpg',
  systemNameDisplay: 'default',
  customSystemName: '',
  themeColor: '#1E88E5',
  loginBackground: 'https://ai-public.mastergo.com/ai/img_res/3adaf08588bbab25b7cf5f816890785d.jpg',
})

const regionOptions = [
  {
    value: 'beijing',
    label: '北京市',
    children: [
      {
        value: 'beijing',
        label: '北京市',
        children: [
          { value: 'haidian', label: '海淀区' },
          { value: 'chaoyang', label: '朝阳区' },
          { value: 'xicheng', label: '西城区' },
        ],
      },
    ],
  },
  {
    value: 'shanghai',
    label: '上海市',
    children: [
      {
        value: 'shanghai',
        label: '上海市',
        children: [
          { value: 'pudong', label: '浦东新区' },
          { value: 'huangpu', label: '黄浦区' },
          { value: 'xuhui', label: '徐汇区' },
        ],
      },
    ],
  },
]

const presetColors = ref([
  '#1E88E5',
  '#43A047',
  '#FB8C00',
  '#E53935',
  '#8E24AA',
])

function handleLogoSuccess(response: any, file: any) {
  form.value.logo = URL.createObjectURL(file.raw)
}

function beforeLogoUpload(file: any) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

function handleBgSuccess(response: any, file: any) {
  form.value.loginBackground = URL.createObjectURL(file.raw)
}

function beforeBgUpload(file: any) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt5M) {
    ElMessage.error('上传图片大小不能超过 5MB!')
  }
  return isJPG && isLt5M
}
</script>

<template>
  <div class="absolute-container" style="padding-bottom: 80px;">
    <!-- <page-header title="" content="">
      <template #content>

      </template>
    </page-header> -->
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="text-gray-800 font-bold">
                  基本信息
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <div class="grid grid-cols-2 gap-4">
                  <el-form-item label="企业名称" prop="companyName" required>
                    <el-input v-model="form.companyName" placeholder="请输入企业名称" />
                  </el-form-item>
                  <el-form-item label="企业类型" prop="companyType">
                    <el-select v-model="form.companyType" placeholder="请选择企业类型" class="w-full">
                      <el-option label="国有企业" value="state-owned" />
                      <el-option label="民营企业" value="private" />
                      <el-option label="外资企业" value="foreign" />
                      <el-option label="合资企业" value="joint-venture" />
                      <el-option label="其他" value="other" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="所属行业" prop="industry" required>
                    <el-select v-model="form.industry" placeholder="请选择所属行业" filterable class="w-full">
                      <el-option label="互联网/IT" value="internet" />
                      <el-option label="金融" value="finance" />
                      <el-option label="制造业" value="manufacturing" />
                      <el-option label="医疗健康" value="healthcare" />
                      <el-option label="教育" value="education" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="企业规模" prop="scale">
                    <el-select v-model="form.scale" placeholder="请选择企业规模" class="w-full">
                      <el-option label="50人以下" value="0-50" />
                      <el-option label="50-200人" value="50-200" />
                      <el-option label="200-500人" value="200-500" />
                      <el-option label="500-1000人" value="500-1000" />
                      <el-option label="1000人以上" value="1000+" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="成立日期" prop="establishDate">
                    <el-date-picker v-model="form.establishDate" type="date" placeholder="选择日期" class="w-full" />
                  </el-form-item>
                  <el-form-item label="法定代表人" prop="legalRepresentative">
                    <el-input v-model="form.legalRepresentative" placeholder="请输入法定代表人姓名" />
                  </el-form-item>
                </div>
                <el-form-item label="统一社会信用代码" prop="creditCode" required>
                  <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />
                </el-form-item>
                <el-form-item label="企业简介" prop="description">
                  <el-input v-model="form.description" type="textarea" :rows="4" placeholder="请输入企业简介" />
                </el-form-item>
              </el-form>
            </el-card>

            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="text-gray-800 font-bold">
                  联系方式
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <div class="grid grid-cols-2 gap-4">
                  <el-form-item label="联系人" prop="contactPerson" required>
                    <el-input v-model="form.contactPerson" placeholder="请输入联系人姓名" />
                  </el-form-item>
                  <el-form-item label="联系电话" prop="contactPhone" required>
                    <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
                  </el-form-item>
                  <el-form-item label="电子邮箱" prop="email">
                    <el-input v-model="form.email" placeholder="请输入电子邮箱" />
                  </el-form-item>
                  <el-form-item label="企业官网" prop="website">
                    <el-input v-model="form.website" placeholder="请输入企业官网地址" />
                  </el-form-item>
                </div>
                <el-form-item label="企业地址" prop="address">
                  <el-cascader
                    v-model="form.address.region" :options="regionOptions" placeholder="请选择省市区"
                    class="mb-2 w-full"
                  />
                  <el-input v-model="form.address.detail" placeholder="请输入详细地址" />
                </el-form-item>
              </el-form>
            </el-card>

            <el-card shadow="hover">
              <template #header>
                <div class="text-gray-800 font-bold">
                  品牌设置
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <el-form-item label="企业Logo" prop="logo">
                  <el-upload
                    class="avatar-uploader" action="https://jsonplaceholder.typicode.com/posts/"
                    :show-file-list="false" :on-success="handleLogoSuccess" :before-upload="beforeLogoUpload"
                  >
                    <img v-if="form.logo" :src="form.logo" class="avatar">
                    <el-icon v-else class="avatar-uploader-icon">
                      <Plus />
                    </el-icon>
                  </el-upload>
                  <div class="mt-2 text-xs text-gray-500">
                    建议尺寸：200×200px，支持JPG/PNG格式
                  </div>
                </el-form-item>
                <el-form-item label="系统名称显示" prop="systemNameDisplay">
                  <el-radio-group v-model="form.systemNameDisplay">
                    <el-radio label="default">
                      使用默认名称
                    </el-radio>
                    <el-radio label="custom">
                      使用自定义名称
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="form.systemNameDisplay === 'custom'" label="自定义系统名称" prop="customSystemName">
                  <el-input v-model="form.customSystemName" placeholder="请输入自定义系统名称" />
                </el-form-item>
                <el-form-item label="主题色" prop="themeColor">
                  <el-color-picker v-model="form.themeColor" />
                  <div class="mt-2 flex gap-2">
                    <div
                      v-for="color in presetColors" :key="color" class="h-6 w-6 cursor-pointer rounded-full"
                      :style="{ backgroundColor: color }" @click="form.themeColor = color"
                    />
                  </div>
                </el-form-item>
                <el-form-item label="登录页背景" prop="loginBackground">
                  <el-upload
                    class="background-uploader" action="https://jsonplaceholder.typicode.com/posts/"
                    :show-file-list="false" :on-success="handleBgSuccess" :before-upload="beforeBgUpload"
                  >
                    <img v-if="form.loginBackground" :src="form.loginBackground" class="background">
                    <div v-else class="background-uploader-placeholder">
                      <el-icon>
                        <picture />
                      </el-icon>
                      <span class="ml-2">点击上传背景图</span>
                    </div>
                  </el-upload>
                  <div class="mt-2 text-xs text-gray-500">
                    建议尺寸：1920×1080px，支持JPG/PNG格式
                  </div>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="sticky top-24">
              <template #header>
                <div class="text-gray-800 font-bold">
                  预览效果
                </div>
              </template>
              <div class="space-y-6">
                <div>
                  <h3 class="mb-2 text-sm text-gray-700 font-medium">
                    Logo预览
                  </h3>
                  <div class="flex justify-center">
                    <img
                      v-if="form.logo" :src="form.logo"
                      class="h-32 w-32 border border-gray-200 rounded object-contain"
                    >
                    <div
                      v-else
                      class="h-32 w-32 flex items-center justify-center border border-gray-300 rounded border-dashed text-gray-400"
                    >
                      <el-icon>
                        <picture />
                      </el-icon>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 class="mb-2 text-sm text-gray-700 font-medium">
                    系统导航栏预览
                  </h3>
                  <div class="border border-gray-200 rounded p-4">
                    <div class="flex items-center justify-between bg-white p-2">
                      <div class="flex items-center">
                        <img v-if="form.logo" :src="form.logo" class="mr-2 h-6 w-6">
                        <span class="text-sm font-medium">
                          {{ form.systemNameDisplay === 'custom' && form.customSystemName ? form.customSystemName : '猫伯伯合规管家' }}
                        </span>
                      </div>
                      <el-avatar
                        :size="24"
                        src="https://ai-public.mastergo.com/ai/img_res/f9e2af8545b4e1c2bdb51eacbcdd1d2d.jpg"
                      />
                    </div>
                    <div class="mt-2">
                      <div class="h-8 flex items-center rounded bg-[#1A1F37] px-3">
                        <span class="text-xs text-white">导航菜单</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 class="mb-2 text-sm text-gray-700 font-medium">
                    登录页面预览
                  </h3>
                  <div class="overflow-hidden border border-gray-200 rounded">
                    <div
                      class="h-40 flex items-center justify-center bg-cover bg-center"
                      :style="{ backgroundImage: form.loginBackground ? `url(${form.loginBackground})` : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' }"
                    >
                      <div class="w-3/4 rounded bg-white bg-opacity-90 p-4 shadow-sm">
                        <div class="mb-2 flex justify-center">
                          <img v-if="form.logo" :src="form.logo" class="h-10 w-10">
                        </div>
                        <h3 class="mb-3 text-center text-sm font-medium">
                          {{ form.systemNameDisplay === 'custom' && form.customSystemName ? form.customSystemName : '猫伯伯合规管家' }}
                        </h3>
                        <el-input placeholder="用户名" size="small" class="mb-2">
                          <template #prefix>
                            <el-icon>
                              <User />
                            </el-icon>
                          </template>
                        </el-input>
                        <el-input placeholder="密码" size="small" type="password" class="mb-2">
                          <template #prefix>
                            <el-icon>
                              <Lock />
                            </el-icon>
                          </template>
                        </el-input>
                        <el-button
                          type="primary" size="small"
                          class="!rounded-button w-full whitespace-nowrap"
                        >
                          登录
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
    <fixed-action-bar>
      <el-button type="primary" size="large" @click="onSubmit">
        提交
      </el-button>
      <el-button size="large" @click="onCancel">
        取消
      </el-button>
    </fixed-action-bar>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .avatar-uploader .avatar {
    display: block;
    width: 120px;
    height: 120px;
    object-fit: contain;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }

  .avatar-uploader .el-upload {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    transition: all 0.2s;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #1e88e5;
  }

  .avatar-uploader-icon {
    width: 120px;
    height: 120px;
    font-size: 28px;
    line-height: 120px;
    color: #8c939d;
    text-align: center;
  }

  .background-uploader .background {
    display: block;
    width: 100%;
    height: 120px;
    object-fit: cover;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }

  .background-uploader-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120px;
    color: #8c939d;
    background-color: #f5f7fa;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }
</style>
