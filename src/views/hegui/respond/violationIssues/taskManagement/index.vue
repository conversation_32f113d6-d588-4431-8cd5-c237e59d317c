<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { ref } from 'vue'

import popMode from './pop.vue'

// 示例数据
const selectedItems = ref(2)
const dialogVisible = ref(false)
const form = ref({})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              违规问题调查
            </h1>
            <!-- <el-tag type="warning" class="ml-4">审查中</el-tag> -->
          </div>
          <div class="flex items-center space-x-3">
            <el-button type="primary" class="!rounded-button">
              <el-icon class="mr-2">
                <video-play />
              </el-icon>新增调查任务
            </el-button>
            <el-button plain class="!rounded-button">
              <el-icon class="mr-2">
                <promotion />
              </el-icon>批量导入
            </el-button>
            <el-button plain class="!rounded-button">
              <el-icon class="mr-2">
                <document />
              </el-icon>导出
            </el-button>
            <el-button plain class="!rounded-button">
              <el-icon class="mr-2">
                <printer />
              </el-icon>统计分析
            </el-button>
            <!-- <el-button plain class="!rounded-button">
              <el-icon class="mr-2"><arrow-left /></el-icon>返回
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="12">
            <el-card class="">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    审查进度
                  </div>
                  <el-text type="primary">
                    查看全部
                  </el-text>
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                  <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-file-alt text-sm" />
                  </div>
                  <div class="flex-1">
                    <div class="text-sm text-gray-800 font-medium">
                      2023年度财务合规审查
                    </div>
                    <div class="mt-1 flex items-center text-xs text-gray-500">
                      <span class="mr-2 rounded bg-green-100 px-2 py-0.5 text-green-800">已完成</span>
                      <span>调查报告</span>
                    </div>
                  </div>
                </div>
                <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                  <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-search text-sm" />
                  </div>
                  <div class="flex-1">
                    <div class="text-sm text-gray-800 font-medium">
                      Q3销售数据异常调查
                    </div>
                    <div class="mt-1 flex items-center text-xs text-gray-500">
                      <span class="mr-2 rounded bg-blue-100 px-2 py-0.5 text-blue-800">进行中</span>
                      <span>调查任务</span>
                    </div>
                  </div>
                </div>
                <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                  <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-microphone text-sm" />
                  </div>
                  <div class="flex-1">
                    <div class="text-sm text-gray-800 font-medium">
                      李总监访谈记录
                    </div>
                    <div class="mt-1 flex items-center text-xs text-gray-500">
                      <span class="mr-2 rounded bg-gray-100 px-2 py-0.5 text-gray-800">已归档</span>
                      <span>过程记录</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    我负责的
                  </div>
                  <el-text type="primary">
                    查看全部
                  </el-text>
                </div>
              </template>
              <div class="space-y-3">
                <div class="border-l-4 border-red-500 rounded-r bg-red-50 p-3">
                  <div class="text-sm text-gray-800 font-medium">
                    市场部广告合规审查
                  </div>
                  <div class="mt-1 flex items-center text-xs text-gray-500">
                    <span>截止: 2023-11-15</span>
                    <span class="mx-2">•</span>
                    <span class="text-red-500">高优先级</span>
                  </div>
                </div>
                <div class="border-l-4 border-orange-500 rounded-r bg-orange-50 p-3">
                  <div class="text-sm text-gray-800 font-medium">
                    供应商合规评估
                  </div>
                  <div class="mt-1 flex items-center text-xs text-gray-500">
                    <span>截止: 2023-11-20</span>
                    <span class="mx-2">•</span>
                    <span class="text-orange-500">中优先级</span>
                  </div>
                </div>
                <div class="border-l-4 border-blue-500 rounded-r bg-blue-50 p-3">
                  <div class="text-sm text-gray-800 font-medium">
                    员工合规培训反馈收集
                  </div>
                  <div class="mt-1 flex items-center text-xs text-gray-500">
                    <span>截止: 2023-11-25</span>
                    <span class="mx-2">•</span>
                    <span class="text-blue-500">低优先级</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-card class="mt-20">
          <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
            <div class="grid grid-cols-4 mb-4 gap-4">
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">调查状态</label>
                <select
                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option>全部状态</option>
                  <option>未开始</option>
                  <option>进行中</option>
                  <option>已完成</option>
                  <option>已暂停</option>
                  <option>已取消</option>
                </select>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">优先级</label>
                <select
                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option>全部优先级</option>
                  <option>高</option>
                  <option>中</option>
                  <option>低</option>
                </select>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">来源部门</label>
                <select
                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option>全部部门</option>
                  <option>财务部</option>
                  <option>市场部</option>
                  <option>人力资源部</option>
                  <option>技术部</option>
                </select>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">调查日期</label>
                <div class="flex items-center">
                  <input
                    type="date"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                  <span class="mx-2 text-gray-500">至</span>
                  <input
                    type="date"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                </div>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <div class="mr-4 flex-1">
                <input
                  type="text" placeholder="输入关键词搜索..."
                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
              </div>
              <div class="flex space-x-2">
                <button
                  class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <i class="fas fa-filter mr-2" />高级筛选
                </button>
                <button
                  class="!rounded-button whitespace-nowrap bg-gray-100 px-4 py-2 text-sm text-gray-700 hover:bg-gray-200"
                >
                  <i class="fas fa-redo mr-2" />重置
                </button>
                <button
                  class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700"
                >
                  <i class="fas fa-search mr-2" />搜索
                </button>
              </div>
            </div>
          </div>
        </el-card>
        <el-card class="mt-20">
          <el-table :data="attachments">
            <el-table-column prop="name" label="调查编号" />
            <el-table-column prop="type" label="调查标题" />
            <el-table-column prop="size" label="调查类型" />
            <el-table-column prop="uploadTime" label="优先级" />
            <el-table-column prop="name" label="来源" />
            <el-table-column prop="type" label="开始日期" />
            <el-table-column prop="size" label="状态" />
            <el-table-column prop="uploadTime" label="负责人" />
            <el-table-column label="操作">
              <template #default>
                <el-button type="text">
                  查看
                </el-button>
                <el-button type="text">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card class="mt-20">
              <h3 class="mb-4 text-gray-800 font-medium" />
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    调查统计概览
                  </div>
                </div>
              </template>
              <div class="grid grid-cols-4 mb-4 gap-4">
                <div class="rounded-lg bg-blue-50 p-4">
                  <div class="mb-1 text-sm text-blue-800">
                    总调查数
                  </div>
                  <div class="text-2xl text-blue-600 font-bold">
                    24
                  </div>
                </div>
                <div class="rounded-lg bg-green-50 p-4">
                  <div class="mb-1 text-sm text-green-800">
                    已完成
                  </div>
                  <div class="text-2xl text-green-600 font-bold">
                    8
                  </div>
                </div>
                <div class="rounded-lg bg-blue-100 p-4">
                  <div class="mb-1 text-sm text-blue-800">
                    进行中
                  </div>
                  <div class="text-2xl text-blue-600 font-bold">
                    12
                  </div>
                </div>
                <div class="rounded-lg bg-yellow-50 p-4">
                  <div class="mb-1 text-sm text-yellow-800">
                    已暂停
                  </div>
                  <div class="text-2xl text-yellow-600 font-bold">
                    4
                  </div>
                </div>
              </div>
              <div class="h-64 flex items-center justify-center rounded-lg bg-gray-50">
                <div class="text-gray-400">
                  调查状态趋势图表
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    AI助手
                  </div>
                </div>
              </template>
              <div class="mb-4 space-y-3">
                <button
                  class="!rounded-button w-full flex items-center whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
                >
                  <i class="fas fa-chart-line mr-2" />分析调查趋势
                </button>
                <button
                  class="!rounded-button w-full flex items-center whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
                >
                  <i class="fas fa-exclamation-circle mr-2" />建议优先处理项
                </button>
                <button
                  class="!rounded-button w-full flex items-center whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
                >
                  <i class="fas fa-calendar-alt mr-2" />生成工作计划
                </button>
              </div>
              <div class="rounded-lg bg-gray-50 p-3">
                <div class="mb-2 text-sm text-gray-700">
                  AI建议：
                </div>
                <div class="text-sm text-gray-600">
                  根据当前调查任务状态分析，建议优先处理市场部广告合规审查任务，该任务优先级高且截止日期临近。
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
    <!-- modulewidth="800px" -->
    <HDialog v-model="dialogVisible" :title="form.id ? '调查记录详情' : '调查记录详情'" modulewidth="1000px">
      <popMode v-if="dialogVisible" />
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="dialogVisible = false, form = {} ">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
  </div>
</template>

<style scoped>
  /* 自定义样式 */
  .fixed {
    position: fixed;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
