<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, ref } from 'vue'
import {
  ChatRound,
  DataAnalysis,
  Document,
  Download,
  Edit,
  List,
  Location,
  More,
  Notebook,
  Printer,
  Search,
  Upload,
  User,
  VideoCamera,
} from '@element-plus/icons-vue'

const activeTab = ref('records')
const recordSummary = ref('')
const recordDate = ref('')
const participants = ref([])
const conclusionStatus = ref('draft')
const conclusionContent = ref('')

const evidenceFilter = ref({
  type: '',
  status: '',
  keyword: '',
})

const involvedPersons = ref([
  { name: '王伟', department: '市场部', position: '高级经理', role: '被调查', status: '被调查' },
  { name: '李娜', department: '市场部', position: '数据分析师', role: '被调查', status: '被调查' },
  { name: '陈明', department: 'IT部', position: '系统管理员', role: '协助调查', status: '协助' },
  { name: '张明远', department: '合规部', position: '合规经理', role: '负责人', status: '负责人' },
  { name: '李思颖', department: 'IT安全部', position: '安全专家', role: '协调人', status: '协调人' },
])

const investigationActivities = ref([
  {
    date: '2023-04-25',
    type: '文件审阅',
    description: '审查市场部员工电脑访问日志',
    participants: '张明远, 李思颖',
    location: 'IT安全部',
  },
  {
    date: '2023-04-26',
    type: '数据分析',
    description: '分析异常数据传输记录',
    participants: '李思颖',
    location: 'IT安全部',
  },
  {
    date: '2023-04-28',
    type: '人员访谈',
    description: '访谈市场部王伟关于数据访问情况',
    participants: '张明远, 李思颖',
    location: '会议室A',
  },
])

const evidenceList = ref([
  {
    id: 'EVD-001',
    name: '访问日志_0420',
    type: 'log',
    uploadTime: '2023-04-25',
    uploader: '李思颖',
    category: '系统日志',
    status: 'verified',
  },
  {
    id: 'EVD-002',
    name: '员工电脑截图',
    type: 'image',
    uploadTime: '2023-04-26',
    uploader: '张明远',
    category: '截图证据',
    status: 'pending',
  },
  {
    id: 'EVD-003',
    name: '访谈记录_王伟',
    type: 'document',
    uploadTime: '2023-04-28',
    uploader: '张明远',
    category: '访谈记录',
    status: 'verified',
  },
])

const investigationFindings = ref([
  {
    id: 'FND-001',
    description: '发现市场部员工电脑存在大量客户数据下载记录',
    severity: '高',
    date: '2023-04-26',
    finder: '李思颖',
    relatedPersons: '王伟',
    status: '已确认',
  },
  {
    id: 'FND-002',
    description: '异常数据传输发生在非工作时间',
    severity: '中',
    date: '2023-04-27',
    finder: '李思颖',
    relatedPersons: '王伟',
    status: '已确认',
  },
])

const violationRecords = ref([
  {
    type: '数据泄露',
    description: '未经授权下载客户数据',
    basis: '公司数据安全政策第3.2条',
    responsible: '王伟',
    suggestion: '纪律处分，数据安全培训',
  },
])

const recommendations = ref([
  {
    type: '技术措施',
    description: '加强数据访问监控系统',
    department: 'IT安全部',
    priority: '高',
    effect: '实时监控异常数据访问',
  },
  {
    type: '管理措施',
    description: '修订数据访问审批流程',
    department: '合规部',
    priority: '中',
    effect: '规范数据访问权限管理',
  },
])

const operationLogs = ref([
  {
    time: '2023-04-25 09:30',
    operator: '张明远',
    type: '创建调查',
    content: '创建了调查任务"员工数据泄露事件调查"',
    ip: '*************',
  },
  {
    time: '2023-04-25 14:15',
    operator: '李思颖',
    type: '上传证据',
    content: '上传了证据"访问日志_0420"',
    ip: '*************',
  },
  {
    time: '2023-04-26 10:20',
    operator: '张明远',
    type: '添加记录',
    content: '添加了调查活动"数据分析"',
    ip: '*************',
  },
])

const changeRecords = ref([
  {
    time: '2023-04-25 09:30',
    changer: '张明远',
    field: '状态',
    before: '',
    after: '未开始',
    reason: '创建调查',
  },
  {
    time: '2023-04-25 14:15',
    changer: '李思颖',
    field: '证据',
    before: '0',
    after: '1',
    reason: '上传证据',
  },
])

const relatedPersons = ref([
  { name: '张明远', role: '负责人', department: '合规部' },
  { name: '李思颖', role: '协调人', department: 'IT安全部' },
  { name: '王伟', role: '被调查', department: '市场部' },
  { name: '陈明', role: '技术支持', department: 'IT部' },
])

const relatedSurveys = ref([
  { id: 'INV-2023-0320', title: '市场部数据使用合规审查', status: '已完成', relation: '相关调查' },
  { id: 'INV-2023-0410', title: 'IT系统安全漏洞检查', status: '进行中', relation: '相关调查' },
])

const filteredEvidence = computed(() => {
  return evidenceList.value.filter((item) => {
    const typeMatch = !evidenceFilter.value.type || item.type === evidenceFilter.value.type
    const statusMatch = !evidenceFilter.value.status || item.status === evidenceFilter.value.status
    const keywordMatch
        = !evidenceFilter.value.keyword
        || item.name.toLowerCase().includes(evidenceFilter.value.keyword.toLowerCase())
        || item.id.toLowerCase().includes(evidenceFilter.value.keyword.toLowerCase())
    return typeMatch && statusMatch && keywordMatch
  })
})

function getActivityTagType(type: string) {
  const map: Record<string, string> = {
    文件审阅: '',
    人员访谈: 'success',
    现场调查: 'warning',
    数据分析: 'info',
    专家咨询: 'danger',
  }
  return map[type] || ''
}

function getEvidenceStatusTagType(status: string) {
  const map: Record<string, string> = {
    pending: 'warning',
    verified: 'success',
    rejected: 'danger',
  }
  return map[status] || ''
}

function getSeverityTagType(severity: string) {
  const map: Record<string, string> = {
    高: 'danger',
    中: 'warning',
    低: '',
  }
  return map[severity] || ''
}

function getFindingStatusTagType(status: string) {
  const map: Record<string, string> = {
    初步发现: 'info',
    已确认: 'success',
    已驳回: 'danger',
  }
  return map[status] || ''
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              员工数据泄露事件调查
            </h1>
            <el-tag type="warning" class="ml-4">
              进行中
            </el-tag>
          </div>
          <div class="flex items-center space-x-4">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              开始调查
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Edit />
              </el-icon>
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Download />
              </el-icon>
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Printer />
              </el-icon>
            </el-button>
            <el-dropdown>
              <el-button class="!rounded-button whitespace-nowrap">
                <el-icon>
                  <More />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>暂停</el-dropdown-item>
                  <el-dropdown-item>取消</el-dropdown-item>
                  <el-dropdown-item>分享</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card class="">
              <!-- 基本信息区 -->
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <div class="grid grid-cols-2 gap-6">
                  <div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        调查编号
                      </h3>
                      <p class="mt-1">
                        INV-2023-0425
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        调查标题
                      </h3>
                      <p class="mt-1">
                        员工数据泄露事件调查
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        调查类型
                      </h3>
                      <p class="mt-1">
                        数据安全违规
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        优先级
                      </h3>
                      <el-tag type="danger" class="mt-1">
                        高
                      </el-tag>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        调查来源
                      </h3>
                      <p class="mt-1">
                        内部举报
                      </p>
                    </div>
                  </div>
                  <div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        开始日期
                      </h3>
                      <p class="mt-1">
                        2023-04-25
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        计划完成日期
                      </h3>
                      <p class="mt-1">
                        2023-05-10
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        负责人
                      </h3>
                      <p class="mt-1">
                        张明远 (合规部)
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        协调人
                      </h3>
                      <p class="mt-1">
                        李思颖 (IT安全部)
                      </p>
                    </div>
                    <div class="mb-4">
                      <h3 class="text-sm text-gray-500 font-medium">
                        状态
                      </h3>
                      <el-tag type="warning" class="mt-1">
                        进行中
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <h2 class="mb-4 text-lg font-bold">
                  调查概述
                </h2>
                <div class="mb-6">
                  <h3 class="text-sm text-gray-500 font-medium">
                    调查背景
                  </h3>
                  <p class="mt-2 text-gray-700">
                    2023年4月20日，公司IT安全监控系统检测到异常数据外传活动。初步分析显示，市场部员工电脑存在大量客户数据异常访问记录。该事件可能违反公司数据安全政策和GDPR相关规定。
                  </p>
                </div>
                <div class="mb-6">
                  <h3 class="text-sm text-gray-500 font-medium">
                    调查目标
                  </h3>
                  <p class="mt-2 text-gray-700">
                    1. 确认数据泄露的范围和程度<br>
                    2. 识别责任人及泄露原因<br>
                    3. 评估潜在法律和合规风险<br>
                    4. 提出改进建议防止类似事件再次发生
                  </p>
                </div>
                <div class="mb-6">
                  <h3 class="text-sm text-gray-500 font-medium">
                    调查范围
                  </h3>
                  <p class="mt-2 text-gray-700">
                    调查范围包括市场部全体成员，时间范围为2023年1月1日至2023年4月20日。涉及的数据类型包括客户基本信息、联系方式、交易记录等敏感数据。
                  </p>
                </div>
                <div>
                  <h3 class="text-sm text-gray-500 font-medium">
                    涉及人员/部门
                  </h3>
                  <el-table :data="involvedPersons" class="mt-2" border>
                    <el-table-column prop="name" label="姓名" width="120" />
                    <el-table-column prop="department" label="部门" width="120" />
                    <el-table-column prop="position" label="职位" width="120" />
                    <el-table-column prop="role" label="角色" width="120" />
                    <el-table-column prop="status" label="状态" width="100">
                      <template #default="{ row }">
                        <el-tag :type="row.status === '被调查' ? 'danger' : 'info'">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="调查记录" name="records">
                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      调查计划
                    </h3>
                    <p class="mt-2 text-gray-700">
                      1. 第一阶段（4月25日-4月27日）：收集日志证据，分析数据流向<br>
                      2. 第二阶段（4月28日-5月2日）：访谈相关人员，确认操作动机<br>
                      3. 第三阶段（5月3日-5月7日）：评估影响范围，制定补救措施<br>
                      4. 第四阶段（5月8日-5月10日）：完成报告，提出改进建议
                    </p>
                    <el-button type="primary" class="!rounded-button mt-4 whitespace-nowrap">
                      编辑计划
                    </el-button>
                  </div>

                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      快速添加记录
                    </h3>
                    <div class="mt-4 flex space-x-2">
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <Document />
                        </el-icon> 文件审阅
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <User />
                        </el-icon> 人员访谈
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <Location />
                        </el-icon> 现场调查
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <DataAnalysis />
                        </el-icon> 数据分析
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        <el-icon>
                          <ChatRound />
                        </el-icon> 专家咨询
                      </el-button>
                    </div>
                    <div class="grid grid-cols-2 mt-4 gap-4">
                      <el-input v-model="recordSummary" placeholder="记录摘要" />
                      <el-date-picker v-model="recordDate" type="datetime" placeholder="选择日期时间" />
                    </div>
                    <el-select v-model="participants" multiple placeholder="选择参与人员" class="mt-4 w-full">
                      <el-option
                        v-for="person in involvedPersons" :key="person.name" :label="person.name"
                        :value="person.name"
                      />
                    </el-select>
                    <el-button type="text" class="mt-2">
                      添加详细记录
                    </el-button>
                  </div>

                  <div>
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-bold">
                        调查活动
                      </h3>
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        新增活动记录
                      </el-button>
                    </div>
                    <el-table :data="investigationActivities" class="mt-4" border>
                      <el-table-column prop="date" label="活动日期" width="150" />
                      <el-table-column prop="type" label="活动类型" width="120">
                        <template #default="{ row }">
                          <el-tag :type="getActivityTagType(row.type)">
                            {{ row.type }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="description" label="活动描述" />
                      <el-table-column prop="participants" label="参与人员" width="150" />
                      <el-table-column prop="location" label="地点" width="120" />
                      <el-table-column label="操作" width="180">
                        <template #default>
                          <el-button type="text">
                            查看详情
                          </el-button>
                          <el-button type="text">
                            编辑
                          </el-button>
                          <el-button type="text">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="证据材料" name="evidence">
                  <div class="mb-6 flex items-center space-x-4">
                    <el-select v-model="evidenceFilter.type" placeholder="证据类型" clearable>
                      <el-option label="文档" value="document" />
                      <el-option label="图片" value="image" />
                      <el-option label="音视频" value="media" />
                      <el-option label="日志" value="log" />
                    </el-select>
                    <el-select v-model="evidenceFilter.status" placeholder="证据状态" clearable>
                      <el-option label="待验证" value="pending" />
                      <el-option label="已验证" value="verified" />
                      <el-option label="已驳回" value="rejected" />
                    </el-select>
                    <el-input v-model="evidenceFilter.keyword" placeholder="搜索证据" suffix-icon="Search" />
                    <el-button type="primary" class="!rounded-button whitespace-nowrap">
                      上传证据
                    </el-button>
                  </div>

                  <el-table :data="filteredEvidence" border>
                    <el-table-column prop="id" label="证据ID" width="120" />
                    <el-table-column prop="name" label="证据名称" width="180" />
                    <el-table-column prop="type" label="证据类型" width="120">
                      <template #default="{ row }">
                        <el-tag :type="row.type === 'document' ? '' : 'info'">
                          {{ row.type }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="uploadTime" label="上传时间" width="150" />
                    <el-table-column prop="uploader" label="上传人" width="120" />
                    <el-table-column prop="category" label="证据分类" width="120" />
                    <el-table-column prop="status" label="状态" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getEvidenceStatusTagType(row.status)">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="180">
                      <template #default>
                        <el-button type="text">
                          查看
                        </el-button>
                        <el-button type="text">
                          验证
                        </el-button>
                        <el-button type="text">
                          下载
                        </el-button>
                        <el-button type="text">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>

                <el-tab-pane label="调查发现" name="findings">
                  <div class="mb-6">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap">
                      新增发现
                    </el-button>
                  </div>
                  <el-table :data="investigationFindings" border>
                    <el-table-column prop="id" label="发现ID" width="120" />
                    <el-table-column prop="description" label="发现描述" width="300" />
                    <el-table-column prop="severity" label="严重程度" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getSeverityTagType(row.severity)">
                          {{ row.severity }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="date" label="发现日期" width="120" />
                    <el-table-column prop="finder" label="发现人" width="120" />
                    <el-table-column prop="relatedPersons" label="涉及人员" width="150" />
                    <el-table-column prop="status" label="状态" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getFindingStatusTagType(row.status)">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="180">
                      <template #default>
                        <el-button type="text">
                          查看详情
                        </el-button>
                        <el-button type="text">
                          编辑
                        </el-button>
                        <el-button type="text">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>

                <el-tab-pane label="调查结论" name="conclusion">
                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      调查结论
                    </h3>
                    <el-select v-model="conclusionStatus" class="mt-2" placeholder="结论状态">
                      <el-option label="初步结论" value="draft" />
                      <el-option label="最终结论" value="final" />
                    </el-select>
                    <el-input
                      v-model="conclusionContent" type="textarea" :rows="4" placeholder="请输入调查结论"
                      class="mt-4"
                    />
                    <div class="mt-4 flex items-center justify-between">
                      <div>
                        <span class="text-sm text-gray-500">结论日期: 2023-05-08</span>
                        <span class="ml-4 text-sm text-gray-500">结论人: 张明远</span>
                      </div>
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        编辑结论
                      </el-button>
                    </div>
                  </div>

                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      结论生成助手
                    </h3>
                    <div class="mt-4 flex space-x-4">
                      <el-button class="!rounded-button whitespace-nowrap">
                        AI生成结论
                      </el-button>
                      <el-button class="!rounded-button whitespace-nowrap">
                        结论优化
                      </el-button>
                    </div>
                    <div class="mt-4 border border-gray-200 rounded p-4">
                      <p class="text-gray-700">
                        基于当前调查发现，AI生成的初步结论将显示在这里...
                      </p>
                    </div>
                  </div>

                  <div class="mb-6">
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-bold">
                        违规认定
                      </h3>
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        新增违规认定
                      </el-button>
                    </div>
                    <el-table :data="violationRecords" class="mt-4" border>
                      <el-table-column prop="type" label="违规类型" width="120" />
                      <el-table-column prop="description" label="违规描述" width="300" />
                      <el-table-column prop="basis" label="违规依据" width="200" />
                      <el-table-column prop="responsible" label="责任人/部门" width="150" />
                      <el-table-column prop="suggestion" label="建议处理措施" />
                      <el-table-column label="操作" width="120">
                        <template #default>
                          <el-button type="text">
                            编辑
                          </el-button>
                          <el-button type="text">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <div>
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-bold">
                        建议措施
                      </h3>
                      <el-button type="primary" class="!rounded-button whitespace-nowrap">
                        新增建议措施
                      </el-button>
                    </div>
                    <el-table :data="recommendations" class="mt-4" border>
                      <el-table-column prop="type" label="措施类型" width="120" />
                      <el-table-column prop="description" label="措施描述" width="300" />
                      <el-table-column prop="department" label="建议部门" width="150" />
                      <el-table-column prop="priority" label="优先级" width="100">
                        <template #default="{ row }">
                          <el-tag :type="row.priority === '高' ? 'danger' : row.priority === '中' ? 'warning' : ''">
                            {{ row.priority }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="effect" label="预期效果" />
                      <el-table-column label="操作" width="120">
                        <template #default>
                          <el-button type="text">
                            编辑
                          </el-button>
                          <el-button type="text">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="操作历史" name="history">
                  <div class="mb-6">
                    <h3 class="text-lg font-bold">
                      操作日志
                    </h3>
                    <div class="mt-4">
                      <el-timeline>
                        <el-timeline-item
                          v-for="(log, index) in operationLogs" :key="index" :timestamp="log.time"
                          placement="top"
                        >
                          <el-card>
                            <div class="flex items-center justify-between">
                              <div>
                                <span class="font-medium">{{ log.operator }}</span>
                                <span class="ml-2 text-gray-500">{{ log.type }}</span>
                              </div>
                              <span class="text-sm text-gray-500">{{ log.ip }}</span>
                            </div>
                            <p class="mt-2 text-gray-700">
                              {{ log.content }}
                            </p>
                          </el-card>
                        </el-timeline-item>
                      </el-timeline>
                    </div>
                  </div>

                  <div>
                    <h3 class="text-lg font-bold">
                      变更记录
                    </h3>
                    <el-table :data="changeRecords" class="mt-4" border>
                      <el-table-column prop="time" label="变更时间" width="150" />
                      <el-table-column prop="changer" label="变更人" width="120" />
                      <el-table-column prop="field" label="变更字段" width="150" />
                      <el-table-column prop="before" label="变更前值" width="200" />
                      <el-table-column prop="after" label="变更后值" width="200" />
                      <el-table-column prop="reason" label="变更原因" />
                    </el-table>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="">
              <template #header>
                <div class="f-16 fw-600">
                  任务时间线
                </div>
              </template>
              <el-timeline class="mt-4">
                <el-timeline-item timestamp="2023-04-20" placement="top">
                  事件发现
                </el-timeline-item>
                <el-timeline-item timestamp="2023-04-25" placement="top">
                  调查启动
                </el-timeline-item>
                <el-timeline-item timestamp="2023-04-28" placement="top">
                  初步分析
                </el-timeline-item>
                <el-timeline-item timestamp="2023-05-03" placement="top" type="primary" color="#409EFF">
                  当前阶段
                </el-timeline-item>
                <el-timeline-item timestamp="2023-05-10" placement="top">
                  计划完成
                </el-timeline-item>
              </el-timeline>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    相关人员
                  </div>
                  <el-button type="text">
                    添加人员
                  </el-button>
                </div>
              </template>
              <div class="mt-4 space-y-4">
                <div v-for="person in relatedPersons" :key="person.name" class="flex items-center">
                  <div class="mr-3 h-10 w-10 rounded-full bg-gray-200" />
                  <div>
                    <p class="font-medium">
                      {{ person.name }}
                    </p>
                    <p class="text-sm text-gray-500">
                      {{ person.role }} · {{ person.department }}
                    </p>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关调查
                </div>
              </template>
              <div class="mt-4 space-y-4">
                <div
                  v-for="survey in relatedSurveys" :key="survey.id"
                  class="cursor-pointer border border-gray-200 rounded p-3 hover:bg-gray-50"
                >
                  <p class="font-medium">
                    {{ survey.title }}
                  </p>
                  <div class="mt-1 flex items-center justify-between">
                    <el-tag :type="survey.status === '已完成' ? 'success' : 'warning'" size="small">
                      {{ survey.status }}
                    </el-tag>
                    <span class="text-sm text-gray-500">{{ survey.relation }}</span>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="mt-4 space-y-3">
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Notebook />
                  </el-icon> 添加调查记录
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Upload />
                  </el-icon> 上传证据
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Search />
                  </el-icon> 添加调查发现
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <Document />
                  </el-icon> 生成调查报告
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <VideoCamera />
                  </el-icon> 发起会议
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap">
                  <el-icon>
                    <List />
                  </el-icon> 创建处理措施
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
  }
</style>
