<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { computed, ref } from 'vue'
import {
  ArrowRight,
  Document,
  Download,
  Edit,
  Printer,
  Share,
} from '@element-plus/icons-vue'

const report = ref({
  id: 'INV-2023-0042',
  title: '关于市场部违规使用营销资金的调查报告',
  relatedInvestigation: '市场部营销资金使用专项调查',
  type: '专项调查报告',
  author: '张明远',
  createdAt: '2023-06-15',
  reviewer: '李华',
  reviewedAt: '2023-06-20',
  status: 'reviewing',
  publishedAt: '',
  version: '1.2',
  wordCount: '12,500字',
  readingTime: '约25分钟',
  sharePermission: '部门内可查看',
  shareScope: '合规部、市场部',
  summary: '本次调查针对市场部在2023年第一季度营销资金使用情况进行专项审查。调查发现存在未经审批的大额资金支出、虚假报销等问题。调查团队通过文件审查、人员访谈等方式收集证据，确认违规事实存在。报告提出了整改建议和后续监管措施。',
  background: '2023年4月，财务部在日常审计中发现市场部营销资金使用存在异常。经初步核查，发现多笔大额支出未经审批流程，部分报销凭证存在疑点。公司管理层决定成立专项调查组进行深入调查。',
  methodology: '调查采用文件审查、人员访谈和数据分析相结合的方法。调查团队由合规部3名专员组成，历时2周完成。共审查财务凭证87份，访谈相关人员12人次，分析交易数据300余条。',
  process: '2023年5月10日-12日：收集基础财务数据\n2023年5月15日-18日：访谈市场部相关人员\n2023年5月20日-22日：现场核查可疑交易\n2023年5月25日-30日：整理证据并形成初步结论',
  findings: '1. 发现3笔合计12.8万元的营销支出未经审批流程\n2. 5笔合计4.2万元的报销凭证存在虚假内容\n3. 2名员工存在违规操作行为\n4. 部门审批流程存在漏洞',
  conclusion: '调查确认市场部存在违规使用营销资金的行为，涉及金额17万元。主要责任人为市场部副经理王某和专员张某。违规行为已对公司造成经济损失和管理风险。',
  recommendations: '1. 对责任人进行纪律处分\n2. 追回违规资金\n3. 完善营销资金审批流程\n4. 加强部门合规培训\n5. 建立定期审计机制',
  attachments: [
    { name: '访谈记录汇总', type: '文档', uploadTime: '2023-06-10', uploader: '张明远', description: '12人次访谈记录' },
    { name: '财务凭证', type: '扫描件', uploadTime: '2023-06-12', uploader: '张明远', description: '87份凭证扫描件' },
    { name: '数据分析报告', type: '报表', uploadTime: '2023-06-14', uploader: '李娜', description: '交易数据分析' },
  ],
  evidences: [
    { name: '银行转账记录', type: '财务凭证', status: '已验证' },
    { name: '报销申请表', type: '审批文件', status: '存疑' },
    { name: '会议纪要', type: '内部文件', status: '已验证' },
  ],
  relatedReports: [
    { title: '市场部2022年度合规报告' },
    { title: '营销资金使用规范(2023版)' },
    { title: '财务审计异常报告(2023Q1)' },
  ],
})

const approvalSteps = ref([
  { name: '提交', active: false },
  { name: '部门审核', active: true },
  { name: '合规审核', active: false },
  { name: '管理层审批', active: false },
])

const approvalRecords = ref([
  { reviewer: '张明远', time: '2023-06-15 14:30', result: '提交', comment: '报告初稿已完成' },
  { reviewer: '王丽', time: '2023-06-18 10:15', result: '审核中', comment: '正在核实证据材料' },
])

const approvalResult = ref('approved')
const approvalComment = ref('')
const isReviewer = ref(true)
const isCreator = ref(true)

const statusText = computed(() => {
  switch (report.value.status) {
    case 'draft': return '草稿'
    case 'reviewing': return '审核中'
    case 'published': return '已发布'
    case 'rejected': return '已驳回'
    default: return ''
  }
})

const statusTagType = computed(() => {
  switch (report.value.status) {
    case 'draft': return 'info'
    case 'reviewing': return 'warning'
    case 'published': return 'success'
    case 'rejected': return 'danger'
    default: return ''
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              关于市场部违规使用营销资金的调查报告
            </h1>
            <el-tag type="warning" class="ml-4">
              进行中
            </el-tag>
          </div>
          <div class="flex items-center space-x-4">
            <el-button
              v-if="report.status === 'draft' || report.status === 'rejected'" type="primary"
              class="!rounded-button whitespace-nowrap"
            >
              <el-icon>
                <Edit />
              </el-icon>
              <span>编辑</span>
            </el-button>
            <el-button v-if="report.status === 'reviewing'" type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Document />
              </el-icon>
              <span>审批</span>
            </el-button>
            <el-dropdown>
              <el-button type="primary" class="!rounded-button whitespace-nowrap">
                <el-icon>
                  <Download />
                </el-icon>
                <span>导出</span>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>PDF</el-dropdown-item>
                  <el-dropdown-item>Word</el-dropdown-item>
                  <el-dropdown-item>PPT</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Printer />
              </el-icon>
              <span>打印</span>
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Share />
              </el-icon>
              <span>分享</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <p class="text-gray-500">
                    报告编号
                  </p>
                  <p class="font-medium">
                    {{ report.id }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    报告标题
                  </p>
                  <p class="font-medium">
                    {{ report.title }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    关联调查
                  </p>
                  <el-link type="primary">
                    {{ report.relatedInvestigation }}
                  </el-link>
                </div>
                <div>
                  <p class="text-gray-500">
                    报告类型
                  </p>
                  <p class="font-medium">
                    {{ report.type }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    编制人
                  </p>
                  <p class="font-medium">
                    {{ report.author }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    编制日期
                  </p>
                  <p class="font-medium">
                    {{ report.createdAt }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    审核人
                  </p>
                  <p class="font-medium">
                    {{ report.reviewer || '--' }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    审核日期
                  </p>
                  <p class="font-medium">
                    {{ report.reviewedAt || '--' }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    状态
                  </p>
                  <p class="font-medium">
                    {{ statusText }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    发布日期
                  </p>
                  <p class="font-medium">
                    {{ report.publishedAt || '--' }}
                  </p>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <h2 class="mb-4 text-lg font-bold">
                调查报告摘要
              </h2>
              <p class="mb-6">
                {{ report.summary }}
              </p>

              <h2 class="mb-4 text-lg font-bold">
                一、调查背景
              </h2>
              <p class="mb-6">
                {{ report.background }}
              </p>

              <h2 class="mb-4 text-lg font-bold">
                二、调查方法
              </h2>
              <p class="mb-6">
                {{ report.methodology }}
              </p>

              <h2 class="mb-4 text-lg font-bold">
                三、调查过程
              </h2>
              <p class="mb-6">
                {{ report.process }}
              </p>

              <h2 class="mb-4 text-lg font-bold">
                四、调查发现
              </h2>
              <p class="mb-6">
                {{ report.findings }}
              </p>

              <h2 class="mb-4 text-lg font-bold">
                五、调查结论
              </h2>
              <p class="mb-6">
                {{ report.conclusion }}
              </p>

              <h2 class="mb-4 text-lg font-bold">
                六、建议措施
              </h2>
              <p class="mb-6">
                {{ report.recommendations }}
              </p>

              <h2 class="mb-4 text-lg font-bold">
                七、附件
              </h2>
              <el-table :data="report.attachments" style="width: 100%;">
                <el-table-column prop="name" label="附件名称" />
                <el-table-column prop="type" label="类型" />
                <el-table-column prop="uploadTime" label="上传时间" />
                <el-table-column prop="uploader" label="上传人" />
                <el-table-column prop="description" label="描述" />
                <el-table-column label="操作">
                  <template #default="scope">
                    <el-button type="text" size="small">
                      查看
                    </el-button>
                    <el-button type="text" size="small">
                      下载
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
            <el-card shadow="hover" class="!rounded-lg">
              <h2 class="mb-4 text-lg font-bold">
                审批记录
              </h2>
              <div class="mb-6 flex items-center justify-between">
                <div class="flex space-x-4">
                  <div
                    v-for="(step, index) in approvalSteps" :key="index"
                    :class="{ 'text-primary': step.active, 'text-gray-400': !step.active }" class="flex items-center"
                  >
                    <div
                      :class="{ 'bg-primary': step.active, 'bg-gray-300': !step.active }"
                      class="h-8 w-8 flex items-center justify-center rounded-full text-white"
                    >
                      {{ index + 1 }}
                    </div>
                    <span class="ml-2">{{ step.name }}</span>
                    <el-icon v-if="index < approvalSteps.length - 1" class="mx-2">
                      <ArrowRight />
                    </el-icon>
                  </div>
                </div>
              </div>

              <el-timeline>
                <el-timeline-item
                  v-for="(record, index) in approvalRecords" :key="index" :timestamp="record.time"
                  placement="top"
                >
                  <el-card>
                    <h4>{{ record.reviewer }}</h4>
                    <p>{{ record.result }} - {{ record.comment }}</p>
                  </el-card>
                </el-timeline-item>
              </el-timeline>

              <div v-if="isReviewer" class="mt-6">
                <el-form label-width="100px">
                  <el-form-item label="审批结果">
                    <el-radio-group v-model="approvalResult">
                      <el-radio label="approved">
                        同意
                      </el-radio>
                      <el-radio label="rejected">
                        驳回
                      </el-radio>
                      <el-radio label="returned">
                        退回修改
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="审批意见">
                    <el-input v-model="approvalComment" type="textarea" :rows="4" placeholder="请输入审批意见" />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" class="!rounded-button whitespace-nowrap">
                      提交审批
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-card>

            <!-- Related Materials -->
            <el-card shadow="hover" class="!rounded-lg">
              <h2 class="mb-4 text-lg font-bold">
                相关资料
              </h2>
              <el-table :data="report.evidences" style="width: 100%;">
                <el-table-column prop="name" label="证据名称" />
                <el-table-column prop="type" label="类型" />
                <el-table-column prop="status" label="状态" />
                <el-table-column label="操作">
                  <template #default>
                    <el-button type="text" size="small">
                      查看详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="">
              <template #header>
                <div class="f-16 fw-600">
                  报告信息
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <p class="text-gray-500">
                    版本
                  </p>
                  <p class="font-medium">
                    {{ report.version }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    字数
                  </p>
                  <p class="font-medium">
                    {{ report.wordCount }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    阅读时间
                  </p>
                  <p class="font-medium">
                    {{ report.readingTime }}
                  </p>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    分享设置
                  </div>
                  <el-button v-if="isCreator" type="text" size="small">
                    修改权限
                  </el-button>
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <p class="text-gray-500">
                    权限
                  </p>
                  <p class="font-medium">
                    {{ report.sharePermission }}
                  </p>
                </div>
                <div>
                  <p class="text-gray-500">
                    范围
                  </p>
                  <p class="font-medium">
                    {{ report.shareScope }}
                  </p>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关报告
                </div>
              </template>
              <div class="space-y-3">
                <el-link v-for="(related, index) in report.relatedReports" :key="index" type="primary" class="block">
                  {{ related.title }}
                </el-link>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <el-button type="primary" class="!rounded-button w-full whitespace-nowrap">
                    导出PDF
                  </el-button>
                </div>
                <div>
                  <el-button type="primary" class="!rounded-button w-full whitespace-nowrap">
                    生成PPT
                  </el-button>
                </div>
                <div>
                  <el-button type="primary" class="!rounded-button w-full whitespace-nowrap">
                    添加附件
                  </el-button>
                </div>
                <div>
                  <el-button type="primary" class="!rounded-button w-full whitespace-nowrap">
                    发起会议
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style scoped>
  .container {
    max-width: 1440px;
  }

  .el-timeline {
    padding-left: 20px;
  }

  .el-timeline-item {
    padding-bottom: 16px;
  }

  .el-card {
    margin-bottom: 24px;
  }

  .el-card:last-child {
    margin-bottom: 0;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
