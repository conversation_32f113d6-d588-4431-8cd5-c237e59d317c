<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Clock, DocumentAdd, Download, Edit, Message, MoreFilled, Phone, Plus, Printer,
} from '@element-plus/icons-vue'

const activeTab = ref('progress')

const progressRecords = ref([
  { date: '2023-05-10 14:30', user: '张明远', content: '完成报销流程重新设计，已提交审核', percent: 30 },
  { date: '2023-05-05 09:15', user: '张明远', content: '召开整改启动会议，明确分工', percent: 15 },
  { date: '2023-04-28 16:45', user: '李华', content: '制定初步整改计划', percent: 5 },
])

const delayRecords = ref([
  { date: '2023-05-08', applicant: '张明远', originalDate: '2023-05-20', newDate: '2023-05-30', reason: '需要增加培训环节', status: '已批准' },
])

const tasks = ref([
  { name: '报销流程重新设计', owner: '张明远', startDate: '2023-05-01', endDate: '2023-05-10', status: '已完成', percent: 100 },
  { name: '票据审核标准制定', owner: '王丽', startDate: '2023-05-05', endDate: '2023-05-15', status: '进行中', percent: 60 },
  { name: '财务人员培训计划', owner: '李强', startDate: '2023-05-10', endDate: '2023-05-20', status: '未开始', percent: 0 },
  { name: '抽查复核机制建立', owner: '赵敏', startDate: '2023-05-15', endDate: '2023-05-25', status: '未开始', percent: 0 },
])

const issues = ref([
  { description: '新流程中部门经理审批节点缺失', date: '2023-05-12', founder: '王丽', severity: '严重', status: '已解决', solution: '增加部门经理审批节点', solveDate: '2023-05-13' },
  { description: '培训材料不完善', date: '2023-05-08', founder: '李强', severity: '中等', status: '处理中', solution: '补充案例材料', solveDate: '' },
])

const materials = ref([
  { name: '财务报销流程规范V2.0', type: '整改方案', uploadDate: '2023-05-10', uploader: '张明远', description: '最新版报销流程规范' },
  { name: '票据审核标准手册', type: '执行记录', uploadDate: '2023-05-12', uploader: '王丽', description: '各类票据审核标准' },
  { name: '培训会议纪要', type: '执行记录', uploadDate: '2023-05-05', uploader: '李华', description: '整改启动会议记录' },
])

const acceptanceChecks = ref([
  { item: '流程设计完整性', requirement: '流程覆盖所有业务场景，无重大漏洞', result: '符合', note: '', evidence: '' },
  { item: '票据审核标准', requirement: '明确各类票据要求，可操作性强', result: '部分符合', note: '差旅票据需补充', evidence: '' },
  { item: '人员培训', requirement: '培训覆盖率100%，考核通过率90%以上', result: '未检查', note: '', evidence: '' },
])

const acceptanceForm = ref({
  conclusion: '',
  opinion: '',
  suggestion: '',
})

const timeline = ref([
  { title: '创建整改', date: '2023-04-25' },
  { title: '整改开始', date: '2023-04-28' },
  { title: '当前进度', date: '2023-05-10' },
  { title: '计划完成', date: '2023-05-30' },
  { title: '实际完成', date: '-' },
])

const relatedPersons = ref([
  { id: 1, name: '张明远', role: '负责人', department: '财务部' },
  { id: 2, name: '李华', role: '监督人', department: '审计部' },
  { id: 3, name: '王丽', role: '参与人员', department: '财务部' },
  { id: 4, name: '李强', role: '参与人员', department: '人力资源部' },
  { id: 5, name: '赵敏', role: '参与人员', department: '财务部' },
])

const relatedRectifications = ref([
  { id: 1, name: '采购审批流程优化整改', status: '已完成', percent: 100 },
  { id: 2, name: '合同管理流程整改', status: '进行中', percent: 65 },
  { id: 3, name: '固定资产盘点整改', status: '进行中', percent: 30 },
])

const progressChart = ref<HTMLElement>()
const taskStatusChart = ref<HTMLElement>()
const taskOwnerChart = ref<HTMLElement>()

function getStatusTagType(status: string) {
  switch (status) {
    case '已完成': return 'success'
    case '进行中': return 'primary'
    case '已逾期': return 'danger'
    default: return 'info'
  }
}

function getSeverityTagType(severity: string) {
  switch (severity) {
    case '严重': return 'danger'
    case '中等': return 'warning'
    default: return 'info'
  }
}

function getAcceptanceTagType(result: string) {
  switch (result) {
    case '符合': return 'success'
    case '部分符合': return 'warning'
    case '不符合': return 'danger'
    default: return 'info'
  }
}

onMounted(() => {
  nextTick(() => {
    if (progressChart.value) {
      const chart = echarts.init(progressChart.value)
      chart.setOption({
        animation: false,
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: ['计划进度', '实际进度'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['04-25', '04-28', '05-05', '05-10', '05-15', '05-20', '05-25', '05-30'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '计划进度',
            type: 'line',
            data: [0, 10, 20, 40, 60, 80, 95, 100],
            itemStyle: {
              color: '#a8a8a8',
            },
            lineStyle: {
              type: 'dashed',
            },
          },
          {
            name: '实际进度',
            type: 'line',
            data: [0, 5, 15, 30, null, null, null, null],
            itemStyle: {
              color: '#409EFF',
            },
          },
        ],
      })
    }

    if (taskStatusChart.value) {
      const chart = echarts.init(taskStatusChart.value)
      chart.setOption({
        animation: false,
        title: {
          text: '任务状态分布',
          left: 'center',
        },
        tooltip: {
          trigger: 'item',
        },
        series: [
          {
            name: '任务状态',
            type: 'pie',
            radius: '70%',
            data: [
              { value: 1, name: '已完成' },
              { value: 1, name: '进行中' },
              { value: 2, name: '未开始' },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
          },
        ],
      })
    }

    if (taskOwnerChart.value) {
      const chart = echarts.init(taskOwnerChart.value)
      chart.setOption({
        animation: false,
        title: {
          text: '负责人任务完成情况',
          left: 'center',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
        },
        yAxis: {
          type: 'category',
          data: ['张明远', '王丽', '李强', '赵敏'],
        },
        series: [
          {
            name: '完成百分比',
            type: 'bar',
            data: [100, 60, 0, 0],
            itemStyle: {
              color: '#409EFF',
            },
          },
        ],
      })
    }
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              关于财务报销流程违规问题的整改
            </h1>
            <el-tag type="warning" class="ml-4">
              进行中
            </el-tag>
          </div>
          <div class="flex items-center space-x-4">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              更新进度
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              完成整改
            </el-button>
            <el-button plain>
              <el-icon>
                <Printer />
              </el-icon>
            </el-button>
            <el-button plain>
              <el-icon>
                <Download />
              </el-icon>
            </el-button>
            <el-dropdown>
              <el-button plain>
                <el-icon>
                  <MoreFilled />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>暂停</el-dropdown-item>
                  <el-dropdown-item>终止</el-dropdown-item>
                  <el-dropdown-item>分享</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!--            <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template> -->
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <p class="text-sm text-gray-500">
                    整改编号
                  </p>
                  <p class="text-gray-800">
                    ZD-2023-0425
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    整改类型
                  </p>
                  <p class="text-gray-800">
                    流程优化
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    优先级
                  </p>
                  <p class="text-red-500 font-medium">
                    高
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    关联处理
                  </p>
                  <p class="cursor-pointer text-blue-600">
                    CL-2023-0425
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    责任部门
                  </p>
                  <p class="text-gray-800">
                    财务部
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    负责人
                  </p>
                  <p class="text-gray-800">
                    张明远
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    监督人
                  </p>
                  <p class="text-gray-800">
                    李华
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    开始日期
                  </p>
                  <p class="text-gray-800">
                    2023-04-25
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    计划完成日期
                  </p>
                  <p class="text-gray-800">
                    2023-05-30
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    实际完成日期
                  </p>
                  <p class="text-gray-800">
                    -
                  </p>
                </div>
              </div>
              <div class="mt-6">
                <div class="mb-2 flex justify-between">
                  <span class="text-sm text-gray-600">进度: 45%</span>
                  <span class="text-sm text-gray-600">剩余 15 天</span>
                </div>
                <el-progress :percentage="45" :show-text="false" />
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  整改内容
                </div>
              </template>
              <div class="space-y-6">
                <div>
                  <h3 class="mb-2 font-medium">
                    整改要求
                  </h3>
                  <p class="text-gray-700 leading-relaxed">
                    针对审计发现的财务报销流程中存在的审批不严、票据不规范等问题进行全面整改。要求重新梳理报销流程，明确各环节审批权限，建立票据审核标准，加强财务人员培训，确保报销流程合规性。
                  </p>
                </div>
                <div>
                  <h3 class="mb-2 font-medium">
                    整改方案
                  </h3>
                  <p class="text-gray-700 leading-relaxed">
                    1. 成立专项整改小组，由财务部牵头，审计部配合<br>
                    2. 重新设计报销审批流程，增加关键节点控制<br>
                    3. 制定票据审核标准手册，明确各类票据要求<br>
                    4. 组织财务人员专项培训，提升审核能力<br>
                    5. 建立抽查复核机制，确保执行效果
                  </p>
                </div>
                <div>
                  <h3 class="mb-2 font-medium">
                    验收标准
                  </h3>
                  <p class="text-gray-700 leading-relaxed">
                    1. 新流程运行稳定，无重大漏洞<br>
                    2. 抽查票据合格率达到95%以上<br>
                    3. 财务人员培训覆盖率100%，考核通过率90%以上<br>
                    4. 建立完善的监督抽查机制<br>
                    5. 相关制度文件完整归档
                  </p>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <!-- <template #header>
                <div class="f-16 fw-600">整改内容</div>
              </template> -->
              <el-tabs v-model="activeTab" class="p-6">
                <el-tab-pane label="进度跟踪" name="progress">
                  <div class="space-y-6">
                    <div>
                      <h3 class="mb-4 font-medium">
                        进度更新记录
                      </h3>
                      <div class="space-y-4">
                        <div
                          v-for="(item, index) in progressRecords" :key="index"
                          class="border-l-2 border-blue-500 py-2 pl-4"
                        >
                          <div class="flex justify-between">
                            <span class="text-sm text-gray-600">{{ item.date }}</span>
                            <span class="text-sm text-gray-600">{{ item.user }}</span>
                          </div>
                          <p class="mt-1 text-gray-800">
                            {{ item.content }}
                          </p>
                          <div class="mt-2 flex items-center">
                            <el-progress :percentage="item.percent" :show-text="false" class="mr-4 w-32" />
                            <span class="text-sm text-gray-600">{{ item.percent }}%</span>
                          </div>
                        </div>
                      </div>
                      <el-button type="primary" class="!rounded-button mt-4 whitespace-nowrap">
                        更新进度
                      </el-button>
                    </div>

                    <div>
                      <h3 class="mb-4 font-medium">
                        进度图表
                      </h3>
                      <div ref="progressChart" class="h-64" />
                    </div>

                    <div>
                      <h3 class="mb-4 font-medium">
                        延期记录
                      </h3>
                      <el-table :data="delayRecords" style="width: 100%;">
                        <el-table-column prop="date" label="申请时间" width="150" />
                        <el-table-column prop="applicant" label="申请人" width="120" />
                        <el-table-column prop="originalDate" label="原计划日期" width="120" />
                        <el-table-column prop="newDate" label="延期日期" width="120" />
                        <el-table-column prop="reason" label="延期原因" />
                        <el-table-column prop="status" label="审批状态" width="120">
                          <template #default="{ row }">
                            <el-tag :type="row.status === '已批准' ? 'success' : 'warning'">
                              {{ row.status }}
                            </el-tag>
                          </template>
                        </el-table-column>
                      </el-table>
                      <el-button type="primary" class="!rounded-button mt-4 whitespace-nowrap">
                        申请延期
                      </el-button>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="整改任务" name="tasks">
                  <div class="space-y-6">
                    <div>
                      <el-button type="primary" class="!rounded-button mb-4 whitespace-nowrap">
                        新增任务
                      </el-button>
                      <el-table :data="tasks" style="width: 100%;">
                        <el-table-column prop="name" label="任务名称" />
                        <el-table-column prop="owner" label="负责人" width="120" />
                        <el-table-column prop="startDate" label="开始日期" width="120" />
                        <el-table-column prop="endDate" label="计划完成日期" width="120" />
                        <el-table-column prop="status" label="状态" width="120">
                          <template #default="{ row }">
                            <el-tag :type="getStatusTagType(row.status)">
                              {{ row.status }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="percent" label="完成百分比" width="120">
                          <template #default="{ row }">
                            <el-progress :percentage="row.percent" :show-text="false" />
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="120">
                          <template #default>
                            <el-button link type="primary">
                              查看
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>

                    <div>
                      <h3 class="mb-4 font-medium">
                        任务进度统计
                      </h3>
                      <div class="grid grid-cols-2 gap-4">
                        <div ref="taskStatusChart" class="h-64" />
                        <div ref="taskOwnerChart" class="h-64" />
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="问题记录" name="issues">
                  <div class="space-y-6">
                    <el-button type="primary" class="!rounded-button mb-4 whitespace-nowrap">
                      新增问题
                    </el-button>
                    <el-table :data="issues" style="width: 100%;">
                      <el-table-column prop="description" label="问题描述" />
                      <el-table-column prop="date" label="发现时间" width="150" />
                      <el-table-column prop="founder" label="发现人" width="120" />
                      <el-table-column prop="severity" label="严重程度" width="120">
                        <template #default="{ row }">
                          <el-tag :type="getSeverityTagType(row.severity)">
                            {{ row.severity }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="status" label="状态" width="120">
                        <template #default="{ row }">
                          <el-tag :type="getStatusTagType(row.status)">
                            {{ row.status }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="solution" label="解决方案" />
                      <el-table-column prop="solveDate" label="解决时间" width="150" />
                      <el-table-column label="操作" width="120">
                        <template #default>
                          <el-button link type="primary">
                            查看
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="相关资料" name="materials">
                  <div class="space-y-6">
                    <el-button type="primary" class="!rounded-button mb-4 whitespace-nowrap">
                      上传资料
                    </el-button>
                    <el-table :data="materials" style="width: 100%;">
                      <el-table-column prop="name" label="资料名称" />
                      <el-table-column prop="type" label="资料类型" width="120" />
                      <el-table-column prop="uploadDate" label="上传时间" width="150" />
                      <el-table-column prop="uploader" label="上传人" width="120" />
                      <el-table-column prop="description" label="描述" />
                      <el-table-column label="操作" width="180">
                        <template #default>
                          <el-button link type="primary">
                            查看
                          </el-button>
                          <el-button link type="primary">
                            下载
                          </el-button>
                          <el-button link type="danger">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="验收评估" name="acceptance">
                  <div class="space-y-6">
                    <div>
                      <h3 class="mb-2 font-medium">
                        验收状态
                      </h3>
                      <div class="flex items-center">
                        <el-tag type="info" class="mr-4">
                          未验收
                        </el-tag>
                        <span class="text-sm text-gray-600">验收时间: -</span>
                        <span class="ml-4 text-sm text-gray-600">验收人: -</span>
                      </div>
                    </div>

                    <div>
                      <h3 class="mb-4 font-medium">
                        验收标准检查
                      </h3>
                      <el-table :data="acceptanceChecks" style="width: 100%;">
                        <el-table-column prop="item" label="检查项" width="200" />
                        <el-table-column prop="requirement" label="标准要求" />
                        <el-table-column prop="result" label="检查结果" width="120">
                          <template #default="{ row }">
                            <el-tag :type="getAcceptanceTagType(row.result)">
                              {{ row.result }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="note" label="备注" width="200" />
                        <el-table-column prop="evidence" label="证据" width="120">
                          <template #default>
                            <el-button link type="primary">
                              查看
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                      <el-button type="primary" class="!rounded-button mt-4 whitespace-nowrap">
                        开始验收
                      </el-button>
                    </div>

                    <div>
                      <h3 class="mb-2 font-medium">
                        验收意见
                      </h3>
                      <el-form :model="acceptanceForm" label-width="100px">
                        <el-form-item label="验收结论">
                          <el-select v-model="acceptanceForm.conclusion" placeholder="请选择">
                            <el-option label="通过" value="pass" />
                            <el-option label="有条件通过" value="conditional" />
                            <el-option label="不通过" value="fail" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="验收意见">
                          <el-input v-model="acceptanceForm.opinion" type="textarea" :rows="3" />
                        </el-form-item>
                        <el-form-item label="整改建议">
                          <el-input v-model="acceptanceForm.suggestion" type="textarea" :rows="3" />
                        </el-form-item>
                        <el-form-item>
                          <el-button type="primary" class="!rounded-button whitespace-nowrap">
                            提交验收
                          </el-button>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  整改时间线
                </div>
              </template>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关人员
                </div>
                <div class="space-y-4">
                  <div
                    v-for="person in relatedPersons" :key="person.id"
                    class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50"
                  >
                    <div
                      class="mr-3 h-10 w-10 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 font-medium"
                    >
                      {{ person.name.substring(0, 1) }}
                    </div>
                    <div>
                      <p class="font-medium">
                        {{ person.name }}
                      </p>
                      <div class="mt-1 flex items-center">
                        <el-tag
                          :type="person.role === '负责人' ? 'primary' : 'info'" size="small"
                          class="mr-2"
                        >
                          {{ person.role }}
                        </el-tag>
                        <span class="text-xs text-gray-500">{{ person.department }}</span>
                      </div>
                    </div>
                    <div class="ml-auto flex space-x-2">
                      <el-button circle size="small">
                        <el-icon>
                          <Phone />
                        </el-icon>
                      </el-button>
                      <el-button circle size="small">
                        <el-icon>
                          <Message />
                        </el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </template>
            </el-card>

            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关整改
                </div>
                <div class="space-y-4">
                  <div
                    v-for="item in relatedRectifications" :key="item.id"
                    class="cursor-pointer border border-gray-100 rounded p-3 hover:bg-gray-50"
                  >
                    <p class="truncate font-medium">
                      {{ item.name }}
                    </p>
                    <div class="mt-2 flex items-center">
                      <el-tag
                        :type="item.status === '已完成' ? 'success' : 'warning'" size="small"
                        class="mr-2"
                      >
                        {{ item.status }}
                      </el-tag>
                      <el-progress :percentage="item.percent" :show-text="false" class="flex-1" />
                    </div>
                  </div>
                  <el-button link type="primary" class="w-full">
                    查看更多
                  </el-button>
                </div>
              </template>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <el-button type="primary" class="!rounded-button w-full whitespace-nowrap">
                    <el-icon class="mr-2">
                      <Edit />
                    </el-icon>
                    更新进度
                  </el-button>
                </div>
                <div>
                  <el-button type="primary" class="!rounded-button w-full whitespace-nowrap">
                    <el-icon class="mr-2">
                      <Plus />
                    </el-icon>
                    添加任务
                  </el-button>
                </div>
                <div>
                  <el-button class="!rounded-button w-full whitespace-nowrap">
                    <el-icon class="mr-2">
                      <DocumentAdd />
                    </el-icon>
                    记录问题
                  </el-button>
                </div>
                <div>
                  <el-button class="!rounded-button w-full whitespace-nowrap">
                    <el-icon class="mr-2">
                      <Clock />
                    </el-icon>
                    申请延期
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
  }
</style>
