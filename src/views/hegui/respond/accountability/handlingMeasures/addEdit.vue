<script lang="ts" setup>
import { ref } from 'vue'
import { View } from '@element-plus/icons-vue'

const similarCases = ref([
  {
    id: 1,
    title: '财务部违规报销处理案例',
    type: '经济处罚+警告',
    result: '罚款5000元，书面警告',
  },
  {
    id: 2,
    title: '人事部违规招聘处理案例',
    type: '培训教育+警告',
    result: '完成合规培训，书面警告',
  },
  {
    id: 3,
    title: '信息安全违规处理案例',
    type: '降级+经济处罚',
    result: '降职一级，罚款10000元',
  },
  {
    id: 4,
    title: '采购部违规操作处理案例',
    type: '警告+减少薪酬',
    result: '书面警告，扣减季度奖金20%',
  },
])

function viewCaseDetail(id: number) {
  // 查看案例详情逻辑
  console.log('查看案例详情:', id)
}
// 表单数据
const formData = ref({
  title: '',
  number: '',
  type: '',
  severity: '',
  relatedInvestigation: '',
  responsiblePerson: [],
  processor: '',
  proposedDate: '',
  plannedCompletionDate: '',
  status: '',
  violationDescription: '',
  violationBasis: [],
  violationImpact: '',
  responsibilityDetermination: '',
  treatmentMethods: [],
  treatmentMeasures: [],
  treatmentBasis: '',
  treatmentDescription: '',
  expectedEffect: '',
  treatmentDeadline: '',
  relatedSystems: [],
  relatedCases: [],
  relatedFiles: [],
  notificationRecipients: [],
  notificationMethods: [],
  notificationEvents: [],
  notificationContent: '',
  viewPermission: '',
  specifiedViewers: [],
  editPermission: '',
  specifiedEditors: [],
  confidentialityLevel: '',
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              新增处理措施
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />保存
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />保存并开始处理
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-close mr-1" />取消
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-view mr-1" />预览
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  基本信息
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="处理标题" required>
                  <el-input placeholder="请输入处理标题" class="h-8" />
                </el-form-item>
                <el-form-item label="处理编号">
                  <div class="flex items-center space-x-3">
                    <el-input placeholder="系统自动生成" class="h-8" />
                    <el-checkbox>自动生成</el-checkbox>
                  </div>
                </el-form-item>
                <el-form-item label="处理类型" required>
                  <el-select placeholder="请选择处理类型" class="w-full">
                    <el-option label="警告" value="warning" />
                    <el-option label="培训教育" value="training" />
                    <el-option label="减少薪酬" value="salary_reduction" />
                    <el-option label="降级" value="demotion" />
                    <el-option label="解聘" value="dismissal" />
                    <el-option label="经济处罚" value="fine" />
                    <el-option label="其他" value="other" />
                  </el-select>
                </el-form-item>
                <el-form-item label="严重程度" required>
                  <el-radio-group>
                    <el-radio-button label="严重" class="text-red-500" />
                    <el-radio-button label="中等" class="text-orange-500" />
                    <el-radio-button label="轻微" class="text-yellow-500" />
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="关联调查">
                  <el-select placeholder="请选择关联调查" filterable allow-create class="w-full">
                    <el-option label="调查编号20230001 - 财务违规调查" value="1" />
                    <el-option label="调查编号20230002 - 人事违规调查" value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="责任人/部门" required>
                  <el-select multiple placeholder="请选择责任人/部门" class="w-full">
                    <el-option label="张三 - 财务部" value="1" />
                    <el-option label="李四 - 人事部" value="2" />
                    <el-option label="财务部" value="3" />
                  </el-select>
                </el-form-item>
                <el-form-item label="处理人" required>
                  <el-select placeholder="请选择处理人" class="w-full">
                    <el-option label="王五 - 合规部" value="1" />
                    <el-option label="赵六 - 人事部" value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="提出日期">
                  <el-date-picker type="date" placeholder="选择日期" class="w-full" />
                </el-form-item>
                <el-form-item label="计划完成日期">
                  <el-date-picker type="date" placeholder="选择日期" class="w-full" />
                </el-form-item>
                <el-form-item label="处理状态">
                  <el-radio-group>
                    <el-radio-button label="待处理" />
                    <el-radio-button label="处理中" />
                    <el-radio-button label="已完成" />
                    <el-radio-button label="已暂停" />
                    <el-radio-button label="已取消" />
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  违规情况
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="违规描述" required>
                  <el-input type="textarea" :rows="5" placeholder="请描述违规行为的具体情况" />
                </el-form-item>
                <el-form-item label="违规依据">
                  <div class="w-full">
                    <el-button type="text" icon="el-icon-plus">
                      添加依据
                    </el-button>
                    <el-button type="text" icon="el-icon-document">
                      从法规库选择
                    </el-button>
                    <el-button type="text" icon="el-icon-document">
                      从制度库选择
                    </el-button>
                    <el-table :data="[]" class="mt-3" border>
                      <el-table-column prop="type" label="依据类型" width="150" />
                      <el-table-column prop="name" label="依据名称" />
                      <el-table-column prop="clause" label="具体条款" />
                      <el-table-column label="操作" width="80">
                        <template #default>
                          <el-button type="text" icon="el-icon-delete" class="text-red-500" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="违规影响" required>
                  <el-input type="textarea" :rows="5" placeholder="请描述违规行为的影响和后果" />
                </el-form-item>
                <el-form-item label="责任认定" required>
                  <el-input type="textarea" :rows="5" placeholder="请描述责任认定的依据和结果" />
                </el-form-item>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  处理措施
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="处理方式">
                  <el-checkbox-group>
                    <el-checkbox label="警告通知" />
                    <el-checkbox label="经济处罚" />
                    <el-checkbox label="调整岗位" />
                    <el-checkbox label="培训教育" />
                    <el-checkbox label="解除关系" />
                    <el-checkbox label="其他" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="处理措施">
                  <div class="w-full">
                    <el-button type="text" icon="el-icon-plus">
                      添加措施
                    </el-button>
                    <el-table :data="[]" class="mt-3" border>
                      <el-table-column prop="name" label="措施名称" />
                      <el-table-column prop="type" label="措施类型" width="120" />
                      <el-table-column prop="startDate" label="开始日期" width="120" />
                      <el-table-column prop="endDate" label="完成日期" width="120" />
                      <el-table-column prop="person" label="负责人" width="120" />
                      <el-table-column prop="desc" label="措施描述" />
                      <el-table-column label="操作" width="120">
                        <template #default>
                          <el-button type="text" icon="el-icon-top" />
                          <el-button type="text" icon="el-icon-bottom" />
                          <el-button type="text" icon="el-icon-delete" class="text-red-500" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="处理依据">
                  <el-input type="textarea" :rows="5" placeholder="请输入处理措施的制定依据" />
                </el-form-item>
                <el-form-item label="处理说明">
                  <el-input type="textarea" :rows="5" placeholder="请输入对处理措施的补充说明" />
                </el-form-item>
                <el-form-item label="预期效果">
                  <el-input type="textarea" :rows="5" placeholder="请输入处理措施预期达到的效果" />
                </el-form-item>
                <el-form-item label="处理期限">
                  <el-date-picker type="date" placeholder="选择日期" class="w-full" />
                </el-form-item>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关信息
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="相关制度">
                  <div class="w-full">
                    <el-button type="text" icon="el-icon-plus">
                      添加制度
                    </el-button>
                    <el-table :data="[]" class="mt-3" border>
                      <el-table-column prop="name" label="制度名称" />
                      <el-table-column prop="version" label="版本号" width="120" />
                      <el-table-column label="操作" width="80">
                        <template #default>
                          <el-button type="text" icon="el-icon-delete" class="text-red-500" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="相关案例">
                  <div class="w-full">
                    <el-button type="text" icon="el-icon-plus">
                      添加案例
                    </el-button>
                    <el-table :data="[]" class="mt-3" border>
                      <el-table-column prop="name" label="案例名称" />
                      <el-table-column prop="type" label="类型" width="120" />
                      <el-table-column label="操作" width="80">
                        <template #default>
                          <el-button type="text" icon="el-icon-delete" class="text-red-500" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
                <el-form-item label="关联文件">
                  <div class="w-full">
                    <el-upload action="#" list-type="text">
                      <el-button type="text" icon="el-icon-upload">
                        上传文件
                      </el-button>
                    </el-upload>
                    <el-table :data="[]" class="mt-3" border>
                      <el-table-column prop="name" label="文件名" />
                      <el-table-column prop="size" label="大小" width="120" />
                      <el-table-column prop="time" label="上传时间" width="180" />
                      <el-table-column label="操作" width="180">
                        <template #default>
                          <el-button type="text" icon="el-icon-view" />
                          <el-button type="text" icon="el-icon-delete" class="text-red-500" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form-item>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  通知设置
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="通知对象">
                  <el-select multiple placeholder="请选择通知对象" class="w-full">
                    <el-option label="张三 - 财务部" value="1" />
                    <el-option label="李四 - 人事部" value="2" />
                    <el-option label="王五 - 合规部" value="3" />
                  </el-select>
                </el-form-item>
                <el-form-item label="通知方式">
                  <el-checkbox-group>
                    <el-checkbox label="系统消息" />
                    <el-checkbox label="电子邮件" />
                    <el-checkbox label="短信" />
                    <el-checkbox label="其他" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="通知事件">
                  <el-checkbox-group>
                    <el-checkbox label="处理开始" />
                    <el-checkbox label="处理完成" />
                    <el-checkbox label="状态变更" />
                    <el-checkbox label="其他重要更新" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="通知内容">
                  <el-input type="textarea" :rows="5" placeholder="请输入自定义通知内容模板" />
                </el-form-item>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  权限设置
                </div>
              </template>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="查看权限">
                  <el-radio-group>
                    <el-radio label="全公司" />
                    <el-radio label="部门主管" />
                    <el-radio label="指定人员" />
                    <el-radio label="仅处理人员" />
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="false" label="指定查看人员">
                  <el-select multiple placeholder="请选择查看人员" class="w-full">
                    <el-option label="张三 - 财务部" value="1" />
                    <el-option label="李四 - 人事部" value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="编辑权限">
                  <el-radio-group>
                    <el-radio label="仅处理人" />
                    <el-radio label="指定人员" />
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="false" label="指定编辑人员">
                  <el-select multiple placeholder="请选择编辑人员" class="w-full">
                    <el-option label="王五 - 合规部" value="1" />
                    <el-option label="赵六 - 人事部" value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="保密级别">
                  <el-radio-group>
                    <el-radio label="普通" />
                    <el-radio label="保密" />
                    <el-radio label="高度保密" />
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  处理模板
                </div>
              </template>
              <div class="space-y-3">
                <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
                  <div class="font-medium">
                    财务违规处理模板
                  </div>
                  <div class="text-xs text-gray-500">
                    适用于财务违规场景
                  </div>
                </div>
                <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
                  <div class="font-medium">
                    人事违规处理模板
                  </div>
                  <div class="text-xs text-gray-500">
                    适用于人事违规场景
                  </div>
                </div>
                <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
                  <div class="font-medium">
                    信息安全违规处理模板
                  </div>
                  <div class="text-xs text-gray-500">
                    适用于信息安全违规场景
                  </div>
                </div>
                <el-button type="text" icon="el-icon-plus" class="w-full">
                  保存为模板
                </el-button>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  AI辅助
                </div>
              </template>
              <div class="space-y-3">
                <el-button type="text" icon="el-icon-magic-stick" class="w-full">
                  AI建议处理措施
                </el-button>
                <el-button type="text" icon="el-icon-data-analysis" class="w-full">
                  AI分析影响
                </el-button>
                <el-button type="text" icon="el-icon-check" class="w-full">
                  AI合规性检查
                </el-button>
                <div class="border rounded p-3">
                  <div class="text-sm font-medium">
                    AI建议区
                  </div>
                  <div class="mt-2 text-xs text-gray-600">
                    根据当前输入内容，AI将在此处生成建议...
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相似案例
                </div>
              </template>
              <div class="space-y-4">
                <div
                  v-for="(item, index) in similarCases" :key="index"
                  class="cursor-pointer border rounded p-4 transition-colors hover:bg-gray-50"
                >
                  <div class="flex items-start justify-between">
                    <div>
                      <div class="text-gray-800 font-medium">
                        {{ item.title }}
                      </div>
                      <div class="mt-2 flex items-center text-sm space-x-4">
                        <span class="text-gray-600">处理类型: {{ item.type }}</span>
                        <span class="text-gray-600">处理结果: {{ item.result }}</span>
                      </div>
                    </div>
                    <el-button type="text" size="small" @click="viewCaseDetail(item.id)">
                      <el-icon>
                        <View />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-form-item__label {
    font-size: 14px;
    color: #666;
  }

  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  .el-textarea__inner {
    min-height: 120px;
  }

  .el-card {
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .el-card__header {
    padding: 18px 20px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-table {
    width: 100%;
  }

  .el-table th {
    background-color: #f5f7fa;
  }

  .el-radio-button--mini .el-radio-button__inner {
    padding: 7px 12px;
  }

  .el-checkbox {
    margin-right: 15px;
  }
</style>
