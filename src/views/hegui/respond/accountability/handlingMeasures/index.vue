<script lang="ts" setup>
import { ref } from 'vue'
import {
  Search as ElIconSearch,
  Plus as ElIconPlus,
  Document as ElIconDocument,
  // Time as ElIconTime,
  Setting as ElIconSetting,
  Check as <PERSON>IconCheck,
  Warning as <PERSON>IconWarning,
  Money as ElIconMoney,
  User as ElIconUser,
} from '@element-plus/icons-vue'

const showAdvancedFilter = ref(false)
const filterTypes = ref({
  warning: false,
  training: false,
  salaryReduction: false,
  demotion: false,
  dismissal: false,
  fine: false,
  other: false,
})

const templates = ref([
  {
    id: 1,
    name: '轻微违规警告模板',
    scenario: '适用于首次轻微违规行为',
  },
  {
    id: 2,
    name: '财务违规处理模板',
    scenario: '适用于报销、费用违规情况',
  },
  {
    id: 3,
    name: '数据安全违规模板',
    scenario: '适用于数据泄露、违规访问等情况',
  },
])

const recommendations = ref([
  {
    id: 1,
    title: '数据泄露事件',
    reason: '高严重程度，截止日期临近',
  },
  {
    id: 2,
    title: '财务报销违规',
    reason: '涉及金额较大，影响范围广',
  },
  {
    id: 3,
    title: '员工行为不当',
    reason: '多次违规，需要及时处理',
  },
])

const aiSuggestion = ref('')

function toggleAllTypes() {
  const allSelected = Object.values(filterTypes.value).every(Boolean)
  for (const key in filterTypes.value) {
    filterTypes.value[key] = !allSelected
  }
}
const viewType = ref('table')
const cardItems = ref([
  {
    id: 1,
    title: '违规使用公司资源',
    type: 'warning',
    typeLabel: '警告',
    severity: 'minor',
    severityLabel: '轻微',
    responsible: '李四/技术部',
    progress: 0,
    daysLeft: 10,
    dueDate: '2023-12-15',
    status: 'pending',
  },
  {
    id: 2,
    title: '财务报销违规',
    type: 'fine',
    typeLabel: '经济处罚',
    severity: 'medium',
    severityLabel: '中等',
    responsible: '王五/财务部',
    progress: 45,
    daysLeft: 5,
    dueDate: '2023-12-10',
    status: 'processing',
  },
  {
    id: 3,
    title: '数据泄露事件',
    type: 'dismissal',
    typeLabel: '解聘',
    severity: 'serious',
    severityLabel: '严重',
    responsible: '赵六/技术部',
    progress: 100,
    daysLeft: -5,
    dueDate: '2023-11-20',
    status: 'overdue',
  },
  {
    id: 4,
    title: '员工行为不当',
    type: 'training',
    typeLabel: '培训教育',
    severity: 'minor',
    severityLabel: '轻微',
    responsible: '张三/人力资源部',
    progress: 100,
    daysLeft: 0,
    dueDate: '2023-12-05',
    status: 'completed',
  },
])
const ganttItems = ref([
  {
    id: 1,
    title: '违规使用公司资源',
    responsible: '李四',
    startDate: '2023-11-15',
    endDate: '2023-12-15',
    startPos: 100,
    duration: 300,
    progress: 0,
    status: 'pending',
  },
  {
    id: 2,
    title: '财务报销违规',
    responsible: '王五',
    startDate: '2023-11-10',
    endDate: '2023-12-10',
    startPos: 50,
    duration: 350,
    progress: 45,
    status: 'processing',
  },
  {
    id: 3,
    title: '数据泄露事件',
    responsible: '赵六',
    startDate: '2023-10-20',
    endDate: '2023-11-20',
    startPos: 0,
    duration: 250,
    progress: 100,
    status: 'overdue',
  },
  {
    id: 4,
    title: '员工行为不当',
    responsible: '张三',
    startDate: '2023-11-05',
    endDate: '2023-12-05',
    startPos: 80,
    duration: 320,
    progress: 100,
    status: 'completed',
  },
])
const ganttDays = ref([])
// Generate gantt days for demo
for (let i = 1; i <= 30; i++) {
  ganttDays.value.push(i)
}
const activeTab = ref('type')
const chartOptions = {
  type: {
    legend: {
      data: ['警告', '培训教育', '经济处罚', '降级', '解聘', '其他'],
    },
    series: [
      {
        name: '处理类型分布',
        type: 'pie',
        radius: '70%',
        data: [
          { value: 356, name: '警告' },
          { value: 289, name: '培训教育' },
          { value: 215, name: '经济处罚' },
          { value: 102, name: '降级' },
          { value: 48, name: '解聘' },
          { value: 235, name: '其他' },
        ],
      },
    ],
  },
  trend: {
    xAxis: {
      type: 'category',
      data: ['第1周', '第2周', '第3周', '第4周', '第5周'],
    },
    yAxis: {
      type: 'value',
      name: '措施数量',
    },
    series: [
      {
        name: '待处理',
        type: 'line',
        data: [120, 132, 101, 134, 90],
      },
      {
        name: '处理中',
        type: 'line',
        data: [220, 182, 191, 234, 290],
      },
      {
        name: '已完成',
        type: 'line',
        data: [150, 232, 201, 154, 190],
      },
      {
        name: '已逾期',
        type: 'line',
        data: [20, 32, 41, 34, 10],
      },
    ],
  },
  department: {
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: ['人力资源部', '财务部', '技术部', '市场部'],
    },
    series: [
      {
        name: '待处理',
        type: 'bar',
        stack: 'total',
        data: [120, 132, 101, 134],
      },
      {
        name: '处理中',
        type: 'bar',
        stack: 'total',
        data: [220, 182, 191, 234],
      },
      {
        name: '已完成',
        type: 'bar',
        stack: 'total',
        data: [150, 232, 201, 154],
      },
      {
        name: '已逾期',
        type: 'bar',
        stack: 'total',
        data: [20, 32, 41, 34],
      },
    ],
  },
  timeliness: {
    xAxis: {
      type: 'value',
      max: 100,
    },
    yAxis: {
      type: 'category',
      data: ['人力资源部', '财务部', '技术部', '市场部', '总体'],
    },
    series: [
      {
        name: '目标及时率',
        type: 'bar',
        data: [90, 90, 90, 90, 90],
      },
      {
        name: '实际及时率',
        type: 'bar',
        data: [85, 78, 92, 88, 86],
      },
    ],
  },
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              处理措施管理
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <button class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
              <el-icon class="mr-1">
                <ElIconPlus />
              </el-icon>新增处理措施
            </button>
            <button
              class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50"
            >
              批量导入
            </button>
            <button
              class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50"
            >
              导出
            </button>
            <button
              class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50"
            >
              统计分析
            </button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div class="flex">
          <el-card shadow="hover" class="mr-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  总处理数
                </div>
                <div class="mt-1 text-2xl font-bold">
                  1,245
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
                <el-icon class="text-blue-600">
                  <ElIconDocument />
                </el-icon>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="ml-10 mr-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  待处理数
                </div>
                <div class="mt-1 text-2xl font-bold">
                  86
                </div>
                <div class="mt-1 text-xs text-green-500">
                  ↑12%
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-orange-100">
                <el-icon class="text-orange-600">
                  <el-icon-time />
                </el-icon>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="ml-10 mr-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  处理中数
                </div>
                <div class="mt-1 text-2xl font-bold">
                  342
                </div>
                <div class="mt-1 text-xs text-green-500">
                  ↑5%
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-purple-100">
                <el-icon class="text-purple-600">
                  <ElIconSetting />
                </el-icon>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="ml-10 mr-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  已完成数
                </div>
                <div class="mt-1 text-2xl font-bold">
                  817
                </div>
                <div class="mt-1 text-xs text-red-500">
                  ↓3%
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-green-100">
                <el-icon class="text-green-600">
                  <ElIconCheck />
                </el-icon>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="ml-10 flex-1">
            <div class="flex justify-between">
              <div>
                <div class="text-sm text-gray-500">
                  已逾期数
                </div>
                <div class="mt-1 text-2xl text-red-600 font-bold">
                  42
                </div>
              </div>
              <div class="h-10 w-10 flex items-center justify-center rounded-full bg-red-100">
                <el-icon class="text-red-600">
                  <ElIconWarning />
                </el-icon>
              </div>
            </div>
          </el-card>
        </div>
        <div class="mt-20">
          <el-row :gutter="20" class="">
            <el-col :span="18">
              <div>
                <el-row :gutter="20" class="">
                  <el-col :span="12">
                    <el-card shadow="hover" class="">
                      <template #header>
                        <div class="aic jcsb flex">
                          <div class="f-16 fw-600">
                            最近访问
                          </div>
                          <el-link type="primary">
                            查看更多
                          </el-link>
                        </div>
                      </template>
                      <div class="space-y-3">
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                              <el-icon class="text-blue-600">
                                <ElIconWarning />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                违规使用公司资源
                              </div>
                              <div class="text-xs text-gray-500">
                                警告
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-gray-100 px-2 py-1 text-xs">待处理</span>
                        </div>
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-orange-100">
                              <el-icon class="text-orange-600">
                                <ElIconMoney />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                财务报销违规
                              </div>
                              <div class="text-xs text-gray-500">
                                经济处罚
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">处理中</span>
                        </div>
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-green-100">
                              <el-icon class="text-green-600">
                                <ElIconUser />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                员工行为不当
                              </div>
                              <div class="text-xs text-gray-500">
                                培训教育
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-green-100 px-2 py-1 text-xs text-green-600">已完成</span>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card shadow="hover" class="">
                      <template #header>
                        <div class="aic jcsb flex">
                          <div class="f-16 fw-600">
                            我负责的
                          </div>
                          <el-link type="primary">
                            查看更多
                          </el-link>
                        </div>
                      </template>
                      <div class="space-y-3">
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-red-100">
                              <el-icon class="text-red-600">
                                <ElIconWarning />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                数据泄露事件
                              </div>
                              <div class="text-xs text-gray-500">
                                截止: 2023-12-15
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-red-100 px-2 py-1 text-xs text-red-600">高优先级</span>
                        </div>
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-yellow-100">
                              <el-icon class="text-yellow-600">
                                <ElIconDocument />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                合同审批延迟
                              </div>
                              <div class="text-xs text-gray-500">
                                截止: 2023-12-20
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-600">中优先级</span>
                        </div>
                        <div class="flex items-center justify-between rounded p-2 hover:bg-gray-50">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                              <el-icon class="text-blue-600">
                                <ElIconSetting />
                              </el-icon>
                            </div>
                            <div>
                              <div class="text-sm">
                                系统权限问题
                              </div>
                              <div class="text-xs text-gray-500">
                                截止: 2023-12-25
                              </div>
                            </div>
                          </div>
                          <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">低优先级</span>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
              <el-card shadow="hover" class="mt-20">
                <!-- 筛选区 -->
                <div class="rounded-lg bg-white p-4 shadow">
                  <div class="flex items-center space-x-4">
                    <div class="w-48">
                      <el-select placeholder="处理状态" class="w-full">
                        <el-option label="全部状态" value="" />
                        <el-option label="待处理" value="pending" />
                        <el-option label="处理中" value="processing" />
                        <el-option label="已完成" value="completed" />
                        <el-option label="已逾期" value="overdue" />
                      </el-select>
                    </div>
                    <div class="w-48">
                      <el-select placeholder="处理类型" class="w-full">
                        <el-option label="全部类型" value="" />
                        <el-option label="警告" value="warning" />
                        <el-option label="培训教育" value="training" />
                        <el-option label="经济处罚" value="fine" />
                        <el-option label="降级" value="demotion" />
                        <el-option label="解聘" value="dismissal" />
                      </el-select>
                    </div>
                    <div class="w-48">
                      <el-select placeholder="责任部门" class="w-full">
                        <el-option label="全部部门" value="" />
                        <el-option label="人力资源部" value="hr" />
                        <el-option label="财务部" value="finance" />
                        <el-option label="技术部" value="tech" />
                        <el-option label="市场部" value="marketing" />
                      </el-select>
                    </div>
                    <div class="w-64">
                      <el-date-picker
                        type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" class="w-full"
                      />
                    </div>
                    <div class="flex-1">
                      <div class="relative">
                        <input
                          type="text" placeholder="搜索处理措施..."
                          class="w-full rounded-full border-none bg-gray-100 px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                        <el-icon class="absolute left-3 top-2.5 text-gray-500">
                          <ElIconSearch />
                        </el-icon>
                      </div>
                    </div>
                    <button
                      class="text-sm text-blue-500"
                      @click="showAdvancedFilter = !showAdvancedFilter"
                    >
                      高级筛选
                    </button>
                  </div>
                  <!-- 高级筛选面板 -->
                  <div v-if="showAdvancedFilter" class="mt-4 rounded-lg bg-white p-4 shadow">
                    <div class="grid grid-cols-4 gap-4">
                      <div>
                        <el-select placeholder="处理人员" class="w-full">
                          <el-option label="全部处理人员" value="" />
                          <el-option label="张三" value="zhangsan" />
                          <el-option label="李四" value="lisi" />
                          <el-option label="王五" value="wangwu" />
                          <el-option label="赵六" value="zhaoliu" />
                        </el-select>
                      </div>
                      <div>
                        <el-select placeholder="涉及人员/部门" class="w-full">
                          <el-option label="全部涉及人员" value="" />
                          <el-option label="人力资源部" value="hr" />
                          <el-option label="财务部" value="finance" />
                          <el-option label="技术部" value="tech" />
                          <el-option label="市场部" value="marketing" />
                        </el-select>
                      </div>
                      <div>
                        <el-select placeholder="严重程度" class="w-full">
                          <el-option label="全部级别" value="" />
                          <el-option label="轻微" value="minor" />
                          <el-option label="中等" value="medium" />
                          <el-option label="严重" value="serious" />
                          <el-option label="非常严重" value="critical" />
                        </el-select>
                      </div>
                      <div>
                        <el-select placeholder="调查来源" class="w-full">
                          <el-option label="全部来源" value="" />
                          <el-option label="内部举报" value="internal" />
                          <el-option label="外部投诉" value="external" />
                          <el-option label="例行检查" value="routine" />
                          <el-option label="系统监测" value="system" />
                        </el-select>
                      </div>
                    </div>
                    <div class="mt-4 flex justify-end space-x-3">
                      <button
                        class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-gray-600 hover:bg-gray-50"
                      >
                        重置
                      </button>
                      <button
                        class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
                      >
                        应用
                      </button>
                    </div>
                  </div>
                </div>
                <!-- 处理措施列表 -->
                <div class="rounded-lg bg-white shadow">
                  <div class="flex items-center justify-between border-b p-4">
                    <div class="flex space-x-2">
                      <button
                        class="rounded px-3 py-1"
                        :class="{ 'bg-blue-600 text-white': viewType === 'table', 'text-gray-600 hover:bg-gray-100': viewType !== 'table' }"
                        @click="viewType = 'table'"
                      >
                        表格视图
                      </button>
                      <button
                        class="rounded px-3 py-1"
                        :class="{ 'bg-blue-600 text-white': viewType === 'card', 'text-gray-600 hover:bg-gray-100': viewType !== 'card' }"
                        @click="viewType = 'card'"
                      >
                        卡片视图
                      </button>
                      <button
                        class="rounded px-3 py-1"
                        :class="{ 'bg-blue-600 text-white': viewType === 'gantt', 'text-gray-600 hover:bg-gray-100': viewType !== 'gantt' }"
                        @click="viewType = 'gantt'"
                      >
                        甘特图视图
                      </button>
                    </div>
                    <div class="text-sm text-gray-500">
                      共 1,245 条记录
                    </div>
                  </div>
                  <!-- 表格视图 -->
                  <div v-if="viewType === 'table'" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            <input type="checkbox" class="rounded text-blue-600">
                          </th>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            处理编号
                          </th>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            处理标题
                          </th>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            处理类型
                          </th>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            严重程度
                          </th>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            责任人/部门
                          </th>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            处理状态
                          </th>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            提出日期
                          </th>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            计划完成日期
                          </th>
                          <th class="px-6 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                          <td class="whitespace-nowrap px-6 py-4">
                            <input type="checkbox" class="rounded text-blue-600">
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            CM-2023-001
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-900 font-medium">
                            违规使用公司资源
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            警告
                          </td>
                          <td class="whitespace-nowrap px-6 py-4">
                            <span class="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800">轻微</span>
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            李四/技术部
                          </td>
                          <td class="whitespace-nowrap px-6 py-4">
                            <span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800">待处理</span>
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            2023-11-15
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            2023-12-15
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            <button class="mr-2 text-blue-500 hover:text-blue-700">
                              查看
                            </button>
                            <button class="text-blue-500 hover:text-blue-700">
                              编辑
                            </button>
                          </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                          <td class="whitespace-nowrap px-6 py-4">
                            <input type="checkbox" class="rounded text-blue-600">
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            CM-2023-002
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-900 font-medium">
                            财务报销违规
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            经济处罚
                          </td>
                          <td class="whitespace-nowrap px-6 py-4">
                            <span class="rounded bg-orange-100 px-2 py-1 text-xs text-orange-800">中等</span>
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            王五/财务部
                          </td>
                          <td class="whitespace-nowrap px-6 py-4">
                            <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">处理中</span>
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            2023-11-10
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            2023-12-10
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            <button class="mr-2 text-blue-500 hover:text-blue-700">
                              查看
                            </button>
                            <button class="text-blue-500 hover:text-blue-700">
                              编辑
                            </button>
                          </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                          <td class="whitespace-nowrap px-6 py-4">
                            <input type="checkbox" class="rounded text-blue-600">
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            CM-2023-003
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-900 font-medium">
                            数据泄露事件
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            降级
                          </td>
                          <td class="whitespace-nowrap px-6 py-4">
                            <span class="rounded bg-red-100 px-2 py-1 text-xs text-red-800">严重</span>
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            赵六/技术部
                          </td>
                          <td class="whitespace-nowrap px-6 py-4">
                            <span class="rounded bg-red-100 px-2 py-1 text-xs text-red-800">已逾期</span>
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            2023-10-20
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            2023-11-20
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            <button class="mr-2 text-blue-500 hover:text-blue-700">
                              查看
                            </button>
                            <button class="text-blue-500 hover:text-blue-700">
                              编辑
                            </button>
                          </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                          <td class="whitespace-nowrap px-6 py-4">
                            <input type="checkbox" class="rounded text-blue-600">
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            CM-2023-004
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-900 font-medium">
                            员工行为不当
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            培训教育
                          </td>
                          <td class="whitespace-nowrap px-6 py-4">
                            <span class="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800">轻微</span>
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            张三/人力资源部
                          </td>
                          <td class="whitespace-nowrap px-6 py-4">
                            <span class="rounded bg-green-100 px-2 py-1 text-xs text-green-800">已完成</span>
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            2023-11-05
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            2023-12-05
                          </td>
                          <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            <button class="mr-2 text-blue-500 hover:text-blue-700">
                              查看
                            </button>
                            <button class="text-blue-500 hover:text-blue-700">
                              编辑
                            </button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <!-- 卡片视图 -->
                  <div v-if="viewType === 'card'" class="p-4">
                    <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
                      <div
                        v-for="item in cardItems" :key="item.id"
                        class="border rounded-lg p-4 transition-shadow hover:shadow-md"
                      >
                        <div class="mb-3 flex items-start justify-between">
                          <h3 class="text-lg font-medium">
                            {{ item.title }}
                          </h3>
                          <span
                            class="rounded px-2 py-1 text-xs" :class="{
                              'bg-yellow-100 text-yellow-800': item.type === 'warning',
                              'bg-orange-100 text-orange-800': item.type === 'fine',
                              'bg-purple-100 text-purple-800': item.type === 'training',
                              'bg-red-100 text-red-800': item.type === 'dismissal',
                            }"
                          >
                            {{ item.typeLabel }}
                          </span>
                        </div>
                        <div class="mb-2 flex items-center space-x-2">
                          <span class="text-sm text-gray-500">严重程度:</span>
                          <span
                            class="rounded px-2 py-1 text-xs" :class="{
                              'bg-yellow-100 text-yellow-800': item.severity === 'minor',
                              'bg-orange-100 text-orange-800': item.severity === 'medium',
                              'bg-red-100 text-red-800': item.severity === 'serious',
                            }"
                          >
                            {{ item.severityLabel }}
                          </span>
                        </div>
                        <div class="mb-3 text-sm">
                          <span class="text-gray-500">责任人:</span>
                          <span class="ml-1">{{ item.responsible }}</span>
                        </div>
                        <div class="mb-4">
                          <div class="mb-1 flex justify-between text-xs text-gray-500">
                            <span>进度: {{ item.progress }}%</span>
                            <span>剩余: {{ item.daysLeft }}天</span>
                          </div>
                          <div class="h-2 w-full rounded-full bg-gray-200">
                            <div class="h-2 rounded-full bg-blue-600" :style="`width: ${item.progress}%`" />
                          </div>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-xs text-gray-500">截止: {{ item.dueDate }}</span>
                          <div class="flex space-x-2">
                            <button class="text-sm text-blue-500 hover:text-blue-700">
                              查看
                            </button>
                            <button class="text-sm text-blue-500 hover:text-blue-700">
                              编辑
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 甘特图视图 -->
                  <div v-if="viewType === 'gantt'" class="flex">
                    <div class="w-64 border-r">
                      <div class="border-b p-4 font-medium">
                        处理措施
                      </div>
                      <div
                        v-for="item in ganttItems" :key="item.id"
                        class="cursor-pointer border-b p-4 hover:bg-gray-50"
                      >
                        <div class="font-medium">
                          {{ item.title }}
                        </div>
                        <div class="text-sm text-gray-500">
                          {{ item.responsible }}
                        </div>
                      </div>
                    </div>
                    <div class="flex-1 overflow-auto">
                      <div class="h-12 flex border-b">
                        <div v-for="day in ganttDays" :key="day" class="w-16 border-r py-3 text-center text-sm">
                          {{ day }}
                        </div>
                      </div>
                      <div class="relative" style="height: 400px;">
                        <div
                          v-for="(item, index) in ganttItems" :key="item.id" class="absolute top-0 h-12"
                          :style="`top: ${index * 48 + 8}px; left: ${item.startPos}px; width: ${item.duration}px;`"
                        >
                          <div
                            class="h-8 flex items-center rounded px-2 text-sm text-white" :class="{
                              'bg-blue-500': item.status === 'processing',
                              'bg-green-500': item.status === 'completed',
                              'bg-red-500': item.status === 'overdue',
                              'bg-gray-400': item.status === 'pending',
                            }"
                          >
                            <div class="truncate">
                              {{ item.title }}
                            </div>
                            <div v-if="item.progress < 100" class="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
                              <div class="h-1 bg-yellow-400" :style="`width: ${item.progress}%`" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center justify-between border-t px-6 py-4">
                    <div class="text-sm text-gray-500">
                      显示 1 到 10 条，共 1,245 条
                    </div>
                    <div class="flex space-x-1">
                      <button class="border rounded px-3 py-1 text-gray-600 hover:bg-gray-100">
                        上一页
                      </button>
                      <button class="border rounded bg-blue-600 px-3 py-1 text-white">
                        1
                      </button>
                      <button class="border rounded px-3 py-1 text-gray-600 hover:bg-gray-100">
                        2
                      </button>
                      <button class="border rounded px-3 py-1 text-gray-600 hover:bg-gray-100">
                        3
                      </button>
                      <button class="border rounded px-3 py-1 text-gray-600 hover:bg-gray-100">
                        下一页
                      </button>
                    </div>
                  </div>
                </div>
              </el-card>
              <el-card shadow="hover" class="mt-20">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      处理措施分析
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                </template>
                <div class="border-b">
                  <div class="flex space-x-4">
                    <button
                      class="px-4 py-2 text-sm"
                      :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'type', 'text-gray-600 hover:text-blue-600': activeTab !== 'type' }"
                      @click="activeTab = 'type'"
                    >
                      类型分布
                    </button>
                    <button
                      class="px-4 py-2 text-sm"
                      :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'trend', 'text-gray-600 hover:text-blue-600': activeTab !== 'trend' }"
                      @click="activeTab = 'trend'"
                    >
                      状态趋势
                    </button>
                    <button
                      class="px-4 py-2 text-sm"
                      :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'department', 'text-gray-600 hover:text-blue-600': activeTab !== 'department' }"
                      @click="activeTab = 'department'"
                    >
                      部门分布
                    </button>
                    <button
                      class="px-4 py-2 text-sm"
                      :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'timeliness', 'text-gray-600 hover:text-blue-600': activeTab !== 'timeliness' }"
                      @click="activeTab = 'timeliness'"
                    >
                      及时率分析
                    </button>
                  </div>
                </div>
                <!-- 类型分布 -->
                <div v-if="activeTab === 'type'" class="grid grid-cols-3 mt-4 gap-4">
                  <div class="col-span-2">
                    <div class="h-64 flex items-center justify-center rounded bg-gray-100">
                      <div class="text-center">
                        <div class="mb-2 text-gray-500">
                          饼图展示不同类型处理措施的分布
                        </div>
                        <div class="flex justify-center text-xs space-x-4">
                          <div class="flex items-center">
                            <div class="mr-1 h-3 w-3 bg-blue-500" />
                            <span>警告 28.6%</span>
                          </div>
                          <div class="flex items-center">
                            <div class="mr-1 h-3 w-3 bg-green-500" />
                            <span>培训 23.2%</span>
                          </div>
                          <div class="flex items-center">
                            <div class="mr-1 h-3 w-3 bg-yellow-500" />
                            <span>经济处罚 17.3%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th class="px-4 py-2 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            处理类型
                          </th>
                          <th class="px-4 py-2 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            数量
                          </th>
                          <th class="px-4 py-2 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
                            占比
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            警告
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            356
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            28.6%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            培训教育
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            289
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            23.2%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            经济处罚
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            215
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            17.3%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            降级
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            102
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            8.2%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            解聘
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            48
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            3.9%
                          </td>
                        </tr>
                        <tr>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            其他
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            235
                          </td>
                          <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                            18.9%
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <!-- 状态趋势 -->
                <div v-if="activeTab === 'trend'" class="mt-4">
                  <div class="h-80 flex items-center justify-center rounded bg-gray-100">
                    <div class="text-center">
                      <div class="mb-2 text-gray-500">
                        折线图展示处理状态趋势
                      </div>
                      <div class="flex justify-center text-xs space-x-4">
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-blue-500" />
                          <span>待处理</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-green-500" />
                          <span>处理中</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-red-500" />
                          <span>已完成</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-gray-500" />
                          <span>已逾期</span>
                        </div>
                      </div>
                      <div class="mt-2 text-xs text-gray-400">
                        X轴: 时间 (按周) | Y轴: 措施数量
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 部门分布 -->
                <div v-if="activeTab === 'department'" class="mt-4">
                  <div class="h-80 flex items-center justify-center rounded bg-gray-100">
                    <div class="text-center">
                      <div class="mb-2 text-gray-500">
                        横向堆叠柱状图展示各部门的处理措施
                      </div>
                      <div class="flex justify-center text-xs space-x-4">
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-blue-500" />
                          <span>人力资源部</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-green-500" />
                          <span>财务部</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-yellow-500" />
                          <span>技术部</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-purple-500" />
                          <span>市场部</span>
                        </div>
                      </div>
                      <div class="mt-2 text-xs text-gray-400">
                        X轴: 部门 | Y轴: 措施数量 (堆叠表示不同状态)
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 及时率分析 -->
                <div v-if="activeTab === 'timeliness'" class="mt-4">
                  <div class="h-80 flex items-center justify-center rounded bg-gray-100">
                    <div class="text-center">
                      <div class="mb-2 text-gray-500">
                        条形图展示各部门/类型的处理及时率
                      </div>
                      <div class="flex justify-center text-xs space-x-4">
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-blue-500" />
                          <span>目标及时率</span>
                        </div>
                        <div class="flex items-center">
                          <div class="mr-1 h-3 w-3 bg-green-500" />
                          <span>实际及时率</span>
                        </div>
                      </div>
                      <div class="mt-2 text-xs text-gray-400">
                        部门/类型 vs 及时率对比
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- <el-card shadow="hover" class="">
                <template #header>
                  <div class="f-16 fw-600">基本信息</div>
                </template>
              </el-card> -->
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      处理类型
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                  <div class="space-y-3">
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.warning" class="mr-2" />
                      <span>警告</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.training" class="mr-2" />
                      <span>培训教育</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.salaryReduction" class="mr-2" />
                      <span>减少薪酬</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.demotion" class="mr-2" />
                      <span>降级</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.dismissal" class="mr-2" />
                      <span>解聘</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.fine" class="mr-2" />
                      <span>经济处罚</span>
                    </div>
                    <div class="flex items-center">
                      <el-checkbox v-model="filterTypes.other" class="mr-2" />
                      <span>其他</span>
                    </div>
                    <a href="#" class="mt-2 block text-sm text-blue-500" @click="toggleAllTypes">全选/取消全选</a>
                  </div>
                </template>
              </el-card>
              <el-card shadow="hover" class="mt-20">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      常用模板
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                </template>

                <div class="space-y-4">
                  <div v-for="template in templates" :key="template.id" class="border-b pb-4 last:border-b-0">
                    <div class="mb-1 font-medium">
                      {{ template.name }}
                    </div>
                    <div class="mb-2 text-sm text-gray-500">
                      {{ template.scenario }}
                    </div>
                    <button
                      class="!rounded-button whitespace-nowrap bg-blue-600 px-3 py-1 text-sm text-white hover:bg-blue-700"
                    >
                      应用模板
                    </button>
                  </div>
                  <a href="#" class="mt-2 block text-sm text-blue-500">管理模板</a>
                </div>
              </el-card>
              <el-card shadow="hover" class="mt-20">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      优先处理建议
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                </template>
                <div class="space-y-3">
                  <div
                    v-for="recommendation in recommendations" :key="recommendation.id"
                    class="border-b pb-3 last:border-b-0"
                  >
                    <div class="font-medium">
                      {{ recommendation.title }}
                    </div>
                    <div class="mb-1 text-sm text-gray-500">
                      {{ recommendation.reason }}
                    </div>
                    <a href="#" class="text-sm text-blue-500">查看详情</a>
                  </div>
                </div>
              </el-card>
              <el-card shadow="hover" class="mt-20">
                <template #header>
                  <div class="aic jcsb flex">
                    <div class="f-16 fw-600">
                      AI助手
                    </div>
                    <!-- <el-link type="primary">查看更多</el-link> -->
                  </div>
                </template>
                <div class="flex flex-col space-y-3">
                  <button class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
                    分析处理效率
                  </button>
                  <button class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
                    生成处理建议
                  </button>
                  <button class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
                    预测处理结果
                  </button>
                </div>
                <div v-if="aiSuggestion" class="mt-4 rounded bg-gray-50 p-3 text-sm">
                  {{ aiSuggestion }}
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .min-h-screen {
    min-height: 1024px;
  }

  .h-screen {
    height: 1024px;
  }
</style>
