<script lang="ts" setup>
import { ref } from 'vue'
import {
  Search as ElIconSearch,
  // Time as ElIconTime,
  Check as ElIconChe<PERSON>,
  Close as ElIconClose,
  CircleCheck as ElIconCircleCheck,
  AlarmClock as ElIconAlarmClock,
  Finished as ElIconFinished,
  RefreshLeft as ElIconRefreshLeft,
  Remove as ElIconRemove,
} from '@element-plus/icons-vue'

const activeTab = ref('pending')
const confirmDialogVisible = ref(false)
const rejectDialogVisible = ref(false)
const exemptDialogVisible = ref(false)
const currentItem = ref<any>(null)
const confirmForm = ref({
  comment: '',
  score: 3,
  confirmer: '张明远',
  confirmDate: new Date().toISOString().split('T')[0],
})
const rejectForm = ref({
  reason: '',
  requirement: '',
  deadline: '',
  rejecter: '张明远',
  rejectDate: new Date().toISOString().split('T')[0],
})
const exemptForm = ref({
  reason: '',
  basis: '',
  approver: '',
  applicant: '张明远',
  applyDate: new Date().toISOString().split('T')[0],
})
function showConfirmDialog(row: any) {
  currentItem.value = row
  confirmDialogVisible.value = true
}
function showRejectDialog(row: any) {
  currentItem.value = row
  rejectDialogVisible.value = true
}
function showExemptDialog(row: any) {
  currentItem.value = row
  exemptDialogVisible.value = true
}
function handleConfirm() {
  // Handle confirm logic
  confirmDialogVisible.value = false
}
function handleReject() {
  // Handle reject logic
  rejectDialogVisible.value = false
}
function handleExempt() {
  // Handle exempt logic
  exemptDialogVisible.value = false
}
const pendingData = [
  {
    id: 'CL-2023-001',
    title: '财务报销违规',
    type: '警告',
    severity: '严重',
    department: '财务部/李华',
    date: '2023-05-10',
    handler: '王明',
    days: '3',
    deadline: '2023-05-15',
  },
  {
    id: 'CL-2023-002',
    title: '考勤造假',
    type: '罚款',
    severity: '中等',
    department: '人力资源部/张伟',
    date: '2023-05-12',
    handler: '刘芳',
    days: '1',
    deadline: '2023-05-15',
  },
  {
    id: 'CL-2023-003',
    title: '数据泄露',
    type: '降级',
    severity: '严重',
    department: '技术部/陈明',
    date: '2023-05-08',
    handler: '赵强',
    days: '5',
    deadline: '2023-05-13',
  },
  {
    id: 'CL-2023-004',
    title: '合同违规',
    type: '警告',
    severity: '轻微',
    department: '市场部/周婷',
    date: '2023-05-11',
    handler: '孙丽',
    days: '2',
    deadline: '2023-05-16',
  },
  {
    id: 'CL-2023-005',
    title: '采购违规',
    type: '罚款',
    severity: '中等',
    department: '采购部/吴刚',
    date: '2023-05-09',
    handler: '郑伟',
    days: '4',
    deadline: '2023-05-14',
  },
]
const confirmedData = [
  {
    id: 'CL-2023-006',
    title: '报销违规',
    type: '警告',
    department: '财务部/李华',
    date: '2023-05-01',
    confirmDate: '2023-05-03',
    confirmer: '张明远',
    score: 4,
    comment: '处理得当，符合规定',
  },
  {
    id: 'CL-2023-007',
    title: '迟到早退',
    type: '罚款',
    department: '人力资源部/张伟',
    date: '2023-05-02',
    confirmDate: '2023-05-04',
    confirmer: '张明远',
    score: 3,
    comment: '处理结果合理',
  },
  {
    id: 'CL-2023-008',
    title: '代码泄露',
    type: '降级',
    department: '技术部/陈明',
    date: '2023-04-28',
    confirmDate: '2023-05-02',
    confirmer: '王丽',
    score: 5,
    comment: '处理及时，措施得当',
  },
  {
    id: 'CL-2023-009',
    title: '合同违规',
    type: '警告',
    department: '市场部/周婷',
    date: '2023-04-30',
    confirmDate: '2023-05-03',
    confirmer: '张明远',
    score: 4,
    comment: '处理符合规定',
  },
  {
    id: 'CL-2023-010',
    title: '采购违规',
    type: '罚款',
    department: '采购部/吴刚',
    date: '2023-04-29',
    confirmDate: '2023-05-01',
    confirmer: '王丽',
    score: 3,
    comment: '处理结果合理',
  },
]
const rejectedData = [
  {
    id: 'CL-2023-011',
    title: '报销违规',
    type: '警告',
    department: '财务部/李华',
    date: '2023-04-25',
    rejectDate: '2023-04-28',
    rejecter: '张明远',
    reason: '处理措施不足',
    deadline: '2023-05-05',
  },
  {
    id: 'CL-2023-012',
    title: '考勤造假',
    type: '罚款',
    department: '人力资源部/张伟',
    date: '2023-04-26',
    rejectDate: '2023-04-29',
    rejecter: '王丽',
    reason: '证据不足',
    deadline: '2023-05-06',
  },
  {
    id: 'CL-2023-013',
    title: '数据泄露',
    type: '降级',
    department: '技术部/陈明',
    date: '2023-04-24',
    rejectDate: '2023-04-27',
    rejecter: '张明远',
    reason: '责任人认定不准确',
    deadline: '2023-05-04',
  },
]
const exemptedData = [
  {
    id: 'CL-2023-014',
    title: '报销违规',
    type: '警告',
    department: '财务部/李华',
    applyDate: '2023-04-20',
    exemptDate: '2023-04-22',
    exempter: '张明远',
    reason: '首次违规，情节轻微',
  },
  {
    id: 'CL-2023-015',
    title: '迟到早退',
    type: '罚款',
    department: '人力资源部/张伟',
    applyDate: '2023-04-21',
    exemptDate: '2023-04-23',
    exempter: '王丽',
    reason: '特殊情况，情有可原',
  },
]
function getSeverityTagType(severity: string) {
  switch (severity) {
    case '严重': return 'danger'
    case '中等': return 'warning'
    case '轻微': return 'success'
    default: return ''
  }
}
function getDaysClass(days: string) {
  const dayNum = Number.parseInt(days)
  if (dayNum > 3) { return 'text-red-500' }
  if (dayNum > 1) { return 'text-yellow-500' }
  return 'text-green-500'
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              处理结果确认
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              批量确认
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              导出
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              统计分析
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="6">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm text-gray-500">
                    待确认数
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    28
                  </div>
                  <div class="mt-1 text-xs text-green-500">
                    同比 +12%
                  </div>
                </div>
                <el-icon class="text-3xl text-blue-500">
                  <el-icon-time />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm text-gray-500">
                    已确认数
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    156
                  </div>
                  <div class="mt-1 text-xs text-green-500">
                    同比 +8%
                  </div>
                </div>
                <el-icon class="text-3xl text-green-500">
                  <ElIconCheck />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm text-gray-500">
                    已驳回数
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    12
                  </div>
                  <div class="mt-1 text-xs text-red-500">
                    同比 -5%
                  </div>
                </div>
                <el-icon class="text-3xl text-red-500">
                  <ElIconClose />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm text-gray-500">
                    已豁免数
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    5
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    同比 0%
                  </div>
                </div>
                <el-icon class="text-3xl text-purple-500">
                  <ElIconCircleCheck />
                </el-icon>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="12">
            <el-card shadow="hover" class="">
              <div class="flex items-start justify-between">
                <div>
                  <div class="mb-2 text-lg font-bold">
                    待我确认
                  </div>
                  <div class="mb-2 text-3xl font-bold">
                    8
                  </div>
                  <div class="mb-4 text-sm text-gray-500">
                    <span class="text-red-500">2 项超期</span>, <span class="text-yellow-500">3 项即将超期</span>
                  </div>
                  <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap">
                    立即处理
                  </el-button>
                </div>
                <el-icon class="text-4xl text-blue-500">
                  <ElIconAlarmClock />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover" class="">
              <div class="flex items-start justify-between">
                <div>
                  <div class="mb-2 text-lg font-bold">
                    我已确认
                  </div>
                  <div class="mb-2 text-3xl font-bold">
                    42
                  </div>
                  <div class="mb-4 text-sm text-gray-500">
                    本周 12 项, 本月 42 项
                  </div>
                  <el-button size="small" class="!rounded-button whitespace-nowrap">
                    查看详情
                  </el-button>
                </div>
                <el-icon class="text-4xl text-green-500">
                  <ElIconFinished />
                </el-icon>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-20">
          <el-col :span="12">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-start justify-between">
                <div>
                  <div class="mb-2 text-lg font-bold">
                    我已驳回
                  </div>
                  <div class="mb-2 text-3xl font-bold">
                    6
                  </div>
                  <div class="mb-4 text-sm text-gray-500">
                    本周 2 项, 本月 6 项
                  </div>
                  <el-button size="small" class="!rounded-button whitespace-nowrap">
                    查看详情
                  </el-button>
                </div>
                <el-icon class="text-4xl text-red-500">
                  <ElIconRefreshLeft />
                </el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover" class="!rounded-lg">
              <div class="flex items-start justify-between">
                <div>
                  <div class="mb-2 text-lg font-bold">
                    我已豁免
                  </div>
                  <div class="mb-2 text-3xl font-bold">
                    2
                  </div>
                  <div class="mb-4 text-sm text-gray-500">
                    本周 0 项, 本月 2 项
                  </div>
                  <el-button size="small" class="!rounded-button whitespace-nowrap">
                    查看详情
                  </el-button>
                </div>
                <el-icon class="text-4xl text-purple-500">
                  <ElIconRemove />
                </el-icon>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-card shadow="hover" class="mt-20">
          <!-- 筛选区 -->
          <el-card shadow="hover" class="mb-6 !rounded-lg">
            <div class="grid grid-cols-4 gap-4">
              <el-select placeholder="处理类型" size="small">
                <el-option label="全部" value="all" />
                <el-option label="警告" value="warning" />
                <el-option label="罚款" value="fine" />
                <el-option label="降级" value="demotion" />
                <el-option label="解雇" value="dismissal" />
              </el-select>
              <el-select placeholder="责任部门" size="small">
                <el-option label="全部" value="all" />
                <el-option label="财务部" value="finance" />
                <el-option label="人力资源部" value="hr" />
                <el-option label="市场部" value="marketing" />
                <el-option label="技术部" value="tech" />
              </el-select>
              <el-select placeholder="确认状态" size="small">
                <el-option label="全部" value="all" />
                <el-option label="待确认" value="pending" />
                <el-option label="已确认" value="confirmed" />
                <el-option label="已驳回" value="rejected" />
                <el-option label="已豁免" value="exempted" />
              </el-select>
              <el-date-picker
                type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                size="small"
              />
            </div>
            <div class="mt-4 flex justify-between">
              <el-input placeholder="请输入关键字搜索" class="w-64" size="small">
                <template #prefix>
                  <el-icon class="el-input__icon">
                    <ElIconSearch />
                  </el-icon>
                </template>
              </el-input>
              <el-button type="text" size="small">
                高级筛选
              </el-button>
            </div>
          </el-card>
          <!-- 表格区域 -->
          <el-card shadow="hover" class="mt-20">
            <el-tabs v-model="activeTab">
              <el-tab-pane label="待确认" name="pending">
                <el-table :data="pendingData" style="width: 100%;">
                  <el-table-column type="selection" width="50" />
                  <el-table-column prop="id" label="处理编号" width="120" />
                  <el-table-column prop="title" label="处理标题" width="180" />
                  <el-table-column prop="type" label="处理类型" width="120" />
                  <el-table-column prop="severity" label="严重程度" width="120">
                    <template #default="{ row }">
                      <el-tag :type="getSeverityTagType(row.severity)" size="small">
                        {{ row.severity }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="department" label="责任人/部门" width="150" />
                  <el-table-column prop="date" label="完成日期" width="120" />
                  <el-table-column prop="handler" label="处理人" width="120" />
                  <el-table-column prop="days" label="待确认天数" width="120">
                    <template #default="{ row }">
                      <span :class="getDaysClass(row.days)">{{ row.days }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="deadline" label="截止日期" width="120" />
                  <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                      <el-button type="text" size="small" @click="showExemptDialog(row)">
                        查看
                      </el-button>
                      <el-button type="text" size="small" @click="showConfirmDialog(row)">
                        确认
                      </el-button>
                      <el-button type="text" size="small" @click="showRejectDialog(row)">
                        驳回
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="mt-4 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    共 28 条记录
                  </div>
                  <el-pagination :page-size="10" :pager-count="5" layout="prev, pager, next" :total="28" />
                </div>
              </el-tab-pane>
              <el-tab-pane label="已确认" name="confirmed">
                <el-table :data="confirmedData" style="width: 100%;">
                  <el-table-column prop="id" label="处理编号" width="120" />
                  <el-table-column prop="title" label="处理标题" width="180" />
                  <el-table-column prop="type" label="处理类型" width="120" />
                  <el-table-column prop="department" label="责任人/部门" width="150" />
                  <el-table-column prop="date" label="完成日期" width="120" />
                  <el-table-column prop="confirmDate" label="确认日期" width="120" />
                  <el-table-column prop="confirmer" label="确认人" width="120" />
                  <el-table-column prop="score" label="确认评分" width="120">
                    <template #default="{ row }">
                      <el-rate v-model="row.score" disabled show-score text-color="#ff9900" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="comment" label="确认意见" />
                  <el-table-column label="操作" width="120">
                    <template #default>
                      <el-button type="text" size="small" @click="showExemptDialog(row)">
                        查看详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="mt-4 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    共 156 条记录
                  </div>
                  <el-pagination :page-size="10" :pager-count="5" layout="prev, pager, next" :total="156" />
                </div>
              </el-tab-pane>
              <el-tab-pane label="已驳回" name="rejected">
                <el-table :data="rejectedData" style="width: 100%;">
                  <el-table-column prop="id" label="处理编号" width="120" />
                  <el-table-column prop="title" label="处理标题" width="180" />
                  <el-table-column prop="type" label="处理类型" width="120" />
                  <el-table-column prop="department" label="责任人/部门" width="150" />
                  <el-table-column prop="date" label="完成日期" width="120" />
                  <el-table-column prop="rejectDate" label="驳回日期" width="120" />
                  <el-table-column prop="rejecter" label="驳回人" width="120" />
                  <el-table-column prop="reason" label="驳回原因" />
                  <el-table-column prop="deadline" label="整改期限" width="120" />
                  <el-table-column label="操作" width="120">
                    <template #default>
                      <el-button type="text" size="small" @click="showExemptDialog(row)">
                        查看详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="mt-4 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    共 12 条记录
                  </div>
                  <el-pagination :page-size="10" :pager-count="5" layout="prev, pager, next" :total="12" />
                </div>
              </el-tab-pane>
              <el-tab-pane label="已豁免" name="exempted">
                <el-table :data="exemptedData" style="width: 100%;">
                  <el-table-column prop="id" label="处理编号" width="120" />
                  <el-table-column prop="title" label="处理标题" width="180" />
                  <el-table-column prop="type" label="处理类型" width="120" />
                  <el-table-column prop="department" label="责任人/部门" width="150" />
                  <el-table-column prop="applyDate" label="申请日期" width="120" />
                  <el-table-column prop="exemptDate" label="豁免日期" width="120" />
                  <el-table-column prop="exempter" label="豁免人" width="120" />
                  <el-table-column prop="reason" label="豁免原因" />
                  <el-table-column label="操作" width="120">
                    <template #default>
                      <el-button type="text" size="small" @click="showExemptDialog(row)">
                        查看详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="mt-4 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    共 5 条记录
                  </div>
                  <el-pagination :page-size="10" :pager-count="5" layout="prev, pager, next" :total="5" />
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-card>
      </div>
    </PageMain>
    <!-- 确认弹窗 -->
    <el-dialog v-model="confirmDialogVisible" title="处理结果确认" width="800px">
      <div class="grid grid-cols-2 mb-4 gap-4">
        <div>
          <div class="text-sm text-gray-500">
            处理标题
          </div>
          <div class="font-medium">
            {{ currentItem?.title }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            处理类型
          </div>
          <div class="font-medium">
            {{ currentItem?.type }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            责任人/部门
          </div>
          <div class="font-medium">
            {{ currentItem?.department }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            完成日期
          </div>
          <div class="font-medium">
            {{ currentItem?.date }}
          </div>
        </div>
      </div>
      <div class="mb-6">
        <div class="mb-2 text-lg font-medium">
          处理结果内容
        </div>
        <el-card shadow="never" class="!rounded-lg">
          <div class="p-4">
            <div class="mb-4">
              <div class="mb-1 text-sm text-gray-500">
                处理结果概述
              </div>
              <div>已按照公司规定对违规行为进行处理，责任人已接受相应处罚</div>
            </div>
            <div class="mb-4">
              <div class="mb-1 text-sm text-gray-500">
                结果说明
              </div>
              <div>详细处理过程已记录在案，相关责任人已签字确认</div>
            </div>
            <div>
              <div class="mb-1 text-sm text-gray-500">
                附件
              </div>
              <el-button type="text" size="small">
                处理记录.pdf
              </el-button>
              <el-button type="text" size="small">
                确认书.docx
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
      <div class="mb-4">
        <div class="mb-2 text-lg font-medium">
          确认表单
        </div>
        <el-form :model="confirmForm" label-width="100px">
          <el-form-item label="确认意见">
            <el-input v-model="confirmForm.comment" type="textarea" :rows="3" placeholder="请输入确认意见" />
          </el-form-item>
          <el-form-item label="确认评分">
            <el-rate v-model="confirmForm.score" />
          </el-form-item>
          <el-form-item label="确认人">
            <el-input v-model="confirmForm.confirmer" disabled />
          </el-form-item>
          <el-form-item label="确认日期">
            <el-input v-model="confirmForm.confirmDate" disabled />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-button @click="confirmDialogVisible = false">
            取消
          </el-button>
          <div class="space-x-3">
            <el-button type="danger" @click="showRejectDialog(currentItem)">
              驳回
            </el-button>
            <el-button type="primary" @click="handleConfirm">
              确认
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    <!-- 驳回弹窗 -->
    <el-dialog v-model="rejectDialogVisible" title="处理结果驳回" width="800px">
      <div class="grid grid-cols-2 mb-4 gap-4">
        <div>
          <div class="text-sm text-gray-500">
            处理标题
          </div>
          <div class="font-medium">
            {{ currentItem?.title }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            处理类型
          </div>
          <div class="font-medium">
            {{ currentItem?.type }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            责任人/部门
          </div>
          <div class="font-medium">
            {{ currentItem?.department }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            完成日期
          </div>
          <div class="font-medium">
            {{ currentItem?.date }}
          </div>
        </div>
      </div>
      <div class="mb-4">
        <el-form :model="rejectForm" label-width="100px">
          <el-form-item label="驳回原因" required>
            <el-input v-model="rejectForm.reason" type="textarea" :rows="3" placeholder="请输入驳回原因" />
          </el-form-item>
          <el-form-item label="整改要求">
            <el-input v-model="rejectForm.requirement" type="textarea" :rows="3" placeholder="请输入整改要求" />
          </el-form-item>
          <el-form-item label="整改期限">
            <el-date-picker v-model="rejectForm.deadline" type="date" placeholder="选择日期" />
          </el-form-item>
          <el-form-item label="驳回人">
            <el-input v-model="rejectForm.rejecter" disabled />
          </el-form-item>
          <el-form-item label="驳回日期">
            <el-input v-model="rejectForm.rejectDate" disabled />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-button @click="rejectDialogVisible = false">
            取消
          </el-button>
          <el-button type="danger" @click="handleReject">
            驳回
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 豁免弹窗 -->
    <el-dialog v-model="exemptDialogVisible" title="处理结果豁免" width="800px">
      <div class="grid grid-cols-2 mb-4 gap-4">
        <div>
          <div class="text-sm text-gray-500">
            处理标题
          </div>
          <div class="font-medium">
            {{ currentItem?.title }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            处理类型
          </div>
          <div class="font-medium">
            {{ currentItem?.type }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            责任人/部门
          </div>
          <div class="font-medium">
            {{ currentItem?.department }}
          </div>
        </div>
        <div>
          <div class="text-sm text-gray-500">
            完成日期
          </div>
          <div class="font-medium">
            {{ currentItem?.date }}
          </div>
        </div>
      </div>
      <div class="mb-6">
        <div class="mb-2 text-lg font-medium">
          处理结果内容
        </div>
        <el-card shadow="never" class="!rounded-lg">
          <div class="p-4">
            <div class="mb-4">
              <div class="mb-1 text-sm text-gray-500">
                处理结果概述
              </div>
              <div>已按照公司规定对违规行为进行处理，责任人已接受相应处罚</div>
            </div>
            <div class="mb-4">
              <div class="mb-1 text-sm text-gray-500">
                结果说明
              </div>
              <div>详细处理过程已记录在案，相关责任人已签字确认</div>
            </div>
            <div>
              <div class="mb-1 text-sm text-gray-500">
                附件
              </div>
              <el-button type="text" size="small">
                处理记录.pdf
              </el-button>
              <el-button type="text" size="small">
                确认书.docx
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
      <div class="mb-4">
        <div class="mb-2 text-lg font-medium">
          豁免申请
        </div>
        <el-form :model="exemptForm" label-width="100px">
          <el-form-item label="豁免原因" required>
            <el-input v-model="exemptForm.reason" type="textarea" :rows="3" placeholder="请输入豁免原因" />
          </el-form-item>
          <el-form-item label="豁免依据">
            <el-input v-model="exemptForm.basis" type="textarea" :rows="3" placeholder="请输入豁免依据" />
          </el-form-item>
          <el-form-item label="审批人">
            <el-select v-model="exemptForm.approver" placeholder="请选择审批人">
              <el-option label="李总" value="李总" />
              <el-option label="王总监" value="王总监" />
              <el-option label="张经理" value="张经理" />
            </el-select>
          </el-form-item>
          <el-form-item label="申请人">
            <el-input v-model="exemptForm.applicant" disabled />
          </el-form-item>
          <el-form-item label="申请日期">
            <el-input v-model="exemptForm.applyDate" disabled />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-button @click="exemptDialogVisible = false">
            取消
          </el-button>
          <el-button type="warning" @click="handleExempt">
            提交豁免
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu {
    border-right: none;
  }

  .el-menu-item.is-active {
    background-color: #1e88e5 !important;
  }

  .el-sub-menu .el-menu-item {
    padding-left: 48px !important;
  }

  .el-card {
    border: none;
  }

  .el-table {
    margin-top: 16px;
  }

  .el-tabs {
    margin-top: -16px;
  }

  .el-tabs__item {
    height: 48px;
    padding: 0 16px;
    line-height: 48px;
  }

  .el-tabs__active-bar {
    height: 3px;
  }
</style>
