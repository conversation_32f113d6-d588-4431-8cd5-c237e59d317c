<script lang="ts" setup>
import { ref } from 'vue'
import {
  Edit,
  Download,
  Printer,
  Share,
  More,
  View,
  // Thumb,
  Star,
  Link,
  // LightBulb
} from '@element-plus/icons-vue'

const activeTab = ref('relatedAnalysis')

const relatedMaterials = ref([
  {
    name: '2023Q1项目复盘报告',
    type: 'PDF文档',
    uploadTime: '2023-04-20',
    uploader: '张明远',
    description: '包含3个延期项目的详细分析',
  },
  {
    name: '客户A项目延期分析',
    type: 'PPT演示',
    uploadTime: '2023-04-18',
    uploader: '李思雨',
    description: '客户A项目时间线及关键节点分析',
  },
  {
    name: '供应商评估报告',
    type: 'Excel表格',
    uploadTime: '2023-04-15',
    uploader: '王建国',
    description: '核心供应商交付能力评估数据',
  },
])

const relatedRisks = ref([
  {
    name: '供应商交付风险',
    type: '供应链风险',
    level: '高',
    description: '直接导致项目延期的关键因素',
  },
  {
    name: '需求变更风险',
    type: '项目风险',
    level: '中',
    description: '间接影响项目进度的重要因素',
  },
])

const relatedPolicies = ref([
  {
    name: '项目管理办法',
    type: '管理制度',
    version: 'V2.3',
    description: '风险管理章节需要修订',
  },
  {
    name: '供应商管理办法',
    type: '管理制度',
    version: 'V1.5',
    description: '供应商评估标准需要完善',
  },
])

const relatedPositions = ref([
  {
    name: '项目经理',
    department: '项目管理办公室',
    description: '风险管理第一责任人',
  },
  {
    name: '采购专员',
    department: '采购部',
    description: '供应商风险管理责任人',
  },
])

const relatedProcesses = ref([
  {
    name: '项目风险管理流程',
    type: '管理流程',
    version: 'V1.2',
    description: '需要优化风险预警机制',
  },
  {
    name: '供应商评估流程',
    type: '业务流程',
    version: 'V2.1',
    description: '需要增加交付能力评估指标',
  },
])
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-xl c-[#000000] font-bold">
              项目延期风险管控不足导致交付延迟
            </h1>
            <!-- <el-tag type="warning" >处理中</el-tag> -->
            <el-tag type="success" size="small">
              已发布
            </el-tag>
          </div>
          <div class="flex items-center space-x-2">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <Edit />
              </el-icon>编辑
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <Download />
              </el-icon>导出
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <Printer />
              </el-icon>打印
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <Share />
              </el-icon>分享
            </el-button>
            <el-dropdown>
              <el-button plain class="!rounded-button whitespace-nowrap">
                <el-icon class="mr-2">
                  <More />
                </el-icon>更多
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>归档</el-dropdown-item>
                  <el-dropdown-item>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <!-- 基本信息区 -->
            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="f-16 fw-600">
                  基本信息
                </div>
              </template>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex">
                  <span class="w-24 text-gray-500">编号：</span>
                  <span>LL-2023-0425-001</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">标题：</span>
                  <span>项目延期风险管控不足导致交付延迟</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">类型：</span>
                  <span>项目管理</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">来源：</span>
                  <el-link type="primary" :underline="false">
                    2023年Q1项目复盘报告
                  </el-link>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">严重程度：</span>
                  <el-tag type="danger" size="small">
                    严重
                  </el-tag>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">责任部门：</span>
                  <span>项目管理办公室</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">创建人：</span>
                  <span>张明远</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">创建日期：</span>
                  <span>2023-04-25</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">状态：</span>
                  <span>已发布</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">阅读量：</span>
                  <span>1,245</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">点赞数：</span>
                  <span>86</span>
                </div>
              </div>
            </el-card>

            <!-- 内容展示区 -->
            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="f-16 fw-600">
                  内容详情
                </div>
              </template>

              <div class="mb-6">
                <h3 class="mb-2 font-semibold">
                  问题背景
                </h3>
                <div class="text-gray-700">
                  <p>在2023年第一季度，公司共有3个重点项目因各种原因导致延期交付，平均延期时间达到2.5周。其中，客户A的数字化转型项目延期最为严重，导致客户满意度下降，并产生了合同约定的延期罚款。</p>
                  <p class="mt-2">
                    项目延期的主要原因包括：风险识别不及时、应对措施不充分、资源调配滞后等。这些问题暴露出项目管理过程中风险管控机制的不足。
                  </p>
                </div>
              </div>

              <div class="mb-6">
                <h3 class="mb-2 font-semibold">
                  事件经过
                </h3>
                <div class="text-gray-700">
                  <p>1. 项目启动阶段：项目计划制定时，对关键路径上的风险识别不足，未充分考虑供应商交付能力、技术难点等因素。</p>
                  <p class="mt-2">
                    2. 项目执行阶段：当第一个里程碑出现2天延迟时，项目组仅做了简单调整，未深入分析延迟原因及可能带来的连锁反应。
                  </p>
                  <p class="mt-2">
                    3. 项目中期：关键供应商因产能问题延迟交付核心组件，项目组应急方案准备不足，导致后续工作全面受阻。
                  </p>
                  <p class="mt-2">
                    4. 项目后期：为追赶进度，加班加点导致质量问题频发，返工进一步延长了项目周期。
                  </p>
                </div>
              </div>

              <div class="mb-6">
                <h3 class="mb-2 font-semibold">
                  问题分析
                </h3>
                <div class="text-gray-700">
                  <p>1. 风险管理流程不完善：缺乏系统性的风险识别、评估和应对机制，风险登记册更新不及时。</p>
                  <p class="mt-2">
                    2. 风险预警机制缺失：没有建立有效的风险预警指标和阈值，无法及时发现风险升级信号。
                  </p>
                  <p class="mt-2">
                    3. 应急资源准备不足：对可能发生的风险事件，缺乏预先准备的应急方案和资源储备。
                  </p>
                  <p class="mt-2">
                    4. 跨部门协作不畅：风险应对涉及多部门协作时，责任划分不明确，响应速度慢。
                  </p>
                </div>
              </div>

              <div class="mb-6">
                <h3 class="mb-2 font-semibold">
                  经验教训
                </h3>
                <div class="text-gray-700">
                  <ul class="list-disc pl-5 space-y-2">
                    <li>必须建立完善的项目风险管理流程，包括风险识别、评估、应对和监控的全周期管理。</li>
                    <li>关键路径上的风险应设置预警指标，当偏差超过阈值时自动触发升级机制。</li>
                    <li>对高概率、高影响风险，应预先制定详细的应急方案，并预留应急资源。</li>
                    <li>跨部门风险应对应明确牵头部门和协作机制，建立快速响应通道。</li>
                    <li>项目进度调整必须评估对整体目标的影响，避免局部优化导致全局恶化。</li>
                  </ul>
                </div>
              </div>

              <div class="mb-6">
                <h3 class="mb-2 font-semibold">
                  改进建议
                </h3>
                <div class="text-gray-700">
                  <ul class="list-disc pl-5 space-y-2">
                    <li>修订《项目管理手册》，增加风险管理专章，明确各环节要求和责任。</li>
                    <li>开发项目风险管理工具，实现风险自动识别、评估和预警功能。</li>
                    <li>建立公司级风险案例库，收集整理各类项目风险事件及应对经验。</li>
                    <li>每季度开展项目风险评估专项培训，提升团队风险意识和管理能力。</li>
                    <li>设立项目风险准备金，用于突发风险事件的应急资源调配。</li>
                  </ul>
                </div>
              </div>
            </el-card>

            <!-- 相关资料区 -->
            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="f-16 fw-600">
                  相关资料
                </div>
              </template>
              <el-table :data="relatedMaterials" style="width: 100%;">
                <el-table-column prop="name" label="资料名称" />
                <el-table-column prop="type" label="资料类型" />
                <el-table-column prop="uploadTime" label="上传时间" />
                <el-table-column prop="uploader" label="上传人" />
                <el-table-column prop="description" label="描述" />
                <el-table-column label="操作">
                  <template #default>
                    <el-link type="primary" :underline="false" class="mr-3">
                      查看
                    </el-link>
                    <el-link type="primary" :underline="false">
                      下载
                    </el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>

            <!-- 内容标签页区 -->
            <el-card shadow="hover" class="mb-6">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="关联分析" name="relatedAnalysis">
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-semibold">
                      关联风险
                    </h3>
                    <el-table :data="relatedRisks" style="width: 100%;">
                      <el-table-column prop="name" label="风险名称" />
                      <el-table-column prop="type" label="风险类型" />
                      <el-table-column prop="level" label="风险等级">
                        <template #default="{ row }">
                          <el-tag :type="row.level === '高' ? 'danger' : 'warning'" size="small">
                            {{ row.level }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="description" label="关联性说明" />
                      <el-table-column label="操作">
                        <template #default>
                          <el-link type="primary" :underline="false">
                            查看详情
                          </el-link>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-semibold">
                      关联制度
                    </h3>
                    <el-table :data="relatedPolicies" style="width: 100%;">
                      <el-table-column prop="name" label="制度名称" />
                      <el-table-column prop="type" label="制度类型" />
                      <el-table-column prop="version" label="版本号" />
                      <el-table-column prop="description" label="关联性说明" />
                      <el-table-column label="操作">
                        <template #default>
                          <el-link type="primary" :underline="false">
                            查看详情
                          </el-link>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-semibold">
                      关联岗位
                    </h3>
                    <el-table :data="relatedPositions" style="width: 100%;">
                      <el-table-column prop="name" label="岗位名称" />
                      <el-table-column prop="department" label="所属部门" />
                      <el-table-column prop="description" label="关联性说明" />
                      <el-table-column label="操作">
                        <template #default>
                          <el-link type="primary" :underline="false">
                            查看详情
                          </el-link>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <div>
                    <h3 class="mb-4 text-lg font-semibold">
                      关联流程
                    </h3>
                    <el-table :data="relatedProcesses" style="width: 100%;">
                      <el-table-column prop="name" label="流程名称" />
                      <el-table-column prop="type" label="流程类型" />
                      <el-table-column prop="version" label="版本号" />
                      <el-table-column prop="description" label="关联性说明" />
                      <el-table-column label="操作">
                        <template #default>
                          <el-link type="primary" :underline="false">
                            查看详情
                          </el-link>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="改进进展" name="improvementProgress" />
                <el-tab-pane label="讨论与反馈" name="discussion" />
                <el-tab-pane label="生命周期" name="lifecycle" />
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <!-- 关联事件模块 -->
            <el-card shadow="hover">
              <template #header>
                <h2 class="text-lg font-semibold">
                  关联事件
                </h2>
              </template>
              <div class="space-y-3">
                <div>
                  <el-link type="primary" :underline="false">
                    客户A项目延期处理
                  </el-link>
                  <div class="mt-1 flex items-center">
                    <el-tag type="info" size="small">
                      项目事件
                    </el-tag>
                    <span class="ml-2 text-xs text-gray-500">2023-03-15</span>
                  </div>
                </div>
                <div>
                  <el-link type="primary" :underline="false">
                    核心供应商违约调查
                  </el-link>
                  <div class="mt-1 flex items-center">
                    <el-tag type="info" size="small">
                      供应商事件
                    </el-tag>
                    <span class="ml-2 text-xs text-gray-500">2023-03-22</span>
                  </div>
                </div>
              </div>
              <el-link type="primary" :underline="false" class="mt-3 block">
                查看更多
              </el-link>
            </el-card>

            <!-- 相似案例模块 -->
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <h2 class="text-lg font-semibold">
                  相似案例
                </h2>
              </template>
              <div class="space-y-3">
                <div>
                  <el-link type="primary" :underline="false">
                    B项目资源调配不足导致延期
                  </el-link>
                  <div class="mt-1 flex items-center">
                    <el-progress :percentage="85" :show-text="false" class="mr-2" />
                    <span class="text-xs text-gray-500">85%</span>
                  </div>
                </div>
                <div>
                  <el-link type="primary" :underline="false">
                    供应商风险管理不足案例
                  </el-link>
                  <div class="mt-1 flex items-center">
                    <el-progress :percentage="78" :show-text="false" class="mr-2" />
                    <span class="text-xs text-gray-500">78%</span>
                  </div>
                </div>
                <div>
                  <el-link type="primary" :underline="false">
                    跨部门协作不畅导致项目延期
                  </el-link>
                  <div class="mt-1 flex items-center">
                    <el-progress :percentage="72" :show-text="false" class="mr-2" />
                    <span class="text-xs text-gray-500">72%</span>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 推荐阅读模块 -->
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <h2 class="text-lg font-semibold">
                  推荐阅读
                </h2>
              </template>
              <div class="space-y-3">
                <div>
                  <el-link type="primary" :underline="false">
                    项目风险管理最佳实践
                  </el-link>
                  <div class="mt-1 flex items-center">
                    <el-tag type="success" size="small">
                      指南
                    </el-tag>
                    <div class="ml-2 flex items-center text-xs text-gray-500">
                      <el-icon class="mr-1">
                        <View />
                      </el-icon>
                      <span>1,024</span>
                    </div>
                  </div>
                </div>
                <div>
                  <el-link type="primary" :underline="false">
                    供应商管理中的风险控制
                  </el-link>
                  <div class="mt-1 flex items-center">
                    <el-tag type="warning" size="small">
                      案例
                    </el-tag>
                    <div class="ml-2 flex items-center text-xs text-gray-500">
                      <el-icon class="mr-1">
                        <View />
                      </el-icon>
                      <span>876</span>
                    </div>
                  </div>
                </div>
                <div>
                  <el-link type="primary" :underline="false">
                    敏捷项目管理中的风险应对
                  </el-link>
                  <div class="mt-1 flex items-center">
                    <el-tag type="info" size="small">
                      白皮书
                    </el-tag>
                    <div class="ml-2 flex items-center text-xs text-gray-500">
                      <el-icon class="mr-1">
                        <View />
                      </el-icon>
                      <span>1,532</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 快捷操作模块 -->
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <h2 class="text-lg font-semibold">
                  快捷操作
                </h2>
              </template>
              <div class="grid grid-cols-2 gap-2">
                <el-button plain class="!rounded-button whitespace-nowrap">
                  <el-icon class="mr-2">
                    <Thumb />
                  </el-icon>点赞 (86)
                </el-button>
                <el-button plain class="!rounded-button whitespace-nowrap">
                  <el-icon class="mr-2">
                    <Star />
                  </el-icon>收藏
                </el-button>
                <el-button plain class="!rounded-button whitespace-nowrap" style="margin-left: 0;">
                  <el-icon class="mr-2">
                    <Link />
                  </el-icon>复制链接
                </el-button>
                <el-button type="primary" class="!rounded-button whitespace-nowrap">
                  <el-icon class="mr-2">
                    <LightBulb />
                  </el-icon>提出改进
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .min-h-[1024px] {
    min-height: 1024px;
  }

  .max-w-[1440px] {
    max-width: 1440px;
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-1\/4 {
    width: 25%;
  }

  .pr-6 {
    padding-right: 1.5rem;
  }

  .bg-gray-50 {
    background-color: #f9fafb;
  }

  .mx-auto {
    margin-right: auto;
    margin-left: auto;
  }

  .flex {
    display: flex;
  }

  .py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .text-gray-500 {
    color: #6b7280;
  }

  .mb-4 {
    margin-bottom: 1rem;
  }

  .items-center {
    align-items: center;
  }

  .text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .mx-2 {
    margin-right: 0.5rem;
    margin-left: 0.5rem;
  }

  .text-gray-700 {
    color: #374151;
  }

  .justify-between {
    justify-content: space-between;
  }

  .text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .font-bold {
    font-weight: 700;
  }

  .mb-6 {
    margin-bottom: 1.5rem;
  }

  .space-x-2 > * + * {
    margin-left: 0.5rem;
  }

  .text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .font-semibold {
    font-weight: 600;
  }

  .mb-3 {
    margin-bottom: 0.75rem;
  }

  .space-y-3 > * + * {
    margin-top: 0.75rem;
  }

  .mt-1 {
    margin-top: 0.25rem;
  }

  .text-gray-900 {
    color: #111827;
  }

  .block {
    display: block;
  }

  .mt-3 {
    margin-top: 0.75rem;
  }

  .underline {
    text-decoration-line: underline;
  }

  .grid {
    display: grid;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .gap-4 {
    gap: 1rem;
  }

  .w-24 {
    width: 6rem;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }

  .list-disc {
    list-style-type: disc;
  }

  .pl-5 {
    padding-left: 1.25rem;
  }

  .space-y-2 > * + * {
    margin-top: 0.5rem;
  }

  .mt-2 {
    margin-top: 0.5rem;
  }

  .mr-2 {
    margin-right: 0.5rem;
  }

  .ml-2 {
    margin-left: 0.5rem;
  }

  .mr-3 {
    margin-right: 0.75rem;
  }

  .sticky {
    position: sticky;
  }

  .top-4 {
    top: 1rem;
  }

  .space-y-4 > * + * {
    margin-top: 1rem;
  }

  .gap-2 {
    gap: 0.5rem;
  }
</style>
