<script lang="ts" setup>
import { ref } from 'vue'
// 表单数据
const formData = ref({
  title: '',
  number: '',
  type: '',
  source: '',
  relatedSource: '',
  severity: '',
  department: '',
  tags: [],
  status: 'draft',
  background: '',
  process: '',
  analysis: '',
  lessons: [],
  suggestions: [],
  attachments: [],
  relatedRisks: [],
  relatedPolicies: [],
  relatedPositions: [],
  relatedProcesses: [],
  publishScope: 'company',
  isImportant: false,
  sendNotification: false,
  notificationTargets: [],
  notificationMethods: [],
  notificationContent: '',
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              新增处理措施
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />保存
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />保存并开始处理
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-close mr-1" />取消
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-view mr-1" />预览
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div class="min-h-screen bg-gray-50">
          <!-- 顶部导航 -->
          <div class="bg-white shadow-sm">
            <div class="mx-auto flex items-center justify-between px-6 py-4 container">
              <div class="flex items-center space-x-4">
                <div class="text-xl text-blue-600 font-bold">
                  应对之翼
                </div>
                <div class="text-gray-500">
                  /
                </div>
                <div class="text-gray-500">
                  持续改进优化
                </div>
                <div class="text-gray-500">
                  /
                </div>
                <div class="text-gray-500">
                  经验教训库
                </div>
                <div class="text-gray-500">
                  /
                </div>
                <div class="text-gray-800 font-medium">
                  新增经验教训
                </div>
              </div>
              <div class="flex space-x-3">
                <button class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-2 text-white">
                  保存草稿
                </button>
                <button
                  class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500"
                >
                  发布
                </button>
                <button
                  class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-gray-600"
                >
                  取消
                </button>
                <button
                  class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500"
                >
                  预览
                </button>
              </div>
            </div>
          </div>
          <!-- 主内容区 -->
          <div class="mx-auto flex px-6 py-8 container">
            <!-- 左侧编辑区 -->
            <div class="w-3/4 pr-6">
              <!-- 基本信息卡片 -->
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  基本信息
                </h2>
                <div class="grid grid-cols-2 gap-6">
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">标题 <span class="text-red-500">*</span></label>
                    <el-input v-model="formData.title" size="small" />
                  </div>
                  <div class="flex items-end">
                    <div class="flex-1">
                      <label class="mb-1 block text-sm text-gray-600">编号</label>
                      <el-input v-model="formData.title" size="small" />
                    </div>
                    <button class="!rounded-button ml-2 h-8 whitespace-nowrap bg-gray-100 px-3 text-sm">
                      自动生成
                    </button>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">类型 <span class="text-red-500">*</span></label>
                    <el-select v-model="formData.type" class="w-full" size="small">
                      <el-option label="流程问题" value="流程问题" />
                      <el-option label="人员问题" value="人员问题" />
                      <el-option label="制度问题" value="制度问题" />
                      <el-option label="系统问题" value="系统问题" />
                      <el-option label="其他" value="其他" />
                    </el-select>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">来源</label>
                    <el-select v-model="formData.source" class="w-full" size="small">
                      <el-option label="调查发现" value="调查发现" />
                      <el-option label="处理措施" value="处理措施" />
                      <el-option label="内部审计" value="内部审计" />
                      <el-option label="外部检查" value="外部检查" />
                      <el-option label="员工反馈" value="员工反馈" />
                      <el-option label="其他" value="其他" />
                    </el-select>
                  </div>
                  <div class="col-span-2">
                    <label class="mb-1 block text-sm text-gray-600">关联来源</label>
                    <div class="flex">
                      <el-input v-model="formData.relatedSource" size="small" />
                      <button
                        class="!rounded-button h-8 whitespace-nowrap border border-l-0 border-gray-300 rounded-r bg-gray-100 px-3"
                      >
                        查找
                      </button>
                    </div>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">严重程度 <span class="text-red-500">*</span></label>
                    <div class="mt-1 flex space-x-4">
                      <label class="flex items-center">
                        <el-radio v-model="formData.severity" label="严重" />
                        <span class="text-sm text-red-500">严重</span>
                      </label>
                      <label class="flex items-center">
                        <el-radio v-model="formData.severity" label="严重" />
                        <span class="text-sm text-orange-500">中等</span>
                      </label>
                      <label class="flex items-center">
                        <el-radio v-model="formData.severity" label="严重" />
                        <span class="text-sm text-yellow-500">轻微</span>
                      </label>
                    </div>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">责任部门</label>
                    <div class="flex">
                      <el-input v-model="formData.relatedSource" size="small" />
                      <button
                        class="!rounded-button h-8 whitespace-nowrap border border-l-0 border-gray-300 rounded-r bg-gray-100 px-3"
                      >
                        选择
                      </button>
                    </div>
                  </div>
                  <div class="col-span-2">
                    <label class="mb-1 block text-sm text-gray-600">标签</label>
                    <div class="min-h-8 flex flex-wrap items-center border border-gray-300 rounded px-2 py-1">
                      <div class="mb-1 mr-2 flex items-center rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">
                        <span>流程优化</span>
                        <span class="ml-1 cursor-pointer text-blue-500">×</span>
                      </div>
                      <input type="text" class="h-6 flex-1 border-none text-sm outline-none" placeholder="添加标签...">
                    </div>
                  </div>
                  <div>
                    <label class="mb-1 block text-sm text-gray-600">状态</label>
                    <div class="mt-1 flex space-x-4">
                      <label class="flex items-center">
                        <el-radio v-model="formData.status" label="draft" />
                        <span class="text-sm">草稿</span>
                      </label>
                      <label class="flex items-center">
                        <el-radio v-model="formData.status" label="published" />
                        <span class="text-sm">已发布</span>
                      </label>
                      <label class="flex items-center">
                        <el-radio v-model="formData.status" label="published" />
                        <span class="text-sm">已归档</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 内容编辑区 -->
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  内容编辑
                </h2>
                <!-- 问题背景 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      问题背景
                    </h3>
                    <span class="text-xs text-gray-500">建议 200-300 字</span>
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.background" type="textarea" :rows="8" placeholder="请输入问题背景" />
                  </div>
                </div>
                <!-- 事件经过 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      事件经过
                    </h3>
                    <span class="text-xs text-gray-500">建议 300-500 字</span>
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.process" type="textarea" :rows="10" placeholder="请输入事件经过" />
                  </div>
                </div>
                <!-- 问题分析 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      问题分析
                    </h3>
                    <span class="text-xs text-gray-500">建议 300-500 字</span>
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.process" type="textarea" :rows="10" placeholder="请输入事件经过" />
                    <div class="mt-3">
                      <h4 class="mb-2 text-xs font-medium">
                        原因分类
                      </h4>
                      <div class="grid grid-cols-3 gap-2">
                        <label class="flex items-center">
                          <el-checkbox v-model="formData.isImportant" />
                          <span class="text-xs">人员因素</span>
                        </label>
                        <label class="flex items-center">
                          <el-checkbox v-model="formData.isImportant" />
                          <span class="text-xs">流程因素</span>
                        </label>
                        <label class="flex items-center">
                          <el-checkbox v-model="formData.isImportant" />
                          <span class="text-xs">制度因素</span>
                        </label>
                        <label class="flex items-center">
                          <el-checkbox v-model="formData.isImportant" />
                          <span class="text-xs">系统因素</span>
                        </label>
                        <label class="flex items-center">
                          <el-checkbox v-model="formData.isImportant" />
                          <span class="text-xs">外部因素</span>
                        </label>
                        <label class="flex items-center">
                          <el-checkbox v-model="formData.isImportant" />
                          <span class="text-xs">其他</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 经验教训 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      经验教训
                    </h3>
                    <span class="text-xs text-gray-500">建议 300-500 字</span>
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.process" type="textarea" :rows="10" placeholder="请输入事件经过" />
                    <button
                      class="!rounded-button mt-3 whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                    >
                      添加要点
                    </button>
                  </div>
                </div>
                <!-- 改进建议 -->
                <div class="mb-6">
                  <div class="mb-3 flex items-center justify-between">
                    <h3 class="text-sm font-bold">
                      改进建议
                    </h3>
                    <span class="text-xs text-gray-500">建议 300-500 字</span>
                  </div>
                  <div class="border border-gray-300 rounded p-3">
                    <el-input v-model="formData.process" type="textarea" :rows="10" placeholder="请输入事件经过" />
                    <button
                      class="!rounded-button mt-3 whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                    >
                      添加建议
                    </button>
                  </div>
                </div>
              </div>
              <!-- 相关资料 -->
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  相关资料
                </h2>
                <div class="border border-gray-300 rounded">
                  <div class="flex items-center justify-between border-b border-gray-300 bg-gray-50 p-3">
                    <span class="text-sm font-medium">资料列表</span>
                    <button
                      class="!rounded-button whitespace-nowrap bg-blue-500 px-3 py-1 text-sm text-white"
                    >
                      添加资料
                    </button>
                  </div>
                  <div class="p-3">
                    <div class="py-8 text-center text-xs text-gray-500">
                      暂无相关资料
                    </div>
                  </div>
                </div>
                <div class="mt-4">
                  <h3 class="mb-2 text-sm font-medium">
                    上传资料
                  </h3>
                  <div class="border-2 border-gray-300 rounded border-dashed p-8 text-center">
                    <div class="mb-2 text-gray-400">
                      拖拽文件到此处或
                    </div>
                    <button class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-2 text-white">
                      选择文件
                    </button>
                    <div class="mt-2 text-xs text-gray-400">
                      支持 PDF, DOC, XLS, PPT, JPG, PNG 格式
                    </div>
                  </div>
                </div>
              </div>
              <!-- 关联信息 -->
              <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  关联信息
                </h2>
                <div class="space-y-4">
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      关联风险
                    </h3>
                    <div class="border border-gray-300 rounded p-3">
                      <div class="mb-2 flex items-center justify-between">
                        <span class="text-xs text-gray-500">已关联 0 个风险</span>
                        <button
                          class="!rounded-button whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                        >
                          添加风险
                        </button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      关联制度
                    </h3>
                    <div class="border border-gray-300 rounded p-3">
                      <div class="mb-2 flex items-center justify-between">
                        <span class="text-xs text-gray-500">已关联 0 个制度</span>
                        <button
                          class="!rounded-button whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                        >
                          添加制度
                        </button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      关联岗位
                    </h3>
                    <div class="border border-gray-300 rounded p-3">
                      <div class="mb-2 flex items-center justify-between">
                        <span class="text-xs text-gray-500">已关联 0 个岗位</span>
                        <button
                          class="!rounded-button whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                        >
                          添加岗位
                        </button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      关联流程
                    </h3>
                    <div class="border border-gray-300 rounded p-3">
                      <div class="mb-2 flex items-center justify-between">
                        <span class="text-xs text-gray-500">已关联 0 个流程</span>
                        <button
                          class="!rounded-button whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                        >
                          添加流程
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 发布设置 -->
              <div class="rounded-lg bg-white p-6 shadow-sm">
                <h2 class="mb-6 text-lg font-bold">
                  发布设置
                </h2>
                <div class="space-y-4">
                  <div>
                    <h3 class="mb-2 text-sm font-medium">
                      发布范围
                    </h3>
                    <div class="flex space-x-4">
                      <label class="flex items-center">
                        <el-radio v-model="formData.publishScope" label="company" />
                        <span class="text-sm">全公司</span>
                      </label>
                      <label class="flex items-center">
                        <el-radio v-model="formData.publishScope" label="department" />
                        <span class="text-sm">指定部门</span>
                      </label>
                      <label class="flex items-center">
                        <el-radio v-model="formData.publishScope" label="department" />
                        <span class="text-sm">指定人员</span>
                      </label>
                    </div>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <el-checkbox v-model="formData.isImportant" />
                      <span class="text-sm">标记为重要经验教训</span>
                    </label>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <el-checkbox v-model="formData.isImportant" />
                      <span class="text-sm">发布时推送通知</span>
                    </label>
                    <div class="ml-6 mt-2 border-l-2 border-gray-200 pl-4">
                      <div class="mb-2">
                        <h4 class="mb-1 text-xs font-medium">
                          通知对象
                        </h4>
                        <div class="flex">
                          <el-input v-model="formData.relatedSource" size="small" />
                          <button
                            class="!rounded-button h-8 whitespace-nowrap border border-l-0 border-gray-300 rounded-r bg-gray-100 px-3"
                          >
                            选择
                          </button>
                        </div>
                      </div>
                      <div class="mb-2">
                        <h4 class="mb-1 text-xs font-medium">
                          通知方式
                        </h4>
                        <div class="grid grid-cols-2 gap-2">
                          <label class="flex items-center">
                            <el-checkbox v-model="formData.isImportant" />
                            <span class="text-xs">系统消息</span>
                          </label>
                          <label class="flex items-center">
                            <el-checkbox v-model="formData.isImportant" />
                            <span class="text-xs">电子邮件</span>
                          </label>
                          <label class="flex items-center">
                            <el-checkbox v-model="formData.isImportant" />
                            <span class="text-xs">短信</span>
                          </label>
                          <label class="flex items-center">
                            <el-checkbox v-model="formData.isImportant" />
                            <span class="text-xs">其他</span>
                          </label>
                        </div>
                      </div>
                      <div>
                        <h4 class="mb-1 text-xs font-medium">
                          通知内容
                        </h4>
                        <el-input
                          v-model="formData.notificationContent" type="textarea" :rows="5"
                          placeholder="自定义通知内容模板"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 右侧辅助区 -->
            <div class="w-1/4">
              <!-- 内容模板 -->
              <div class="mb-4 rounded-lg bg-white p-4 shadow-sm">
                <h2 class="mb-3 text-sm font-bold">
                  内容模板
                </h2>
                <div class="space-y-2">
                  <div class="cursor-pointer border border-gray-200 rounded p-2 hover:bg-blue-50">
                    <div class="text-xs font-medium">
                      流程问题模板
                    </div>
                    <div class="text-xs text-gray-500">
                      适用于流程相关的问题分析
                    </div>
                  </div>
                  <div class="cursor-pointer border border-gray-200 rounded p-2 hover:bg-blue-50">
                    <div class="text-xs font-medium">
                      人员问题模板
                    </div>
                    <div class="text-xs text-gray-500">
                      适用于人员操作失误分析
                    </div>
                  </div>
                  <div class="cursor-pointer border border-gray-200 rounded p-2 hover:bg-blue-50">
                    <div class="text-xs font-medium">
                      系统问题模板
                    </div>
                    <div class="text-xs text-gray-500">
                      适用于系统缺陷分析
                    </div>
                  </div>
                </div>
                <button
                  class="!rounded-button mt-3 w-full whitespace-nowrap bg-gray-100 px-3 py-1 text-sm text-gray-600"
                >
                  保存为模板
                </button>
              </div>
              <!-- AI辅助 -->
              <div class="mb-4 rounded-lg bg-white p-4 shadow-sm">
                <h2 class="mb-3 text-sm font-bold">
                  AI辅助
                </h2>
                <div class="space-y-2">
                  <button
                    class="!rounded-button w-full whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                  >
                    AI优化内容
                  </button>
                  <button
                    class="!rounded-button w-full whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                  >
                    AI分析原因
                  </button>
                  <button
                    class="!rounded-button w-full whitespace-nowrap bg-blue-100 px-3 py-1 text-sm text-blue-600"
                  >
                    AI生成建议
                  </button>
                </div>
                <div class="mt-3 h-32 overflow-auto border border-gray-200 rounded p-2">
                  <div class="text-xs text-gray-500">
                    AI建议将显示在这里...
                  </div>
                </div>
              </div>
              <!-- 相似案例 -->
              <div class="rounded-lg bg-white p-4 shadow-sm">
                <h2 class="mb-3 text-sm font-bold">
                  相似案例
                </h2>
                <div class="space-y-2">
                  <div class="cursor-pointer border border-gray-200 rounded p-2 hover:bg-blue-50">
                    <div class="text-xs font-medium">
                      订单处理流程延误问题
                    </div>
                    <div class="text-xs text-gray-500">
                      相似度 85%
                    </div>
                  </div>
                  <div class="cursor-pointer border border-gray-200 rounded p-2 hover:bg-blue-50">
                    <div class="text-xs font-medium">
                      客户信息录入错误分析
                    </div>
                    <div class="text-xs text-gray-500">
                      相似度 72%
                    </div>
                  </div>
                  <div class="cursor-pointer border border-gray-200 rounded p-2 hover:bg-blue-50">
                    <div class="text-xs font-medium">
                      系统接口超时问题
                    </div>
                    <div class="text-xs text-gray-500">
                      相似度 68%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
  }
</style>
