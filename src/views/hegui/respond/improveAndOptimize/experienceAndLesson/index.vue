<script lang="ts" setup>
import { ref } from 'vue'
import {
  Calendar,
  Check,
  Document,
  Edit,
  Key,
  Monitor,
  More,
  PieChart,
  Search,
  Setting,
  Share,
  Star,
  Top,
  User,
} from '@element-plus/icons-vue'

const viewType = ref('table')
const activeTab = ref('type')
const timeRange = ref('month')
const tableData = [
  {
    id: 'LL-2023-001',
    title: '销售合同审批流程优化',
    type: '流程问题',
    source: '审计发现',
    severity: '中等',
    department: '销售部',
    creator: '张明',
    createTime: '2023-05-12',
    views: 245,
    likes: 32,
    status: '已发布',
  },
  {
    id: 'LL-2023-002',
    title: '新员工合规培训缺失',
    type: '人员问题',
    source: '员工提交',
    severity: '轻微',
    department: '人力资源部',
    creator: '李华',
    createTime: '2023-06-08',
    views: 186,
    likes: 24,
    status: '已发布',
  },
  {
    id: 'LL-2023-003',
    title: '报销制度漏洞修复',
    type: '制度问题',
    source: '处理结果',
    severity: '严重',
    department: '财务部',
    creator: '王伟',
    createTime: '2023-04-22',
    views: 312,
    likes: 45,
    status: '已发布',
  },
  {
    id: 'LL-2023-004',
    title: '系统权限管理不当',
    type: '系统问题',
    source: '审计发现',
    severity: '严重',
    department: 'IT部',
    creator: '陈晨',
    createTime: '2023-03-15',
    views: 298,
    likes: 38,
    status: '已发布',
  },
  {
    id: 'LL-2023-005',
    title: '供应商评估标准更新',
    type: '其他',
    source: '员工提交',
    severity: '中等',
    department: '采购部',
    creator: '赵静',
    createTime: '2023-07-03',
    views: 156,
    likes: 19,
    status: '已发布',
  },
]
const contributionData = [
  { rank: 1, name: '张明', department: '销售部', count: 28, likes: 156 },
  { rank: 2, name: '李华', department: '人力资源部', count: 22, likes: 132 },
  { rank: 3, name: '王伟', department: '财务部', count: 18, likes: 98 },
  { rank: 4, name: '陈晨', department: 'IT部', count: 15, likes: 87 },
  { rank: 5, name: '赵静', department: '采购部', count: 12, likes: 65 },
]

const hotData = [
  {
    rank: 1,
    title: '数据隐私保护最佳实践',
    type: '制度问题',
    source: '审计发现',
    views: 1245,
    likes: 86,
    date: '2023-06-15',
  },
  {
    rank: 2,
    title: '反洗钱系统使用指南',
    type: '系统问题',
    source: '处理结果',
    views: 982,
    likes: 72,
    date: '2023-05-28',
  },
  {
    rank: 3,
    title: '跨部门协作沟通技巧',
    type: '人员问题',
    source: '员工提交',
    views: 856,
    likes: 65,
    date: '2023-07-02',
  },
  {
    rank: 4,
    title: '合同风险管理要点',
    type: '流程问题',
    source: '审计发现',
    views: 723,
    likes: 58,
    date: '2023-06-22',
  },
  {
    rank: 5,
    title: '合规检查清单更新',
    type: '制度问题',
    source: '处理结果',
    views: 689,
    likes: 49,
    date: '2023-05-10',
  },
]
function getTagType(type: string) {
  switch (type) {
    case '流程问题': return ''
    case '人员问题': return 'success'
    case '制度问题': return 'warning'
    case '系统问题': return 'danger'
    default: return 'info'
  }
}
function getSeverityTagType(severity: string) {
  switch (severity) {
    case '严重': return 'danger'
    case '中等': return 'warning'
    case '轻微': return 'success'
    default: return ''
  }
}
function getStatusTagType(status: string) {
  switch (status) {
    case '已发布': return 'success'
    case '草稿': return 'info'
    case '已归档': return ''
    default: return ''
  }
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              经验教训库
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />保存
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />保存并开始处理
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-close mr-1" />取消
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-view mr-1" />预览
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <!-- 页面内容 -->
            <div class="flex flex-1 overflow-y-auto p-6">
              <div class="pr-6">
                <!-- 统计卡片区 -->
                <div class="grid grid-cols-4 mb-6 gap-4">
                  <el-card shadow="hover" class="!rounded-lg">
                    <div class="flex items-center">
                      <div class="mr-3 h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                        <el-icon class="text-xl text-blue-500">
                          <Document />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm text-gray-500">
                          总条目数
                        </div>
                        <div class="text-2xl font-bold">
                          1,248
                        </div>
                        <div class="flex items-center text-xs text-green-500">
                          <el-icon>
                            <Top />
                          </el-icon>
                          <span>12.5%</span>
                        </div>
                      </div>
                    </div>
                  </el-card>
                  <el-card shadow="hover" class="!rounded-lg">
                    <div class="flex items-center">
                      <div class="mr-3 h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
                        <el-icon class="text-xl text-green-500">
                          <Calendar />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm text-gray-500">
                          本月新增
                        </div>
                        <div class="text-2xl font-bold">
                          86
                        </div>
                        <div class="flex items-center text-xs text-green-500">
                          <el-icon>
                            <Top />
                          </el-icon>
                          <span>5.2%</span>
                        </div>
                      </div>
                    </div>
                  </el-card>
                  <el-card shadow="hover" class="!rounded-lg">
                    <div class="flex items-center">
                      <div class="mr-3 h-12 w-12 flex items-center justify-center rounded-full bg-purple-100">
                        <el-icon class="text-xl text-purple-500">
                          <PieChart />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm text-gray-500">
                          来源分布
                        </div>
                        <div class="text-sm text-gray-600">
                          审计发现 45%
                        </div>
                        <div class="text-xs text-gray-500">
                          员工提交 32%
                        </div>
                      </div>
                    </div>
                  </el-card>
                  <el-card shadow="hover" class="!rounded-lg">
                    <div class="flex items-center">
                      <div class="mr-3 h-12 w-12 flex items-center justify-center rounded-full bg-orange-100">
                        <el-icon class="text-xl text-orange-500">
                          <view />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm text-gray-500">
                          阅读总量
                        </div>
                        <div class="text-2xl font-bold">
                          8,742
                        </div>
                        <div class="flex items-center text-xs text-green-500">
                          <el-icon>
                            <Top />
                          </el-icon>
                          <span>18.3%</span>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </div>
                <!-- 最近访问区 -->
                <el-card shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-3 text-gray-800 font-medium">
                    最近访问
                  </div>
                  <div class="space-y-3">
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                        <el-icon class="text-blue-500">
                          <Document />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          销售合同审批流程优化
                        </div>
                        <div class="text-xs text-gray-500">
                          流程问题 · 审计发现
                        </div>
                      </div>
                    </div>
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-green-100">
                        <el-icon class="text-green-500">
                          <User />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          新员工合规培训缺失
                        </div>
                        <div class="text-xs text-gray-500">
                          人员问题 · 员工提交
                        </div>
                      </div>
                    </div>
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-purple-100">
                        <el-icon class="text-purple-500">
                          <Setting />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          报销制度漏洞修复
                        </div>
                        <div class="text-xs text-gray-500">
                          制度问题 · 处理结果
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="mt-3 cursor-pointer text-sm text-blue-500">
                    查看全部 >
                  </div>
                </el-card>
                <!-- 热门推荐区 -->
                <el-card shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-3 text-gray-800 font-medium">
                    热门推荐
                  </div>
                  <div class="space-y-3">
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-orange-100">
                        <el-icon class="text-orange-500">
                          <Star />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          数据隐私保护最佳实践
                        </div>
                        <div class="text-xs text-gray-500">
                          阅读量 1,245 · 点赞 86
                        </div>
                      </div>
                    </div>
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                        <el-icon class="text-blue-500">
                          <Star />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          反洗钱系统使用指南
                        </div>
                        <div class="text-xs text-gray-500">
                          阅读量 982 · 点赞 72
                        </div>
                      </div>
                    </div>
                    <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                      <div class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-green-100">
                        <el-icon class="text-green-500">
                          <Star />
                        </el-icon>
                      </div>
                      <div>
                        <div class="text-sm font-medium">
                          跨部门协作沟通技巧
                        </div>
                        <div class="text-xs text-gray-500">
                          阅读量 856 · 点赞 65
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="mt-3 cursor-pointer text-sm text-blue-500">
                    查看全部 >
                  </div>
                </el-card>
                <!-- 筛选区 -->
                <el-card shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-4 flex items-center space-x-4">
                    <el-select placeholder="类型" size="small" class="w-32">
                      <el-option label="全部" value="" />
                      <el-option label="流程问题" value="process" />
                      <el-option label="人员问题" value="person" />
                      <el-option label="制度问题" value="system" />
                    </el-select>
                    <el-select placeholder="来源" size="small" class="w-32">
                      <el-option label="全部" value="" />
                      <el-option label="审计发现" value="audit" />
                      <el-option label="员工提交" value="employee" />
                      <el-option label="处理结果" value="result" />
                    </el-select>
                    <el-select placeholder="责任部门" size="small" class="w-32">
                      <el-option label="全部" value="" />
                      <el-option label="财务部" value="finance" />
                      <el-option label="法务部" value="legal" />
                      <el-option label="人力资源部" value="hr" />
                    </el-select>
                    <el-date-picker
                      type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                      size="small" class="w-64"
                    />
                    <el-input placeholder="关键字搜索" size="small" class="w-48">
                      <template #prefix>
                        <el-icon>
                          <Search />
                        </el-icon>
                      </template>
                    </el-input>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      高级筛选
                    </el-button>
                  </div>
                </el-card>
                <!-- 经验教训列表区 -->
                <el-card shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-4 flex items-center justify-between">
                    <div class="text-gray-500">
                      共 1,248 条记录
                    </div>
                    <div class="flex space-x-2">
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        批量导入
                      </el-button>
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        导出
                      </el-button>
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        统计分析
                      </el-button>
                      <el-radio-group v-model="viewType" size="small">
                        <el-radio-button label="table">
                          表格视图
                        </el-radio-button>
                        <el-radio-button label="card">
                          卡片视图
                        </el-radio-button>
                      </el-radio-group>
                    </div>
                  </div>
                  <el-table v-if="viewType === 'table'" :data="tableData" style="width: 100%;">
                    <el-table-column type="selection" width="50" />
                    <el-table-column prop="id" label="编号" width="80" />
                    <el-table-column prop="title" label="标题" width="180" />
                    <el-table-column prop="type" label="类型" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getTagType(row.type)" size="small">
                          {{ row.type }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="source" label="来源" width="120" />
                    <el-table-column prop="severity" label="严重程度" width="100">
                      <template #default="{ row }">
                        <el-tag :type="getSeverityTagType(row.severity)" size="small">
                          {{ row.severity }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="department" label="责任部门" width="120" />
                    <el-table-column prop="creator" label="创建人" width="120" />
                    <el-table-column prop="createTime" label="创建日期" width="120" />
                    <el-table-column prop="views" label="阅读量" width="80" />
                    <el-table-column prop="likes" label="点赞数" width="80" />
                    <el-table-column prop="status" label="状态" width="80">
                      <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)" size="small">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                      <template #default>
                        <el-button type="text" size="small">
                          查看
                        </el-button>
                        <el-button type="text" size="small">
                          编辑
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div v-else class="grid grid-cols-3 gap-4">
                    <el-card v-for="item in tableData" :key="item.id" shadow="hover" class="!rounded-lg">
                      <div class="mb-2 flex items-start justify-between">
                        <div class="text-gray-800 font-medium">
                          {{ item.title }}
                        </div>
                        <el-tag :type="getTagType(item.type)" size="small">
                          {{ item.type }}
                        </el-tag>
                      </div>
                      <div class="mb-2 flex items-center justify-between">
                        <el-tag :type="getSeverityTagType(item.severity)" size="small">
                          {{ item.severity }}
                        </el-tag>
                        <div class="text-xs text-gray-500">
                          {{ item.createTime }}
                        </div>
                      </div>
                      <div class="line-clamp-2 mb-3 text-sm text-gray-600">
                        这是一条关于{{ item.type }}的经验教训描述内容，详细说明了问题的发现过程和解决方案...
                      </div>
                      <div class="flex items-center justify-between text-xs text-gray-500">
                        <div>{{ item.creator }} · {{ item.department }}</div>
                        <div class="flex space-x-2">
                          <span class="flex items-center">
                            <el-icon class="mr-1">
                              <view />
                            </el-icon>
                            {{ item.views }}
                          </span>
                          <span class="flex items-center">
                            <el-icon class="mr-1">
                              <Star />
                            </el-icon>
                            {{ item.likes }}
                          </span>
                        </div>
                      </div>
                      <div class="mt-3 flex justify-end border-t border-gray-100 pt-3 space-x-2">
                        <el-button size="small" class="!rounded-button whitespace-nowrap" type="text">
                          <el-icon>
                            <view />
                          </el-icon>
                        </el-button>
                        <el-button size="small" class="!rounded-button whitespace-nowrap" type="text">
                          <el-icon>
                            <Edit />
                          </el-icon>
                        </el-button>
                        <el-button size="small" class="!rounded-button whitespace-nowrap" type="text">
                          <el-icon>
                            <Share />
                          </el-icon>
                        </el-button>
                      </div>
                    </el-card>
                  </div>
                  <div class="mt-4 flex justify-center">
                    <el-pagination :total="1000" :page-size="10" :current-page="1" layout="prev, pager, next" />
                  </div>
                </el-card>
                <!-- 分类浏览区 -->
                <el-card shadow="hover" class="mb-6 !rounded-lg">
                  <div class="mb-4 text-gray-800 font-medium">
                    分类浏览
                  </div>
                  <el-tabs v-model="activeTab">
                    <el-tab-pane label="按类型" name="type">
                      <div class="grid grid-cols-5 gap-4">
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                            <el-icon class="text-xl text-blue-500">
                              <Document />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            流程问题
                          </div>
                          <div class="text-sm text-gray-500">
                            328 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-green-100"
                          >
                            <el-icon class="text-xl text-green-500">
                              <User />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            人员问题
                          </div>
                          <div class="text-sm text-gray-500">
                            256 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-purple-100"
                          >
                            <el-icon class="text-xl text-purple-500">
                              <Setting />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            制度问题
                          </div>
                          <div class="text-sm text-gray-500">
                            198 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-orange-100"
                          >
                            <el-icon class="text-xl text-orange-500">
                              <Monitor />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            系统问题
                          </div>
                          <div class="text-sm text-gray-500">
                            156 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-gray-100">
                            <el-icon class="text-xl text-gray-500">
                              <More />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            其他
                          </div>
                          <div class="text-sm text-gray-500">
                            310 条
                          </div>
                        </el-card>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="按来源" name="source">
                      <div class="grid grid-cols-5 gap-4">
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                            <el-icon class="text-xl text-blue-500">
                              <Search />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            审计发现
                          </div>
                          <div class="text-sm text-gray-500">
                            562 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-green-100"
                          >
                            <el-icon class="text-xl text-green-500">
                              <User />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            员工提交
                          </div>
                          <div class="text-sm text-gray-500">
                            399 条
                          </div>
                        </el-card>
                        <el-card shadow="hover" class="cursor-pointer text-center !rounded-lg">
                          <div
                            class="mx-auto mb-2 h-12 w-12 flex items-center justify-center rounded-full bg-purple-100"
                          >
                            <el-icon class="text-xl text-purple-500">
                              <Check />
                            </el-icon>
                          </div>
                          <div class="font-medium">
                            处理结果
                          </div>
                          <div class="text-sm text-gray-500">
                            287 条
                          </div>
                        </el-card>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </el-card>
                <!-- 热门经验教训区 -->
                <el-card shadow="hover" class="!rounded-lg">
                  <div class="mb-4 flex items-center justify-between">
                    <div class="text-gray-800 font-medium">
                      热门经验教训
                    </div>
                    <el-select v-model="timeRange" size="small" class="w-32">
                      <el-option label="本周" value="week" />
                      <el-option label="本月" value="month" />
                      <el-option label="本季度" value="quarter" />
                      <el-option label="本年" value="year" />
                    </el-select>
                  </div>
                  <el-table :data="hotData" style="width: 100%;">
                    <el-table-column prop="rank" label="排名" width="80">
                      <template #default="{ row }">
                        <span
                          v-if="row.rank <= 3" class="font-bold"
                          :class="{ 'text-red-500': row.rank === 1, 'text-orange-500': row.rank === 2, 'text-yellow-500': row.rank === 3 }"
                        >
                          {{ row.rank }}
                        </span>
                        <span v-else>{{ row.rank }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="title" label="标题">
                      <template #default="{ row }">
                        <span class="cursor-pointer hover:text-blue-500">{{ row.title }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="type" label="类型" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getTagType(row.type)" size="small">
                          {{ row.type }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="source" label="来源" width="120" />
                    <el-table-column prop="views" label="阅读量" width="100" />
                    <el-table-column prop="likes" label="点赞数" width="100" />
                    <el-table-column prop="date" label="发布日期" width="120" />
                  </el-table>
                  <div class="mt-3 cursor-pointer text-sm text-blue-500">
                    查看更多 >
                  </div>
                </el-card>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  我的收藏
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    销售合同审批流程优化
                  </div>
                  <div class="text-xs text-gray-500">
                    收藏于 2023-06-15
                  </div>
                </div>
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    新员工合规培训缺失
                  </div>
                  <div class="text-xs text-gray-500">
                    收藏于 2023-07-02
                  </div>
                </div>
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    报销制度漏洞修复
                  </div>
                  <div class="text-xs text-gray-500">
                    收藏于 2023-05-28
                  </div>
                </div>
              </div>
              <div class="mt-3 cursor-pointer text-sm text-blue-500">
                管理收藏 >
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  热门标签
                </div>
              </template>
              <div class="flex flex-wrap gap-2">
                <el-tag size="small" class="cursor-pointer">
                  合同管理
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  数据隐私
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  反洗钱
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  培训
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  报销
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  权限管理
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  供应商
                </el-tag>
                <el-tag size="small" class="cursor-pointer">
                  合规检查
                </el-tag>
              </div>
              <div class="mt-3 cursor-pointer text-sm text-blue-500">
                查看全部标签 >
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  学习建议
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    跨部门协作沟通技巧
                  </div>
                  <div class="text-xs text-gray-500">
                    基于您近期查看的协作问题
                  </div>
                </div>
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    数据隐私保护最佳实践
                  </div>
                  <div class="text-xs text-gray-500">
                    与您部门相关的高频问题
                  </div>
                </div>
                <div class="flex flex-col cursor-pointer rounded p-2 hover:bg-gray-50">
                  <div class="text-sm font-medium">
                    反洗钱系统使用指南
                  </div>
                  <div class="text-xs text-gray-500">
                    您团队近期关注的领域
                  </div>
                </div>
              </div>
              <div class="mt-3 cursor-pointer text-sm text-blue-500">
                查看全部建议 >
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  贡献排行
                </div>
              </template>
              <el-table :data="contributionData" size="small" class="w-full">
                <el-table-column prop="rank" label="排名" width="50" />
                <el-table-column prop="name" label="用户" width="80" />
                <el-table-column prop="department" label="部门" />
                <el-table-column prop="count" label="贡献" />
                <el-table-column prop="likes" label="点赞" />
              </el-table>
              <div class="mt-3 cursor-pointer text-sm text-blue-500">
                我的贡献 >
              </div>
            </el-card>
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  AI助手
                </div>
              </template>
              <div class="space-y-2">
                <el-button size="small" class="!rounded-button w-full whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Star />
                  </el-icon>
                  推荐相关教训
                </el-button>
                <el-button size="small" class="!rounded-button w-full whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Document />
                  </el-icon>
                  生成教训摘要
                </el-button>
                <el-button size="small" class="!rounded-button w-full whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Key />
                  </el-icon>
                  提取关键经验
                </el-button>
              </div>
              <div class="mt-3 rounded bg-gray-50 p-2 text-sm text-gray-600">
                根据您最近的浏览记录，建议关注"数据隐私保护"相关经验教训。
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .line-clamp-2 {
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
</style>
