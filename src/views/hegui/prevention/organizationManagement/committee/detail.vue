<script lang="ts" setup>
import { computed, ref } from 'vue'
import {
  Back,
  Download,
  Edit,
  Printer,
  Upload,
  View,
} from '@element-plus/icons-vue'

const activeTab = ref('agenda')
const resolutionFilter = ref('all')

const agendaItems = ref([
  { time: '14:00-14:10', title: '会议开场', content: '主持人致欢迎词，介绍会议议程' },
  { time: '14:10-14:30', title: '上次会议决议执行情况汇报', content: '各部门汇报上次会议决议的执行进度和完成情况' },
  { time: '14:30-15:00', title: '新版合规管理制度审议', content: '讨论并通过新版合规管理制度草案' },
  { time: '15:00-15:30', title: '合规风险季度报告', content: '合规部门汇报本季度合规风险识别与评估情况' },
  { time: '15:30-15:50', title: '合规问题讨论', content: '讨论近期出现的合规问题及解决方案' },
  { time: '15:50-16:00', title: '总结与下一步工作安排', content: '会议总结，明确下一步工作计划和责任人' },
])

const participants = ref([
  { name: '张明', position: '总经理', committeePosition: '主任', status: '出席', signTime: '2024-04-10 13:45', remark: '' },
  { name: '李华', position: '财务总监', committeePosition: '委员', status: '出席', signTime: '2024-04-10 13:50', remark: '' },
  { name: '王芳', position: '人力资源总监', committeePosition: '委员', status: '出席', signTime: '2024-04-10 13:55', remark: '' },
  { name: '赵强', position: '法务总监', committeePosition: '委员', status: '出席', signTime: '2024-04-10 13:58', remark: '' },
  { name: '刘伟', position: 'IT总监', committeePosition: '委员', status: '出席', signTime: '2024-04-10 14:00', remark: '' },
  { name: '陈静', position: '内控总监', committeePosition: '委员', status: '出席', signTime: '2024-04-10 14:02', remark: '' },
  { name: '黄勇', position: '合规总监', committeePosition: '副主任', status: '出席', signTime: '2024-04-10 13:40', remark: '会议记录人' },
  { name: '周涛', position: '审计总监', committeePosition: '委员', status: '出席', signTime: '2024-04-10 13:53', remark: '' },
  { name: '吴敏', position: '运营总监', committeePosition: '委员', status: '出席', signTime: '2024-04-10 13:59', remark: '' },
])

const minutes = ref([
  {
    title: '会议开场',
    content: '<p>张总主持会议开场，欢迎各位委员参加2024年第四次合规委员会例会。会议议程包括：</p><ul><li>上次会议决议执行情况汇报</li><li>新版合规管理制度审议</li><li>合规风险季度报告</li><li>合规问题讨论</li><li>总结与下一步工作安排</li></ul>',
  },
  {
    title: '上次会议决议执行情况汇报',
    content: '<p>各部门汇报了上次会议决议的执行情况：</p><ul><li>财务部已完成资金合规检查，发现3个问题已整改</li><li>人力资源部已完成新员工合规培训计划</li><li>IT部已完成系统权限合规审计</li></ul><p>总体执行情况良好，所有决议事项均按时完成。</p>',
  },
  {
    title: '新版合规管理制度审议',
    content: '<p>合规部黄总介绍了新版合规管理制度的主要修改内容：</p><ul><li>增加了数据合规管理章节</li><li>细化了合规风险评估流程</li><li>完善了合规举报机制</li></ul><p>经过讨论，全体委员一致通过新版合规管理制度，自2024年5月1日起实施。</p>',
  },
])

const resolutions = ref([
  { id: 'R2024-04-01', content: '组织新版合规管理制度培训', responsible: '黄勇', deadline: '2024-04-30', status: 'not_started', followUp: '' },
  { id: 'R2024-04-02', content: '开展数据合规专项检查', responsible: '刘伟', deadline: '2024-05-15', status: 'in_progress', followUp: '已完成前期准备工作' },
  { id: 'R2024-04-03', content: '更新合规风险清单', responsible: '陈静', deadline: '2024-04-20', status: 'completed', followUp: '已提交审核' },
  { id: 'R2024-04-04', content: '制定合规举报奖励办法', responsible: '赵强', deadline: '2024-04-25', status: 'overdue', followUp: '草案已完成，待法务审核' },
])

const filteredResolutions = computed(() => {
  if (resolutionFilter.value === 'all') { return resolutions.value }
  return resolutions.value.filter(item => item.status === resolutionFilter.value)
})

const attachments = ref([
  { name: '2024年第四次合规委员会会议议程.docx', type: '文档', uploader: '黄勇', uploadTime: '2024-04-09', size: '256KB' },
  { name: '新版合规管理制度草案.pdf', type: 'PDF', uploader: '黄勇', uploadTime: '2024-04-05', size: '1.2MB' },
  { name: 'Q1合规风险报告.pptx', type: '演示文稿', uploader: '陈静', uploadTime: '2024-04-08', size: '3.5MB' },
])

const relatedMeetings = ref([
  { date: '2024-03-15', title: '2024年第三次合规委员会例会' },
  { date: '2024-02-10', title: '2024年第二次合规委员会例会' },
  { date: '2024-01-08', title: '2024年第一次合规委员会例会' },
])

const tasks = ref([
  { content: '组织新版合规管理制度培训', responsible: '黄勇', deadline: '2024-04-30', status: 'not_started' },
  { content: '开展数据合规专项检查', responsible: '刘伟', deadline: '2024-05-15', status: 'in_progress' },
  { content: '更新合规风险清单', responsible: '陈静', deadline: '2024-04-20', status: 'completed' },
])

const satisfaction = ref({
  rating: 4.5,
  count: 7,
})

function getStatusTagType(status: string) {
  switch (status) {
    case 'completed': return 'success'
    case 'in_progress': return 'primary'
    case 'not_started': return 'info'
    case 'overdue': return 'danger'
    default: return 'info'
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'completed': return '已完成'
    case 'in_progress': return '进行中'
    case 'not_started': return '未开始'
    case 'overdue': return '已逾期'
    default: return status
  }
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              2024年第四次合规委员会例会
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-2">
            <button class="!rounded-button flex items-center whitespace-nowrap bg-blue-500 px-4 py-2 text-white">
              <el-icon class="mr-1">
                <Edit />
              </el-icon>
              编辑
            </button>
            <button class="!rounded-button flex items-center whitespace-nowrap bg-blue-500 px-4 py-2 text-white">
              <el-icon class="mr-1">
                <Download />
              </el-icon>
              导出会议纪要
            </button>
            <button class="!rounded-button flex items-center whitespace-nowrap bg-blue-500 px-4 py-2 text-white">
              <el-icon class="mr-1">
                <Printer />
              </el-icon>
              打印
            </button>
            <button class="!rounded-button flex items-center whitespace-nowrap bg-gray-300 px-4 py-2 text-gray-700">
              <el-icon class="mr-1">
                <Back />
              </el-icon>
              返回
            </button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  会议基本信息
                </div>
              </template>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex">
                  <div class="w-1/3 pr-4 text-right text-gray-500">
                    会议编号：
                  </div>
                  <div class="w-2/3">
                    M2024-04
                  </div>
                </div>
                <div class="flex">
                  <div class="w-1/3 pr-4 text-right text-gray-500">
                    会议时间：
                  </div>
                  <div class="w-2/3">
                    2024-04-10 14:00-16:00
                  </div>
                </div>
                <div class="flex">
                  <div class="w-1/3 pr-4 text-right text-gray-500">
                    会议地点：
                  </div>
                  <div class="w-2/3">
                    总部大楼5楼会议室
                  </div>
                </div>
                <div class="flex">
                  <div class="w-1/3 pr-4 text-right text-gray-500">
                    会议类型：
                  </div>
                  <div class="w-2/3">
                    例会
                  </div>
                </div>
                <div class="flex">
                  <div class="w-1/3 pr-4 text-right text-gray-500">
                    主持人：
                  </div>
                  <div class="w-2/3">
                    张总（总经理）
                  </div>
                </div>
                <div class="flex">
                  <div class="w-1/3 pr-4 text-right text-gray-500">
                    记录人：
                  </div>
                  <div class="w-2/3">
                    黄总（合规总监）
                  </div>
                </div>
                <div class="flex">
                  <div class="w-1/3 pr-4 text-right text-gray-500">
                    应到人数：
                  </div>
                  <div class="w-2/3">
                    9人
                  </div>
                </div>
                <div class="flex">
                  <div class="w-1/3 pr-4 text-right text-gray-500">
                    实到人数：
                  </div>
                  <div class="w-2/3">
                    9人
                  </div>
                </div>
                <div class="flex">
                  <div class="w-1/3 pr-4 text-right text-gray-500">
                    会议状态：
                  </div>
                  <div class="w-2/3 text-green-500">
                    已完成
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <!--              <template #header>
                <div class="f-16 fw-600">相关会议</div>
              </template> -->
              <el-tabs v-model="activeTab" class="p-4">
                <el-tab-pane label="会议议程" name="agenda">
                  <div class="timeline">
                    <div v-for="(item, index) in agendaItems" :key="index" class="timeline-item">
                      <div class="timeline-time">
                        {{ item.time }}
                      </div>
                      <div class="timeline-content">
                        <h3 class="font-medium">
                          {{ item.title }}
                        </h3>
                        <p class="mt-1 text-gray-600">
                          {{ item.content }}
                        </p>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="参会人员" name="participants">
                  <el-table :data="participants" style="width: 100%;">
                    <el-table-column prop="name" label="姓名" width="120" />
                    <el-table-column prop="position" label="职位" width="150" />
                    <el-table-column prop="committeePosition" label="委员会职务" width="150" />
                    <el-table-column prop="status" label="出席状态" width="120">
                      <template #default="{ row }">
                        <el-tag :type="row.status === '出席' ? 'success' : 'danger'" size="small">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="signTime" label="签到时间" width="150" />
                    <el-table-column prop="remark" label="备注" />
                  </el-table>
                  <div class="mt-4 flex justify-end text-sm text-gray-600">
                    <div class="mr-4">
                      应到人数：9人
                    </div>
                    <div class="mr-4">
                      实到人数：9人
                    </div>
                    <div>出席率：100%</div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="会议纪要" name="minutes">
                  <div class="p-4">
                    <div v-for="(item, index) in minutes" :key="index" class="mb-6">
                      <h3 class="mb-2 text-lg font-medium">
                        {{ item.title }}
                      </h3>
                      <div class="max-w-none prose" v-html="item.content" />
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="会议决议" name="resolutions">
                  <div class="mb-4">
                    <el-select v-model="resolutionFilter" placeholder="全部" class="w-40">
                      <el-option label="全部" value="all" />
                      <el-option label="未开始" value="not_started" />
                      <el-option label="进行中" value="in_progress" />
                      <el-option label="已完成" value="completed" />
                      <el-option label="已逾期" value="overdue" />
                    </el-select>
                  </div>
                  <el-table :data="filteredResolutions" style="width: 100%;">
                    <el-table-column prop="id" label="决议编号" width="120" />
                    <el-table-column prop="content" label="决议内容" width="300" />
                    <el-table-column prop="responsible" label="负责人" width="150" />
                    <el-table-column prop="deadline" label="完成期限" width="150" />
                    <el-table-column prop="status" label="当前状态" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)" size="small">
                          {{ getStatusText(row.status) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="followUp" label="跟进记录" />
                  </el-table>
                </el-tab-pane>

                <el-tab-pane label="附件资料" name="attachments">
                  <div class="p-4">
                    <el-button type="primary" class="mb-4">
                      <el-icon class="mr-1">
                        <Upload />
                      </el-icon>
                      上传附件
                    </el-button>
                    <el-table :data="attachments" style="width: 100%;">
                      <el-table-column prop="name" label="附件名称" width="200" />
                      <el-table-column prop="type" label="附件类型" width="120" />
                      <el-table-column prop="uploader" label="上传人" width="150" />
                      <el-table-column prop="uploadTime" label="上传时间" width="150" />
                      <el-table-column prop="size" label="文件大小" width="120" />
                      <el-table-column label="操作" width="150">
                        <template #default="{ row }">
                          <el-button type="text" size="small">
                            <el-icon>
                              <Download />
                            </el-icon>
                            下载
                          </el-button>
                          <el-button type="text" size="small">
                            <el-icon>
                              <View />
                            </el-icon>
                            预览
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  相关会议
                </div>
              </template>
              <div class="space-y-3">
                <div v-for="(meeting, index) in relatedMeetings" :key="index" class="border-b pb-3 last:border-b-0">
                  <div class="text-sm text-gray-500">
                    {{ meeting.date }}
                  </div>
                  <div class="text-sm font-medium">
                    {{ meeting.title }}
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  待办事项
                </div>
              </template>
              <div class="space-y-3">
                <div v-for="(task, index) in tasks" :key="index" class="border-b pb-3 last:border-b-0">
                  <div class="text-sm font-medium">
                    {{ task.content }}
                  </div>
                  <div class="mt-1 flex justify-between text-xs text-gray-500">
                    <span>{{ task.responsible }}</span>
                    <span>{{ task.deadline }}</span>
                  </div>
                  <el-tag :type="getStatusTagType(task.status)" size="small" class="mt-1">
                    {{ getStatusText(task.status) }}
                  </el-tag>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  会议满意度
                </div>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .timeline {
    position: relative;
    padding-left: 1.5rem;
  }

  .timeline::before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0.5rem;
    width: 2px;
    content: "";
    background: #e5e7eb;
  }

  .timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
    padding-left: 1.5rem;
  }

  .timeline-item:last-child {
    padding-bottom: 0;
  }

  .timeline-item::before {
    position: absolute;
    top: 0.25rem;
    left: 0;
    z-index: 1;
    width: 1rem;
    height: 1rem;
    content: "";
    background: #3b82f6;
    border-radius: 50%;
  }

  .timeline-time {
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: #4b5563;
  }

  .timeline-content {
    padding: 1rem;
    background: #f9fafb;
    border-radius: 0.375rem;
  }

  .prose :deep(p) {
    margin-bottom: 1rem;
  }

  .prose :deep(ul) {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
    list-style-type: disc;
  }

  .prose :deep(li) {
    margin-bottom: 0.5rem;
  }
</style>
