<script lang="ts" setup>
import { ref } from 'vue'
import { ArrowDown, Delete, Plus, Search } from '@element-plus/icons-vue'

// 表单数据
const formData = ref({
  person: '',
  department: '',
  appointmentDate: '',
  status: 'active',
  phone: '',
  email: '',
  officeLocation: '',
  responsibilityAreas: [],
  specificResponsibilities: '',
  responsibleRegions: [],
  reportTo: '',
  requiredTrainings: [],
  trainingStatus: 'notStarted',
  certificateName: '',
  certificateDate: '',
  validityPeriod: '',
  authorizationScopes: [],
  workPlans: [],
  reportingFrequency: 'quarterly',
  reportingMethod: 'system',
  systemPermissions: [],
  dataPermission: 'departmentAndSub',
  notificationPermissions: [],
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              新增合规联络人
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-4">
            <button class="!rounded-button whitespace-nowrap bg-blue-600 px-6 py-2 text-white hover:bg-blue-700">
              保存
            </button>
            <button
              class="!rounded-button whitespace-nowrap border border-blue-600 bg-white px-6 py-2 text-blue-600 hover:bg-blue-50"
            >
              保存并新增
            </button>
            <button
              class="!rounded-button whitespace-nowrap border border-gray-300 bg-white px-6 py-2 text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  基本信息
                </div>
              </template>
              <div class="space-y-6">
                <!-- 人员选择 -->
                <div class="grid grid-cols-5 items-center gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">
                    <span class="text-red-500">*</span> 人员选择
                  </label>
                  <div class="col-span-4">
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <input type="text" placeholder="请选择人员" class="flex-1 border-none outline-none">
                      <el-icon class="cursor-pointer text-gray-500">
                        <Search />
                      </el-icon>
                    </div>
                    <div class="mt-2 rounded bg-gray-50 p-3">
                      <div class="flex items-center space-x-4">
                        <div class="h-10 w-10 rounded-full bg-gray-200" />
                        <div>
                          <p class="font-medium">
                            张明远
                          </p>
                          <p class="text-sm text-gray-500">
                            风险管理部 | 合规主管
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 所属部门 -->
                <div class="grid grid-cols-5 items-center gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">
                    <span class="text-red-500">*</span> 所属部门
                  </label>
                  <div class="col-span-4">
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <input type="text" value="风险管理部" class="flex-1 border-none outline-none">
                      <el-icon class="cursor-pointer text-gray-500">
                        <ArrowDown />
                      </el-icon>
                    </div>
                  </div>
                </div>

                <!-- 任命日期 -->
                <div class="grid grid-cols-5 items-center gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">
                    <span class="text-red-500">*</span> 任命日期
                  </label>
                  <div class="col-span-4">
                    <el-date-picker type="date" placeholder="选择日期" class="w-full" />
                  </div>
                </div>

                <!-- 状态 -->
                <div class="grid grid-cols-5 items-center gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">状态</label>
                  <div class="col-span-4 flex space-x-6">
                    <label class="flex items-center">
                      <input type="radio" name="status" checked class="mr-2">
                      <span>在职</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="status" class="mr-2">
                      <span>离职</span>
                    </label>
                  </div>
                </div>

                <!-- 联系方式 -->
                <div class="grid grid-cols-5 items-start gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">联系方式</label>
                  <div class="col-span-4 space-y-4">
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <input type="text" placeholder="电话" class="flex-1 border-none outline-none">
                    </div>
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <input type="text" value="<EMAIL>" class="flex-1 border-none outline-none">
                    </div>
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <input type="text" placeholder="办公地点" class="flex-1 border-none outline-none">
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  职责信息
                </div>
              </template>
              <div class="space-y-6">
                <!-- 职责范围 -->
                <div class="grid grid-cols-5 items-start gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">
                    <span class="text-red-500">*</span> 职责范围
                  </label>
                  <div class="col-span-4">
                    <div class="grid grid-cols-3 gap-4">
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>财务合规</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>人事合规</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>销售合规</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>采购合规</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>研发合规</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>市场合规</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>客服合规</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>IT合规</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>法务合规</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>其他</span>
                      </label>
                    </div>
                  </div>
                </div>

                <!-- 具体职责 -->
                <div class="grid grid-cols-5 items-start gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">具体职责</label>
                  <div class="col-span-4">
                    <textarea
                      rows="4" placeholder="请详细描述联络人的工作职责"
                      class="w-full border border-gray-300 rounded p-3 outline-none"
                    />
                  </div>
                </div>

                <!-- 负责区域 -->
                <div class="grid grid-cols-5 items-center gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">负责区域</label>
                  <div class="col-span-4">
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <input type="text" placeholder="请选择负责区域" class="flex-1 border-none outline-none">
                      <el-icon class="cursor-pointer text-gray-500">
                        <ArrowDown />
                      </el-icon>
                    </div>
                  </div>
                </div>

                <!-- 汇报对象 -->
                <div class="grid grid-cols-5 items-center gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">
                    <span class="text-red-500">*</span> 汇报对象
                  </label>
                  <div class="col-span-4">
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <input type="text" placeholder="请选择汇报对象" class="flex-1 border-none outline-none">
                      <el-icon class="cursor-pointer text-gray-500">
                        <Search />
                      </el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  培训与授权
                </div>
              </template>
              <div class="space-y-6">
                <!-- 必修培训 -->
                <div class="grid grid-cols-5 items-start gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">必修培训</label>
                  <div class="col-span-4">
                    <div class="grid grid-cols-2 gap-4">
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>联络人基础培训</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>合规风险识别</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>合规报告编写</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>举报处理培训</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>其他</span>
                      </label>
                    </div>
                  </div>
                </div>

                <!-- 培训状态 -->
                <div class="grid grid-cols-5 items-center gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">培训状态</label>
                  <div class="col-span-4 flex space-x-6">
                    <label class="flex items-center">
                      <input type="radio" name="trainingStatus" checked class="mr-2">
                      <span>未培训</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="trainingStatus" class="mr-2">
                      <span>培训中</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="trainingStatus" class="mr-2">
                      <span>已完成</span>
                    </label>
                  </div>
                </div>

                <!-- 证书信息 -->
                <div class="grid grid-cols-5 items-start gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">证书信息</label>
                  <div class="col-span-4 space-y-4">
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <input type="text" placeholder="证书名称" class="flex-1 border-none outline-none">
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                      <el-date-picker type="date" placeholder="获取日期" class="w-full" />
                      <el-date-picker type="date" placeholder="有效期" class="w-full" />
                    </div>
                  </div>
                </div>

                <!-- 授权范围 -->
                <div class="grid grid-cols-5 items-start gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">授权范围</label>
                  <div class="col-span-4">
                    <div class="grid grid-cols-2 gap-4">
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>合规检查</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>合规培训</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>合规咨询</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>问题处理</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>其他</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  工作计划
                </div>
              </template>
              <div class="space-y-6">
                <!-- 工作计划表格 -->
                <div class="overflow-x-auto">
                  <table class="w-full">
                    <thead>
                      <tr class="border-b border-gray-200 text-left text-sm text-gray-600">
                        <th class="pb-2">
                          工作内容
                        </th>
                        <th class="pb-2">
                          开始日期
                        </th>
                        <th class="pb-2">
                          结束日期
                        </th>
                        <th class="pb-2">
                          目标/指标
                        </th>
                        <th class="pb-2">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr class="border-b border-gray-100">
                        <td class="py-3">
                          <input
                            type="text" placeholder="输入工作内容"
                            class="w-full border border-gray-300 rounded px-3 py-1 outline-none"
                          >
                        </td>
                        <td class="py-3">
                          <el-date-picker type="date" placeholder="选择日期" class="w-full" />
                        </td>
                        <td class="py-3">
                          <el-date-picker type="date" placeholder="选择日期" class="w-full" />
                        </td>
                        <td class="py-3">
                          <input
                            type="text" placeholder="输入目标/指标"
                            class="w-full border border-gray-300 rounded px-3 py-1 outline-none"
                          >
                        </td>
                        <td class="py-3">
                          <el-icon class="cursor-pointer text-red-500">
                            <Delete />
                          </el-icon>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- 添加计划按钮 -->
                <button
                  class="!rounded-button whitespace-nowrap border border-gray-300 border-dashed bg-white px-4 py-2 text-gray-500 hover:border-blue-500 hover:text-blue-500"
                >
                  <el-icon class="mr-1">
                    <Plus />
                  </el-icon>
                  添加计划
                </button>

                <!-- 定期汇报 -->
                <div class="grid grid-cols-5 items-center gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">定期汇报</label>
                  <div class="grid col-span-4 grid-cols-2 gap-4">
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <select class="flex-1 border-none outline-none">
                        <option>每周</option>
                        <option>每月</option>
                        <option selected>
                          每季度
                        </option>
                        <option>半年</option>
                        <option>年度</option>
                      </select>
                    </div>
                    <div class="flex items-center border border-gray-300 rounded px-3 py-2">
                      <select class="flex-1 border-none outline-none">
                        <option>书面报告</option>
                        <option>会议汇报</option>
                        <option selected>
                          系统填报
                        </option>
                        <option>其他</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  权限设置
                </div>
              </template>
              <div class="space-y-6">
                <!-- 系统权限 -->
                <div class="grid grid-cols-5 items-start gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">系统权限</label>
                  <div class="col-span-4 border border-gray-300 rounded p-3">
                    <div class="space-y-2">
                      <div class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>合规管理系统</span>
                      </div>
                      <div class="ml-6 space-y-2">
                        <div class="flex items-center">
                          <input type="checkbox" class="mr-2" checked>
                          <span>合规报告查看</span>
                        </div>
                        <div class="flex items-center">
                          <input type="checkbox" class="mr-2">
                          <span>合规报告编辑</span>
                        </div>
                        <div class="flex items-center">
                          <input type="checkbox" class="mr-2" checked>
                          <span>合规问题处理</span>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>培训管理系统</span>
                      </div>
                      <div class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>举报管理系统</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 数据权限 -->
                <div class="grid grid-cols-5 items-center gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">数据权限</label>
                  <div class="col-span-4 flex space-x-6">
                    <label class="flex items-center">
                      <input type="radio" name="dataPermission" class="mr-2">
                      <span>本部门数据</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="dataPermission" checked class="mr-2">
                      <span>本部门及下级部门数据</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="dataPermission" class="mr-2">
                      <span>全部数据</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="dataPermission" class="mr-2">
                      <span>自定义</span>
                    </label>
                  </div>
                </div>

                <!-- 通知权限 -->
                <div class="grid grid-cols-5 items-start gap-4">
                  <label class="col-span-1 text-right text-sm text-gray-600">通知权限</label>
                  <div class="col-span-4">
                    <div class="grid grid-cols-2 gap-4">
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>接收合规预警</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>接收举报通知</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>接收培训通知</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2" checked>
                        <span>接收检查通知</span>
                      </label>
                      <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span>其他</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  联络人职责指引
                </div>
              </template>
              <div class="text-sm space-y-3">
                <div class="flex items-start">
                  <div class="mr-2 mt-1 h-2 w-2 rounded-full bg-blue-500" />
                  <p>监督本部门合规政策的执行情况</p>
                </div>
                <div class="flex items-start">
                  <div class="mr-2 mt-1 h-2 w-2 rounded-full bg-blue-500" />
                  <p>收集和反馈合规风险信息</p>
                </div>
                <div class="flex items-start">
                  <div class="mr-2 mt-1 h-2 w-2 rounded-full bg-blue-500" />
                  <p>协助处理合规问题和举报</p>
                </div>
                <div class="flex items-start">
                  <div class="mr-2 mt-1 h-2 w-2 rounded-full bg-blue-500" />
                  <p>组织本部门合规培训</p>
                </div>
                <div class="flex items-start">
                  <div class="mr-2 mt-1 h-2 w-2 rounded-full bg-blue-500" />
                  <p>定期提交合规工作报告</p>
                </div>
                <div class="flex items-start">
                  <div class="mr-2 mt-1 h-2 w-2 rounded-full bg-blue-500" />
                  <p>参与合规风险评估和改进</p>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  工作流程
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <div class="mb-1 flex items-center">
                    <div
                      class="mr-2 h-6 w-6 flex items-center justify-center rounded-full bg-blue-100 text-sm text-blue-600 font-medium"
                    >
                      1
                    </div>
                    <h3 class="font-medium">
                      风险识别
                    </h3>
                  </div>
                  <p class="ml-8 text-sm text-gray-600">
                    发现并记录合规风险点
                  </p>
                </div>
                <div>
                  <div class="mb-1 flex items-center">
                    <div
                      class="mr-2 h-6 w-6 flex items-center justify-center rounded-full bg-blue-100 text-sm text-blue-600 font-medium"
                    >
                      2
                    </div>
                    <h3 class="font-medium">
                      初步评估
                    </h3>
                  </div>
                  <p class="ml-8 text-sm text-gray-600">
                    评估风险等级和影响范围
                  </p>
                </div>
                <div>
                  <div class="mb-1 flex items-center">
                    <div
                      class="mr-2 h-6 w-6 flex items-center justify-center rounded-full bg-blue-100 text-sm text-blue-600 font-medium"
                    >
                      3
                    </div>
                    <h3 class="font-medium">
                      报告提交
                    </h3>
                  </div>
                  <p class="ml-8 text-sm text-gray-600">
                    向合规部门提交报告
                  </p>
                </div>
                <div>
                  <div class="mb-1 flex items-center">
                    <div
                      class="mr-2 h-6 w-6 flex items-center justify-center rounded-full bg-blue-100 text-sm text-blue-600 font-medium"
                    >
                      4
                    </div>
                    <h3 class="font-medium">
                      跟进处理
                    </h3>
                  </div>
                  <p class="ml-8 text-sm text-gray-600">
                    跟踪问题处理进展
                  </p>
                </div>
                <div>
                  <div class="mb-1 flex items-center">
                    <div
                      class="mr-2 h-6 w-6 flex items-center justify-center rounded-full bg-blue-100 text-sm text-blue-600 font-medium"
                    >
                      5
                    </div>
                    <h3 class="font-medium">
                      反馈闭环
                    </h3>
                  </div>
                  <p class="ml-8 text-sm text-gray-600">
                    向相关部门反馈处理结果
                  </p>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  表单验证
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">基本信息</span>
                  <span class="text-sm text-green-500 font-medium">已完成</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">职责信息</span>
                  <span class="text-sm text-yellow-500 font-medium">待完善</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">培训与授权</span>
                  <span class="text-sm text-gray-500 font-medium">未开始</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">工作计划</span>
                  <span class="text-sm text-gray-500 font-medium">未开始</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">权限设置</span>
                  <span class="text-sm text-gray-500 font-medium">未开始</span>
                </div>
              </div>
              <div class="mt-4 rounded bg-red-50 p-3 text-sm text-red-600">
                <p class="font-medium">
                  以下必填项未完成：
                </p>
                <ul class="mt-1 list-disc pl-5">
                  <li class="cursor-pointer hover:underline">
                    职责范围
                  </li>
                  <li class="cursor-pointer hover:underline">
                    汇报对象
                  </li>
                </ul>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
  }

  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 输入框聚焦样式 */
  input:focus,
  textarea:focus,
  select:focus {
    box-shadow: 0 0 0 2px rgb(30 136 229 / 20%);
  }

  /* 表格行悬停效果 */
  tr:hover {
    background-color: #f8fafc;
  }

  /* 日期选择器宽度适配 */
  .el-date-editor {
    width: 100%;
  }
</style>
