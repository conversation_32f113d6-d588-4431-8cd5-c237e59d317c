<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { Bell, Download, Edit, Share } from '@element-plus/icons-vue'

const activeTab = ref('duty')

// 负责区域数据
const responsibleAreas = ref([
  { areaName: '财务部', areaType: '部门', mainBusiness: '财务管理', riskLevel: '中' },
  { areaName: '税务申报', areaType: '业务', mainBusiness: '税务处理', riskLevel: '高' },
  { areaName: '资金管理', areaType: '业务', mainBusiness: '资金运作', riskLevel: '中' },
  { areaName: '财务报告', areaType: '业务', mainBusiness: '报表编制', riskLevel: '低' },
])

// 工作计划数据
const workPlans = ref([
  { content: '季度财务合规检查', startDate: '2023-10-01', endDate: '2023-10-31', target: '完成全部财务合规检查', status: '已完成' },
  { content: '年度税务合规审查', startDate: '2023-11-01', endDate: '2023-11-30', target: '完成税务合规审查报告', status: '进行中' },
  { content: '财务合规培训', startDate: '2023-12-01', endDate: '2023-12-15', target: '组织2次财务合规培训', status: '未开始' },
])

// 工作日志数据
const workLogs = ref([
  { date: '2023-10-10', content: '完成第三季度财务合规检查', result: '发现3个问题，已整改2个' },
  { date: '2023-10-15', content: '参加税务合规培训', result: '学习了最新的税务法规' },
  { date: '2023-10-20', content: '编制财务合规报告', result: '完成报告初稿，待审核' },
])

// 合规检查记录数据
const complianceChecks = ref([
  { date: '2023-10-05', item: '资金审批流程', result: '符合要求', issueCount: 0, followUpStatus: '已完成' },
  { date: '2023-10-12', item: '费用报销', result: '发现2个问题', issueCount: 2, followUpStatus: '整改中' },
  { date: '2023-10-18', item: '税务申报', result: '符合要求', issueCount: 0, followUpStatus: '已完成' },
])

// 培训完成情况数据
const trainings = ref([
  { name: '财务合规基础', progress: 100, completionTime: '2023-09-15' },
  { name: '税务合规实务', progress: 80, completionTime: '2023-10-10' },
  { name: '反洗钱培训', progress: 100, completionTime: '2023-08-20' },
  { name: '数据隐私保护', progress: 30, completionTime: '' },
])

// 证书信息数据
const certificates = ref([
  { name: '注册会计师', obtainDate: '2020-06-15', expiryDate: '2025-06-15', issuer: '中国注册会计师协会', status: '有效' },
  { name: '税务师', obtainDate: '2019-11-20', expiryDate: '2024-11-20', issuer: '中国注册税务师协会', status: '有效' },
  { name: '国际内部审计师', obtainDate: '2018-05-10', expiryDate: '2023-05-10', issuer: '国际内部审计师协会', status: '已过期' },
])

// 培训记录数据
const trainingRecords = ref([
  { time: '2023-10-15', content: '税务合规实务培训', form: '线上', participationStatus: '已完成' },
  { time: '2023-09-20', content: '财务合规案例分析', form: '线下', participationStatus: '已完成' },
  { time: '2023-08-10', content: '反洗钱法规解读', form: '线上', participationStatus: '已完成' },
])

// 考核记录数据
const evaluationRecords = ref([
  { period: '2023年第三季度', level: '优秀', evaluator: '李总', time: '2023-10-05', comment: '工作认真负责，合规意识强' },
  { period: '2023年第二季度', level: '良好', evaluator: '李总', time: '2023-07-05', comment: '工作表现良好，沟通能力有待提高' },
  { period: '2023年第一季度', level: '优秀', evaluator: '李总', time: '2023-04-05', comment: '工作出色，及时发现并解决了多个合规风险' },
])

// 最近活动数据
const recentActivities = ref([
  { time: '2023-10-20 14:30', content: '提交了季度财务合规报告' },
  { time: '2023-10-18 10:15', content: '参加了部门合规会议' },
  { time: '2023-10-15 09:00', content: '完成了税务合规培训' },
  { time: '2023-10-10 16:45', content: '处理了2个合规问题' },
])

const radarChart = ref<HTMLElement>()

// 风险等级标签类型
function getRiskTagType(level: string) {
  switch (level) {
    case '高': return 'danger'
    case '中': return 'warning'
    default: return 'success'
  }
}

// 考核等级标签类型
function getEvaluationTagType(level: string) {
  switch (level) {
    case '优秀': return 'success'
    case '良好': return 'warning'
    default: return ''
  }
}

// 初始化雷达图
function initRadarChart() {
  if (!radarChart.value) { return }

  const chart = echarts.init(radarChart.value)
  const option = {
    animation: false,
    radar: {
      indicator: [
        { name: '工作质量', max: 100 },
        { name: '工作效率', max: 100 },
        { name: '合规意识', max: 100 },
        { name: '沟通能力', max: 100 },
        { name: '团队协作', max: 100 },
      ],
      radius: '65%',
    },
    series: [{
      type: 'radar',
      data: [
        {
          value: [95, 85, 98, 80, 90],
          name: '绩效评估',
          areaStyle: {
            color: 'rgba(64, 158, 255, 0.2)',
          },
          lineStyle: {
            color: 'rgba(64, 158, 255, 1)',
          },
          itemStyle: {
            color: 'rgba(64, 158, 255, 1)',
          },
        },
      ],
    }],
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

onMounted(() => {
  nextTick(() => {
    initRadarChart()
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              合规联络人详情 - 王明
            </h1>
            <el-tag type="warning" class="ml-4">
              在职
            </el-tag>
          </div>
          <div class="flex space-x-2">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Edit />
              </el-icon>
              <span>编辑</span>
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Download />
              </el-icon>
              <span>导出</span>
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Share />
              </el-icon>
              <span>分配任务</span>
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Bell />
              </el-icon>
              <span>发送通知</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- 基本信息卡片 -->
              <div class="">
                <div class="flex">
                  <!-- 头像 -->
                  <div class="mr-6 flex-shrink-0">
                    <div
                      class="h-20 w-20 flex items-center justify-center rounded-full bg-blue-100 text-3xl text-blue-600 font-bold"
                    >
                      王
                    </div>
                  </div>
                  <!-- 基本信息 -->
                  <div class="grid grid-cols-2 w-full gap-4">
                    <div>
                      <p class="text-sm text-gray-500">
                        姓名
                      </p>
                      <p class="text-gray-800">
                        王明
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        所属部门
                      </p>
                      <p class="text-gray-800">
                        财务部
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        职位
                      </p>
                      <p class="text-gray-800">
                        财务主管
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        任命日期
                      </p>
                      <p class="text-gray-800">
                        2023-10-15
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        联系电话
                      </p>
                      <p class="text-gray-800">
                        13800001234
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        电子邮箱
                      </p>
                      <p class="text-gray-800">
                        <EMAIL>
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        办公地点
                      </p>
                      <p class="text-gray-800">
                        总部大楼3层
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        状态
                      </p>
                      <p class="text-gray-800">
                        在职
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <el-tabs v-model="activeTab" class="p-6">
                <el-tab-pane label="职责信息" name="duty">
                  <!-- 职责范围 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-bold">
                      职责范围
                    </h3>
                    <ul class="list-disc pl-6 space-y-2">
                      <li>财务合规</li>
                      <li>税务合规</li>
                      <li>资金管理合规</li>
                      <li>财务报告合规</li>
                    </ul>
                  </div>

                  <!-- 具体职责 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-bold">
                      具体职责
                    </h3>
                    <p class="text-gray-700 leading-relaxed">
                      负责监督公司财务活动的合规性，确保所有财务操作符合相关法律法规和公司内部政策。定期审查财务报告，识别潜在的合规风险并提出改进建议。协调内外部审计工作，确保审计发现的问题得到及时整改。组织财务合规培训，提高团队合规意识。
                    </p>
                  </div>

                  <!-- 负责区域 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-bold">
                      负责区域
                    </h3>
                    <el-table :data="responsibleAreas" style="width: 100%;">
                      <el-table-column prop="areaName" label="区域名称" width="180" />
                      <el-table-column prop="areaType" label="区域类型" width="180" />
                      <el-table-column prop="mainBusiness" label="主要业务" />
                      <el-table-column prop="riskLevel" label="风险等级">
                        <template #default="{ row }">
                          <el-tag :type="getRiskTagType(row.riskLevel)">
                            {{ row.riskLevel }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 汇报关系 -->
                  <div>
                    <h3 class="mb-4 text-lg font-bold">
                      汇报关系
                    </h3>
                    <div class="grid grid-cols-2 gap-6">
                      <div>
                        <p class="text-sm text-gray-500">
                          汇报对象
                        </p>
                        <p class="text-gray-800">
                          李总（财务总监）
                        </p>
                      </div>
                      <div>
                        <p class="text-sm text-gray-500">
                          合作对象
                        </p>
                        <p class="text-gray-800">
                          张三（合规专员）
                        </p>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="工作记录" name="work">
                  <!-- 工作计划 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-bold">
                      工作计划
                    </h3>
                    <el-table :data="workPlans" style="width: 100%;">
                      <el-table-column prop="content" label="工作内容" />
                      <el-table-column prop="startDate" label="开始日期" width="120" />
                      <el-table-column prop="endDate" label="结束日期" width="120" />
                      <el-table-column prop="target" label="目标/指标" />
                      <el-table-column prop="status" label="完成状态" width="120">
                        <template #default="{ row }">
                          <el-tag :type="row.status === '已完成' ? 'success' : 'warning'">
                            {{ row.status }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 工作日志 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-bold">
                      工作日志
                    </h3>
                    <el-timeline>
                      <el-timeline-item
                        v-for="(log, index) in workLogs" :key="index" :timestamp="log.date"
                        placement="top"
                      >
                        <el-card>
                          <h4>{{ log.content }}</h4>
                          <p class="mt-2 text-gray-600">
                            {{ log.result }}
                          </p>
                        </el-card>
                      </el-timeline-item>
                    </el-timeline>
                  </div>

                  <!-- 合规检查记录 -->
                  <div>
                    <h3 class="mb-4 text-lg font-bold">
                      合规检查记录
                    </h3>
                    <el-table :data="complianceChecks" style="width: 100%;">
                      <el-table-column prop="date" label="检查日期" width="120" />
                      <el-table-column prop="item" label="检查项目" />
                      <el-table-column prop="result" label="检查结果" />
                      <el-table-column prop="issueCount" label="问题数量" width="120" />
                      <el-table-column prop="followUpStatus" label="跟进状态" width="120">
                        <template #default="{ row }">
                          <el-tag
                            :type="row.followUpStatus === '已完成' ? 'success' : 'warning'"
                          >
                            {{ row.followUpStatus }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="培训情况" name="training">
                  <!-- 培训完成情况 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-bold">
                      培训完成情况
                    </h3>
                    <div class="space-y-4">
                      <div v-for="(training, index) in trainings" :key="index">
                        <div class="mb-1 flex items-center justify-between">
                          <span>{{ training.name }}</span>
                          <span class="text-sm text-gray-500">{{ training.completionTime || '未完成' }}</span>
                        </div>
                        <el-progress
                          :percentage="training.progress"
                          :status="training.progress === 100 ? 'success' : ''"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- 证书信息 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-bold">
                      证书信息
                    </h3>
                    <el-table :data="certificates" style="width: 100%;">
                      <el-table-column prop="name" label="证书名称" />
                      <el-table-column prop="obtainDate" label="获取日期" width="120" />
                      <el-table-column prop="expiryDate" label="有效期" width="120" />
                      <el-table-column prop="issuer" label="发证机构" />
                      <el-table-column prop="status" label="状态" width="120">
                        <template #default="{ row }">
                          <el-tag :type="row.status === '有效' ? 'success' : 'danger'">
                            {{ row.status }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 培训记录 -->
                  <div>
                    <h3 class="mb-4 text-lg font-bold">
                      培训记录
                    </h3>
                    <el-timeline>
                      <el-timeline-item
                        v-for="(record, index) in trainingRecords" :key="index" :timestamp="record.time"
                        placement="top"
                      >
                        <el-card>
                          <h4>{{ record.content }}</h4>
                          <p class="mt-2 text-gray-600">
                            <span class="mr-4">培训形式: {{ record.form }}</span>
                            <span>参与状态: {{ record.participationStatus }}</span>
                          </p>
                        </el-card>
                      </el-timeline-item>
                    </el-timeline>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="考核评估" name="evaluation">
                  <!-- 绩效评估 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-bold">
                      绩效评估
                    </h3>
                    <div class="h-80">
                      <div ref="radarChart" class="h-full w-full" />
                    </div>
                  </div>

                  <!-- 考核记录 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-lg font-bold">
                      考核记录
                    </h3>
                    <el-table :data="evaluationRecords" style="width: 100%;">
                      <el-table-column prop="period" label="考核周期" width="180" />
                      <el-table-column prop="level" label="考核等级" width="120">
                        <template #default="{ row }">
                          <el-tag :type="getEvaluationTagType(row.level)">
                            {{ row.level }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="evaluator" label="考核人" width="180" />
                      <el-table-column prop="time" label="考核时间" width="180" />
                      <el-table-column prop="comment" label="考核意见" />
                    </el-table>
                  </div>

                  <!-- 自我评估 -->
                  <div>
                    <h3 class="mb-4 text-lg font-bold">
                      自我评估
                    </h3>
                    <el-card>
                      <h4 class="mb-2">
                        工作成果
                      </h4>
                      <p class="mb-4 text-gray-700">
                        本季度完成了财务合规体系的全面梳理，建立了合规检查清单，组织了3次部门合规培训，及时发现并纠正了2个潜在的合规风险点。
                      </p>

                      <h4 class="mb-2">
                        存在问题
                      </h4>
                      <p class="mb-4 text-gray-700">
                        对新的税务法规理解不够深入，需要加强学习；合规检查的覆盖面还可以进一步扩大；与业务部门的沟通效率有待提高。
                      </p>

                      <h4 class="mb-2">
                        改进计划
                      </h4>
                      <p class="text-gray-700">
                        1. 参加税务合规专项培训<br>
                        2. 扩大合规检查范围至所有子公司<br>
                        3. 每月与业务部门召开合规沟通会<br>
                        4. 建立合规知识库供团队学习
                      </p>
                    </el-card>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
            <el-card shadow="hover" class="mt-20" />
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  工作统计
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-500">
                    本月合规检查
                  </p>
                  <p class="text-2xl font-bold">
                    8次
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    本月合规培训
                  </p>
                  <p class="text-2xl font-bold">
                    2次
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    本月问题处理
                  </p>
                  <p class="text-2xl font-bold">
                    5个
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    合规报告提交
                  </p>
                  <p class="text-2xl font-bold">
                    3份
                  </p>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  任务进度
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <div class="mb-1 flex items-center justify-between">
                    <span>季度财务合规报告</span>
                    <span class="text-sm text-gray-500">截止: 2023-12-31</span>
                  </div>
                  <el-progress :percentage="75" />
                </div>
                <div>
                  <div class="mb-1 flex items-center justify-between">
                    <span>年度税务合规审查</span>
                    <span class="text-sm text-gray-500">截止: 2023-11-30</span>
                  </div>
                  <el-progress :percentage="40" />
                </div>
                <div>
                  <div class="mb-1 flex items-center justify-between">
                    <span>合规培训计划</span>
                    <span class="text-sm text-gray-500">截止: 2023-12-15</span>
                  </div>
                  <el-progress :percentage="90" />
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  最近活动
                </div>
              </template>
              <el-timeline>
                <el-timeline-item
                  v-for="(activity, index) in recentActivities" :key="index" :timestamp="activity.time"
                  placement="top"
                >
                  <p>{{ activity.content }}</p>
                </el-timeline-item>
              </el-timeline>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .bg-blue-100 {
    background-color: #ebf5ff;
  }

  .text-blue-600 {
    color: #1a73e8;
  }
</style>
