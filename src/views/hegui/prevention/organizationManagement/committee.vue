<script lang="ts" setup>
  import { ref } from 'vue'
  import {
    Delete as ElIconDelete,
    Download as ElIconDownload,
    Edit as ElIconEdit,
    Plus as ElIconPlus,
    Setting as ElIconSetting,
    View as ElIconView,
  } from '@element-plus/icons-vue'

  const activeMeetingTab = ref('recent')

  const committeeMembers = ref([
    {
      name: '张总',
      position: '总经理',
      committeeRole: '主任委员',
      department: '公司高层',
      appointmentDate: '2023/5/15',
      contact: '<EMAIL>',
      status: '在任',
    },
    {
      name: '李经理',
      position: '法务总监',
      committeeRole: '副主任委员',
      department: '法务部',
      appointmentDate: '2023/5/15',
      contact: '<EMAIL>',
      status: '在任',
    },
    {
      name: '王总监',
      position: '财务总监',
      committeeRole: '委员',
      department: '财务部',
      appointmentDate: '2023/5/15',
      contact: '<EMAIL>',
      status: '在任',
    },
    {
      name: '赵经理',
      position: '人力资源总监',
      committeeRole: '委员',
      department: '人力资源部',
      appointmentDate: '2023/5/15',
      contact: '<EMAIL>',
      status: '在任',
    },
    {
      name: '钱经理',
      position: 'IT总监',
      committeeRole: '委员',
      department: '信息技术部',
      appointmentDate: '2023/5/15',
      contact: '<EMAIL>',
      status: '在任',
    },
    {
      name: '孙经理',
      position: '市场总监',
      committeeRole: '委员',
      department: '市场部',
      appointmentDate: '2023/5/15',
      contact: '<EMAIL>',
      status: '在任',
    },
    {
      name: '周经理',
      position: '运营总监',
      committeeRole: '委员',
      department: '运营部',
      appointmentDate: '2023/5/15',
      contact: '<EMAIL>',
      status: '在任',
    },
    {
      name: '吴经理',
      position: '审计总监',
      committeeRole: '委员',
      department: '审计部',
      appointmentDate: '2023/5/15',
      contact: '<EMAIL>',
      status: '在任',
    },
    {
      name: '郑经理',
      position: '合规总监',
      committeeRole: '委员',
      department: '合规部',
      appointmentDate: '2023/5/15',
      contact: '<EMAIL>',
      status: '在任',
    },
  ])

  const meetingRecords = ref([
    {
      date: '2024/4/10',
      topic: '2024年第四次合规委员会例会',
      type: '例会',
      participants: 9,
      host: '张总',
      resolutions: '通过新版合规管理制度',
      recorder: '黄总',
    },
    {
      date: '2024/3/15',
      topic: '2024年第三次合规委员会例会',
      type: '例会',
      participants: 8,
      host: '张总',
      resolutions: '讨论季度合规风险报告',
      recorder: '黄总',
    },
    {
      date: '2024/2/20',
      topic: '年度合规风险评估专项会议',
      type: '专项会议',
      participants: 7,
      host: '李经理',
      resolutions: '确定年度合规风险防控重点',
      recorder: '王总监',
    },
    {
      date: '2024/1/18',
      topic: '2024年第一次合规委员会例会',
      type: '例会',
      participants: 9,
      host: '张总',
      resolutions: '审议年度合规工作计划',
      recorder: '黄总',
    },
    {
      date: '2023/12/15',
      topic: '合规培训计划讨论会',
      type: '临时会议',
      participants: 6,
      host: '李经理',
      resolutions: '确定下年度合规培训计划',
      recorder: '赵经理',
    },
  ])

  const workPlans = ref([
    {
      time: '2024年5月',
      name: '合规管理制度修订',
      description: '根据最新法规要求修订公司合规管理制度',
      owner: '李经理',
      status: '进行中',
    },
    {
      time: '2024年4月',
      name: '季度合规风险报告',
      description: '编制第一季度合规风险分析报告',
      owner: '王总监',
      status: '已完成',
    },
    {
      time: '2024年3月',
      name: '合规培训计划实施',
      description: '开展全员合规意识培训',
      owner: '赵经理',
      status: '已完成',
    },
    {
      time: '2024年6月',
      name: '半年度合规审计',
      description: '对公司各部门进行合规审计',
      owner: '吴经理',
      status: '待开始',
    },
  ])

  const recentEvents = ref([
    {
      date: '2024/5/15',
      name: '合规委员会月度例会',
      status: '进行中',
    },
    {
      date: '2024/4/30',
      name: '合规培训讲座',
      status: '已完成',
    },
    {
      date: '2024/4/25',
      name: '合规风险防控研讨会',
      status: '已完成',
    },
    {
      date: '2024/4/10',
      name: '合规管理制度修订会议',
      status: '已完成',
    },
  ])
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              合规委员会
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" @click="goEdit(null)">
              <svg-icon name="ep:plus" />
              <span class="ml-4">编辑委员会</span>
            </el-button>
            <el-button type="primary" plain>
              <svg-icon name="ep:download" />
              <span class="ml-4">导出</span>
            </el-button>
            <el-button type="primary" plain>
              <svg-icon name="ep:download" />
              <span class="ml-4">新增会议记录</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  委员会基本信息
                </div>
              </template>
              <div class="grid grid-cols-2 mb-4 gap-4">
                <div>
                  <div class="text-sm text-gray-500">
                    委员会名称
                  </div>
                  <div class="text-gray-800">
                    公司合规委员会
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    成立日期
                  </div>
                  <div class="text-gray-800">
                    2023-05-15
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    委员数量
                  </div>
                  <div class="text-gray-800">
                    9人
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    最近会议日期
                  </div>
                  <div class="text-gray-800">
                    2024-04-10
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    下次会议日期
                  </div>
                  <div class="text-gray-800">
                    2024-05-15
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    例会频率
                  </div>
                  <div class="text-gray-800">
                    月度
                  </div>
                </div>
              </div>
              <div>
                <div class="mb-1 text-sm text-gray-500">
                  委员会简介
                </div>
                <div class="text-gray-800">
                  公司合规委员会是公司合规管理的最高决策机构，负责制定公司合规政策、监督合规管理体系的运行、审议重大合规事项、指导合规风险防控工作。委员会由公司高层管理人员和相关部门负责人组成，定期召开会议审议合规工作进展和重大合规事项。
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    委员列表
                  </div>
                  <button
                    class="!rounded-button whitespace-nowrap border border-blue-500 px-3 py-1.5 text-sm text-blue-500">
                    <el-icon class="mr-1">
                      <ElIconSetting />
                    </el-icon>
                    管理委员
                  </button>
                </div>
              </template>
              <el-table :data="committeeMembers" style="width: 100%;" class="border">
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="position" label="职位" width="150" />
                <el-table-column prop="committeeRole" label="委员会职务" width="150">
                  <template #default="{ row }">
                    <span :class="{
                        'text-blue-500': row.committeeRole === '主任委员',
                        'text-green-500': row.committeeRole === '副主任委员',
                      }">
                      {{ row.committeeRole }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="department" label="所属部门" width="150" />
                <el-table-column prop="appointmentDate" label="任命日期" width="120" />
                <el-table-column prop="contact" label="联系方式" width="180" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <span :class="{ 'text-green-500': row.status === '在任', 'text-gray-500': row.status === '离任' }">
                      {{ row.status }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                  <template #default="{ row }">
                    <div class="flex space-x-2">
                      <el-icon class="cursor-pointer text-blue-500">
                        <ElIconView />
                      </el-icon>
                      <el-icon class="cursor-pointer text-blue-500">
                        <ElIconEdit />
                      </el-icon>
                      <el-icon class="cursor-pointer text-red-500">
                        <ElIconDelete />
                      </el-icon>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="mt-4 flex items-center justify-between">
                <div class="text-sm text-gray-500">
                  共 {{ committeeMembers.length }} 条记录
                </div>
                <el-pagination :page-size="10" :pager-count="5" layout="prev, pager, next"
                  :total="committeeMembers.length" />
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    会议记录
                  </div>
                  <button
                    class="!rounded-button whitespace-nowrap border border-blue-500 px-3 py-1.5 text-sm text-blue-500">
                    <el-icon class="mr-1">
                      <ElIconPlus />
                    </el-icon>
                    新增会议记录
                  </button>
                </div>
              </template>
              <el-tabs v-model="activeMeetingTab" class="mb-4">
                <el-tab-pane label="最近会议" name="recent" />
                <el-tab-pane label="所有会议" name="all" />
                <el-tab-pane label="待办事项" name="todo" />
              </el-tabs>
              <el-table :data="meetingRecords" style="width: 100%;" class="border">
                <el-table-column prop="date" label="会议日期" width="120" />
                <el-table-column prop="topic" label="会议主题" width="200" />
                <el-table-column prop="type" label="会议类型" width="120">
                  <template #default="{ row }">
                    <span :class="{
                        'text-blue-500': row.type === '例会',
                        'text-orange-500': row.type === '临时会议',
                        'text-purple-500': row.type === '专项会议',
                      }">
                      {{ row.type }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="participants" label="参与人数" width="100" />
                <el-table-column prop="host" label="主持人" width="120" />
                <el-table-column prop="resolutions" label="重要决议" width="200" />
                <el-table-column prop="recorder" label="记录人" width="120" />
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <div class="flex space-x-2">
                      <el-icon class="cursor-pointer text-blue-500">
                        <ElIconView />
                      </el-icon>
                      <el-icon class="cursor-pointer text-blue-500">
                        <ElIconEdit />
                      </el-icon>
                      <el-icon class="cursor-pointer text-red-500">
                        <ElIconDelete />
                      </el-icon>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="mt-4 flex items-center justify-between">
                <div class="text-sm text-gray-500">
                  共 {{ meetingRecords.length }} 条记录
                </div>
                <el-pagination :page-size="10" :pager-count="5" layout="prev, pager, next"
                  :total="meetingRecords.length" />
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  工作计划
                </div>
              </template>
              <div class="timeline">
                <div v-for="(plan, index) in workPlans" :key="index" class="timeline-item mb-6">
                  <div class="flex">
                    <div class="timeline-date w-32 flex-shrink-0">
                      <div class="text-blue-500 font-medium">
                        {{ plan.time }}
                      </div>
                    </div>
                    <div class="timeline-content flex-1 border rounded-lg bg-gray-50 p-4">
                      <div class="flex items-start justify-between">
                        <div>
                          <h3 class="text-gray-800 font-medium">
                            {{ plan.name }}
                          </h3>
                          <p class="mt-1 text-sm text-gray-600">
                            {{ plan.description }}
                          </p>
                          <div class="mt-2 text-sm text-gray-500">
                            负责人: {{ plan.owner }}
                          </div>
                        </div>
                        <div class="flex items-center space-x-2">
                          <span :class="{
                              'bg-blue-100 text-blue-800': plan.status === '进行中',
                              'bg-green-100 text-green-800': plan.status === '已完成',
                              'bg-gray-100 text-gray-800': plan.status === '待开始',
                            }" class="rounded-full px-2 py-1 text-xs">
                            {{ plan.status }}
                          </span>
                          <el-icon class="cursor-pointer text-blue-500">
                            <ElIconEdit />
                          </el-icon>
                          <el-icon class="cursor-pointer text-red-500">
                            <ElIconDelete />
                          </el-icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    工作统计
                  </div>
                  <button
                    class="!rounded-button whitespace-nowrap border border-blue-500 px-3 py-1.5 text-sm text-blue-500">
                    <el-icon class="mr-1">
                      <ElIconPlus />
                    </el-icon>
                    新增工作计划
                  </button>
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <div class="text-sm text-gray-500">
                    本年度已召开会议
                  </div>
                  <div class="text-2xl text-blue-500 font-bold">
                    12 次
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    已完成工作项
                  </div>
                  <div class="text-2xl text-green-500 font-bold">
                    28 项
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    进行中工作项
                  </div>
                  <div class="text-2xl text-orange-500 font-bold">
                    5 项
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    重大决策数量
                  </div>
                  <div class="text-2xl text-purple-500 font-bold">
                    8 项
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  工作效率
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                      工作完成率
                    </div>
                    <div class="text-sm text-green-500">
                      ↑ 5.2%
                    </div>
                  </div>
                  <div class="mt-1">
                    <div class="h-2.5 w-full rounded-full bg-gray-200">
                      <div class="h-2.5 rounded-full bg-blue-500" style="width: 85%;" />
                    </div>
                    <div class="mt-1 text-right text-sm text-gray-500">
                      85%
                    </div>
                  </div>
                </div>
                <div>
                  <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                      决议执行率
                    </div>
                    <div class="text-sm text-green-500">
                      ↑ 3.8%
                    </div>
                  </div>
                  <div class="mt-1">
                    <div class="h-2.5 w-full rounded-full bg-gray-200">
                      <div class="h-2.5 rounded-full bg-green-500" style="width: 92%;" />
                    </div>
                    <div class="mt-1 text-right text-sm text-gray-500">
                      92%
                    </div>
                  </div>
                </div>
                <div>
                  <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                      会议出勤率
                    </div>
                    <div class="text-sm text-red-500">
                      ↓ 1.5%
                    </div>
                  </div>
                  <div class="mt-1">
                    <div class="h-2.5 w-full rounded-full bg-gray-200">
                      <div class="h-2.5 rounded-full bg-purple-500" style="width: 78%;" />
                    </div>
                    <div class="mt-1 text-right text-sm text-gray-500">
                      78%
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  近期重要活动
                </div>
              </template>
              <div class="space-y-4">
                <div v-for="(event, index) in recentEvents" :key="index" class="border-b pb-3 last:border-0 last:pb-0">
                  <div class="flex items-start justify-between">
                    <div>
                      <div class="text-sm text-gray-500">
                        {{ event.date }}
                      </div>
                      <div class="mt-1 text-gray-800 font-medium">
                        {{ event.name }}
                      </div>
                    </div>
                    <span :class="{
                        'bg-blue-100 text-blue-800': event.status === '进行中',
                        'bg-green-100 text-green-800': event.status === '已完成',
                        'bg-gray-100 text-gray-800': event.status === '待开始',
                      }" class="rounded-full px-2 py-1 text-xs">
                      {{ event.status }}
                    </span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .timeline {
    position: relative;
  }

  .timeline::before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 4rem;
    width: 2px;
    content: "";
    background-color: #e5e7eb;
  }

  .timeline-item {
    position: relative;
  }

  .timeline-item::before {
    position: absolute;
    top: 0.5rem;
    left: 4rem;
    z-index: 1;
    width: 0.75rem;
    height: 0.75rem;
    content: "";
    background-color: #3b82f6;
    border-radius: 50%;
    transform: translateX(-50%);
  }

  .timeline-date {
    padding-right: 1rem;
    text-align: right;
  }

  .timeline-content {
    position: relative;
  }
</style>
