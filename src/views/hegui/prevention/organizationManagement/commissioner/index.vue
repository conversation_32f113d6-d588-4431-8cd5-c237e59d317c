<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Download as ElIconDownload,
  Plus as ElIconPlus,
  Upload as ElIconUpload,
} from '@element-plus/icons-vue'
  // 筛选条件
const filter = ref({
  status: 'all',
  responsibilities: [],
  training: 'all',
})
// 组织架构树
const orgTree = ref([
  {
    id: 1,
    label: '总公司',
    children: [
      {
        id: 2,
        label: '财务部',
        children: [
          { id: 3, label: '财务一组' },
          { id: 4, label: '财务二组' },
        ],
      },
      {
        id: 5,
        label: '人力资源部',
        children: [
          { id: 6, label: '招聘组' },
          { id: 7, label: '培训组' },
        ],
      },
      {
        id: 8,
        label: '法务部',
      },
    ],
  },
])
const treeProps = {
  children: 'children',
  label: 'label',
}
// 联络人列表
const contactList = ref([
  {
    id: 1,
    name: '王明',
    department: '财务部',
    position: '财务主管',
    appointmentDate: '2023/10/15',
    responsibility: '财务合规',
    contact: '<EMAIL>',
    trainingStatus: '已完成',
    status: '在职',
  },
  {
    id: 2,
    name: '李红',
    department: '人力资源部',
    position: 'HR经理',
    appointmentDate: '2023/10/20',
    responsibility: '人事合规',
    contact: '<EMAIL>',
    trainingStatus: '已完成',
    status: '在职',
  },
  {
    id: 3,
    name: '张伟',
    department: '法务部',
    position: '法务专员',
    appointmentDate: '2023/09/05',
    responsibility: '法务合规',
    contact: '<EMAIL>',
    trainingStatus: '未完成',
    status: '在职',
  },
  {
    id: 4,
    name: '陈芳',
    department: '财务部',
    position: '财务专员',
    appointmentDate: '2023/08/12',
    responsibility: '财务合规',
    contact: '<EMAIL>',
    trainingStatus: '已完成',
    status: '离职',
  },
])
// 分页
const pagination = ref({
  current: 1,
  size: 10,
  total: 40,
})
// 选中的联络人
const selectedContacts = ref([])
// 图表引用
const coverageChart = ref(null)
const trainingChart = ref(null)
// 处理节点点击
function handleNodeClick(data) {
  console.log('点击节点:', data)
}
// 处理选择变化
function handleSelectionChange(selection) {
  selectedContacts.value = selection
}
// 查看详情
function handleView(row) {
  console.log('查看:', row)
}
// 编辑
function handleEdit(row) {
  console.log('编辑:', row)
}
// 初始化图表
function initCharts() {
  nextTick(() => {
    // 部门覆盖率图表
    const coverage = echarts.init(coverageChart.value)
    coverage.setOption({
      animation: false,
      series: [
        {
          type: 'pie',
          radius: ['70%', '90%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 80, name: '覆盖率', itemStyle: { color: '#1E88E5' } },
            { value: 20, name: '未覆盖', itemStyle: { color: '#E8E8E8' } },
          ],
        },
      ],
    })
    // 培训完成率图表
    const training = echarts.init(trainingChart.value)
    training.setOption({
      animation: false,
      series: [
        {
          type: 'pie',
          radius: ['70%', '90%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 75, name: '完成率', itemStyle: { color: '#4CAF50' } },
            { value: 25, name: '未完成', itemStyle: { color: '#E8E8E8' } },
          ],
        },
      ],
    })
  })
}
onMounted(() => {
  initCharts()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              合规专员管理
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon><ElIconPlus /></el-icon>
              <span>新增联络人</span>
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon><ElIconUpload /></el-icon>
              <span>批量导入</span>
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon><ElIconDownload /></el-icon>
              <span>导出</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div />
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu {
    border-right: none;
  }

  .el-table {
    --el-table-border-color: #f0f0f0;
  }

  .el-tree {
    --el-tree-node-hover-bg-color: #f5f7fa;
  }

  .el-tag {
    --el-tag-border-radius: 4px;
  }
</style>
