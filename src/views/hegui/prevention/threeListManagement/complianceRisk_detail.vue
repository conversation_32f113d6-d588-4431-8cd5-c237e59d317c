<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowRight,
  Check,
  Delete,
  Document,
  Download,
  Edit,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import threeListApi from '@/api/complianceApi/prevention/threeList'
import dictApi from '@/api/modules/system/dict'
import storage from '@/utils/storage'

const route = useRoute()
const router = useRouter()
const activeTab = ref('detail')
const isLoading = ref(false)

// 详情数据
const detailData = reactive({
  // 基础信息
  complianceRiskMainId: '',
  tenantId: 0,
  orgUnitId: 0,
  businessType: 0,
  riskLevel: 'MEDIUM',
  approvalStatus: 'DRAFT',
  metadata: '',

  // 风险信息
  riskDescription: '',
  riskCause: '',
  riskConsequence: '',

  // 合规义务
  lawsRegulations: '',
  regulatoryRequirements: '',
  rulesRegulations: '',

  // 管理信息
  controlMeasures: '',
  responsibleOrgUnitId: [],
  cooperatingOrgUnitId: [],

  // 扩展字段
  updatedBy: '',
  customBusinessType: '',
  complianceBasis: [] as Array<{
    id: string
    name: string
    type: string
    issuer: string
    effectiveDate: string
    level: string
  }>,
  primaryDepartment: '',
  supportDepartments: [] as string[],
  riskAssessmentAdvice: '',

  // 时间字段
  createdAt: '',
  updatedAt: '',
})

// 业务类型数据
const businessTypes = ref([] as { value: number, name: string, description?: string }[])

// 部门数据
const departments = ref([] as { id: number, name: string, code: string, type: string }[])

// 获取部门数据
function getDepartments() {
  try {
    const departmentsData = storage.local.get('departments')
    if (departmentsData) {
      departments.value = JSON.parse(departmentsData)
    }
  }
  catch (error) {
    console.error('获取部门数据失败:', error)
  }
}

// 风险等级映射
const riskLevelMap: Record<string, { label: string, color: string }> = {
  HIGH: { label: '高风险', color: 'text-red-500' },
  MEDIUM: { label: '中风险', color: 'text-orange-500' },
  LOW: { label: '低风险', color: 'text-green-500' },
}

// 审批状态映射
const approvalStatusMap: Record<string, { label: string, color: string }> = {
  DRAFT: { label: '草稿', color: 'bg-gray-500' },
  PENDING: { label: '待确认', color: 'bg-orange-500' },
  APPROVED: { label: '已确认', color: 'bg-green-500' },
  REJECTED: { label: '已拒绝', color: 'bg-red-500' },
}

// 计算属性
const businessTypeLabel = computed(() => {
  if (detailData.businessType === 8 && detailData.customBusinessType) {
    return detailData.customBusinessType
  }
  const businessType = businessTypes.value.find(item => item.value === detailData.businessType)
  return businessType?.name || '未知'
})

const riskLevelInfo = computed(() => {
  return riskLevelMap[detailData.riskLevel] || { label: '未知', color: 'text-gray-500' }
})

const approvalStatusInfo = computed(() => {
  return approvalStatusMap[detailData.approvalStatus] || { label: '未知', color: 'bg-gray-500' }
})

// 负责部门名称映射
const responsibleDepartmentNames = computed(() => {
  if (!Array.isArray(detailData.responsibleOrgUnitId) || detailData.responsibleOrgUnitId.length === 0) {
    return []
  }
  return detailData.responsibleOrgUnitId.map((id) => {
    const department = departments.value.find(dept => dept.id === id)
    return department ? department.name : `未知部门(${id})`
  })
})

// 协作部门名称映射
const cooperatingDepartmentNames = computed(() => {
  if (!Array.isArray(detailData.cooperatingOrgUnitId) || detailData.cooperatingOrgUnitId.length === 0) {
    return []
  }
  return detailData.cooperatingOrgUnitId.map((id) => {
    const department = departments.value.find(dept => dept.id === id)
    return department ? department.name : `未知部门(${id})`
  })
})

// 获取基础数据
async function loadBasicData() {
  try {
    // 通过字典API获取业务类型数据
    const response = await dictApi.dictAll(88)
    businessTypes.value = response
  }
  catch (error) {
    console.error('加载基础数据失败:', error)
    ElMessage.error('获取业务类型数据失败')
  }
}

// 加载详情数据
async function loadDetailData() {
  const id = route.query.id as string
  if (!id) {
    ElMessage.error('缺少必要参数')
    router.back()
    return
  }

  try {
    isLoading.value = true
    const response = await threeListApi.getComplianceRiskDetail(id)
    if (response) {
      const data = response
      // 映射API返回数据到详情数据
      Object.assign(detailData, {
        complianceRiskMainId: data.complianceRiskMainId || id,
        tenantId: data.tenantId || 0,
        orgUnitId: data.orgUnitId || 0,
        businessType: data.businessType || 0,
        riskLevel: data.riskLevel || 'MEDIUM',
        approvalStatus: data.approvalStatus || 'DRAFT',
        metadata: data.metadata || '',
        riskDescription: data.riskDescription || '',
        riskCause: data.riskCause || '',
        riskConsequence: data.riskConsequence || '',
        lawsRegulations: data.lawsRegulations || '',
        regulatoryRequirements: data.regulatoryRequirements || '',
        rulesRegulations: data.rulesRegulations || '',
        controlMeasures: data.controlMeasures || '',
        responsibleOrgUnitId: Array.isArray(data.responsibleOrgUnitId) ? data.responsibleOrgUnitId : [],
        cooperatingOrgUnitId: Array.isArray(data.cooperatingOrgUnitId) ? data.cooperatingOrgUnitId : [],
        updatedBy: data.updatedBy || '',
        customBusinessType: data.customBusinessType || '',
        complianceBasis: Array.isArray(data.complianceBasis) ? data.complianceBasis : [],
        primaryDepartment: data.primaryDepartment || '',
        supportDepartments: Array.isArray(data.supportDepartments) ? data.supportDepartments : [],
        riskAssessmentAdvice: data.riskAssessmentAdvice || '',
        createdAt: data.createdAt || '',
        updatedAt: data.updatedAt || '',
      })
    }
  }
  catch (error) {
    console.error('加载详情数据失败:', error)
    ElMessage.error('加载数据失败')
  }
  finally {
    isLoading.value = false
  }
}

function goAddEdit(item?: any) {
  const id = item?.id || detailData.complianceRiskMainId
  if (id) {
    // 编辑清单
    router.push({
      name: '/threeListManagement/complianceRisk/edit',
      query: { id },
    })
  }
  else {
    // 新增清单
    router.push({
      name: '/threeListManagement/complianceRisk/edit',
    })
  }
}

// 初始化页面
onMounted(() => {
  getDepartments()
  loadBasicData()
  loadDetailData()
})
</script>

<template>
  <div v-loading="isLoading">
    <!-- 页面标题区域 -->
    <div class="mx-auto px-6 pb-6 container">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <h1 class="mr-4 text-xl font-bold">
            {{ detailData.riskDescription || '合规风险识别清单详情' }}
          </h1>
          <span :class="`rounded px-3 flex-shrink-0 py-1 text-sm text-white ${approvalStatusInfo.color}`">
            {{ approvalStatusInfo.label }}
          </span>
        </div>
        <div class="flex space-x-3">
          <el-button type="primary" @click="goAddEdit()">
            <el-icon class="mr-1">
              <Edit />
            </el-icon>
            编辑
          </el-button>
          <el-button type="success" class="!rounded-button whitespace-nowrap">
            <el-icon class="mr-1">
              <Check />
            </el-icon>
            确认
          </el-button>
          <el-button plain class="!rounded-button whitespace-nowrap">
            <el-icon class="mr-1">
              <Download />
            </el-icon>
            导出
          </el-button>
          <el-button type="danger" plain class="!rounded-button whitespace-nowrap">
            <el-icon class="mr-1">
              <Delete />
            </el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div>
    <!-- 基本信息卡片 -->
    <div class="mx-auto px-6 pb-6 container">
      <div class="rounded-lg bg-white p-6 shadow-sm">
        <h2 class="mb-4 text-lg font-bold">
          基本信息
        </h2>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <div class="flex items-center">
              <span class="w-24 text-sm text-gray-500">
                ID：
              </span>
              <span class="font-medium">
                {{ detailData.complianceRiskMainId || '-' }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                业务类型：
              </span>
              <span class="font-medium">
                {{ businessTypeLabel }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                最后更新：
              </span>
              <span class="font-medium">
                {{ detailData.updatedAt || '-' }}
              </span>
            </div>
            <!-- <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                归口部门：
              </span>
              <span class="font-medium">
                {{ detailData.primaryDepartment || '-' }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                配合部门：
              </span>
              <span class="font-medium">
                {{ detailData.supportDepartments.join(', ') || '-' }}
              </span>
            </div> -->
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                创建人员：
              </span>
              <span class="font-medium">
                {{ detailData.updatedBy || '-' }}
              </span>
            </div>
          </div>
          <div>
            <div class="flex items-center">
              <span class="w-24 text-sm text-gray-500">
                风险等级：
              </span>
              <span :class="`font-medium ${riskLevelInfo.color}`">
                {{ riskLevelInfo.label }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                状态：
              </span>
              <span class="font-medium">
                {{ approvalStatusInfo.label }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                创建时间：
              </span>
              <span class="font-medium">
                {{ detailData.createdAt || '-' }}
              </span>
            </div>
            <!-- <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                最后更新：
              </span>
              <span class="font-medium">
                {{ detailData.updatedAt || '-' }}
              </span>
            </div> -->
            <div class="mt-3 flex items-center">
              <span class="w-26 text-sm text-gray-500">
                风险评估建议：
              </span>
              <span class="font-medium">
                {{ detailData.riskAssessmentAdvice || '-' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 标签页内容区 -->
    <div class="mx-auto px-6 pb-10 container">
      <div class="rounded-lg bg-white shadow-sm">
        <el-tabs v-model="activeTab" class="p-6">
          <el-tab-pane label="风险详情" name="detail">
            <div class="space-y-6">
              <div>
                <h3 class="mb-2 font-bold">
                  合规风险描述点
                </h3>
                <p class="text-gray-700">
                  {{ detailData.riskDescription || '暂无描述' }}
                </p>
              </div>
              <div>
                <h3 class="mb-2 font-bold">
                  风险产生的原因
                </h3>
                <div class="text-gray-700">
                  <div v-if="detailData.riskCause" v-html="detailData.riskCause.replace(/\n/g, '<br>')" />
                  <p v-else>
                    暂无风险原因描述
                  </p>
                </div>
              </div>
              <div>
                <h3 class="mb-2 font-bold">
                  风险发生的责任或后果
                </h3>
                <div class="text-gray-700">
                  <div v-if="detailData.riskConsequence" v-html="detailData.riskConsequence.replace(/\n/g, '<br>')" />
                  <p v-else>
                    暂无风险后果描述
                  </p>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="合规义务" name="obligation">
            <div class="space-y-6">
              <div>
                <h3 class="mb-2 font-bold">
                  法律法规
                </h3>
                <div class="text-gray-700">
                  <div v-if="detailData.lawsRegulations" v-html="detailData.lawsRegulations.replace(/\n/g, '<br>')" />
                  <p v-else class="text-gray-500">
                    暂无相关法律法规
                  </p>
                </div>
              </div>
              <div>
                <h3 class="mb-2 font-bold">
                  监管规定
                </h3>
                <div class="text-gray-700">
                  <div v-if="detailData.regulatoryRequirements" v-html="detailData.regulatoryRequirements.replace(/\n/g, '<br>')" />
                  <p v-else class="text-gray-500">
                    暂无相关监管规定
                  </p>
                </div>
              </div>
              <div>
                <h3 class="mb-2 font-bold">
                  规章制度
                </h3>
                <div class="text-gray-700">
                  <div v-if="detailData.rulesRegulations" v-html="detailData.rulesRegulations.replace(/\n/g, '<br>')" />
                  <p v-else class="text-gray-500">
                    暂无相关规章制度
                  </p>
                </div>
              </div>
              <div v-if="detailData.complianceBasis && detailData.complianceBasis.length > 0">
                <h3 class="mb-2 font-bold">
                  合规依据
                </h3>
                <ul class="text-gray-700 space-y-2">
                  <li v-for="item in detailData.complianceBasis" :key="item.id" class="flex items-center">
                    <el-icon class="mr-2 text-blue-500">
                      <Document />
                    </el-icon>
                    <div>
                      <div class="font-medium">
                        {{ item.name }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ item.type }} | {{ item.issuer }} | {{ item.effectiveDate }}
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="管控措施" name="measure">
            <div class="space-y-6">
              <h3 class="mb-2 font-bold">
                风险控制措施
              </h3>
              <div class="text-gray-700">
                <div v-if="detailData.controlMeasures" v-html="detailData.controlMeasures.replace(/\n/g, '<br>')" />
                <p v-else class="text-gray-500">
                  暂无管控措施
                </p>
              </div>
              <div v-if="responsibleDepartmentNames.length > 0 || cooperatingDepartmentNames.length > 0" class="mt-4">
                <h4 class="mb-2 font-medium">
                  归口部门
                </h4>
                <div class="text-gray-700">
                  <div v-if="responsibleDepartmentNames.length > 0">
                    <span class="font-medium">负责部门：</span>
                    <el-tag v-for="name in responsibleDepartmentNames" :key="name" class="mb-1 mr-2" type="primary">
                      {{ name }}
                    </el-tag>
                  </div>
                  <div v-if="cooperatingDepartmentNames.length > 0" class="mt-2">
                    <span class="font-medium">配合部门：</span>
                    <el-tag v-for="name in cooperatingDepartmentNames" :key="name" class="mb-1 mr-2" type="info">
                      {{ name }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="更新记录" name="history">
            <div class="space-y-4">
              <div class="relative border-l-2 border-gray-200 pb-4 pl-8">
                <div class="absolute top-0 h-4 w-4 rounded-full bg-blue-500 -left-0.1" />
                <div class="text-sm font-medium">
                  2024-01-16 14:30
                </div>
                <div class="mb-1 text-sm text-gray-500">
                  张三
                </div>
                <div class="text-gray-700">
                  修改风险控制措施，补充外部法律顾问机制
                </div>
              </div>
              <div class="relative border-l-2 border-gray-200 pb-4 pl-8">
                <div class="absolute top-0 h-4 w-4 rounded-full bg-blue-500 -left-0.1" />
                <div class="text-sm font-medium">
                  2024-01-15 16:45
                </div>
                <div class="mb-1 text-sm text-gray-500">
                  李四
                </div>
                <div class="text-gray-700">
                  确认风险描述和产生原因
                </div>
              </div>
              <div class="relative border-l-2 border-gray-200 pl-8">
                <div class="absolute top-0 h-4 w-4 rounded-full bg-blue-500 -left-0.1" />
                <div class="text-sm font-medium">
                  2024-01-15 10:20
                </div>
                <div class="mb-1 text-sm text-gray-500">
                  系统
                </div>
                <div class="text-gray-700">
                  AI智能识别生成初始风险清单项
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

  <style scoped>
  .container {
  max-width: none;
  width: 100%;
  padding-left: 80px;
  padding-right: 80px;
  }
  </style>
