<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowDown, CircleCheck, Clock, Document, Loading, MagicStick, Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/modules/user'
import threeListApi from '@/api/complianceApi/prevention/threeList'
import roleApi from '@/api/permissions/role'
import dictApi from '@/api/modules/system/dict'
import uploadApi from '@/api/upload'
import threeAiApi from '@/api/complianceApi/prevention/threeAi'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'

const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const action = `${(import.meta.env.DEV && import.meta.env.VITE_OPEN_PROXY === 'true') ? '/proxy/' : import.meta.env.VITE_APP_API_BASEURL}whiskerguardgeneralservice/api/file/upload?serviceName=whiskerguardgeneralservice`
// 页面状态
const isLoading = ref(false)
const isSubmitting = ref(false)
const pageType = ref('edit') // 'edit' | 'add'
const itemId = ref('')
const _lawyer = ref(false)
_lawyer.value = userStore.lawyer

// 表单数据
const formData = ref({
  orgUnitId: '', // 部门ID
  orgUnitName: '', // 部门名称
  postId: '', // 岗位ID
  postName: '', // 岗位名称
  basicDuty: '', // 基本职责
  complianceInfo: {
    positiveComplianceRequirement: '', // 正面合规要求
    negativeComplianceRequirement: '', // 负面合规要求
    complianceBasis: '', // 合规依据
  },
  riskControlInfo: {
    riskTypes: [], // 风险来源（八项权力）
    riskLevel: '', // 风险等级
    controlMeasures: '', // 防控措施
  },
  approvalStatus: 'DRAFT', // 审批状态
})

// 上传文件列表
const uploadedFiles = ref<Array<{ name: string, url: string, size?: string }>>([])

// AI会话ID
const conversationId = ref<string>('')

// 智能识别相关
const showProgressDialog = ref(false)
const progressSteps = ref([
  { title: '正在分析岗位职责...', completed: false },
  { title: '正在匹配合规要求...', completed: false },
  { title: '正在识别权力风险...', completed: false },
  { title: '正在生成防控措施...', completed: false },
  { title: '生成完成！', completed: false },
])
const currentStep = ref(0)

// 兼容性计算属性
const _selectedDepartment = computed({
  get: () => formData.value.orgUnitId,
  set: (value) => { formData.value.orgUnitId = value },
})
const _positionName = computed({
  get: () => formData.value.postName,
  set: (value) => { formData.value.postName = value },
})
const _basicResponsibilities = computed({
  get: () => formData.value.basicDuty,
  set: (value) => { formData.value.basicDuty = value },
})

const collapsed = ref({
  quickImport: false,
  responsibility: false,
  compliance: false,
  risk: false,
})

function _toggleSection(section: keyof typeof collapsed.value) {
  collapsed.value[section] = !collapsed.value[section]
}

// 处理文件上传成功
function handleFileUploadSuccess(fileUrls: string[]) {
  if (fileUrls && fileUrls.length > 0) {
    const fileUrl = fileUrls[0]
    // 从URL中提取文件名
    const fileName = fileUrl.split('/').pop() || '未知文件'

    // 添加到上传文件列表
    uploadedFiles.value.push({
      name: fileName,
      url: fileUrl,
      size: '未知大小',
    })

    ElMessage.success('文件上传成功')
  }
}

// 删除已上传的文件
function removeUploadedFile(index: number) {
  uploadedFiles.value.splice(index, 1)
  ElMessage.success('文件已删除')
}

// 预览文件
async function previewFile(file: { name: string, url: string }) {
  try {
    // 先获取真实的文件地址
    const realUrl = await uploadApi.getFileUrl(file.url)
    // 根据文件类型决定预览方式
    const fileExt = file.name.split('.').pop()?.toLowerCase()
    if (fileExt === 'pdf') {
      // PDF文件在新窗口打开
      window.open(realUrl, '_blank')
    }
    else {
      // 其他文件类型直接下载
      const link = document.createElement('a')
      link.href = realUrl
      link.download = file.name
      link.click()
    }
  }
  catch (error) {
    console.error('获取文件地址失败:', error)
    ElMessage.error('文件预览失败，请稍后重试')
  }
}

// 权力列表
const powers = ref<Array<{ value: string, name: string }>>([])

// 获取八项权力字典数据
async function fetchPowersDict() {
  try {
    const response = await dictApi.dictAll(80)
    powers.value = response
  }
  catch (error) {
    console.error('获取八项权力字典失败:', error)
    ElMessage.error('获取八项权力数据失败')
  }
}

// 智能识别 - 综合分析
async function handleSmartIdentify() {
  // 检查是否有上传文件
  if (uploadedFiles.value.length === 0) {
    ElMessage.warning('请先上传岗位说明书文件')
    return
  }

  if (!formData.value.orgUnitId || !formData.value.postName) {
    ElMessage.warning('请先完善岗位基本信息')
    return
  }

  try {
    isLoading.value = true
    showProgressDialog.value = true
    currentStep.value = 0
    progressSteps.value.forEach(step => step.completed = false)

    // 获取会话ID
    if (!conversationId.value) {
      const sessionResult = await threeAiApi.getSessionId()
      conversationId.value = sessionResult.conversationId
    }

    // 模拟进度更新
    const interval = setInterval(() => {
      if (currentStep.value < progressSteps.value.length) {
        progressSteps.value[currentStep.value].completed = true
        currentStep.value++
      }
    }, 800)

    // 调用AI综合分析接口
    const params = {
      conversationId: conversationId.value,
      fileUrl: uploadedFiles.value[0].url,
      toolKey: 'kimi',
      orgUnitName: formData.value.orgUnitName,
      postName: formData.value.postName,
    }

    const result = await threeAiApi.analysisAI(params)

    if (result) {
      // 填充AI生成的内容
      if (result.basicDuty) {
        formData.value.basicDuty = result.basicDuty
      }
      if (result.positiveRequirements) {
        formData.value.complianceInfo.positiveComplianceRequirement = result.positiveRequirements
      }
      if (result.negativeRequirements) {
        formData.value.complianceInfo.negativeComplianceRequirement = result.negativeRequirements
      }
      if (result.complianceBasis) {
        formData.value.complianceInfo.complianceBasis = result.complianceBasis
      }
      if (result.identifiedPowers) {
        formData.value.riskControlInfo.riskTypes = result.identifiedPowers
      }
      if (result.preventiveMeasures) {
        formData.value.riskControlInfo.controlMeasures = result.preventiveMeasures
      }
      if (result.riskLevel) {
        formData.value.riskControlInfo.riskLevel = result.riskLevel
      }

      clearInterval(interval)
      // 完成所有步骤
      progressSteps.value.forEach(step => step.completed = true)
      setTimeout(() => {
        isLoading.value = false
        showProgressDialog.value = false
        ElMessage.success('智能识别完成')
      }, 1000)
    }
    else {
      throw new Error('AI分析返回结果为空')
    }
  }
  catch (error) {
    console.error('智能识别失败:', error)
    isLoading.value = false
    showProgressDialog.value = false
    ElMessage.error('智能识别失败，请稍后重试')
  }
}

// 岗位职责智能生成
async function handleDutyGenerate() {
  if (uploadedFiles.value.length <= 0) {
    ElMessage.warning('请先上传岗位说明书')
    return
  }

  try {
    // 获取会话ID
    if (!conversationId.value) {
      const sessionResult = await threeAiApi.getSessionId()
      conversationId.value = sessionResult.conversationId
    }

    const params = {
      conversationId: conversationId.value,
      fileUrl: uploadedFiles.value.length > 0 ? uploadedFiles.value[0].url : '',
      departmentName: formData.value.orgUnitName,
      positionName: formData.value.postName,
      toolKey: 'kimi',
    }

    ElMessage.info('正在生成岗位职责...')
    const result = await threeAiApi.getDutyPosition(params)

    if (result && result.basicDuty) {
      formData.value.basicDuty = result.basicDuty
      ElMessage.success('岗位职责生成成功')
    }
    else {
      ElMessage.warning('未能生成岗位职责，请手动填写')
    }
  }
  catch (error) {
    console.error('岗位职责生成失败:', error)
    ElMessage.error('岗位职责生成失败，请稍后重试')
  }
}

// 合规要求智能生成
async function handleComplianceGenerate() {
  if (!formData.value.basicDuty) {
    ElMessage.warning('请先填写基本职责')
    return
  }

  try {
    // 获取会话ID
    if (!conversationId.value) {
      const sessionResult = await threeAiApi.getSessionId()
      conversationId.value = sessionResult.conversationId
    }

    const params = {
      conversationId: conversationId.value,
      fileUrl: uploadedFiles.value.length > 0 ? uploadedFiles.value[0].url : '',
      basicDuty: formData.value.basicDuty,
      departmentName: formData.value.orgUnitName,
      positionName: formData.value.postName,
      toolKey: 'kimi',
    }

    ElMessage.info('正在生成合规要求...')
    const result = await threeAiApi.getCompliance(params)

    if (result) {
      if (result.positiveRequirements) {
        formData.value.complianceInfo.positiveComplianceRequirement = result.positiveRequirements
      }
      if (result.negativeRequirements) {
        formData.value.complianceInfo.negativeComplianceRequirement = result.negativeRequirements
      }
      if (result.complianceBasis) {
        formData.value.complianceInfo.complianceBasis = result.complianceBasis
      }
      ElMessage.success('合规要求生成成功')
    }
    else {
      ElMessage.warning('未能生成合规要求，请手动填写')
    }
  }
  catch (error) {
    console.error('合规要求生成失败:', error)
    ElMessage.error('合规要求生成失败，请稍后重试')
  }
}

// 风险防控智能生成
async function handleRiskGenerate() {
  if (!formData.value.basicDuty) {
    ElMessage.warning('请先填写基本职责')
    return
  }

  try {
    // 获取会话ID
    if (!conversationId.value) {
      const sessionResult = await threeAiApi.getSessionId()
      conversationId.value = sessionResult.conversationId
    }

    const params = {
      conversationId: conversationId.value,
      fileUrl: uploadedFiles.value.length > 0 ? uploadedFiles.value[0].url : '',
      basicDuty: formData.value.basicDuty,
      departmentName: formData.value.orgUnitName,
      positionName: formData.value.postName,
      positiveComplianceRequirement: formData.value.complianceInfo.positiveComplianceRequirement,
      negativeComplianceRequirement: formData.value.complianceInfo.negativeComplianceRequirement,
      complianceBasis: formData.value.complianceInfo.complianceBasis,
      toolKey: 'kimi',
    }

    ElMessage.info('正在生成风险防控信息...')
    const result = await threeAiApi.getEight(params)

    if (result) {
      if (result.identifiedPowers) {
        formData.value.riskControlInfo.riskTypes = result.identifiedPowers
      }
      if (result.preventiveMeasures) {
        formData.value.riskControlInfo.controlMeasures = result.preventiveMeasures
      }
      if (result.riskLevel) {
        formData.value.riskControlInfo.riskLevel = result.riskLevel
      }
      ElMessage.success('风险防控信息生成成功')
    }
    else {
      ElMessage.warning('未能生成风险防控信息，请手动填写')
    }
  }
  catch (error) {
    console.error('风险防控生成失败:', error)
    ElMessage.error('风险防控生成失败，请稍后重试')
  }
}

function _handleUpload(_file: any) {
  // 处理文件上传逻辑
  return false
}

function _toggleCollapse(section: string) {
  if (section in collapsed.value) {
    collapsed.value[section as keyof typeof collapsed.value] = !collapsed.value[section as keyof typeof collapsed.value]
  }
}

// 岗位列表
const positionList = ref<Array<{ id: string | number, name: string }>>([])
const loadingPositions = ref(false)

// 获取部门下的岗位
async function fetchPositionsByDept(deptId: string) {
  if (!deptId) {
    positionList.value = []
    return
  }

  try {
    loadingPositions.value = true
    const response = await roleApi.getDeptByPositionId({ id: deptId })
    positionList.value = (response as Array<{ id: string | number, name: string }>) || []
  }
  catch (error: any) {
    // console.error('获取岗位列表失败:', error)
    ElMessage.error('获取岗位列表失败')
    positionList.value = []
  }
  finally {
    loadingPositions.value = false
  }
}

// 监听部门变化
watch(
  () => formData.value.orgUnitId,
  (newDeptId) => {
    if (newDeptId) {
      // 清空当前选中的岗位
      formData.value.postName = ''
      // 获取新部门下的岗位
      fetchPositionsByDept(newDeptId)
    }
    else {
      positionList.value = []
      formData.value.postName = ''
    }
  },
)

onMounted(async () => {
  // 获取八项权力字典数据
  fetchPowersDict()

  // 初始化AI会话ID
  try {
    const sessionResult = await threeAiApi.getSessionId()
    conversationId.value = sessionResult.conversationId
  }
  catch (error) {
    console.error('获取AI会话ID失败:', error)
  }

  // 根据路由参数判断页面类型
  const id = route.params.id || route.query.id
  if (id) {
    pageType.value = 'edit'
    itemId.value = id as string
    loadDetailData()
  }
  else {
    pageType.value = 'add'
  }
})

// 加载详情数据
function loadDetailData() {
  if (!itemId.value) {
    return
  }

  isLoading.value = true
  threeListApi.getDutyMainDetail(Number(itemId.value))
    .then((response: any) => {
      if (response) {
        // console.log(response, 'ppppppp')
        const data = response
        formData.value = {
          orgUnitId: data.orgUnitId || '',
          orgUnitName: data.orgUnitName || '',
          postId: data.postId || '',
          // 移除重复的 orgUnitId 属性赋值
          postName: data.postName || '',
          basicDuty: data.basicDuty || '',
          complianceInfo: {
            positiveComplianceRequirement: data.complianceInfo?.positiveComplianceRequirement || '',
            negativeComplianceRequirement: data.complianceInfo?.negativeComplianceRequirement || '',
            complianceBasis: data.complianceInfo?.complianceBasis || '',
          },
          riskControlInfo: {
            riskTypes: data.riskControlInfo?.riskTypes || [],
            riskLevel: data.riskControlInfo?.riskLevel || '',
            controlMeasures: data.riskControlInfo?.controlMeasures || '',
          },
          approvalStatus: data.approvalStatus || 'DRAFT',
        }

        // 如果有部门ID，加载该部门下的岗位列表
        if (data.orgUnitId) {
          fetchPositionsByDept(data.orgUnitId)
        }
      }
    })
    .catch(() => {
      ElMessage.error('加载数据失败')
    })
    .finally(() => {
      isLoading.value = false
    })
}

// 保存草稿
function saveDraft() {
  if (isSubmitting.value) {
    return
  }

  isSubmitting.value = true
  // 使用已有的selectedPowers.value

  const requestData = {
    ...formData.value,
    ...(itemId.value && { dutyMainId: itemId.value }),
  }

  const apiCall = pageType.value === 'edit'
    ? threeListApi.updateDutyMain(Number(itemId.value), requestData)
    : threeListApi.draftDuty(requestData)

  apiCall
    .then((response: any) => {
      if (response.code === 200) {
        ElMessage.success('保存草稿成功')
        // 如果是新增模式，保存成功后更新为编辑模式
        if (pageType.value === 'add' && response.data?.id) {
          pageType.value = 'edit'
          itemId.value = response.data.id
        }
      }
      else {
        ElMessage.error(response.message || '保存失败')
      }
    })
    .catch(() => {
      ElMessage.error('保存失败')
    })
    .finally(() => {
      isSubmitting.value = false
    })
}

// 提交审核
function submitForReview() {
  if (isSubmitting.value) {
    return
  }

  // 基本验证
  if (!formData.value.orgUnitId) {
    ElMessage.warning('请选择部门')
    return
  }
  if (!formData.value.postName) {
    ElMessage.warning('请输入岗位名称')
    return
  }

  isSubmitting.value = true

  const requestData = {
    ...formData.value,
    ...(itemId.value && { dutyMainId: itemId.value }),
  }

  const apiCall = pageType.value === 'edit'
    ? threeListApi.updateDutyMain(Number(itemId.value), requestData)
    : threeListApi.submitDuty(requestData)

  apiCall
    .then((response: any) => {
      if (response.code === 200) {
        ElMessage.success('提交审核成功')
        // 跳转回列表页
        router.push('/hegui/prevention/threeListManagement')
      }
      else {
        ElMessage.error(response.message || '提交失败')
      }
    })
    .catch(() => {
      ElMessage.error('提交失败')
    })
    .finally(() => {
      isSubmitting.value = false
    })
}

// 取消操作
function handleCancel() {
  router.push('/hegui/prevention/threeListManagement')
}

// 预览
function handlePreview() {
  const previewPath = `/hegui/prevention/threeListManagement/detail/${itemId.value}`
  router.push(previewPath)
}
</script>

<template>
  <div v-loading="isLoading" class="w-full">
    <!-- header -->
    <header class="mx-auto bg-white px-6 py-4">
      <div class="flex items-center justify-between">
        <h1 class="text-xl text-gray-800 font-semibold">
          {{ pageType === 'edit' ? '重点岗位合规职责清单编辑' : '重点岗位合规职责清单新增' }}
        </h1>
        <el-button
          type="primary"
          class="!rounded-button flex items-center whitespace-nowrap bg-blue-500"
          :disabled="isLoading"
          @click="handleSmartIdentify"
        >
          {{ isLoading ? '识别中...' : '岗位职责智能识别' }}
        </el-button>
        <div class="flex space-x-2">
          <!-- 律师在PENDING状态下可以操作 -->
          <template v-if="_lawyer && formData.approvalStatus === 'PENDING' && !isLoading">
            <el-button
              type="primary"
              class="!rounded-button whitespace-nowrap"
              :loading="isSubmitting"
              @click="saveDraft"
            >
              发布
            </el-button>
          </template>
          <!-- 非律师在草稿状态或无状态下可以操作，或者数据正在加载时显示默认按钮 -->
          <template v-if="(!_lawyer && (formData.approvalStatus === 'DRAFT' || !formData.approvalStatus)) || isLoading">
            <el-button
              class="!rounded-button whitespace-nowrap"
              :loading="isSubmitting || isLoading"
              :disabled="isLoading"
              @click="saveDraft"
            >
              保存草稿
            </el-button>
            <el-button
              type="success"
              class="!rounded-button whitespace-nowrap bg-green-600"
              :loading="isSubmitting || isLoading"
              :disabled="isLoading"
              @click="submitForReview"
            >
              {{ pageType === 'edit' ? '更新并提交' : '提交审核' }}
            </el-button>
          </template>
          <el-button
            class="!rounded-button whitespace-nowrap"
            @click="handlePreview"
          >
            预览
          </el-button>
          <el-button
            class="!rounded-button whitespace-nowrap"
            @click="handleCancel"
          >
            取消
          </el-button>
        </div>
      </div>
    </header>

    <!-- 主体内容区 -->
    <div class="mx-auto px-6 pb-10 pt-6 container">
      <!-- 快速导入区块 -->
      <div class="mb-6 rounded-lg bg-white shadow-sm">
        <div class="item-title flex cursor-pointer items-center justify-between px-6 py-1" @click="_toggleCollapse('quickImport')">
          <h2 class="text-base text-gray-800 font-bold">
            快速导入
          </h2>
          <el-icon :class="{ 'rotate-180': !collapsed.quickImport }">
            <ArrowDown />
          </el-icon>
        </div>
        <div v-show="!collapsed.quickImport" class="p-6 space-y-6">
          <!-- 上传区域 - 当没有文件时显示 -->
          <div v-if="uploadedFiles.length === 0" class="mb-4 h-32 flex flex-col items-center justify-center border-2 border-gray-300 rounded-lg border-dashed transition-colors hover:border-blue-400">
            <el-icon class="mb-2 text-3xl text-gray-400">
              <Upload />
            </el-icon>
            <p class="mb-2 text-gray-500">
              导入岗位说明书，系统自动填充所有信息
            </p>
            <FileUpload
              :action="action"
              :notip="true"
              :ext="['pdf', 'doc', 'docx']"
              @on-success="handleFileUploadSuccess"
            />
          </div>

          <!-- 已上传文件展示区域 - 当有文件时显示 -->
          <div v-else class="mb-4">
            <div class="mb-3 flex items-center justify-between">
              <h4 class="flex items-center text-sm text-gray-700 font-medium">
                <el-icon class="mr-2 text-green-500">
                  <CircleCheck />
                </el-icon>
                文件上传成功
              </h4>
              <span class="text-xs text-gray-500">支持 PDF、DOC、DOCX 格式</span>
            </div>
            <div
              v-for="(file, index) in uploadedFiles"
              :key="index"
              class="flex items-center justify-between border border-green-200 rounded-lg bg-green-50 p-4 shadow-sm"
            >
              <div class="flex items-center space-x-3">
                <div class="h-10 w-10 flex items-center justify-center rounded-full bg-green-100">
                  <el-icon class="text-green-600">
                    <Document />
                  </el-icon>
                </div>
                <div>
                  <p class="text-sm text-gray-900 font-medium">
                    {{ file.name }}
                  </p>
                  <p class="text-xs text-gray-500">
                    {{ file.size }} • 上传完成
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <el-button
                  size="small"
                  type="primary"
                  plain
                  @click="previewFile(file)"
                >
                  <el-icon class="mr-1">
                    <Document />
                  </el-icon>
                  预览
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  plain
                  @click="removeUploadedFile(index)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
          <div class="relative my-6">
            <div class="relative flex justify-between">
              <span class="text-right text-xs text-gray-500">支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB</span> <span class="bg-white px-4 text-sm text-gray-500">或手动填写岗位信息</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 职责信息区 -->
      <div class="mb-6 rounded-lg bg-white shadow-sm">
        <div
          class="item-title flex cursor-pointer items-center justify-between border-b border-gray-200 px-6 py-1"
          @click="_toggleCollapse('responsibility')"
        >
          <div class="w-full flex items-center justify-between">
            <h2 class="text-base text-gray-800 font-bold">
              岗位职责信息区
            </h2>
            <span
              class="cursor-pointer rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800 hover:bg-green-200"
              @click.stop="handleDutyGenerate"
            >
              智能生成
            </span>
          </div>
          <el-icon :class="{ 'rotate-180': !collapsed.responsibility }">
            <ArrowDown />
          </el-icon>
        </div>
        <div v-show="!collapsed.responsibility" class="p-6 space-y-6">
          <div class="grid grid-cols-2 mb-6 gap-6">
            <div>
              <label class="mb-1 block text-sm text-gray-700 font-medium">部门</label>
              <DepartmentTreeSelect
                v-model="formData.orgUnitId"
                placeholder="请选择部门"
                clearable
              />
            </div>
            <div>
              <label class="mb-1 block text-sm text-gray-700 font-medium">岗位</label>
              <el-select
                v-model="formData.postName"
                class="w-full"
                placeholder="请选择岗位"
                filterable
                clearable
                :loading="loadingPositions"
                :disabled="!formData.orgUnitId"
              >
                <el-option
                  v-for="position in positionList"
                  :key="position.id"
                  :label="position.name"
                  :value="position.name"
                />
              </el-select>
            </div>
          </div>
          <div class="mb-6">
            <label class="mb-1 block text-sm text-gray-700 font-medium">创建人员</label>
            <el-input model-value="张明远 (合规管理部)" readonly class="bg-gray-100" />
          </div>
          <div>
            <label class="mb-1 block text-sm text-gray-700 font-medium">基本职责</label>
            <el-input
              v-model="formData.basicDuty"
              type="textarea"
              :rows="6"
              placeholder="请描述该岗位的基本职责"
              class="border border-gray-300 rounded"
            />
          </div>
        </div>
      </div>

      <!-- 合规信息区 -->
      <div class="mb-6 rounded-lg bg-white shadow-sm">
        <div
          class="item-title flex cursor-pointer items-center justify-between border-b border-gray-200 px-6 py-1"
          @click="_toggleCollapse('compliance')"
        >
          <div class="w-full flex items-center justify-between">
            <h2 class="text-base text-gray-800 font-bold">
              合规信息区
            </h2>
            <span
              class="cursor-pointer rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800 hover:bg-green-200"
              @click.stop="handleComplianceGenerate"
            >
              智能生成
            </span>
          </div>
          <el-icon :class="{ 'rotate-180': !collapsed.compliance }">
            <ArrowDown />
          </el-icon>
        </div>
        <div v-show="!collapsed.compliance" class="p-6 space-y-6">
          <div class="mb-6">
            <div class="mb-1">
              <label class="text-sm text-gray-700 font-medium">合规要求（正面清单）</label>
            </div>
            <el-input
              v-model="formData.complianceInfo.positiveComplianceRequirement"
              type="textarea"
              :rows="4"
              placeholder="系统将根据岗位信息智能生成"
              class="border border-gray-300 rounded"
            />
          </div>
          <div class="mb-6">
            <div class="mb-1">
              <label class="text-sm text-gray-700 font-medium">合规要求（负面清单）</label>
            </div>
            <el-input
              v-model="formData.complianceInfo.negativeComplianceRequirement"
              type="textarea"
              :rows="4"
              placeholder="系统将根据岗位信息智能生成"
              class="border border-gray-300 rounded"
            />
          </div>
          <div>
            <div class="mb-1">
              <label class="text-sm text-gray-700 font-medium">合规职责依据</label>
            </div>
            <el-input
              v-model="formData.complianceInfo.complianceBasis"
              type="textarea"
              :rows="3"
              placeholder="系统将根据岗位信息智能生成"
              class="border border-gray-300 rounded"
            />
          </div>
        </div>
      </div>

      <!-- 风险防控区 -->
      <div class="rounded-lg bg-white shadow-sm">
        <div
          class="item-title flex cursor-pointer items-center justify-between px-6 py-1"
          @click="_toggleCollapse('risk')"
        >
          <div class="w-full flex items-center justify-between">
            <h2 class="text-base text-gray-800 font-bold">
              风险防控区
            </h2>
            <span
              class="cursor-pointer rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800 hover:bg-green-200"
              @click.stop="handleRiskGenerate"
            >
              智能生成
            </span>
          </div>
          <el-icon :class="{ 'rotate-180': !collapsed.risk }">
            <ArrowDown />
          </el-icon>
        </div>
        <div v-show="!collapsed.risk" class="p-6 space-y-6">
          <div class="mb-6">
            <div class="mb-1">
              <label class="text-sm text-gray-700 font-medium">风险来源</label>
            </div>
            <el-input
              v-model="formData.riskControlInfo.controlMeasures"
              type="textarea"
              :rows="3"
              placeholder="系统将根据岗位信息智能生成"
              class="border border-gray-300 rounded"
            />
          </div>
          <div class="mb-6">
            <label class="mb-3 block text-sm text-gray-700 font-medium">八项权力识别</label>
            <el-checkbox-group v-model="formData.riskControlInfo.riskTypes" class="flex flex-wrap gap-4">
              <el-checkbox v-for="power in powers" :key="power.value" :label="power.value" class="mb-2 mr-4">
                {{ power.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div class="mb-6">
            <label class="mb-3 block text-sm text-gray-700 font-medium">风险等级评估</label>
            <div class="flex items-center gap-6">
              <div class="flex items-center">
                <el-tag type="danger" size="large" class="mr-3 rounded-full">
                  高风险
                </el-tag>
                <p class="text-sm text-gray-700">
                  该岗位涉及多项关键权力，存在较高的合规风险，需重点关注
                </p>
              </div>
              <p class="text-xs text-blue-500">
                系统自动评估
              </p>
            </div>
          </div>
          <div>
            <div class="mb-1">
              <label class="text-sm text-gray-700 font-medium">防控措施</label>
            </div>
            <el-input
              type="textarea"
              :rows="3"
              placeholder="系统将根据岗位信息智能生成"
              class="border border-gray-300 rounded"
              readonly
            />
          </div>
        </div>
      </div>

      <el-dialog v-model="showProgressDialog" title="智能识别进度" width="30%" :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
        <div class="space-y-4">
          <div v-for="(step, index) in progressSteps" :key="index" class="flex items-center">
            <el-icon :class="{ 'text-green-500': step.completed, 'text-blue-500': currentStep === index && !step.completed, 'text-gray-400': currentStep > index && !step.completed }" class="mr-2">
              <component :is="step.completed ? 'CircleCheck' : currentStep === index ? 'Loading' : 'Clock'" />
            </el-icon>
            <span :class="{ 'text-gray-700': step.completed, 'text-blue-500': currentStep === index && !step.completed, 'text-gray-500': currentStep > index && !step.completed }">{{ step.title }}</span>
          </div>
          <div class="mt-6">
            <el-progress :percentage="(currentStep / progressSteps.length) * 100" :show-text="false" :stroke-width="8" color="#3b82f6" />
            <p class="mt-2 text-center text-xs text-gray-500">
              已完成 {{ currentStep }} / {{ progressSteps.length }} 项
            </p>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<style scoped>
:deep(.el-input__wrapper) {
  box-shadow: none !important;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f3f4f6;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: none !important;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}
.input-box{
  border:1px solid #dcdfe6;
  border-radius: 0.3rem;
  background-color: #fafafa;
}
.item-title{
  border-bottom: 1px solid #dcdfe6;
}
</style>
