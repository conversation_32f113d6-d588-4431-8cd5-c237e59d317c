<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import {
  ArrowDown,
  Delete,
  Document,
  Download,
  Edit,
  Help,
  Plus,
  Search,
  TrendCharts,
  Upload,
  View,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import threeListApi from '@/api/complianceApi/prevention/threeList'
import ImportComponent from '@/components/import/index.vue'

const router = useRouter()

// 筛选条件
const searchForm = ref({
  searchKeyword: '', // 搜索关键词
  businessDomainName: '', // 业务领域
  businessDomainType: '', // 流程类型
  processStatus: '', // 流程状态
  approvalStatus: '', // 审批状态
})

// 流程列表数据
const processList = ref([])
const loading = ref(false)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 统计概览数据
const totalProcessCount = ref(0)
const highRiskCount = ref(0)
const mediumRiskCount = ref(0)
const lowRiskCount = ref(0)

// 图表引用
const pieChart = ref<HTMLElement>()
const barChart = ref<HTMLElement>()

// 导入弹窗状态
const importDialogVisible = ref(false)

// 获取流程列表数据
async function getProcessList() {
  try {
    loading.value = true
    const params = {
      page: currentPage.value - 1, // 接口从0开始
      size: pageSize.value,
      sort: 'createdAt',
      direction: 'desc',
      businessDomainName: searchForm.value.businessDomainName || undefined,
      businessDomainType: searchForm.value.businessDomainType || undefined,
      businessProcess: searchForm.value.searchKeyword || undefined,
      processStatus: searchForm.value.processStatus || undefined,
      approvalStatus: searchForm.value.approvalStatus || undefined,
      controlLinkName: searchForm.value.searchKeyword || undefined,
    }

    const response = await threeListApi.getProcessList(params)
    if (response) {
      processList.value = response.content || []
      total.value = response.totalElements || 0

      // 更新统计数据
      updateStatistics()
    }
  }
  catch (error) {
    console.error('获取流程列表失败:', error)
    ElMessage.error('获取流程列表失败')
  }
  finally {
    loading.value = false
  }
}

// 更新统计数据
function updateStatistics() {
  totalProcessCount.value = total.value

  // 根据风险等级统计（这里需要根据实际的风险等级字段进行统计）
  const riskStats = processList.value.reduce((acc, item) => {
    // 这里需要根据实际的风险等级判断逻辑进行调整
    const riskLevel = getRiskLevelFromProcess(item)
    if (riskLevel === '高风险') {
      acc.high++
    }
    else if (riskLevel === '中风险') {
      acc.medium++
    }
    else if (riskLevel === '低风险') {
      acc.low++
    }
    return acc
  }, { high: 0, medium: 0, low: 0 })

  highRiskCount.value = riskStats.high
  mediumRiskCount.value = riskStats.medium
  lowRiskCount.value = riskStats.low
}

// 从流程数据中获取风险等级（需要根据实际业务逻辑调整）
function getRiskLevelFromProcess(process: any) {
  // 这里需要根据实际的数据结构来判断风险等级
  // 可能需要从 controlLinks 中的 controlDetail 获取风险信息
  if (process.controlLinks && process.controlLinks.length > 0) {
    const hasHighRisk = process.controlLinks.some((link: any) =>
      link.controlDetail && link.controlDetail.riskDescriptions
      && link.controlDetail.riskDescriptions.includes('高风险'),
    )
    if (hasHighRisk) {
      return '高风险'
    }

    const hasMediumRisk = process.controlLinks.some((link: any) =>
      link.controlDetail && link.controlDetail.riskDescriptions
      && link.controlDetail.riskDescriptions.includes('中风险'),
    )
    if (hasMediumRisk) {
      return '中风险'
    }
  }
  return '低风险'
}

// 获取管控环节描述
function getControlPointsDescription(process: any) {
  if (process.controlLinks && process.controlLinks.length > 0) {
    return process.controlLinks.map((link: any) => link.controlLinkName).join('、')
  }
  return process.processDescription || ''
}

// 获取风险描述
function getRiskDescription(process: any) {
  if (process.controlLinks && process.controlLinks.length > 0) {
    const riskDescriptions = process.controlLinks
      .map((link: any) => link.controlDetail?.riskDescriptions)
      .filter(Boolean)
    return riskDescriptions.join('；') || '暂无风险描述'
  }
  return '暂无风险描述'
}

// 格式化创建时间
function formatCreateTime(createdAt: any) {
  if (!createdAt) {
    return ''
  }
  if (createdAt.seconds) {
    return new Date(createdAt.seconds * 1000).toLocaleDateString()
  }
  return new Date(createdAt).toLocaleDateString()
}

// 获取状态显示文本
function getStatusText(approvalStatus: string) {
  const statusMap: Record<string, string> = {
    DRAFT: '草稿',
    PENDING: '待审批',
    APPROVED: '已通过',
    REJECTED: '已拒绝',
  }
  return statusMap[approvalStatus] || approvalStatus
}

// 风险等级标签样式
function getRiskTagType(level: string) {
  switch (level) {
    case '高风险': return 'danger'
    case '中风险': return 'warning'
    case '低风险': return 'success'
    default: return 'primary'
  }
}

// 状态标签样式
function getStatusTagType(status: string) {
  switch (status) {
    case 'PENDING': return 'warning'
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    case 'DRAFT': return 'info'
    default: return 'primary'
  }
}

// 搜索
function handleSearch() {
  currentPage.value = 1
  getProcessList()
}

// 重置筛选条件
function handleReset() {
  searchForm.value = {
    searchKeyword: '',
    businessDomainName: '',
    businessDomainType: '',
    processStatus: '',
    approvalStatus: '',
  }
  currentPage.value = 1
  getProcessList()
}

// 分页变化
function handlePageChange() {
  getProcessList()
}

// 操作函数
function goAddEdit(item: any) {
  if (item?.id) {
    // 编辑清单
    router.push({
      name: '/threeListManagement/operationFlow/edit',
      query: { id: item.id },
    })
  }
  else {
    // 新增清单
    router.push({
      name: '/threeListManagement/operationFlow/edit',
    })
  }
}

// 查看流程清单详情
function goDetail(row: any) {
  router.push({
    name: '/threeListManagement/operationFlow/detail',
    query: { id: row.id },
  })
}

function confirmProcess(_row: any) {
  // 这里可以调用确认接口
}

function deleteProcess(_row: any) {
  // 这里可以调用删除接口
}

// 导出Excel
async function handleExport() {
  try {
    loading.value = true
    const params = {
      page: 0,
      size: 999999, // 导出所有数据
      sort: 'createdAt',
      direction: 'desc',
      businessDomainName: searchForm.value.businessDomainName || undefined,
      businessDomainType: searchForm.value.businessDomainType || undefined,
      businessProcess: searchForm.value.searchKeyword || undefined,
      processStatus: searchForm.value.processStatus || undefined,
      approvalStatus: searchForm.value.approvalStatus || undefined,
      controlLinkName: searchForm.value.searchKeyword || undefined,
    }

    const response = await threeListApi.exportProcessList(params)

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
    link.download = `业务流程管控清单_${timestamp}.xlsx`

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  }
  catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
  finally {
    loading.value = false
  }
}

// 显示导入弹窗
function showImportDialog() {
  importDialogVisible.value = true
}

// 导入成功回调
function handleImportSuccess(result: any) {
  ElMessage.success(`导入成功！共处理 ${result.successCount + result.failureCount} 行，成功 ${result.successCount} 行`)
  // 刷新列表
  getProcessList()
}

// 导入失败回调
function handleImportError(error: any) {
  console.error('导入失败:', error)
}

// 初始化图表
function initCharts() {
  nextTick(() => {
  // 业务领域分布饼图
    const pieChartInstance = echarts.init(pieChart.value)
    pieChartInstance.setOption({
      animation: false,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: ['合同管理', '财务管理', '采购管理', '销售管理', '人力资源', '生产管理', '其他'],
      },
      series: [
        {
          name: '业务领域分布',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 28, name: '合同管理', itemStyle: { color: '#5470C6' } },
            { value: 22, name: '财务管理', itemStyle: { color: '#91CC75' } },
            { value: 20, name: '采购管理', itemStyle: { color: '#FAC858' } },
            { value: 18, name: '销售管理', itemStyle: { color: '#EE6666' } },
            { value: 15, name: '人力资源', itemStyle: { color: '#73C0DE' } },
            { value: 15, name: '生产管理', itemStyle: { color: '#3BA272' } },
            { value: 10, name: '其他', itemStyle: { color: '#9A60B4' } },
          ],
        },
      ],
    })
    // 流程类型分布条形图
    const barChartInstance = echarts.init(barChart.value)
    barChartInstance.setOption({
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
      },
      yAxis: {
        type: 'category',
        data: ['审批流程', '业务流程', '管理流程', '支持流程', '其他'],
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          data: [
            { value: 45, itemStyle: { color: '#5470C6' } },
            { value: 35, itemStyle: { color: '#91CC75' } },
            { value: 25, itemStyle: { color: '#FAC858' } },
            { value: 15, itemStyle: { color: '#EE6666' } },
            { value: 8, itemStyle: { color: '#73C0DE' } },
          ],
        },
      ],
    })
    // 窗口大小变化时重绘图表
    window.addEventListener('resize', () => {
      pieChartInstance.resize()
      barChartInstance.resize()
    })
  })
}

onMounted(() => {
  initCharts()
  getProcessList()
})
</script>

<template>
  <div>
    <!-- 主内容区 -->
    <div class="w-full px-4 py-4">
      <div class="flex space-x-6">
        <!-- 左侧内容区 -->
        <div class="flex-1 overflow-x-auto">
          <!-- 标题和操作区 -->
          <div class="mb-6 flex items-center justify-between">
            <h1 class="text-xl font-bold">
              关键业务流程管控清单
            </h1>
            <div class="flex items-center space-x-4">
              <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="goAddEdit(null)">
                <i class="el-icon-plus mr-1" />新增流程管控
              </el-button>
              <el-button class="!rounded-button whitespace-nowrap" @click="showImportDialog">
                <i class="el-icon-upload2 mr-1" />批量导入
              </el-button>
              <el-button class="!rounded-button whitespace-nowrap" :loading="loading" @click="handleExport">
                <i class="el-icon-download mr-1" />导出Excel
              </el-button>
            </div>
          </div>
          <!-- 筛选条件区 -->
          <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
            <div class="flex items-center space-x-4">
              <el-input
                v-model="searchForm.searchKeyword"
                placeholder="搜索流程名称、管控环节..."
                class="w-64"
                suffix-icon="el-icon-search"
                @keyup.enter="handleSearch"
              />
              <el-select v-model="searchForm.businessDomainName" placeholder="业务领域" class="w-40" @change="handleSearch">
                <el-option label="全部" value="" />
                <el-option label="合同管理" value="合同管理" />
                <el-option label="财务管理" value="财务管理" />
                <el-option label="采购管理" value="采购管理" />
                <el-option label="销售管理" value="销售管理" />
                <el-option label="人力资源" value="人力资源" />
                <el-option label="生产管理" value="生产管理" />
                <el-option label="其他" value="其他" />
              </el-select>
              <el-select v-model="searchForm.businessDomainType" placeholder="流程类型" class="w-40" @change="handleSearch">
                <el-option label="全部" value="" />
                <el-option label="审批流程" value="审批流程" />
                <el-option label="业务流程" value="业务流程" />
                <el-option label="管理流程" value="管理流程" />
                <el-option label="支持流程" value="支持流程" />
                <el-option label="其他" value="其他" />
              </el-select>
              <el-select v-model="searchForm.processStatus" placeholder="流程状态" class="w-40" @change="handleSearch">
                <el-option label="全部" value="" />
                <el-option label="使用中" value="IN_USE" />
                <el-option label="已废弃" value="DEPRECATED" />
              </el-select>
              <el-select v-model="searchForm.approvalStatus" placeholder="审批状态" class="w-40" @change="handleSearch">
                <el-option label="全部" value="" />
                <el-option label="草稿" value="DRAFT" />
                <el-option label="待审批" value="PENDING" />
                <el-option label="已通过" value="APPROVED" />
                <el-option label="已拒绝" value="REJECTED" />
              </el-select>
              <el-button type="primary" @click="handleSearch">
                查询
              </el-button>
              <el-button @click="handleReset">
                重置
              </el-button>
            </div>
          </div>

          <!-- 导入弹窗 -->
          <ImportComponent
            v-model:visible="importDialogVisible"
            title="业务流程管控清单导入"
            :download-template-api="threeListApi.downloadProcessTemplate"
            :import-data-api="threeListApi.importProcessList"
            template-file-name="业务流程管控清单导入模板.xlsx"
            @success="handleImportSuccess"
            @error="handleImportError"
          />

          <!-- 流程清单列表 -->
          <div class="overflow-hidden rounded-lg bg-white shadow-sm">
            <el-table v-loading="loading" :data="processList" style="width: 100%">
              <el-table-column type="selection" width="50" />
              <el-table-column prop="id" label="序号" width="80" />
              <el-table-column prop="businessDomainName" label="业务领域" width="100" />
              <el-table-column prop="businessProcess" label="业务流程" width="120">
                <template #default="{ row }">
                  <span>{{ row.businessProcess }}</span>
                </template>
              </el-table-column>
              <el-table-column label="管控环节" width="180">
                <template #default="{ row }">
                  <span>{{ getControlPointsDescription(row) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="风险点描述">
                <template #default="{ row }">
                  <span>{{ getRiskDescription(row) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="风险等级" width="100">
                <template #default="{ row }">
                  <el-tag :type="getRiskTagType(getRiskLevelFromProcess(row))" size="small">
                    {{ getRiskLevelFromProcess(row) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="approvalStatus" label="状态" width="120">
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.approvalStatus)" size="small">
                    {{ getStatusText(row.approvalStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" width="120">
                <template #default="{ row }">
                  <span>{{ formatCreateTime(row.createdAt) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="260" fixed="right">
                <template #default="{ row }">
                  <div class="flex items-center gap-0.1">
                    <el-button size="small" type="primary" text @click="goDetail(row)">
                      查看
                    </el-button>
                    <el-button size="small" type="warning" text @click="goAddEdit(row)">
                      编辑
                    </el-button>
                    <el-button size="small" type="success" text @click="confirmProcess(row)">
                      确认
                    </el-button>
                    <el-button size="small" type="danger" text class="text-red-500" @click="deleteProcess(row)">
                      删除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div class="flex items-center justify-between border-t p-4">
              <div class="text-sm text-gray-500">
                共 {{ total }} 条记录
              </div>
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @current-change="handlePageChange"
                @size-change="handlePageChange"
              />
            </div>
          </div>
        </div>
        <!-- 右侧统计概览区 -->
        <div v-if="false" class="w-80 shrink-0">
          <!-- 流程概览卡片 -->
          <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
            <h3 class="mb-4 text-base font-bold">
              流程概览
            </h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-gray-600">总流程数</span>
                <span class="text-2xl font-bold">{{ totalProcessCount }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">高风险项</span>
                <span class="text-xl text-red-600 font-bold">{{ highRiskCount }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">中风险项</span>
                <span class="text-xl text-orange-500 font-bold">{{ mediumRiskCount }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">低风险项</span>
                <span class="text-xl text-green-600 font-bold">{{ lowRiskCount }}</span>
              </div>
            </div>
          </div>
          <!-- 业务领域分布 -->
          <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
            <h3 class="mb-4 text-base font-bold">
              业务领域分布
            </h3>
            <div ref="pieChart" style="height: 200px;" />
          </div>
          <!-- 流程类型分布 -->
          <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
            <h3 class="mb-4 text-base font-bold">
              流程类型分布
            </h3>
            <div ref="barChart" style="height: 200px;" />
          </div>
          <!-- 快捷操作卡片 -->
          <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
            <h3 class="mb-4 text-base font-bold">
              快捷操作
            </h3>
            <div class="flex flex-col space-y-3">
              <a href="#" class="flex items-center text-sm text-gray-700 hover:underline">
                <el-icon class="mr-2"><Document /></el-icon>
                关键业务流程管控风险评估
              </a>
              <a href="#" class="flex items-center text-sm text-gray-700 hover:underline">
                <el-icon class="mr-2"><TrendCharts /></el-icon>
                管控效果分析
              </a>
              <a href="#" class="flex items-center text-sm text-gray-700 hover:underline">
                <el-icon class="mr-2"><Help /></el-icon>
                帮助文档
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.container {
max-width: 1440px;
margin: 0 auto;
}
:deep(.el-table .cell) {
padding-left: 12px;
padding-right: 12px;
}
:deep(.el-table th.el-table__cell) {
background-color: #f5f7fa;
}
:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
background-color: #f5f7fa;
}
:deep(.el-pagination) {
justify-content: flex-end;
}
</style>
