---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 03-企业组织架构服务/新闻管理的REST控制器

## POST 创建新闻

POST /whiskerguardorgservice/api/news/create

> Body 请求参数

```json
{
  "id": 0,
  "status": "DRAFT",
  "sortOrder": 0,
  "subtitle": "string",
  "title": "string",
  "summary": "string",
  "keywords": "string",
  "content": "string",
  "publishDate": {
    "seconds": 0,
    "nanos": 0
  },
  "publishedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "viewCount": 0,
  "likeCount": 0,
  "commentCount": 0,
  "shareCount": 0,
  "coverImageUrl": "string",
  "isSticky": true,
  "stickyStartTime": {
    "seconds": 0,
    "nanos": 0
  },
  "stickyEndTime": {
    "seconds": 0,
    "nanos": 0
  },
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "category": {
    "id": 0,
    "status": "DRAFT",
    "sortOrder": 0,
    "name": "string",
    "description": "string",
    "coverImageUrl": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "orgUnit": {
      "id": 0,
      "name": "string",
      "code": "string",
      "type": "COMPANY",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "parentId": 0,
      "children": "new ArrayList<>()"
    },
    "parent": {
      "id": 0,
      "status": "DRAFT",
      "sortOrder": 0,
      "name": "string",
      "description": "string",
      "coverImageUrl": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "orgUnit": {
        "id": 0,
        "name": "string",
        "code": "string",
        "type": "COMPANY",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "parentId": 0,
        "children": "new ArrayList<>()"
      },
      "parent": {
        "id": 0,
        "status": "DRAFT",
        "sortOrder": 0,
        "name": "string",
        "description": "string",
        "coverImageUrl": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "orgUnit": {
          "id": null,
          "name": null,
          "code": null,
          "type": null,
          "level": null,
          "status": null,
          "sortOrder": null,
          "description": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parentId": null,
          "children": null
        },
        "parent": {
          "id": null,
          "status": null,
          "sortOrder": null,
          "name": null,
          "description": null,
          "coverImageUrl": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "orgUnit": null,
          "parent": null
        }
      }
    }
  },
  "orgUnit": {
    "id": 0,
    "name": "string",
    "code": "string",
    "type": "COMPANY",
    "level": 0,
    "status": 0,
    "sortOrder": 0,
    "description": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "parentId": 0,
    "children": "new ArrayList<>()"
  },
  "author": {
    "id": 0,
    "tenantId": 0,
    "username": "string",
    "password": "string",
    "salt": "string",
    "realName": "string",
    "avatar": "string",
    "email": "string",
    "phone": "string",
    "gender": "UNKNOWN",
    "birthDate": "string",
    "idCard": "string",
    "employeeNo": "string",
    "status": "ACTIVE",
    "hireDate": "string",
    "leaveDate": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "lastLoginTime": {
      "seconds": 0,
      "nanos": 0
    },
    "lastLoginIp": "string",
    "loginFailureCount": 0,
    "accountLockedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordChangedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordExpiredTime": {
      "seconds": 0,
      "nanos": 0
    },
    "isFirstLogin": true,
    "forceChangePassword": true,
    "wechatOpenId": "string",
    "wechatUnionId": "string",
    "positionList": [
      {
        "id": 0,
        "code": "string",
        "name": "string",
        "level": 1,
        "category": "MANAGEMENT",
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "orgUnitId": 0,
        "orgUnitName": "string"
      }
    ],
    "orgUnitList": [
      {
        "id": 0,
        "name": "string",
        "code": "string",
        "type": "COMPANY",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parentId": 0,
        "children": "new ArrayList<>()"
      }
    ],
    "roleList": [
      {
        "id": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parent": {
          "id": 0,
          "name": "string",
          "code": "string",
          "description": "string",
          "status": 0,
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "parent": {}
        }
      }
    ],
    "employeeOrgList": [
      {
        "id": 0,
        "startDate": "string",
        "endDate": "string",
        "isPrimary": true,
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "employee": {
          "id": 0,
          "tenantId": 0,
          "username": "string",
          "password": "string",
          "salt": "string",
          "realName": "string",
          "avatar": "string",
          "email": "string",
          "phone": "string",
          "gender": "[",
          "birthDate": "string",
          "idCard": "string",
          "employeeNo": "string",
          "status": "[",
          "hireDate": "string",
          "leaveDate": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "lastLoginTime": {},
          "lastLoginIp": "string",
          "loginFailureCount": 0,
          "accountLockedTime": {},
          "passwordChangedTime": {},
          "passwordExpiredTime": {},
          "isFirstLogin": true,
          "forceChangePassword": true,
          "wechatOpenId": "string",
          "wechatUnionId": "string",
          "positionList": [
            null
          ],
          "orgUnitList": [
            null
          ],
          "roleList": [
            null
          ],
          "employeeOrgList": [
            null
          ],
          "departmentName": "string",
          "positionName": "string"
        },
        "orgUnit": {
          "id": 0,
          "name": "string",
          "code": "string",
          "type": "[",
          "level": 0,
          "status": 0,
          "sortOrder": 0,
          "description": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "parentId": 0,
          "children": "new ArrayList<>()"
        },
        "position": {
          "id": 0,
          "code": "string",
          "name": "string",
          "level": 1,
          "category": "[",
          "description": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "orgUnitId": 0,
          "orgUnitName": "string"
        }
      }
    ],
    "departmentName": "string",
    "positionName": "string"
  },
  "tags": "new HashSet<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[NewsDTO](#schemanewsdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "status": "",
  "sortOrder": 0,
  "subtitle": "",
  "title": "",
  "summary": "",
  "keywords": "",
  "content": "",
  "publishDate": {
    "seconds": 0,
    "nanos": 0
  },
  "publishedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "viewCount": 0,
  "likeCount": 0,
  "commentCount": 0,
  "shareCount": 0,
  "coverImageUrl": "",
  "isSticky": false,
  "stickyStartTime": {
    "seconds": 0,
    "nanos": 0
  },
  "stickyEndTime": {
    "seconds": 0,
    "nanos": 0
  },
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "category": {
    "id": 0,
    "status": "",
    "sortOrder": 0,
    "name": "",
    "description": "",
    "coverImageUrl": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "orgUnit": {
      "id": 0,
      "tenantId": 0,
      "name": "",
      "code": "",
      "type": "",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentId": 0,
      "children": [
        {
          "id": 0,
          "tenantId": 0,
          "name": "",
          "code": "",
          "type": "",
          "level": 0,
          "status": 0,
          "sortOrder": 0,
          "description": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "parentId": 0,
          "children": [
            {}
          ]
        }
      ]
    },
    "parent": {
      "id": 0,
      "status": "",
      "sortOrder": 0,
      "name": "",
      "description": "",
      "coverImageUrl": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "orgUnit": {
        "id": 0,
        "tenantId": 0,
        "name": "",
        "code": "",
        "type": "",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "parentId": 0,
        "children": [
          {
            "id": 0,
            "tenantId": 0,
            "name": "",
            "code": "",
            "type": "",
            "level": 0,
            "status": 0,
            "sortOrder": 0,
            "description": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "parentId": 0,
            "children": [
              {}
            ]
          }
        ]
      },
      "parent": {}
    }
  },
  "orgUnit": {
    "id": 0,
    "tenantId": 0,
    "name": "",
    "code": "",
    "type": "",
    "level": 0,
    "status": 0,
    "sortOrder": 0,
    "description": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parentId": 0,
    "children": [
      {
        "id": 0,
        "tenantId": 0,
        "name": "",
        "code": "",
        "type": "",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "parentId": 0,
        "children": [
          {}
        ]
      }
    ]
  },
  "author": {
    "id": 0,
    "tenantId": 0,
    "username": "",
    "password": "",
    "salt": "",
    "realName": "",
    "email": "",
    "phone": "",
    "gender": "",
    "birthDate": "",
    "idCard": "",
    "employeeNo": "",
    "status": "",
    "hireDate": "",
    "leaveDate": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "lastLoginTime": {
      "seconds": 0,
      "nanos": 0
    },
    "lastLoginIp": "",
    "loginFailureCount": 0,
    "accountLockedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordChangedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordExpiredTime": {
      "seconds": 0,
      "nanos": 0
    },
    "isFirstLogin": false,
    "forceChangePassword": false
  },
  "tags": [
    {
      "id": 0,
      "name": "",
      "description": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "category": {
        "id": 0,
        "name": "",
        "description": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "news": [
        {
          "id": 0,
          "status": "",
          "sortOrder": 0,
          "subtitle": "",
          "title": "",
          "summary": "",
          "keywords": "",
          "content": "",
          "publishDate": {
            "seconds": 0,
            "nanos": 0
          },
          "publishedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "viewCount": 0,
          "likeCount": 0,
          "commentCount": 0,
          "shareCount": 0,
          "coverImageUrl": "",
          "isSticky": false,
          "stickyStartTime": {
            "seconds": 0,
            "nanos": 0
          },
          "stickyEndTime": {
            "seconds": 0,
            "nanos": 0
          },
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "category": {
            "id": 0,
            "status": "",
            "sortOrder": 0,
            "name": "",
            "description": "",
            "coverImageUrl": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "orgUnit": {
              "id": 0,
              "tenantId": 0,
              "name": "",
              "code": "",
              "type": "",
              "level": 0,
              "status": 0,
              "sortOrder": 0,
              "description": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "parentId": 0,
              "children": [
                {
                  "id": 0,
                  "tenantId": 0,
                  "name": "",
                  "code": "",
                  "type": "",
                  "level": 0,
                  "status": 0,
                  "sortOrder": 0,
                  "description": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "parentId": 0,
                  "children": [
                    "[Object]"
                  ]
                }
              ]
            },
            "parent": {
              "id": 0,
              "status": "",
              "sortOrder": 0,
              "name": "",
              "description": "",
              "coverImageUrl": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "orgUnit": {
                "id": 0,
                "tenantId": 0,
                "name": "",
                "code": "",
                "type": "",
                "level": 0,
                "status": 0,
                "sortOrder": 0,
                "description": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "parentId": 0,
                "children": [
                  {
                    "id": 0,
                    "tenantId": 0,
                    "name": "",
                    "code": "",
                    "type": "",
                    "level": 0,
                    "status": 0,
                    "sortOrder": 0,
                    "description": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false,
                    "parentId": 0,
                    "children": "[Object]"
                  }
                ]
              },
              "parent": {}
            }
          },
          "orgUnit": {
            "id": 0,
            "tenantId": 0,
            "name": "",
            "code": "",
            "type": "",
            "level": 0,
            "status": 0,
            "sortOrder": 0,
            "description": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "parentId": 0,
            "children": [
              {
                "id": 0,
                "tenantId": 0,
                "name": "",
                "code": "",
                "type": "",
                "level": 0,
                "status": 0,
                "sortOrder": 0,
                "description": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "parentId": 0,
                "children": [
                  {}
                ]
              }
            ]
          },
          "author": {
            "id": 0,
            "tenantId": 0,
            "username": "",
            "password": "",
            "salt": "",
            "realName": "",
            "email": "",
            "phone": "",
            "gender": "",
            "birthDate": "",
            "idCard": "",
            "employeeNo": "",
            "status": "",
            "hireDate": "",
            "leaveDate": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "lastLoginTime": {
              "seconds": 0,
              "nanos": 0
            },
            "lastLoginIp": "",
            "loginFailureCount": 0,
            "accountLockedTime": {
              "seconds": 0,
              "nanos": 0
            },
            "passwordChangedTime": {
              "seconds": 0,
              "nanos": 0
            },
            "passwordExpiredTime": {
              "seconds": 0,
              "nanos": 0
            },
            "isFirstLogin": false,
            "forceChangePassword": false
          },
          "tags": [
            {
              "id": 0,
              "name": "",
              "description": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "category": {
                "id": 0,
                "name": "",
                "description": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "news": [
                {
                  "id": 0,
                  "status": "",
                  "sortOrder": 0,
                  "subtitle": "",
                  "title": "",
                  "summary": "",
                  "keywords": "",
                  "content": "",
                  "publishDate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "publishedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "viewCount": 0,
                  "likeCount": 0,
                  "commentCount": 0,
                  "shareCount": 0,
                  "coverImageUrl": "",
                  "isSticky": false,
                  "stickyStartTime": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "stickyEndTime": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "category": {
                    "id": 0,
                    "status": "",
                    "sortOrder": 0,
                    "name": "",
                    "description": "",
                    "coverImageUrl": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false,
                    "orgUnit": "[Object]",
                    "parent": "[Object]"
                  },
                  "orgUnit": {
                    "id": 0,
                    "tenantId": 0,
                    "name": "",
                    "code": "",
                    "type": "",
                    "level": 0,
                    "status": 0,
                    "sortOrder": 0,
                    "description": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false,
                    "parentId": 0,
                    "children": "[Object]"
                  },
                  "author": {
                    "id": 0,
                    "tenantId": 0,
                    "username": "",
                    "password": "",
                    "salt": "",
                    "realName": "",
                    "email": "",
                    "phone": "",
                    "gender": "",
                    "birthDate": "",
                    "idCard": "",
                    "employeeNo": "",
                    "status": "",
                    "hireDate": "",
                    "leaveDate": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false,
                    "lastLoginTime": "[Object]",
                    "lastLoginIp": "",
                    "loginFailureCount": 0,
                    "accountLockedTime": "[Object]",
                    "passwordChangedTime": "[Object]",
                    "passwordExpiredTime": "[Object]",
                    "isFirstLogin": false,
                    "forceChangePassword": false
                  },
                  "tags": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityNewsDTO](#schemaresponseentitynewsdto)|

# 数据模型

<h2 id="tocS_TagCategoryDTO">TagCategoryDTO</h2>

<a id="schematagcategorydto"></a>
<a id="schema_TagCategoryDTO"></a>
<a id="tocStagcategorydto"></a>
<a id="tocstagcategorydto"></a>

```json
{
  "id": 0,
  "name": "string",
  "description": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|name|string|true|none||分类名称|
|description|string|false|none||描述|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|

<h2 id="tocS_RoleDTO">RoleDTO</h2>

<a id="schemaroledto"></a>
<a id="schema_RoleDTO"></a>
<a id="tocSroledto"></a>
<a id="tocsroledto"></a>

```json
{
  "id": 0,
  "name": "string",
  "code": "string",
  "description": "string",
  "status": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "parent": {
    "id": 0,
    "name": "string",
    "code": "string",
    "description": "string",
    "status": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "parent": {
      "id": 0,
      "name": "string",
      "code": "string",
      "description": "string",
      "status": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "parent": {
        "id": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "parent": {
          "id": null,
          "name": null,
          "code": null,
          "description": null,
          "status": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parent": null
        }
      }
    }
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|name|string|true|none||角色名称|
|code|string|true|none||角色编码|
|description|string|false|none||描述|
|status|integer|true|none||状态|
|metadata|string|false|none||扩展元数据|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|parent|[RoleDTO](#schemaroledto)|false|none||none|

<h2 id="tocS_NewsCategoryDTO">NewsCategoryDTO</h2>

<a id="schemanewscategorydto"></a>
<a id="schema_NewsCategoryDTO"></a>
<a id="tocSnewscategorydto"></a>
<a id="tocsnewscategorydto"></a>

```json
{
  "id": 0,
  "status": "DRAFT",
  "sortOrder": 0,
  "name": "string",
  "description": "string",
  "coverImageUrl": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "orgUnit": {
    "id": 0,
    "name": "string",
    "code": "string",
    "type": "COMPANY",
    "level": 0,
    "status": 0,
    "sortOrder": 0,
    "description": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "parentId": 0,
    "children": "new ArrayList<>()"
  },
  "parent": {
    "id": 0,
    "status": "DRAFT",
    "sortOrder": 0,
    "name": "string",
    "description": "string",
    "coverImageUrl": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "orgUnit": {
      "id": 0,
      "name": "string",
      "code": "string",
      "type": "COMPANY",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "parentId": 0,
      "children": "new ArrayList<>()"
    },
    "parent": {
      "id": 0,
      "status": "DRAFT",
      "sortOrder": 0,
      "name": "string",
      "description": "string",
      "coverImageUrl": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "orgUnit": {
        "id": 0,
        "name": "string",
        "code": "string",
        "type": "COMPANY",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "parentId": 0,
        "children": "new ArrayList<>()"
      },
      "parent": {
        "id": 0,
        "status": "DRAFT",
        "sortOrder": 0,
        "name": "string",
        "description": "string",
        "coverImageUrl": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "orgUnit": {
          "id": null,
          "name": null,
          "code": null,
          "type": null,
          "level": null,
          "status": null,
          "sortOrder": null,
          "description": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parentId": null,
          "children": null
        },
        "parent": {
          "id": null,
          "status": null,
          "sortOrder": null,
          "name": null,
          "description": null,
          "coverImageUrl": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "orgUnit": null,
          "parent": null
        }
      }
    }
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|status|string|true|none||状态|
|sortOrder|integer|false|none||排序序号|
|name|string|true|none||分类名称|
|description|string|false|none||描述信息|
|coverImageUrl|string|false|none||封面图 URL|
|metadata|string|false|none||扩展元数据（JSONB）|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|orgUnit|[OrgUnitDTO](#schemaorgunitdto)|false|none||none|
|parent|[NewsCategoryDTO](#schemanewscategorydto)|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|status|DRAFT|
|status|PENDING_REVIEW|
|status|PUBLISHED|
|status|ARCHIVED|

<h2 id="tocS_TagDTO">TagDTO</h2>

<a id="schematagdto"></a>
<a id="schema_TagDTO"></a>
<a id="tocStagdto"></a>
<a id="tocstagdto"></a>

```json
{
  "id": 0,
  "name": "string",
  "description": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "category": {
    "id": 0,
    "name": "string",
    "description": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "news": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|name|string|true|none||标签名称|
|description|string|false|none||描述|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|category|[TagCategoryDTO](#schematagcategorydto)|false|none||none|
|news|[[NewsDTO](#schemanewsdto)]|false|none||none|

<h2 id="tocS_NewsDTO">NewsDTO</h2>

<a id="schemanewsdto"></a>
<a id="schema_NewsDTO"></a>
<a id="tocSnewsdto"></a>
<a id="tocsnewsdto"></a>

```json
{
  "id": 0,
  "status": "DRAFT",
  "sortOrder": 0,
  "subtitle": "string",
  "title": "string",
  "summary": "string",
  "keywords": "string",
  "content": "string",
  "publishDate": {
    "seconds": 0,
    "nanos": 0
  },
  "publishedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "viewCount": 0,
  "likeCount": 0,
  "commentCount": 0,
  "shareCount": 0,
  "coverImageUrl": "string",
  "isSticky": true,
  "stickyStartTime": {
    "seconds": 0,
    "nanos": 0
  },
  "stickyEndTime": {
    "seconds": 0,
    "nanos": 0
  },
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "category": {
    "id": 0,
    "status": "DRAFT",
    "sortOrder": 0,
    "name": "string",
    "description": "string",
    "coverImageUrl": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "orgUnit": {
      "id": 0,
      "name": "string",
      "code": "string",
      "type": "COMPANY",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "parentId": 0,
      "children": "new ArrayList<>()"
    },
    "parent": {
      "id": 0,
      "status": "DRAFT",
      "sortOrder": 0,
      "name": "string",
      "description": "string",
      "coverImageUrl": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "orgUnit": {
        "id": 0,
        "name": "string",
        "code": "string",
        "type": "COMPANY",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "parentId": 0,
        "children": "new ArrayList<>()"
      },
      "parent": {
        "id": 0,
        "status": "DRAFT",
        "sortOrder": 0,
        "name": "string",
        "description": "string",
        "coverImageUrl": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "orgUnit": {
          "id": null,
          "name": null,
          "code": null,
          "type": null,
          "level": null,
          "status": null,
          "sortOrder": null,
          "description": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parentId": null,
          "children": null
        },
        "parent": {
          "id": null,
          "status": null,
          "sortOrder": null,
          "name": null,
          "description": null,
          "coverImageUrl": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "orgUnit": null,
          "parent": null
        }
      }
    }
  },
  "orgUnit": {
    "id": 0,
    "name": "string",
    "code": "string",
    "type": "COMPANY",
    "level": 0,
    "status": 0,
    "sortOrder": 0,
    "description": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "parentId": 0,
    "children": "new ArrayList<>()"
  },
  "author": {
    "id": 0,
    "tenantId": 0,
    "username": "string",
    "password": "string",
    "salt": "string",
    "realName": "string",
    "avatar": "string",
    "email": "string",
    "phone": "string",
    "gender": "UNKNOWN",
    "birthDate": "string",
    "idCard": "string",
    "employeeNo": "string",
    "status": "ACTIVE",
    "hireDate": "string",
    "leaveDate": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "lastLoginTime": {
      "seconds": 0,
      "nanos": 0
    },
    "lastLoginIp": "string",
    "loginFailureCount": 0,
    "accountLockedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordChangedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordExpiredTime": {
      "seconds": 0,
      "nanos": 0
    },
    "isFirstLogin": true,
    "forceChangePassword": true,
    "wechatOpenId": "string",
    "wechatUnionId": "string",
    "positionList": [
      {
        "id": 0,
        "code": "string",
        "name": "string",
        "level": 1,
        "category": "MANAGEMENT",
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "orgUnitId": 0,
        "orgUnitName": "string"
      }
    ],
    "orgUnitList": [
      {
        "id": 0,
        "name": "string",
        "code": "string",
        "type": "COMPANY",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parentId": 0,
        "children": "new ArrayList<>()"
      }
    ],
    "roleList": [
      {
        "id": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parent": {
          "id": 0,
          "name": "string",
          "code": "string",
          "description": "string",
          "status": 0,
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "parent": {}
        }
      }
    ],
    "employeeOrgList": [
      {
        "id": 0,
        "startDate": "string",
        "endDate": "string",
        "isPrimary": true,
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "employee": {
          "id": 0,
          "tenantId": 0,
          "username": "string",
          "password": "string",
          "salt": "string",
          "realName": "string",
          "avatar": "string",
          "email": "string",
          "phone": "string",
          "gender": "[",
          "birthDate": "string",
          "idCard": "string",
          "employeeNo": "string",
          "status": "[",
          "hireDate": "string",
          "leaveDate": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "lastLoginTime": {},
          "lastLoginIp": "string",
          "loginFailureCount": 0,
          "accountLockedTime": {},
          "passwordChangedTime": {},
          "passwordExpiredTime": {},
          "isFirstLogin": true,
          "forceChangePassword": true,
          "wechatOpenId": "string",
          "wechatUnionId": "string",
          "positionList": [
            null
          ],
          "orgUnitList": [
            null
          ],
          "roleList": [
            null
          ],
          "employeeOrgList": [
            null
          ],
          "departmentName": "string",
          "positionName": "string"
        },
        "orgUnit": {
          "id": 0,
          "name": "string",
          "code": "string",
          "type": "[",
          "level": 0,
          "status": 0,
          "sortOrder": 0,
          "description": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "parentId": 0,
          "children": "new ArrayList<>()"
        },
        "position": {
          "id": 0,
          "code": "string",
          "name": "string",
          "level": 1,
          "category": "[",
          "description": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "orgUnitId": 0,
          "orgUnitName": "string"
        }
      }
    ],
    "departmentName": "string",
    "positionName": "string"
  },
  "tags": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|status|string|true|none||状态|
|sortOrder|integer|false|none||排序序号|
|subtitle|string|false|none||副标题|
|title|string|true|none||标题|
|summary|string|false|none||摘要|
|keywords|string|false|none||关键词（用于SEO，全局搜索）|
|content|string|false|none||正文内容|
|publishDate|[Instant](#schemainstant)|false|none||发布时间|
|publishedAt|[Instant](#schemainstant)|false|none||正式发布时戳|
|viewCount|integer|false|none||浏览量|
|likeCount|integer|false|none||点赞数|
|commentCount|integer|false|none||评论数|
|shareCount|integer|false|none||分享数|
|coverImageUrl|string|false|none||封面图 URL|
|isSticky|boolean|false|none||是否置顶|
|stickyStartTime|[Instant](#schemainstant)|false|none||置顶开始时间|
|stickyEndTime|[Instant](#schemainstant)|false|none||置顶结束时间|
|metadata|string|false|none||扩展元数据（JSONB）|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|category|[NewsCategoryDTO](#schemanewscategorydto)|false|none||none|
|orgUnit|[OrgUnitDTO](#schemaorgunitdto)|false|none||none|
|author|[EmployeeDTO](#schemaemployeedto)|false|none||none|
|tags|[[TagDTO](#schematagdto)]|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|status|DRAFT|
|status|PENDING_REVIEW|
|status|PUBLISHED|
|status|ARCHIVED|

<h2 id="tocS_OrgUnitDTO">OrgUnitDTO</h2>

<a id="schemaorgunitdto"></a>
<a id="schema_OrgUnitDTO"></a>
<a id="tocSorgunitdto"></a>
<a id="tocsorgunitdto"></a>

```json
{
  "id": 0,
  "name": "string",
  "code": "string",
  "type": "COMPANY",
  "level": 0,
  "status": 0,
  "sortOrder": 0,
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "parentId": 0,
  "children": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|name|string|true|none||组织单位名称|
|code|string|true|none||唯一编码，用于外部系统对接或导入映射|
|type|string|true|none||组织单元类型|
|level|integer|true|none||层级深度，根节点为 1|
|status|integer|true|none||状态：1=启用，0=禁用|
|sortOrder|integer|false|none||排序序号|
|description|string|false|none||描述信息|
|metadata|string|false|none||扩展元数据（JSON）|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|parentId|integer(int64)|false|none||父节点|
|children|[[OrgUnitDTO](#schemaorgunitdto)]|false|none||子节点|

#### 枚举值

|属性|值|
|---|---|
|type|COMPANY|
|type|SUBSIDIARY|
|type|BUSINESS_GROUP|
|type|DEPARTMENT|
|type|TEAM|

<h2 id="tocS_ResponseEntityNewsDTO">ResponseEntityNewsDTO</h2>

<a id="schemaresponseentitynewsdto"></a>
<a id="schema_ResponseEntityNewsDTO"></a>
<a id="tocSresponseentitynewsdto"></a>
<a id="tocsresponseentitynewsdto"></a>

```json
{
  "id": 0,
  "status": "DRAFT",
  "sortOrder": 0,
  "subtitle": "string",
  "title": "string",
  "summary": "string",
  "keywords": "string",
  "content": "string",
  "publishDate": {
    "seconds": 0,
    "nanos": 0
  },
  "publishedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "viewCount": 0,
  "likeCount": 0,
  "commentCount": 0,
  "shareCount": 0,
  "coverImageUrl": "string",
  "isSticky": true,
  "stickyStartTime": {
    "seconds": 0,
    "nanos": 0
  },
  "stickyEndTime": {
    "seconds": 0,
    "nanos": 0
  },
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "category": {
    "id": 0,
    "status": "DRAFT",
    "sortOrder": 0,
    "name": "string",
    "description": "string",
    "coverImageUrl": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "orgUnit": {
      "id": 0,
      "name": "string",
      "code": "string",
      "type": "COMPANY",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "parentId": 0,
      "children": "new ArrayList<>()"
    },
    "parent": {
      "id": 0,
      "status": "DRAFT",
      "sortOrder": 0,
      "name": "string",
      "description": "string",
      "coverImageUrl": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "orgUnit": {
        "id": 0,
        "name": "string",
        "code": "string",
        "type": "COMPANY",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "parentId": 0,
        "children": "new ArrayList<>()"
      },
      "parent": {
        "id": 0,
        "status": "DRAFT",
        "sortOrder": 0,
        "name": "string",
        "description": "string",
        "coverImageUrl": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "orgUnit": {
          "id": null,
          "name": null,
          "code": null,
          "type": null,
          "level": null,
          "status": null,
          "sortOrder": null,
          "description": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parentId": null,
          "children": null
        },
        "parent": {
          "id": null,
          "status": null,
          "sortOrder": null,
          "name": null,
          "description": null,
          "coverImageUrl": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "orgUnit": null,
          "parent": null
        }
      }
    }
  },
  "orgUnit": {
    "id": 0,
    "name": "string",
    "code": "string",
    "type": "COMPANY",
    "level": 0,
    "status": 0,
    "sortOrder": 0,
    "description": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "parentId": 0,
    "children": "new ArrayList<>()"
  },
  "author": {
    "id": 0,
    "tenantId": 0,
    "username": "string",
    "password": "string",
    "salt": "string",
    "realName": "string",
    "avatar": "string",
    "email": "string",
    "phone": "string",
    "gender": "UNKNOWN",
    "birthDate": "string",
    "idCard": "string",
    "employeeNo": "string",
    "status": "ACTIVE",
    "hireDate": "string",
    "leaveDate": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "lastLoginTime": {
      "seconds": 0,
      "nanos": 0
    },
    "lastLoginIp": "string",
    "loginFailureCount": 0,
    "accountLockedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordChangedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordExpiredTime": {
      "seconds": 0,
      "nanos": 0
    },
    "isFirstLogin": true,
    "forceChangePassword": true,
    "wechatOpenId": "string",
    "wechatUnionId": "string",
    "positionList": [
      {
        "id": 0,
        "code": "string",
        "name": "string",
        "level": 1,
        "category": "MANAGEMENT",
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "orgUnitId": 0,
        "orgUnitName": "string"
      }
    ],
    "orgUnitList": [
      {
        "id": 0,
        "name": "string",
        "code": "string",
        "type": "COMPANY",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parentId": 0,
        "children": "new ArrayList<>()"
      }
    ],
    "roleList": [
      {
        "id": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parent": {
          "id": 0,
          "name": "string",
          "code": "string",
          "description": "string",
          "status": 0,
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "parent": {}
        }
      }
    ],
    "employeeOrgList": [
      {
        "id": 0,
        "startDate": "string",
        "endDate": "string",
        "isPrimary": true,
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "employee": {
          "id": 0,
          "tenantId": 0,
          "username": "string",
          "password": "string",
          "salt": "string",
          "realName": "string",
          "avatar": "string",
          "email": "string",
          "phone": "string",
          "gender": "[",
          "birthDate": "string",
          "idCard": "string",
          "employeeNo": "string",
          "status": "[",
          "hireDate": "string",
          "leaveDate": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "lastLoginTime": {},
          "lastLoginIp": "string",
          "loginFailureCount": 0,
          "accountLockedTime": {},
          "passwordChangedTime": {},
          "passwordExpiredTime": {},
          "isFirstLogin": true,
          "forceChangePassword": true,
          "wechatOpenId": "string",
          "wechatUnionId": "string",
          "positionList": [
            null
          ],
          "orgUnitList": [
            null
          ],
          "roleList": [
            null
          ],
          "employeeOrgList": [
            null
          ],
          "departmentName": "string",
          "positionName": "string"
        },
        "orgUnit": {
          "id": 0,
          "name": "string",
          "code": "string",
          "type": "[",
          "level": 0,
          "status": 0,
          "sortOrder": 0,
          "description": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "parentId": 0,
          "children": "new ArrayList<>()"
        },
        "position": {
          "id": 0,
          "code": "string",
          "name": "string",
          "level": 1,
          "category": "[",
          "description": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "orgUnitId": 0,
          "orgUnitName": "string"
        }
      }
    ],
    "departmentName": "string",
    "positionName": "string"
  },
  "tags": [
    {
      "id": 0,
      "name": "string",
      "description": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "category": {
        "id": 0,
        "name": "string",
        "description": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true
      },
      "news": "new HashSet<>()"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|status|string|true|none||状态|
|sortOrder|integer|false|none||排序序号|
|subtitle|string|false|none||副标题|
|title|string|true|none||标题|
|summary|string|false|none||摘要|
|keywords|string|false|none||关键词（用于SEO，全局搜索）|
|content|string|false|none||正文内容|
|publishDate|[Instant](#schemainstant)|false|none||发布时间|
|publishedAt|[Instant](#schemainstant)|false|none||正式发布时戳|
|viewCount|integer|false|none||浏览量|
|likeCount|integer|false|none||点赞数|
|commentCount|integer|false|none||评论数|
|shareCount|integer|false|none||分享数|
|coverImageUrl|string|false|none||封面图 URL|
|isSticky|boolean|false|none||是否置顶|
|stickyStartTime|[Instant](#schemainstant)|false|none||置顶开始时间|
|stickyEndTime|[Instant](#schemainstant)|false|none||置顶结束时间|
|metadata|string|false|none||扩展元数据（JSONB）|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|category|[NewsCategoryDTO](#schemanewscategorydto)|false|none||none|
|orgUnit|[OrgUnitDTO](#schemaorgunitdto)|false|none||none|
|author|[EmployeeDTO](#schemaemployeedto)|false|none||none|
|tags|[[TagDTO](#schematagdto)]|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|status|DRAFT|
|status|PENDING_REVIEW|
|status|PUBLISHED|
|status|ARCHIVED|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_EmployeeDTO">EmployeeDTO</h2>

<a id="schemaemployeedto"></a>
<a id="schema_EmployeeDTO"></a>
<a id="tocSemployeedto"></a>
<a id="tocsemployeedto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "username": "string",
  "password": "string",
  "salt": "string",
  "realName": "string",
  "avatar": "string",
  "email": "string",
  "phone": "string",
  "gender": "UNKNOWN",
  "birthDate": "string",
  "idCard": "string",
  "employeeNo": "string",
  "status": "ACTIVE",
  "hireDate": "string",
  "leaveDate": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "lastLoginTime": {
    "seconds": 0,
    "nanos": 0
  },
  "lastLoginIp": "string",
  "loginFailureCount": 0,
  "accountLockedTime": {
    "seconds": 0,
    "nanos": 0
  },
  "passwordChangedTime": {
    "seconds": 0,
    "nanos": 0
  },
  "passwordExpiredTime": {
    "seconds": 0,
    "nanos": 0
  },
  "isFirstLogin": true,
  "forceChangePassword": true,
  "wechatOpenId": "string",
  "wechatUnionId": "string",
  "positionList": [
    {
      "id": 0,
      "code": "string",
      "name": "string",
      "level": 1,
      "category": "MANAGEMENT",
      "description": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "orgUnitId": 0,
      "orgUnitName": "string"
    }
  ],
  "orgUnitList": [
    {
      "id": 0,
      "name": "string",
      "code": "string",
      "type": "COMPANY",
      "level": 0,
      "status": 0,
      "sortOrder": 0,
      "description": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "parentId": 0,
      "children": "new ArrayList<>()"
    }
  ],
  "roleList": [
    {
      "id": 0,
      "name": "string",
      "code": "string",
      "description": "string",
      "status": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "parent": {
        "id": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parent": {
          "id": 0,
          "name": "string",
          "code": "string",
          "description": "string",
          "status": 0,
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "parent": {}
        }
      }
    }
  ],
  "employeeOrgList": [
    {
      "id": 0,
      "startDate": "string",
      "endDate": "string",
      "isPrimary": true,
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "employee": {
        "id": 0,
        "tenantId": 0,
        "username": "string",
        "password": "string",
        "salt": "string",
        "realName": "string",
        "avatar": "string",
        "email": "string",
        "phone": "string",
        "gender": "UNKNOWN",
        "birthDate": "string",
        "idCard": "string",
        "employeeNo": "string",
        "status": "ACTIVE",
        "hireDate": "string",
        "leaveDate": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "lastLoginTime": {
          "seconds": 0,
          "nanos": 0
        },
        "lastLoginIp": "string",
        "loginFailureCount": 0,
        "accountLockedTime": {
          "seconds": 0,
          "nanos": 0
        },
        "passwordChangedTime": {
          "seconds": 0,
          "nanos": 0
        },
        "passwordExpiredTime": {
          "seconds": 0,
          "nanos": 0
        },
        "isFirstLogin": true,
        "forceChangePassword": true,
        "wechatOpenId": "string",
        "wechatUnionId": "string",
        "positionList": [
          {
            "id": null,
            "code": null,
            "name": null,
            "level": null,
            "category": null,
            "description": null,
            "metadata": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "orgUnitId": null,
            "orgUnitName": null
          }
        ],
        "orgUnitList": [
          {
            "id": null,
            "name": null,
            "code": null,
            "type": null,
            "level": null,
            "status": null,
            "sortOrder": null,
            "description": null,
            "metadata": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "parentId": null,
            "children": null
          }
        ],
        "roleList": [
          {
            "id": null,
            "name": null,
            "code": null,
            "description": null,
            "status": null,
            "metadata": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "parent": null
          }
        ],
        "employeeOrgList": [
          {
            "id": null,
            "startDate": null,
            "endDate": null,
            "isPrimary": null,
            "version": null,
            "createdBy": null,
            "createdAt": null,
            "updatedBy": null,
            "updatedAt": null,
            "isDeleted": null,
            "employee": null,
            "orgUnit": null,
            "position": null
          }
        ],
        "departmentName": "string",
        "positionName": "string"
      },
      "orgUnit": {
        "id": 0,
        "name": "string",
        "code": "string",
        "type": "COMPANY",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parentId": 0,
        "children": "new ArrayList<>()"
      },
      "position": {
        "id": 0,
        "code": "string",
        "name": "string",
        "level": 1,
        "category": "MANAGEMENT",
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "orgUnitId": 0,
        "orgUnitName": "string"
      }
    }
  ],
  "departmentName": "string",
  "positionName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|tenantId|integer(int64)|false|none||租户 ID|
|username|string|true|none||登录用户名|
|password|string|true|none||登录密码（加密存储）|
|salt|string|false|none||密码盐值|
|realName|string|true|none||真实姓名|
|avatar|string|false|none||头像|
|email|string|false|none||邮箱地址|
|phone|string|false|none||手机号|
|gender|string|false|none||性别|
|birthDate|string|false|none||生日|
|idCard|string|false|none||身份证号|
|employeeNo|string|false|none||员工编号（工号）|
|status|string|false|none||员工状态|
|hireDate|string|false|none||入职日期|
|leaveDate|string|false|none||离职日期|
|metadata|string|false|none||扩展元数据|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|lastLoginTime|[Instant](#schemainstant)|false|none||最后登录时间|
|lastLoginIp|string|false|none||最后登录IP|
|loginFailureCount|integer|false|none||登录失败次数|
|accountLockedTime|[Instant](#schemainstant)|false|none||账号锁定时间|
|passwordChangedTime|[Instant](#schemainstant)|false|none||密码修改时间|
|passwordExpiredTime|[Instant](#schemainstant)|false|none||密码过期时间|
|isFirstLogin|boolean|false|none||是否首次登录|
|forceChangePassword|boolean|false|none||是否强制修改密码|
|wechatOpenId|string|false|none||微信OpenID|
|wechatUnionId|string|false|none||微信UnionID|
|positionList|[[PositionDTO](#schemapositiondto)]|false|none||岗位列表|
|orgUnitList|[[OrgUnitDTO](#schemaorgunitdto)]|false|none||组织列表|
|roleList|[[RoleDTO](#schemaroledto)]|false|none||角色列表|
|employeeOrgList|[[EmployeeOrgDTO](#schemaemployeeorgdto)]|false|none||员工组织关系列表|
|departmentName|string|false|none||部门名称|
|positionName|string|false|none||岗位名称|

#### 枚举值

|属性|值|
|---|---|
|gender|UNKNOWN|
|gender|MALE|
|gender|FEMALE|
|status|ACTIVE|
|status|INACTIVE|
|status|FROZEN|

<h2 id="tocS_PositionDTO">PositionDTO</h2>

<a id="schemapositiondto"></a>
<a id="schema_PositionDTO"></a>
<a id="tocSpositiondto"></a>
<a id="tocspositiondto"></a>

```json
{
  "id": 0,
  "code": "string",
  "name": "string",
  "level": 1,
  "category": "MANAGEMENT",
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "orgUnitId": 0,
  "orgUnitName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|code|string|true|none||岗位编码|
|name|string|true|none||岗位名称|
|level|integer|false|none||岗位级别|
|category|string|false|none||岗位分类|
|description|string|false|none||描述信息|
|metadata|string|false|none||扩展元数据|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|orgUnitId|integer(int64)|true|none||组织单元 ID|
|orgUnitName|string|false|none||组织单元名称|

#### 枚举值

|属性|值|
|---|---|
|category|MANAGEMENT|
|category|TECHNICAL|
|category|BUSINESS|
|category|SUPPORT|

<h2 id="tocS_EmployeeOrgDTO">EmployeeOrgDTO</h2>

<a id="schemaemployeeorgdto"></a>
<a id="schema_EmployeeOrgDTO"></a>
<a id="tocSemployeeorgdto"></a>
<a id="tocsemployeeorgdto"></a>

```json
{
  "id": 0,
  "startDate": "string",
  "endDate": "string",
  "isPrimary": true,
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "employee": {
    "id": 0,
    "tenantId": 0,
    "username": "string",
    "password": "string",
    "salt": "string",
    "realName": "string",
    "avatar": "string",
    "email": "string",
    "phone": "string",
    "gender": "UNKNOWN",
    "birthDate": "string",
    "idCard": "string",
    "employeeNo": "string",
    "status": "ACTIVE",
    "hireDate": "string",
    "leaveDate": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "lastLoginTime": {
      "seconds": 0,
      "nanos": 0
    },
    "lastLoginIp": "string",
    "loginFailureCount": 0,
    "accountLockedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordChangedTime": {
      "seconds": 0,
      "nanos": 0
    },
    "passwordExpiredTime": {
      "seconds": 0,
      "nanos": 0
    },
    "isFirstLogin": true,
    "forceChangePassword": true,
    "wechatOpenId": "string",
    "wechatUnionId": "string",
    "positionList": [
      {
        "id": 0,
        "code": "string",
        "name": "string",
        "level": 1,
        "category": "MANAGEMENT",
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "orgUnitId": 0,
        "orgUnitName": "string"
      }
    ],
    "orgUnitList": [
      {
        "id": 0,
        "name": "string",
        "code": "string",
        "type": "COMPANY",
        "level": 0,
        "status": 0,
        "sortOrder": 0,
        "description": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parentId": 0,
        "children": "new ArrayList<>()"
      }
    ],
    "roleList": [
      {
        "id": 0,
        "name": "string",
        "code": "string",
        "description": "string",
        "status": 0,
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "parent": {
          "id": 0,
          "name": "string",
          "code": "string",
          "description": "string",
          "status": 0,
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "parent": {}
        }
      }
    ],
    "employeeOrgList": [
      {
        "id": 0,
        "startDate": "string",
        "endDate": "string",
        "isPrimary": true,
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": true,
        "employee": {
          "id": 0,
          "tenantId": 0,
          "username": "string",
          "password": "string",
          "salt": "string",
          "realName": "string",
          "avatar": "string",
          "email": "string",
          "phone": "string",
          "gender": "[",
          "birthDate": "string",
          "idCard": "string",
          "employeeNo": "string",
          "status": "[",
          "hireDate": "string",
          "leaveDate": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "lastLoginTime": {},
          "lastLoginIp": "string",
          "loginFailureCount": 0,
          "accountLockedTime": {},
          "passwordChangedTime": {},
          "passwordExpiredTime": {},
          "isFirstLogin": true,
          "forceChangePassword": true,
          "wechatOpenId": "string",
          "wechatUnionId": "string",
          "positionList": [
            null
          ],
          "orgUnitList": [
            null
          ],
          "roleList": [
            null
          ],
          "employeeOrgList": [
            null
          ],
          "departmentName": "string",
          "positionName": "string"
        },
        "orgUnit": {
          "id": 0,
          "name": "string",
          "code": "string",
          "type": "[",
          "level": 0,
          "status": 0,
          "sortOrder": 0,
          "description": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "parentId": 0,
          "children": "new ArrayList<>()"
        },
        "position": {
          "id": 0,
          "code": "string",
          "name": "string",
          "level": 1,
          "category": "[",
          "description": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {},
          "updatedBy": "string",
          "updatedAt": {},
          "isDeleted": true,
          "orgUnitId": 0,
          "orgUnitName": "string"
        }
      }
    ],
    "departmentName": "string",
    "positionName": "string"
  },
  "orgUnit": {
    "id": 0,
    "name": "string",
    "code": "string",
    "type": "COMPANY",
    "level": 0,
    "status": 0,
    "sortOrder": 0,
    "description": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "parentId": 0,
    "children": "new ArrayList<>()"
  },
  "position": {
    "id": 0,
    "code": "string",
    "name": "string",
    "level": 1,
    "category": "MANAGEMENT",
    "description": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "orgUnitId": 0,
    "orgUnitName": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|startDate|string|false|none||任职开始日期|
|endDate|string|false|none||任职结束日期|
|isPrimary|boolean|true|none||是否主部门|
|version|integer|false|none||乐观锁版本|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||软删除标志|
|employee|[EmployeeDTO](#schemaemployeedto)|false|none||none|
|orgUnit|[OrgUnitDTO](#schemaorgunitdto)|false|none||none|
|position|[PositionDTO](#schemapositiondto)|false|none||none|

