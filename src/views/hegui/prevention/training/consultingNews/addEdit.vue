<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import systemApi from '@/api/curriculum/news'
import richText from '@/components/richText/index.vue'
import ImageUpload from '@/components/ImageUpload/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'

const router = useRouter()
const route = useRoute()

// 编辑状态
const isEdit = ref(false)
const newsId = ref<string | null>(null)

// 表单数据
const newsForm = reactive({
  id: null,
  title: '',
  subtitle: '',
  summary: '',
  content: '',
  keywords: '',
  status: 'DRAFT',
  sortOrder: 0,
  coverImageUrl: '',
  isSticky: false,
  stickyStartTime: undefined,
  stickyEndTime: undefined,
  publishDate: undefined,
  category: undefined,
  tags: [],
})

// 表单引用
const newsFormRef = ref()

// 表单验证规则
const newsFormRules = {
  title: [{ required: true, message: '请输入新闻标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入新闻内容', trigger: 'blur' }],
  category: [{ required: true, message: '请选择新闻分类', trigger: 'change' }],
}

// 分类和标签数据
const newsCategories = ref<any[]>([])
const tagCategories = ref<any[]>([])
const selectedTags = ref<any[]>([])

// 加载状态
const submitLoading = ref(false)

// 发布设置
const publishSettings = reactive({
  publishStatus: 'publish', // publish, draft, review
  publishTime: '',
  validStartTime: '',
  validEndTime: '',
  pushToAll: true,
  pushToDepartments: false,
  pushToPositions: false,
  systemPush: true,
  emailPush: false,
  mobilePush: true,
  allowComments: true,
  showViewCount: true,
})

// 文件上传
const attachmentFiles = ref<any[]>([])

// 获取新闻分类
async function getNewsCategories() {
  try {
    const response = await systemApi.getNewsCategories({})
    if (response.content) {
      newsCategories.value = response.content
    }
  }
  catch (error) {
    console.error('获取新闻分类失败:', error)
    ElMessage.error('获取新闻分类失败')
  }
}

// 获取标签分类
async function getTagCategories() {
  try {
    const response = await systemApi.getTagCategories({})
    if (response.content) {
      tagCategories.value = response.content
    }
  }
  catch (error) {
    console.error('获取标签分类失败:', error)
    ElMessage.error('获取标签分类失败')
  }
}

// 获取新闻详情
async function getNewsDetail() {
  if (!newsId.value) {
    return
  }

  try {
    const response = await systemApi.news({ id: newsId.value }, 'info')
    if (response) {
      // 数据回填
      Object.assign(newsForm, response)

      // 处理标签回填
      if (response.tags && Array.isArray(response.tags)) {
        selectedTags.value = response.tags.map((tag: any) => tag.id || tag)
      }

      // 处理封面图片回填
      if (response.coverImageUrl) {
        newsForm.coverImageUrl = response.coverImageUrl
      }

      // 处理附件回填
      if (response.attachments && Array.isArray(response.attachments)) {
        attachmentFiles.value = response.attachments
      }
    }
  }
  catch (error) {
    console.error('获取新闻详情失败:', error)
    ElMessage.error('获取新闻详情失败')
  }
}

// 保存草稿
async function saveDraft() {
  try {
    submitLoading.value = true
    newsForm.status = 'DRAFT'

    const formData = {
      ...newsForm,
      category: newsForm.category ? { id: newsForm.category } : null,
      tags: selectedTags.value.map((tagId) => {
        const tag = tagCategories.value.find(t => t.id === tagId)
        return { id: tagId, name: tag?.name || '' }
      }),
    }

    if (isEdit.value && newsForm.id) {
      // 更新操作
      await systemApi.news(formData, 'update')
      ElMessage.success('草稿更新成功')
    }
    else {
      // 新增操作
      await systemApi.news(formData, 'addEdit')
      ElMessage.success('草稿保存成功')
    }
  }
  catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败')
  }
  finally {
    submitLoading.value = false
  }
}

// 保存并发布
async function saveAndPublish() {
  try {
    await newsFormRef.value?.validate()

    const result = await ElMessageBox.confirm(
      '确定要发布这篇新闻吗？发布后将推送给相关用户。',
      '确认发布',
      {
        confirmButtonText: '确定发布',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    if (result === 'confirm') {
      submitLoading.value = true
      newsForm.status = 'PUBLISHED'

      const formData = {
        ...newsForm,
        category: newsForm.category ? { id: newsForm.category } : null,
        tags: selectedTags.value.map((tagId) => {
          const tag = tagCategories.value.find(t => t.id === tagId)
          return { id: tagId, name: tag?.name || '' }
        }),
      }

      if (isEdit.value && newsForm.id) {
        // 更新操作
        await systemApi.news(formData, 'update')
        ElMessage.success('新闻更新并发布成功')
      }
      else {
        // 新增操作
        await systemApi.news(formData, 'addEdit')
        ElMessage.success('新闻发布成功')
      }
      router.push('/training/consultingNews')
    }
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('发布新闻失败:', error)
      ElMessage.error('发布新闻失败')
    }
  }
  finally {
    submitLoading.value = false
  }
}

// 预览效果
function previewNews() {
  ElMessage.info('预览功能开发中')
}

// 取消编辑
async function cancelEdit() {
  try {
    await ElMessageBox.confirm(
      '确定要取消编辑吗？未保存的内容将丢失。',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续编辑',
        type: 'warning',
      },
    )
    router.back()
  }
  catch {
    // 用户取消
  }
}

// 这些函数已被新的组件替代，不再需要

// 历史内容数据
const histories = ref<Array<{ title: string, date: string }>>([
  { title: '关于加强合规培训的通知', date: '2024-03-10' },
  { title: '新版合规手册发布', date: '2024-03-08' },
  { title: '合规培训考试安排', date: '2024-03-05' },
])

// 处理封面图片上传成功
function handleCoverImageSuccess(response: any) {
  // ImageUpload 组件上传成功后的回调
  if (response) {
    newsForm.coverImageUrl = response
  }
  ElMessage.success('封面上传成功')
}

// 处理附件上传成功
function handleAttachmentSuccess(files: any[]) {
  attachmentFiles.value = files
}

// 处理富文本内容变化
function handleContentChange(content: string) {
  newsForm.content = content
}

// 数据回填函数
function _fillFormData(data: any) {
  if (data) {
    Object.assign(newsForm, data)
    // 处理分类回填
    if (data.category && typeof data.category === 'object' && data.category.id) {
      newsForm.category = data.category.id
    }
    // 处理标签回填
    if (data.tags && Array.isArray(data.tags)) {
      selectedTags.value = data.tags.map((tag: any) => tag.id || tag)
    }
  }
}

// 组件挂载时获取数据
onMounted(() => {
  // 检查路由参数，判断是新增还是编辑
  const id = route.params.id || route.query.id
  if (id) {
    isEdit.value = true
    newsId.value = typeof id === 'string' ? id : id[0]
  }

  getNewsCategories()
  getTagCategories()

  // 如果是编辑模式，获取详情数据
  if (isEdit.value) {
    getNewsDetail()
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-xl c-[#000000] font-bold">
              {{ isEdit ? '编辑资讯动态' : '新建资讯动态' }}
            </h1>
            <!-- <el-tag type="success" class="mt-2">生产中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" :loading="submitLoading" @click="saveAndPublish">
              保存并发布
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" :loading="submitLoading" @click="saveDraft">
              保存草稿
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="previewNews">
              预览效果
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="cancelEdit">
              取消
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div class="min-h-screen flex bg-gray-50">
          <!-- 主内容区 -->
          <div class="flex-1 overflow-auto">
            <!-- 主编辑区 -->
            <div class="mx-6 mt-6 flex">
              <!-- 中间编辑区 -->
              <div class="mr-6 flex-1">
                <!-- 基本信息设置 -->
                <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                  <h2 class="mb-4 text-lg text-gray-900 font-semibold">
                    基本信息设置
                  </h2>
                  <el-form ref="newsFormRef" :model="newsForm" :rules="newsFormRules" label-width="120px">
                    <!-- 资讯标题 -->
                    <el-form-item label="资讯标题" prop="title">
                      <el-input
                        v-model="newsForm.title"
                        placeholder="请输入资讯标题，建议30字以内"
                        maxlength="60"
                        show-word-limit
                        clearable
                      />
                    </el-form-item>

                    <!-- 副标题 -->
                    <el-form-item label="副标题">
                      <el-input
                        v-model="newsForm.subtitle"
                        placeholder="请输入副标题（可选）"
                        maxlength="100"
                        show-word-limit
                        clearable
                      />
                    </el-form-item>

                    <!-- 资讯分类 -->
                    <el-form-item label="资讯分类" prop="category">
                      <el-select
                        v-model="newsForm.category"
                        placeholder="请选择资讯分类"
                        style="width: 100%"
                        clearable
                      >
                        <el-option
                          v-for="category in newsCategories"
                          :key="category.id"
                          :label="category.name"
                          :value="category.id"
                        />
                      </el-select>
                    </el-form-item>

                    <!-- 标签选择 -->
                    <el-form-item label="标签">
                      <el-select
                        v-model="selectedTags"
                        placeholder="请选择标签"
                        multiple
                        style="width: 100%"
                        clearable
                      >
                        <el-option
                          v-for="tag in tagCategories"
                          :key="tag.id"
                          :label="tag.name"
                          :value="tag.id"
                        />
                      </el-select>
                    </el-form-item>

                    <!-- 关键词 -->
                    <el-form-item label="关键词">
                      <el-input
                        v-model="newsForm.keywords"
                        placeholder="请输入关键词，多个关键词用逗号分隔"
                        maxlength="200"
                        show-word-limit
                        clearable
                      />
                    </el-form-item>

                    <!-- 资讯摘要 -->
                    <el-form-item label="资讯摘要">
                      <el-input
                        v-model="newsForm.summary"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入资讯摘要，将在移动端预览中显示"
                        maxlength="200"
                        show-word-limit
                      />
                    </el-form-item>

                    <!-- 排序 -->
                    <el-form-item label="排序">
                      <el-input-number
                        v-model="newsForm.sortOrder"
                        :min="0"
                        :max="9999"
                        placeholder="数值越大排序越靠前"
                      />
                    </el-form-item>
                  </el-form>
                </div>
                <!-- 内容编辑区 -->
                <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                  <h2 class="mb-4 text-lg text-gray-900 font-semibold">
                    内容编辑
                  </h2>
                  <richText :content="newsForm.content" @update:content="handleContentChange" />
                </div>
                <!-- 媒体资源管理 -->
                <div class="rounded-lg bg-white p-6 shadow-sm">
                  <h2 class="mb-4 text-lg text-gray-900 font-semibold">
                    媒体资源管理
                  </h2>
                  <!-- 封面图片 -->
                  <div class="mb-6">
                    <label class="mb-1 block text-sm text-gray-700 font-medium">封面图片</label>
                    <ImageUpload
                      v-model:url="newsForm.coverImageUrl"
                      :width="300"
                      :height="169"
                      :size="10"
                      :ext="['jpg', 'jpeg', 'png', 'gif']"
                      placeholder=""
                      :notip="false"
                      dirname="news"
                      @on-success="handleCoverImageSuccess"
                    />
                    <div class="mt-2 text-xs text-gray-500">
                      建议尺寸：16:9比例，最小分辨率800x450px
                    </div>
                  </div>
                  <!-- 附件管理 -->
                  <div v-if="false">
                    <label class="mb-1 block text-sm text-gray-700 font-medium">附件管理</label>
                    <UploadMbb
                      v-model="attachmentFiles"
                      :max="10"
                      :size="20"
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                      tip-text="支持 PDF、DOC、XLS、PPT 格式文件，大小不超过 20MB"
                      service-name="whiskerguardregulatoryservice"
                      category-name="news"
                      @upload-success="handleAttachmentSuccess"
                    />
                  </div>
                </div>
              </div>
              <!-- 右侧设置面板 -->
              <div class="w-80 flex-shrink-0">
                <div class="sticky rounded-lg bg-white p-6 shadow-sm">
                  <!-- 发布设置 -->
                  <div class="mb-6">
                    <h3 class="mb-3 text-base text-gray-900 font-semibold">
                      发布设置
                    </h3>
                    <el-form :model="publishSettings" label-width="80px">
                      <!-- 发布状态 -->
                      <el-form-item label="发布状态">
                        <el-radio-group v-model="publishSettings.publishStatus">
                          <el-radio label="publish">
                            立即发布
                          </el-radio>
                          <el-radio label="draft">
                            保存草稿
                          </el-radio>
                          <el-radio label="review">
                            提交审核
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <!-- 发布时间 -->
                      <el-form-item label="发布时间">
                        <el-date-picker
                          v-model="newsForm.publishDate"
                          type="datetime"
                          placeholder="选择发布时间"
                          style="width: 100%"
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>

                      <!-- 有效期设置 -->
                      <el-form-item label="有效期">
                        <div class="space-y-2">
                          <el-date-picker
                            v-model="newsForm.stickyStartTime"
                            type="date"
                            placeholder="开始时间"
                            style="width: 100%"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                          />
                          <el-date-picker
                            v-model="newsForm.stickyEndTime"
                            type="date"
                            placeholder="结束时间"
                            style="width: 100%"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                          />
                        </div>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 推送设置 -->
                  <div class="mb-6">
                    <h3 class="mb-3 text-base text-gray-900 font-semibold">
                      推送设置
                    </h3>
                    <el-form :model="publishSettings" label-width="80px">
                      <!-- 推送对象 -->
                      <el-form-item label="推送对象">
                        <el-checkbox v-model="publishSettings.pushToAll">
                          全体员工
                        </el-checkbox>
                        <el-checkbox v-model="publishSettings.pushToDepartments">
                          指定部门
                        </el-checkbox>
                        <el-checkbox v-model="publishSettings.pushToPositions">
                          指定岗位
                        </el-checkbox>
                        <div class="mt-2 text-xs text-gray-500">
                          已选择：<span class="text-blue-600">356人</span>
                        </div>
                      </el-form-item>

                      <!-- 推送方式 -->
                      <el-form-item label="推送方式">
                        <el-checkbox v-model="publishSettings.systemPush">
                          系统内推送
                        </el-checkbox>
                        <el-checkbox v-model="publishSettings.emailPush">
                          邮件提醒
                        </el-checkbox>
                        <el-checkbox v-model="publishSettings.mobilePush">
                          移动端推送
                        </el-checkbox>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 显示设置 -->
                  <div class="mb-6">
                    <h3 class="mb-3 text-base text-gray-900 font-semibold">
                      显示设置
                    </h3>
                    <el-form :model="publishSettings" label-width="80px">
                      <el-form-item label="是否置顶">
                        <el-switch v-model="newsForm.isSticky" />
                      </el-form-item>

                      <el-form-item label="允许评论">
                        <el-switch v-model="publishSettings.allowComments" />
                      </el-form-item>

                      <el-form-item label="显示阅读数">
                        <el-switch v-model="publishSettings.showViewCount" />
                      </el-form-item>
                    </el-form>
                  </div>
                  <!-- 快捷工具 -->
                  <div>
                    <h3 class="mb-3 text-base text-gray-900 font-semibold">
                      快捷工具
                    </h3>
                    <div class="space-y-3">
                      <div>
                        <label class="mb-1 block text-sm text-gray-700 font-medium">历史内容</label>
                        <div class="space-y-2">
                          <div
                            v-for="(history, index) in histories" :key="index"
                            class="cursor-pointer border border-gray-200 rounded-md p-2 hover:bg-gray-50"
                          >
                            <p class="truncate text-sm text-gray-700 font-medium">
                              {{ history.title }}
                            </p>
                            <p class="text-xs text-gray-500">
                              {{ history.date }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";
</style>

<style scoped>
  /* 自定义样式 */
  .min-h-screen {
    min-height: 1024px;
  }

  .h-screen {
    height: 1024px;
  }

  .sticky {
    position: sticky;
  }

  .object-cover {
    object-fit: cover;
    object-position: top;
  }

  /* 过渡动画 */
  .transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  /* 开关按钮样式 */
  .bg-gray-200[role="switch"] {
    background-color: rgb(229, 231, 235);
  }

  .bg-blue-600[role="switch"] {
    background-color: rgb(37, 99, 235);
  }

  .translate-x-0 {
    transform: translateX(0);
  }

  .translate-x-5 {
    transform: translateX(1.25rem);
  }

  /* 封面图片上传样式已移除，使用新的FileUpload组件 */
</style>
