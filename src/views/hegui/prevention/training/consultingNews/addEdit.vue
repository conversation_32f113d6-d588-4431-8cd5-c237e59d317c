<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const selectedType = ref('trainingNotice')
const infoTypes = [
  { label: '培训通知', value: 'trainingNotice' },
  { label: '学习资料', value: 'learningMaterials' },
  { label: '政策解读', value: 'policyInterpretation' },
  { label: '经验分享', value: 'experienceSharing' },
  { label: '工作动态', value: 'workUpdates' },
]
const files = ref([
  { name: '2024年合规培训计划.pdf', size: '2.4MB', date: '2024-03-15 10:23' },
  { name: '合规培训签到表.xlsx', size: '1.2MB', date: '2024-03-15 11:45' },
])
const histories = ref([
  { title: '关于开展2023年第四季度合规培训的通知', date: '2023-12-10' },
  { title: '2023年度合规政策解读与案例分析', date: '2023-11-25' },
  { title: '合规风险防范经验分享会通知', date: '2023-10-15' },
])
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-xl c-[#000000] font-bold">
              新建资讯动态
            </h1>
            <!-- <el-tag type="success" class="mt-2">生产中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="goEdit(null)">
              保存并发布
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              保存草稿
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              预览效果
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              取消
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div class="min-h-screen flex bg-gray-50">
          <!-- 主内容区 -->
          <div class="flex-1 overflow-auto">
            <!-- 顶部面包屑和操作按钮 -->
            <!-- <div class="bg-white shadow-sm sticky top-0 z-10">
              <div class="px-6 py-4 flex items-center justify-between border-b border-gray-200">
                <div>
                  <div class="flex items-center text-sm text-gray-500">
                    <span>预防之翼</span>
                    <el-icon class="mx-1"><i class="fas fa-chevron-right text-xs"></i></el-icon>
                    <span>合规培训体系</span>
                    <el-icon class="mx-1"><i class="fas fa-chevron-right text-xs"></i></el-icon>
                    <span>资讯动态</span>
                    <el-icon class="mx-1"><i class="fas fa-chevron-right text-xs"></i></el-icon>
                    <span class="text-gray-700">新建资讯</span>
                  </div>
                  <h1 class="text-2xl font-bold text-gray-900 mt-1">新建资讯动态</h1>
                  <p class="text-sm text-gray-500 mt-1">创建或编辑合规培训相关的资讯内容</p>
                </div>
                <div class="flex space-x-3">
                  <button
                    class="!rounded-button whitespace-nowrap px-4 py-2 bg-blue-600 text-white text-sm font-medium hover:bg-blue-700">
                    保存并发布
                  </button>
                  <button
                    class="!rounded-button whitespace-nowrap px-4 py-2 bg-white text-blue-600 text-sm font-medium border border-blue-600 hover:bg-blue-50">
                    保存草稿
                  </button>
                  <button
                    class="!rounded-button whitespace-nowrap px-4 py-2 bg-white text-gray-700 text-sm font-medium border border-gray-300 hover:bg-gray-50">
                    预览效果
                  </button>
                  <button
                    class="!rounded-button whitespace-nowrap px-4 py-2 bg-white text-red-600 text-sm font-medium border border-red-600 hover:bg-red-50">
                    取消
                  </button>
                </div>
              </div>
            </div> -->
            <!-- 进度提示区 -->
            <div class="mx-6 mt-6 rounded-lg bg-white p-4 shadow-sm">
              <div class="flex items-center justify-between">
                <div class="flex space-x-8">
                  <div class="flex items-center">
                    <div class="h-6 w-6 flex items-center justify-center rounded-full bg-blue-600">
                      <el-icon class="text-xs text-white">
                        <i class="fas fa-check" />
                      </el-icon>
                    </div>
                    <span class="ml-2 text-sm text-blue-600 font-medium">基本信息设置</span>
                  </div>
                  <div class="flex items-center">
                    <div class="h-6 w-6 flex items-center justify-center rounded-full bg-blue-600">
                      <span class="text-xs text-white">2</span>
                    </div>
                    <span class="ml-2 text-sm text-blue-600 font-medium">内容编辑</span>
                  </div>
                  <div class="flex items-center">
                    <div class="h-6 w-6 flex items-center justify-center rounded-full bg-gray-200">
                      <span class="text-xs text-gray-500">3</span>
                    </div>
                    <span class="ml-2 text-sm text-gray-500 font-medium">发布设置</span>
                  </div>
                  <div class="flex items-center">
                    <div class="h-6 w-6 flex items-center justify-center rounded-full bg-gray-200">
                      <span class="text-xs text-gray-500">4</span>
                    </div>
                    <span class="ml-2 text-sm text-gray-500 font-medium">确认发布</span>
                  </div>
                </div>
                <div class="text-sm text-green-600">
                  自动保存于 2024-03-15 14:23
                </div>
              </div>
            </div>
            <!-- 主编辑区 -->
            <div class="mx-6 mt-6 flex">
              <!-- 中间编辑区 -->
              <div class="mr-6 flex-1">
                <!-- 基本信息设置 -->
                <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                  <h2 class="mb-4 text-lg text-gray-900 font-semibold">
                    基本信息设置
                  </h2>
                  <!-- 资讯标题 -->
                  <div class="mb-6">
                    <label class="mb-1 block text-sm text-gray-700 font-bold font-medium">资讯标题</label>
                    <div class="relative">
                      <input
                        type="text"
                        class="w-full border border-gray-300 rounded-md px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                        placeholder="请输入资讯标题，建议30字以内" maxlength="60"
                      >
                      <span class="absolute bottom-2 right-2 text-xs text-gray-500">25/60</span>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                      标题预览：<span class="text-gray-700">关于开展2024年第一季度合规培训的通知</span>
                    </div>
                  </div>
                  <!-- 资讯分类 -->
                  <div class="mb-6">
                    <label class="mb-1 block text-sm text-gray-700 font-bold font-medium">资讯分类</label>
                    <div class="grid grid-cols-2 gap-3">
                      <div
                        v-for="(type, index) in infoTypes" :key="index"
                        class="flex cursor-pointer items-center border border-gray-300 rounded-md p-3 hover:border-blue-500"
                        :class="{ 'border-blue-500 bg-blue-50': selectedType === type.value }"
                        @click="selectedType = type.value"
                      >
                        <div class="mr-2 h-4 w-4 flex items-center justify-center border border-gray-400 rounded-full">
                          <div v-if="selectedType === type.value" class="h-2 w-2 rounded-full bg-blue-600" />
                        </div>
                        <span class="text-sm">{{ type.label }}</span>
                      </div>
                    </div>
                  </div>
                  <!-- 资讯摘要 -->
                  <div>
                    <label class="mb-1 block text-sm text-gray-700 font-bold font-medium">资讯摘要</label>
                    <div class="relative">
                      <textarea
                        class="h-24 w-full border border-gray-300 rounded-md px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                        placeholder="请输入资讯摘要，将在移动端预览中显示" maxlength="200"
                      />
                      <span class="absolute bottom-2 right-2 text-xs text-gray-500">156/200</span>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                      摘要预览：<span
                        class="text-gray-700"
                      >根据公司年度合规培训计划，现组织开展2024年第一季度合规培训，请各部门员工按时参加...</span>
                    </div>
                  </div>
                </div>
                <!-- 内容编辑区 -->
                <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
                  <h2 class="mb-4 text-lg text-gray-900 font-semibold">
                    内容编辑
                  </h2>
                  <richText content="" />
                  <!-- 富文本编辑器工具栏 -->
                  <div class="flex flex-wrap items-center border border-gray-300 rounded-t-md bg-gray-50 p-2">
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-font" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-bold" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-italic" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-underline" /></el-icon>
                    </button>
                    <div class="mx-2 h-6 border-l border-gray-300" />
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-align-left" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-align-center" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-align-right" /></el-icon>
                    </button>
                    <div class="mx-2 h-6 border-l border-gray-300" />
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-list-ul" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-list-ol" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-quote-right" /></el-icon>
                    </button>
                    <div class="mx-2 h-6 border-l border-gray-300" />
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-link" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-image" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-table" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-code" /></el-icon>
                    </button>
                    <div class="mx-2 h-6 border-l border-gray-300" />
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-undo" /></el-icon>
                    </button>
                    <button class="mx-1 rounded p-1 text-gray-700 hover:bg-gray-200">
                      <el-icon><i class="fas fa-redo" /></el-icon>
                    </button>
                  </div>
                  <!-- 富文本编辑区域 -->
                  <div class="min-h-96 border border-t-0 border-gray-300 rounded-b-md p-4">
                    <p>请在此输入资讯内容...</p>
                  </div>
                  <!-- 辅助功能 -->
                  <div class="mt-4 flex items-center justify-between text-sm text-gray-500">
                    <div>
                      <span>字数统计：1568字</span>
                      <span class="mx-2">|</span>
                      <span>阅读时长估算：约5分钟</span>
                    </div>
                    <button class="text-blue-600 hover:text-blue-800">
                      <el-icon class="mr-1">
                        <i class="fas fa-list" />
                      </el-icon>
                      内容大纲
                    </button>
                  </div>
                </div>
                <!-- 媒体资源管理 -->
                <div class="rounded-lg bg-white p-6 shadow-sm">
                  <h2 class="mb-4 text-lg text-gray-900 font-semibold">
                    媒体资源管理
                  </h2>
                  <!-- 封面图片 -->
                  <div class="mb-6">
                    <label class="mb-1 block text-sm text-gray-700 font-medium">封面图片</label>
                    <div
                      class="cursor-pointer border-2 border-gray-300 rounded-md border-dashed p-6 text-center hover:border-blue-500"
                    >
                      <div class="flex flex-col items-center justify-center">
                        <el-icon class="mb-2 text-3xl text-gray-400">
                          <i class="fas fa-cloud-upload-alt" />
                        </el-icon>
                        <p class="text-sm text-gray-600">
                          点击上传或拖拽图片到此处
                        </p>
                        <p class="mt-1 text-xs text-gray-500">
                          建议尺寸：750×400px，支持jpg/png格式
                        </p>
                      </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                      已上传封面预览：
                    </div>
                    <div class="mt-2 h-40 w-full overflow-hidden rounded-md bg-gray-100">
                      <img
                        src="https://ai-public.mastergo.com/ai/img_res/c04ead94bb0aaf2b5ccc3e7d02d5244d.jpg"
                        alt="封面图片" class="h-full w-full object-cover"
                      >
                    </div>
                    <div class="mt-2 flex space-x-2">
                      <button
                        class="!rounded-button whitespace-nowrap border border-blue-600 bg-white px-3 py-1 text-xs text-blue-600 font-medium hover:bg-blue-50"
                      >
                        <el-icon class="mr-1">
                          <i class="fas fa-crop" />
                        </el-icon>
                        裁剪
                      </button>
                      <button
                        class="!rounded-button whitespace-nowrap border border-blue-600 bg-white px-3 py-1 text-xs text-blue-600 font-medium hover:bg-blue-50"
                      >
                        <el-icon class="mr-1">
                          <i class="fas fa-sync-alt" />
                        </el-icon>
                        旋转
                      </button>
                      <button
                        class="!rounded-button whitespace-nowrap border border-red-600 bg-white px-3 py-1 text-xs text-red-600 font-medium hover:bg-red-50"
                      >
                        <el-icon class="mr-1">
                          <i class="fas fa-trash" />
                        </el-icon>
                        删除
                      </button>
                    </div>
                  </div>
                  <!-- 附件管理 -->
                  <div>
                    <label class="mb-1 block text-sm text-gray-700 font-medium">附件管理</label>
                    <div class="border border-gray-300 rounded-md p-4">
                      <div
                        class="mb-4 cursor-pointer border-2 border-gray-300 rounded-md border-dashed p-4 text-center hover:border-blue-500"
                      >
                        <div class="flex flex-col items-center justify-center">
                          <el-icon class="mb-2 text-3xl text-gray-400">
                            <i class="fas fa-file-upload" />
                          </el-icon>
                          <p class="text-sm text-gray-600">
                            点击上传或拖拽文件到此处
                          </p>
                          <p class="mt-1 text-xs text-gray-500">
                            支持格式：pdf、doc、docx、xls、xlsx、ppt、pptx
                          </p>
                        </div>
                      </div>
                      <div class="space-y-3">
                        <div
                          v-for="(file, index) in files" :key="index"
                          class="flex items-center justify-between border border-gray-200 rounded-md p-2 hover:bg-gray-50"
                        >
                          <div class="flex items-center">
                            <el-icon class="mr-2 text-gray-500">
                              <i class="fas fa-file-pdf" />
                            </el-icon>
                            <div>
                              <p class="text-sm text-gray-700 font-medium">
                                {{ file.name }}
                              </p>
                              <p class="text-xs text-gray-500">
                                {{ file.size }} · {{ file.date }}
                              </p>
                            </div>
                          </div>
                          <div class="flex space-x-2">
                            <button class="p-1 text-blue-600 hover:text-blue-800">
                              <el-icon><i class="fas fa-eye" /></el-icon>
                            </button>
                            <button class="p-1 text-red-600 hover:text-red-800">
                              <el-icon><i class="fas fa-trash" /></el-icon>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 右侧设置面板 -->
              <div class="w-80 flex-shrink-0">
                <div class="sticky top-6 rounded-lg bg-white p-6 shadow-sm">
                  <!-- 发布设置 -->
                  <div class="mb-6">
                    <h3 class="mb-3 text-base text-gray-900 font-semibold">
                      发布设置
                    </h3>
                    <div class="space-y-3">
                      <div>
                        <label class="mb-1 block text-sm text-gray-700 font-medium">发布状态</label>
                        <div class="space-y-2">
                          <div class="flex items-center">
                            <input
                              id="publishNow" type="radio" name="publishStatus"
                              class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked
                            >
                            <label for="publishNow" class="ml-2 block text-sm text-gray-700">立即发布</label>
                          </div>
                          <div class="flex items-center">
                            <input
                              id="saveDraft" type="radio" name="publishStatus"
                              class="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            >
                            <label for="saveDraft" class="ml-2 block text-sm text-gray-700">保存草稿</label>
                          </div>
                          <div class="flex items-center">
                            <input
                              id="submitReview" type="radio" name="publishStatus"
                              class="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            >
                            <label for="submitReview" class="ml-2 block text-sm text-gray-700">提交审核</label>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label class="mb-1 block text-sm text-gray-700 font-medium">发布时间</label>
                        <div class="relative">
                          <input
                            type="datetime-local"
                            class="w-full border border-gray-300 rounded-md px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                        </div>
                      </div>
                      <div>
                        <label class="mb-1 block text-sm text-gray-700 font-medium">有效期设置</label>
                        <div class="grid grid-cols-2 gap-2">
                          <div>
                            <label class="mb-1 block text-xs text-gray-500">开始时间</label>
                            <input
                              type="date"
                              class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                            >
                          </div>
                          <div>
                            <label class="mb-1 block text-xs text-gray-500">结束时间</label>
                            <input
                              type="date"
                              class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 推送设置 -->
                  <div class="mb-6">
                    <h3 class="mb-3 text-base text-gray-900 font-semibold">
                      推送设置
                    </h3>
                    <div class="space-y-3">
                      <div>
                        <label class="mb-1 block text-sm text-gray-700 font-medium">推送对象</label>
                        <div class="space-y-2">
                          <div class="flex items-center">
                            <input
                              id="allEmployees" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500"
                              checked
                            >
                            <label for="allEmployees" class="ml-2 block text-sm text-gray-700">全体员工</label>
                          </div>
                          <div class="flex items-center">
                            <input
                              id="specificDepartments" type="checkbox"
                              class="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            >
                            <label for="specificDepartments" class="ml-2 block text-sm text-gray-700">指定部门</label>
                          </div>
                          <div class="flex items-center">
                            <input
                              id="specificPositions" type="checkbox"
                              class="h-4 w-4 text-blue-600 focus:ring-blue-500"
                            >
                            <label for="specificPositions" class="ml-2 block text-sm text-gray-700">指定岗位</label>
                          </div>
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                          已选择：<span class="text-blue-600">356人</span>
                        </div>
                      </div>
                      <div>
                        <label class="mb-1 block text-sm text-gray-700 font-medium">推送方式</label>
                        <div class="space-y-2">
                          <div class="flex items-center">
                            <input
                              id="systemPush" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500"
                              checked
                            >
                            <label for="systemPush" class="ml-2 block text-sm text-gray-700">系统内推送</label>
                          </div>
                          <div class="flex items-center">
                            <input id="emailPush" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="emailPush" class="ml-2 block text-sm text-gray-700">邮件提醒</label>
                          </div>
                          <div class="flex items-center">
                            <input
                              id="mobilePush" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500"
                              checked
                            >
                            <label for="mobilePush" class="ml-2 block text-sm text-gray-700">移动端推送</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 显示设置 -->
                  <div class="mb-6">
                    <h3 class="mb-3 text-base text-gray-900 font-semibold">
                      显示设置
                    </h3>
                    <div class="space-y-3">
                      <div class="flex items-center justify-between">
                        <label class="block text-sm text-gray-700 font-medium">是否置顶</label>
                        <button
                          type="button"
                          class="relative h-6 w-11 inline-flex flex-shrink-0 cursor-pointer border-2 border-transparent rounded-full bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          role="switch"
                        >
                          <span class="sr-only">是否置顶</span>
                          <span
                            aria-hidden="true"
                            class="inline-block h-5 w-5 translate-x-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                          />
                        </button>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm text-gray-700 font-medium">允许评论</label>
                        <button
                          type="button"
                          class="relative h-6 w-11 inline-flex flex-shrink-0 cursor-pointer border-2 border-transparent rounded-full bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          role="switch"
                        >
                          <span class="sr-only">允许评论</span>
                          <span
                            aria-hidden="true"
                            class="inline-block h-5 w-5 translate-x-0 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                          />
                        </button>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm text-gray-700 font-medium">显示阅读数</label>
                        <button
                          type="button"
                          class="relative h-6 w-11 inline-flex flex-shrink-0 cursor-pointer border-2 border-transparent rounded-full bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          role="switch"
                        >
                          <span class="sr-only">显示阅读数</span>
                          <span
                            aria-hidden="true"
                            class="inline-block h-5 w-5 translate-x-0 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                  <!-- 快捷工具 -->
                  <div>
                    <h3 class="mb-3 text-base text-gray-900 font-semibold">
                      快捷工具
                    </h3>
                    <div class="space-y-3">
                      <div>
                        <label class="mb-1 block text-sm text-gray-700 font-medium">历史内容</label>
                        <div class="space-y-2">
                          <div
                            v-for="(history, index) in histories" :key="index"
                            class="cursor-pointer border border-gray-200 rounded-md p-2 hover:bg-gray-50"
                          >
                            <p class="truncate text-sm text-gray-700 font-medium">
                              {{ history.title }}
                            </p>
                            <p class="text-xs text-gray-500">
                              {{ history.date }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 底部操作区 -->
            <div class="mx-6 mb-6 rounded-lg bg-white p-6 shadow-sm">
              <h3 class="mb-4 text-lg text-gray-900 font-semibold">
                发布前检查
              </h3>
              <div class="mb-6 space-y-3">
                <div class="flex items-center">
                  <el-icon class="mr-2 text-green-500">
                    <i class="fas fa-check-circle" />
                  </el-icon>
                  <span class="text-sm text-gray-700">必填项检查：标题、内容、类型等必填项已完整</span>
                </div>
                <div class="flex items-center">
                  <el-icon class="mr-2 text-green-500">
                    <i class="fas fa-check-circle" />
                  </el-icon>
                  <span class="text-sm text-gray-700">内容质量检查：内容长度、图片质量、格式规范符合要求</span>
                </div>
                <div class="flex items-center">
                  <el-icon class="mr-2 text-green-500">
                    <i class="fas fa-check-circle" />
                  </el-icon>
                  <span class="text-sm text-gray-700">推送对象确认：将推送至356名员工</span>
                </div>
              </div>
              <div class="flex justify-end space-x-3">
                <button
                  class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-sm text-white font-medium hover:bg-blue-700"
                >
                  保存并发布
                </button>
                <button
                  class="!rounded-button whitespace-nowrap border border-blue-600 bg-white px-4 py-2 text-sm text-blue-600 font-medium hover:bg-blue-50"
                >
                  保存草稿
                </button>
                <button
                  class="!rounded-button whitespace-nowrap border border-gray-300 bg-white px-4 py-2 text-sm text-gray-700 font-medium hover:bg-gray-50"
                >
                  预览效果
                </button>
                <button
                  class="!rounded-button whitespace-nowrap border border-red-600 bg-white px-4 py-2 text-sm text-red-600 font-medium hover:bg-red-50"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";
</style>

<style scoped>
  /* 自定义样式 */
  .min-h-screen {
    min-height: 1024px;
  }

  .h-screen {
    height: 1024px;
  }

  .sticky {
    position: sticky;
  }

  .object-cover {
    object-fit: cover;
    object-position: top;
  }

  /* 过渡动画 */
  .transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  /* 开关按钮样式 */
  .bg-gray-200[role="switch"] {
    background-color: rgb(229, 231, 235);
  }

  .bg-blue-600[role="switch"] {
    background-color: rgb(37, 99, 235);
  }

  .translate-x-0 {
    transform: translateX(0);
  }

  .translate-x-5 {
    transform: translateX(1.25rem);
  }
</style>
