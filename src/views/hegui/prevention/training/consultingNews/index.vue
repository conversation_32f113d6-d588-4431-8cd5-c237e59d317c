<script lang="ts" setup>
import { computed, ref } from 'vue'
import {
  ArrowRight,
  Delete,
  Document,
  Download,
  FolderOpened,
  InfoFilled,
  Operation,
  Plus,
  Search,
  Upload,
  Warning,
} from '@element-plus/icons-vue'
import systemApi from '@/api/complianceApi/one/systemManagement.ts'

const selectAll = ref(false)
const selectedRows = ref([])
const selectedCount = computed(() => selectedRows.value.length)
function handleSelectionChange(val: any) {
  selectedRows.value = val
}
function getTypeTag(type: string) {
  const map: Record<string, string> = {
    培训通知: '',
    学习资料: 'info',
    政策解读: 'primary',
    经验分享: 'success',
    工作动态: 'warning',
  }
  return map[type] || ''
}
function getStatusTag(status: string) {
  const map: Record<string, string> = {
    草稿: 'info',
    待审核: 'warning',
    已发布: 'success',
    已下线: 'danger',
  }
  return map[status] || ''
}

const dataList: any = ref([])
const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})
// 新增/编辑
const router = useRouter()

function goAddEdit(item: any) {
  if (item?.id) {
    // 编辑咨讯
    router.push({
      name: '/training/consultingNews/addEdit',
      query: { id: item.id },
    })
  }
  else {
    // 新增咨讯
    router.push({
      name: '/training/consultingNews/addEdit',
    })
  }
}
// 查看咨讯详情
function goDetail(row) {
  router.push({
    path: '/training/consultingNews/detail',
    query: { id: row.id },
  })
}

onMounted(() => {
  getList()
})

function getList() {
  systemApi.news({
    page: Number(paging.value.page) - 1,
    limit: Number(paging.value.limit),
  }, null).then((res: any) => {
    console.log(res, 'news')
    dataList.value = res.content ? res.content : [],
    paging.value.total = res.totalElements ? res.totalElements : 0
  })
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-xl c-[#000000] font-bold">
              资讯动态管理
            </h1>
            <el-tag type="success" class="mt-2">
              生产中
            </el-tag>
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="goAddEdit(null)">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>发布资讯
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <FolderOpened />
              </el-icon>分类管理
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="6">
            <el-card shadow="hover">
              <div class="text-3xl text-blue-500 font-bold">
                128
              </div>
              <div class="mt-1 text-sm text-gray-500">
                总资讯数
              </div>
              <div class="mt-2 text-xs text-green-600">
                +18 本月新增
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <div class="text-3xl text-green-600 font-bold">
                115
              </div>
              <div class="mt-1 text-sm text-gray-500">
                已发布
              </div>
              <div class="mt-2 text-xs text-green-600">
                89.8% 发布率
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <div class="text-3xl text-yellow-600 font-bold">
                8
              </div>
              <div class="mt-1 text-sm text-gray-500">
                待审核
              </div>
              <div class="mt-2 text-xs text-yellow-600">
                8 待处理
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <div class="text-3xl text-gray-600 font-bold">
                186
              </div>
              <div class="mt-1 text-sm text-gray-500">
                平均阅读量
              </div>
              <div class="mt-2 text-xs text-green-600">
                +23 较上月
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-20">
          <el-col :span="18">
            <el-card shadow="hover">
              <div class="flex items-center space-x-4">
                <el-select placeholder="资讯类型" class="w-40">
                  <el-option label="全部" value="all" />
                  <el-option label="培训通知" value="notice" />
                  <el-option label="学习资料" value="material" />
                  <el-option label="政策解读" value="policy" />
                  <el-option label="经验分享" value="experience" />
                  <el-option label="工作动态" value="news" />
                </el-select>
                <el-select placeholder="发布状态" class="w-32">
                  <el-option label="全部" value="all" />
                  <el-option label="草稿" value="draft" />
                  <el-option label="待审核" value="pending" />
                  <el-option label="已发布" value="published" />
                  <el-option label="已下线" value="offline" />
                </el-select>
                <el-date-picker
                  type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  class="w-64"
                />
                <el-input placeholder="请输入关键词" class="flex-1">
                  <template #append>
                    <el-button>
                      <el-icon>
                        <Search />
                      </el-icon>
                    </el-button>
                  </template>
                </el-input>
              </div>
              <el-table :data="dataList" style="width: 100%" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="50" />
                <el-table-column prop="title" label="资讯标题" width="220">
                  <template #default="{ row }">
                    {{ row.title }}
                  </template>
                </el-table-column>

                <!--                <el-table-column prop="type" label="资讯类型" width="120">
                  <template #default="{row}">
                    <el-tag :type="getTypeTag(row.type)" size="small">{{ row.type }}</el-tag>
                  </template>
                </el-table-column> -->
                <el-table-column prop="status" label="发布状态" width="120">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTag(row.status)" size="small">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createdBy" label="创建人" width="120" />
                <el-table-column prop="createdAt" label="创建时间" width="150" />
                <el-table-column prop="publishDate" label="发布时间" width="150" />

                <el-table-column label="操作">
                  <template #default="{ row }">
                    <el-link type="primary" :underline="false" class="mr-3" @click="goDetail(row)">
                      查看
                    </el-link>
                    <el-link type="warning" :underline="false" class="mr-3" @click="goAddEdit(row)">
                      编辑
                    </el-link>
                    <el-link type="danger" :underline="false">
                      下线
                    </el-link>
                  </template>
                </el-table-column>
              </el-table>
              <page-compon
                :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
                @pag-change="Object.assign(paging, $event), getList()"
              />
              <!-- 批量操作 -->
              <!-- <div class="flex items-center justify-between p-3 border-t bg-gray-50">
                <div class="flex items-center">
                  <el-checkbox v-model="selectAll" class="mr-3">全选</el-checkbox>
                  <span class="text-sm text-gray-500">已选择 {{ selectedCount }} 条资讯</span>
                </div>
                <div class="space-x-2">
                  <el-button type="success" size="small" class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <upload />
                    </el-icon>批量发布
                  </el-button>
                  <el-button type="warning" size="small" class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <download />
                    </el-icon>批量下线
                  </el-button>
                  <el-button type="danger" size="small" class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <delete />
                    </el-icon>批量删除
                  </el-button>
                </div>
              </div> -->
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="font-bold">
                  类型分布
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm">培训通知</span>
                  <span class="text-sm font-medium">42篇 (32.8%)</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm">学习资料</span>
                  <span class="text-sm font-medium">35篇 (27.3%)</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm">政策解读</span>
                  <span class="text-sm font-medium">28篇 (21.9%)</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm">经验分享</span>
                  <span class="text-sm font-medium">15篇 (11.7%)</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm">工作动态</span>
                  <span class="text-sm font-medium">8篇 (6.3%)</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="font-bold">
                  热门资讯
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <p class="text-sm font-medium">
                    财务合规新政策解读
                  </p>
                  <p class="text-xs text-gray-500">
                    阅读量 356次
                  </p>
                </div>
                <div>
                  <p class="text-sm font-medium">
                    合规培训考试安排通知
                  </p>
                  <p class="text-xs text-gray-500">
                    阅读量 289次
                  </p>
                </div>
                <div>
                  <p class="text-sm font-medium">
                    优秀合规案例分享
                  </p>
                  <p class="text-xs text-gray-500">
                    阅读量 234次
                  </p>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="font-bold">
                  发布趋势
                </div>
              </template>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-sm">本周发布</span>
                  <span class="text-sm font-medium">12篇 <span class="text-green-500">+3篇</span></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm">本月发布</span>
                  <span class="text-sm font-medium">35篇 <span class="text-green-500">+8篇</span></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm">员工参与度</span>
                  <span class="text-sm font-medium">82.5%</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="font-bold">
                  待处理事项
                </div>
              </template>
              <div class="space-y-2">
                <div class="flex items-center">
                  <el-icon class="mr-2 text-yellow-500">
                    <Warning />
                  </el-icon>
                  <span class="text-sm">8篇资讯待审核</span>
                </div>
                <div class="flex items-center">
                  <el-icon class="mr-2 text-gray-500">
                    <Document />
                  </el-icon>
                  <span class="text-sm">5篇草稿未完成</span>
                </div>
                <div class="flex items-center">
                  <el-icon class="mr-2 text-orange-500">
                    <InfoFilled />
                  </el-icon>
                  <span class="text-sm">3篇资讯阅读量偏低</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";
</style>

<style scoped>
  :deep(.el-table .published-row) {
    --el-table-tr-bg-color: #f0fdf4;
    border-left: 4px solid #059669;
  }

  :deep(.el-table .pending-row) {
    --el-table-tr-bg-color: #fef3cd;
    border-left: 4px solid #d97706;
  }

  :deep(.el-table .draft-row) {
    --el-table-tr-bg-color: #f8fafc;
    border-left: 4px solid #9ca3af;
  }

  :deep(.el-table .offline-row) {
    --el-table-tr-bg-color: #fef2f2;
    border-left: 4px solid #ef4444;
  }
</style>
