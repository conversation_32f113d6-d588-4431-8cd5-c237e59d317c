<script setup lang="ts">
import { onMounted, ref } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import trainingCurriculum from '@/api/complianceApi/prevention/trainingCurriculum'
// 路由实例
const router = useRouter()
const homeimg = new URL('@/assets/images/homeimg4.jpg', import.meta.url).href

// 课程库

const activeName = ref(1)

function handleClick(tab: TabsPaneContext, event: Event) {
  console.log(tab, event)
}
const tabsList: Array<any> = ref([
  {
    id: 1,
    name: '全部制度 (127)',
  },
  {
    id: 2,
    name: '我创建的 (45)',
  },
  {
    id: 3,
    name: '待我审批 (12)',
  },
  {
    id: 4,
    name: '近期更新 (23)',
  },
])

// 左侧树
interface Tree {
  label: string
  children?: Tree[]
}

function handleNodeClick(data: Tree) {
  console.log(data)
}

const data: Tree[] = [
  {
    label: '发布机构',
    children: [
      {
        label: '银保监会',
      },
      {
        label: '商务部',
      },
      {
        label: '证监会',
      },
    ],
  },
  {
    label: '业务领域',
    children: [
      {
        label: '销售管理',
      },
      {
        label: '财务管理',
      },
      {
        label: '运营管理',
      },
    ],
  },
  {
    label: '制度类型',
    children: [
      {
        label: '规章制度',
      },
      {
        label: '操作规程',
      },
      {
        label: '工作指引',
      },
    ],
  },
]

const defaultProps = {
  children: 'children',
  label: 'label',
}
const paging: any = ref({
  page: 0,
  size: 10,
  limit: 10,
  total: 0,
  courseName: '',
  courseType: '',
  status: '',
  instructor: '',
  searchTerm: '',
})
const dataList: any = ref([])
const loading = ref(false)

const sliderValue: any = ref(40)

// 获取课程列表
const getList = debounce(async () => {
  try {
    loading.value = true
    const queryParams = {
      courseName: paging.value.courseName,
      courseType: paging.value.courseType,
      status: paging.value.status,
      instructor: paging.value.instructor,
      searchTerm: paging.value.searchTerm,
    }
    // 确保分页参数正确传递
    const pagingParams = {
      page: paging.value.page,
      size: paging.value.size,
    }
    console.log('发送请求参数:', { paging: pagingParams, queryParams })
    const res = await trainingCurriculum.system(pagingParams, queryParams)
    console.log('API返回结果:', res)

    // 根据API拦截器逻辑，如果返回的是Object类型，会直接返回data部分
    // 如果是Array类型，会返回完整的response结构
    if (res) {
      if (res.content && Array.isArray(res.content)) {
        // 标准分页结构
        dataList.value = res.content
        paging.value.total = res.totalElements || res.total || 0
        console.log('使用标准分页结构，数据条数:', dataList.value.length, '总数:', paging.value.total)
      }
      else if (Array.isArray(res)) {
        // 直接返回数组
        dataList.value = res
        paging.value.total = res.length
        console.log('使用数组结构，数据条数:', dataList.value.length)
      }
      else if (res.data) {
        // 检查data字段
        if (res.data.content && Array.isArray(res.data.content)) {
          dataList.value = res.data.content
          paging.value.total = res.data.totalElements || res.data.total || 0
          console.log('使用data.content结构，数据条数:', dataList.value.length, '总数:', paging.value.total)
        }
        else if (Array.isArray(res.data)) {
          dataList.value = res.data
          paging.value.total = res.data.length
          console.log('使用data数组结构，数据条数:', dataList.value.length)
        }
        else {
          console.warn('未知的data数据结构:', res.data)
          dataList.value = []
        }
      }
      else {
        console.warn('API返回数据格式异常:', res)
        dataList.value = []
      }
    }
    else {
      console.warn('API返回空结果')
      dataList.value = []
    }

    console.log('最终数据列表:', dataList.value)
  }
  catch (error) {
    console.error('获取课程列表失败', error)
    dataList.value = []
  }
  finally {
    loading.value = false
  }
}, 300)

// 分页变化
function pagChange(val: any) {
  paging.value.page = val.page - 1
  paging.value.size = val.limit
  paging.value.limit = val.limit
  console.log('分页变化:', { page: paging.value.page, size: paging.value.size, limit: paging.value.limit })
  getList()
}

onMounted(() => {
  getList()
})

const yxfwImg = new URL('@/assets/images/icons/yxfw.png', import.meta.url).href
const gjyqImg = new URL('@/assets/images/icons/gjyq.png', import.meta.url).href
const dqjdImg = new URL('@/assets/images/icons/dqjd.png', import.meta.url).href
function goDetail(item: any) {
  router.push({
    name: '/training/curriculum/detail',
    query: { id: item.id },
  })
}
function goAddEdit(item: any) {
  if (item?.id) {
    // 编辑课程
    router.push({
      name: '/training/curriculum/edit',
      query: { id: item.id },
    })
  }
  else {
    // 新增课程
    router.push({
      name: '/training/curriculum/edit',
    })
  }
}
</script>

<template>
  <div class="absolute-container">
    <page-header>
      <template #content>
        <div class="aic jcsb flex">
          <div class="f-20 c-['#000000']">
            合规培训课程库
          </div>
          <div class="aic flex">
            <div>
              <el-button type="primary" @click="goAddEdit(null)">
                <svg-icon name="ep:plus" />
                <span class="ml-4">新增课程</span>
              </el-button>
            </div>
            <div class="ml-14">
              <el-button type="primary" plain>
                <svg-icon name="ep:download" />
                <span class="ml-4">我的学习</span>
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </page-header>
    <!-- 培训课程库 -->
    <PageMain style="background-color: transparent;">
      <div class="card flex p-16">
        <!-- <div>
          <el-button type="primary" @click="goAddEdit(null)">
            <svg-icon name="ep:plus" />
            <span class="ml-4">新增案例</span>
          </el-button>
        </div>
        <div class="ml-14">
          <el-button type="primary" plain>
            <svg-icon name="ep:download" />
            <span class="ml-4">批量导出</span>
          </el-button>
        </div> -->
        <!-- <div class="ml-32"> -->
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="名称:">
            <el-input v-model="paging.courseName" clearable placeholder="搜索课程名称..." @clear="paging.courseName = ''" />
          </el-form-item>
          <!-- <el-form-item label="发布机构:">
            <el-cascader v-model="paging.group_id" :props="props1" clearable class="w-full" :options="groupList"
              @change="changegroup" @clear="paging.group_id = null" />
          </el-form-item> -->
          <el-form-item label="课程类型" style="width: 260px;">
            <el-select v-model="paging.courseType" clearable placeholder="请选择课程类型" @clear="paging.courseType = ''">
              <el-option label="法律法规" value="法律法规" />
              <el-option label="合规知识" value="合规知识" />
              <el-option label="案例分析" value="案例分析" />
              <el-option label="技能培训" value="技能培训" />
            </el-select>
          </el-form-item>
          <el-form-item label="课程状态" style="width: 260px;">
            <el-select v-model="paging.status" clearable placeholder="请选择课程状态" @clear="paging.status = ''">
              <el-option label="已发布" value="已发布" />
              <el-option label="草稿" value="草稿" />
              <el-option label="已下架" value="已下架" />
            </el-select>
          </el-form-item>
          <el-form-item label="讲师" style="width: 260px;">
            <el-input v-model="paging.instructor" clearable placeholder="请输入讲师姓名" @clear="paging.instructor = ''" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getList()">
              <template #icon>
                <svg-icon name="ep:search" />
              </template>
              查询
            </el-button>
            <el-button
              @click="paging = {
                page: 0,
                size: 10,
                limit: 10,
                total: 0,
                courseName: '',
                courseType: '',
                status: '',
                instructor: '',
                searchTerm: '',
              }, getList()"
            >
              重置
            </el-button>
            <!-- <el-button>
              <svg-icon name="ep:filter" />
              <span class="ml-4">高级筛选</span>
            </el-button> -->
            <!-- <el-button type="danger" @click="removealldata()">
                <template #icon>
                  <svg-icon name="ep:search" />
                </template>
                批量删除
              </el-button> -->
          </el-form-item>
        </el-form>
        <!-- </div> -->
      </div>
      <!--      <div>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="i,j in tabsList" :key="j" :label="i.name" :name="i.id"></el-tab-pane>
        </el-tabs>
      </div> -->
      <div class="conBox mt-20 pr-20">
        <!--        <LayoutContainer style="padding:0;" :enable-left-side="true" :enable-right-side="true" :left-side-width="230"
          :right-side-width="476">
          <template #leftSide>
            <div class="flex aic jcsb">
              <div class="f-16 f-500">
                筛选条件
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree :data="data" :props="defaultProps" :default-expand-all="true" @node-click="handleNodeClick" />
            </div>
          </template>
          <div> -->
        <!-- <page-header title="卡片列表" content="卡片类型的列表，配合栅格实现响应式布局。" /> -->
        <el-row v-loading="loading" :gutter="20" style="">
          <el-col
            v-for="(item, index) in dataList" :key="index" class="mb-24" :lg="6" :md="8" :sm="12"
            @click="goDetail(item)"
          >
            <el-card shadow="hover" class="action-card">
              <div style="width: 100%;height: 160px;">
                <img
                  style="width: 100%;height: 100%;border-radius: 8px 8px 0 0;"
                  src="https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg" alt=""
                >
              </div>
              <div class="pl-24 pr-24 pt-24">
                <div class="f-16">
                  {{ item.courseName || '未命名课程' }}
                </div>
                <div class="mt-10">
                  <el-tag>{{ item.courseType || '未分类' }}</el-tag>
                </div>
                <div class="f-14 mt-8">
                  时长：{{ item.durationMinutes || 0 }}分钟
                </div>
                <div class="f-14 mt-4">
                  培训对象：{{ item.applicableRole || '未指定' }}
                </div>
                <div class="f-14 mt-4">
                  更新日期：{{ item.lastUpdate ? new Date(item.lastUpdate.seconds * 1000).toLocaleDateString() : '未更新' }}
                </div>
                <div class="aic mt-10 flex">
                  <div class="flex-1">
                    <el-progress :percentage="50" :show-text="false" />
                  </div>
                  <div class="f-14 ml-10">
                    120/150
                  </div>
                </div>
                <div class="aic jcsb mt-16 flex">
                  <el-text type="primary">
                    查看详情
                  </el-text>
                </div>
              </div>
              <!-- <div class="flex aic jcsb">
                <div class="c-[#fff] f-12" style="padding: 4px 8px;width: fit-content;border-radius: 30px;" :style="{
                      'background-color': ['','#FDC929','#F57C00','#E83335'][3]
                    }">
                  {{['','一般风险','典型风险','重大风险'][3]}}
                </div>
                <div class="f-14 fw-400">2024-01-15</div>
              </div>
              <div class="f-16 fw-600 mt-10">
                某银行员工违规操作客户资金案例
              </div>
              <div class="f-14 fw-400 mt-8 c-[#666]">
                银行从业人员利用职务便利，违规挪用客户资金进行个人投资，造成重大损失。
              </div>
              <div class="">
                <div class="c-[#2563EB] f-12 mt-14" style="padding: 4px 8px;width: fit-content;border-radius: 4px;"
                  :style="{
                      'background-color': '#EFF6FF'
                    }">
                  资金管理
                </div>
              </div>
              <div class="mt-14 mb-12" style="border-top: 1px solid var(--el-border-color);">

              </div>
              <div class="flex aic jcsb">
                <div class="c-[#999] flex aic">
                  <div class="flex aic">
                    <svg-icon name="ep:view" />
                    <span class="ml-2 f-14">123</span>
                  </div>
                  <div class="ml-16 flex aic">
                    <svg-icon name="ep:comment" />
                    <span class="ml-2 f-14">12</span>
                  </div>
                </div>
                <div class="c-[#999] flex aic">
                  <div>
                    <svg-icon name="ep:edit" />
                  </div>
                  <div class="ml-16">
                    <svg-icon name="ep:share" />
                  </div>
                  <div class="ml-16">
                    <svg-icon name="ep:star" />
                  </div>
                </div>
              </div> -->

              <!-- <div class="content">
                    <div class="item">
                      <el-avatar size="default">
                        <svg-icon name="ep:user-filled" />
                      </el-avatar>
                    </div>
                    <div class="item">
                      <div class="name">
                        Hooray
                      </div>
                      <div class="intro">
                        前端开发工程师，10年+开发经验，可开发 Web / H5 / 小程序 等应用。前端开发工程师，10年+开发经验，可开发 Web / H5 / 小程序 等应用
                      </div>
                    </div>
                  </div>
                  <div class="action-bar">
                    <el-button text>
                      操作一
                    </el-button>
                    <el-button text>
                      操作二
                    </el-button>
                  </div> -->
            </el-card>
          </el-col>
        </el-row>
        <page-compon
          :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
          @pag-change="pagChange"
        />
        <!-- <centerContent></centerContent> -->
        <!-- </div>
        </LayoutContainer>
      -->
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }

  .conBox {
    :deep(.main) {
      background-color: transparent;

      .main-container {
        padding: 0 !important;
      }

      .el-slider__button-wrapper {
        display: none;
      }

      .el-slider__runway.is-disabled .el-slider__bar {
        height: 8px;
        background: #4caf50;
        border-radius: 9999px;
      }
    }

    :deep(.flex-container) {
      padding-right: 40px !important;
    }
  }
</style>
