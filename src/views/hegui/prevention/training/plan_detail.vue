<script setup lang="ts">
import * as Echarts from 'echarts'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

onMounted(() => {

})
// 路由实例
const router = useRouter()
const form = ref({})

const responseMeasures = ref(false)
const rateNum = ref(3.5)

const activeName = ref(1)

function goEdit(item: any) {
  console.log(item)
}
onMounted(() => {
  // initChart2()
  // initChart3()
  initChart11()
  initChart22()
  initChart33()
})

const chart2Ref = ref(null)
const chart3Ref = ref(null)
const chart11Ref = ref(null)
const chart22Ref = ref(null)
const chart33Ref = ref(null)
function initChart2() {
  const chart1 = Echarts.init(chart2Ref.value)
  // 配置数据
  const option = {
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    color: ['#1677FF', '#fff'],
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 55 },
          { value: 45 },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
      {
        type: 'gauge',
        radius: '40%',
        startAngle: 90,
        endAngle: -270,
        axisLine: {
          lineStyle: {
            width: 10,
            color: [[1, '#E4E4E4']], // 背景颜色
          },
        },
        pointer: {
          length: '70%',
          width: 5,
          itemStyle: {
            color: '#1677FF', // 指针颜色
          },
        },
        detail: {
          formatter: '{value}%', // 显示百分比
          fontSize: 20,
          color: 'black',
        },
        data: [
          {
            value: 45,
            name: 'Progress',
          },
        ],
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
function initChart11() {
  const chart1 = Echarts.init(chart11Ref.value)
  // 配置数据
  const option = {
    xAxis: {
      type: 'category',
      data: ['技术部', '运营部', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [120, 200, 150, 80, 70, 110, 130],
        type: 'bar',
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
function initChart22() {
  const chart1 = Echarts.init(chart22Ref.value)
  // 配置数据
  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      bottom: '5%',
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '60%'],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          borderRadius: 5,
        },
        data: [
          { value: 1048, name: '已完成' },
          { value: 735, name: '进行中' },
          { value: 580, name: '未开始' },
        ],
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
function initChart33() {
  const chart1 = Echarts.init(chart33Ref.value)
  // 配置数据
  const option = {
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [10, 20, 30, 20, 40, 20, 30],
        type: 'line',
        smooth: true,
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
</script>

<template>
  <div>
    <!-- 培训计划 -->
    <el-form ref="formRef" :rules="formRules" :model="form" label-width="110px" label-position="left">
      <page-header title="" content="">
        <template #content>
          <div class="aic jcsb flex">
            <div class="f-28">
              <span class="mr-10 c-[#000]">2024年第一季度合规培训计划</span>
              <el-tag class="ml-4">
                进行中
              </el-tag>
              <!-- <el-tag class="ml-2" type="success">Tag 2</el-tag> -->
              <!-- <el-tag class="ml-2" type="info">Tag 3</el-tag> -->
              <!-- <el-tag class="ml-2" type="warning">Tag 4</el-tag> -->
              <!-- <el-tag class="ml-2" type="danger">Tag 5</el-tag> -->
            </div>
            <div>
              <div class="aic flex">
                <div>
                  <el-button type="primary" @click="goAddEdit(null)">
                    <svg-icon name="ep:plus" />
                    <span class="ml-4">编辑</span>
                  </el-button>
                </div>
                <div class="ml-14">
                  <el-button type="primary" plain>
                    <svg-icon name="ep:download" />
                    <span class="ml-4">编辑课程</span>
                  </el-button>
                </div>
                <div class="ml-14">
                  <el-button type="primary" plain>
                    <svg-icon name="ep:download" />
                    <span class="ml-4">删除课程</span>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </page-header>
      <page-main style="background-color: transparent;">
        <el-row>
          <el-col :span="16" class="pr-10">
            <el-card shadow="hover">
              <div class="flex">
                <!--                <div style="width: 200px;height: 112px;">
                  <img style="width: 100%;height: 100%;border-radius: 8px 8px;"
                    src="https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg" alt="" />
                </div> -->
                <div class="f-14 ml-20 flex-1">
                  <el-row>
                    <el-col :span="12">
                      <div>
                        <span class="c-[#666666]">计划名称：</span>
                        <span class="c-[#000]">2024年第一季度合规培训计划</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">创建人：</span>
                        <span class="c-[#000]">王明</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">培训对象：</span>
                        <span class="c-[#000]">全体员工</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">包含课程数：</span>
                        <span class="c-[#000]">5</span>
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div>
                        <span class="c-[#666666]">创建人：</span>
                        <span class="c-[#000]">王明</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">结束时间：</span>
                        <span class="c-[#000]">2024-02-15 18:00</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">创建日期：</span>
                        <span class="c-[#000]">2023-12-20</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">计划状态：</span>
                        <span class="c-[#000]">进行中分</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <el-row style="height: 160px;background-color: #e5e7eb;">
                <el-col :span="6" class="aic jcc flex">
                  <div class="fdc aic jcc flex">
                    <!-- <div ref="chart2Ref" style="width: 100%;height: 180px;" /> -->
                    <el-progress stroke-width="20" type="circle" :percentage="90" />
                  </div>
                </el-col>
                <el-col :span="6" class="aic jcc flex">
                  <div class="fdc aic jcc flex">
                    <div class="f-24">
                      120/150
                    </div>
                    <div class="f-14 c-[#666666]">
                      参与人数
                    </div>
                  </div>
                </el-col>
                <el-col :span="6" class="aic jcc flex">
                  <div class="fdc aic jcc flex">
                    <div class="f-24">
                      3.5h
                    </div>
                    <div class="f-14 c-[#666666]">
                      平均学习时长
                    </div>
                  </div>
                </el-col>
                <el-col :span="6" class="aic jcc flex">
                  <div class="fdc aic jcc flex">
                    <div class="f-24">
                      85分
                    </div>
                    <div class="f-14 c-[#666666]">
                      平均得分
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <div ref="chart11Ref" style="width: 100%;height: 240px;" />
                </el-col>
                <el-col :span="8">
                  <div ref="chart22Ref" style="width: 100%;height: 240px;" />
                </el-col>
                <el-col :span="8">
                  <div ref="chart33Ref" style="width: 100%;height: 240px;" />
                </el-col>
              </el-row>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
                <el-tab-pane label="课程列表" :name="1">
                  课程列表
                </el-tab-pane>
                <el-tab-pane label="学员情况" :name="2">
                  学员情况
                </el-tab-pane>
                <el-tab-pane label="培训公告" :name="3">
                  培训公告
                </el-tab-pane>
                <el-tab-pane label="培训报告" :name="4">
                  培训报告
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="8" class="pl-10">
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  关键时间点
                </div>
              </template>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 aic flex fw-500">
                  <div style="width: 10px;height: 10px;background-color: #1677ff;border-radius: 50%;" />
                  <span class="ml-8 c-[#111827]">2024-01-01</span>
                </div>
                <div class="f-16 aic mt-8 flex pl-16 fw-500">
                  <span class="c-[#666]">培训计划开始</span>
                </div>
                <!-- <div class="mt-8"> -->
                <!-- <el-tag>Tag 1</el-tag> -->
                <!-- <el-tag class="ml-2" type="success">已完成</el-tag> -->
                <!-- <el-tag class="ml-2" type="info">Tag 3</el-tag> -->
                <!-- <el-tag class="ml-2" type="warning">Tag 4</el-tag> -->
                <!-- <el-tag class="ml-2" type="danger">Tag 5</el-tag> -->
                <!-- </div> -->
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 aic flex fw-500">
                  <span class="c-[#111827]">2023年第四季度合规培训计划</span>
                </div>
                <div class="f-16 aic mt-8 flex fw-500">
                  <span class="c-[#666]">2023-10-01至2023-12-31</span>
                </div>
                <!-- <div class="mt-8">
                  <el-tag class="ml-2" type="info">未开始</el-tag>
                </div> -->
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  培训管理员
                </div>
              </template>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="flex">
                  <div class="ml-8" style="width: 32px;height: 32px;">
                    <img
                      style="width: 100%;height: 100%;border-radius: 50%;"
                      src="https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg" alt=""
                    >
                  </div>
                  <div class="ml-8">
                    <div class="f-14">
                      王管理
                    </div>
                    <div class="f-14 c-[#666666]">
                      负责人 | 培训管理部
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="flex">
                  <div class="ml-8" style="width: 32px;height: 32px;">
                    <img
                      style="width: 100%;height: 100%;border-radius: 50%;"
                      src="https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg" alt=""
                    >
                  </div>
                  <div class="ml-8">
                    <div class="f-14">
                      王管理
                    </div>
                    <div class="f-14 c-[#666666]">
                      负责人 | 培训管理部
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <!-- <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">相关课程</div>
              </template>
              <div class="mt-16"
                style="flex: 1;padding: 17px;background: rgba(0, 0, 0, 0.00);border: 1px solid #E5E7EB;border-radius: 4px;">
                <div class="f-16 fw-500 flex aic ">
                  <span class="c-[#111827]">反洗钱合规培训</span>
                </div>
                <div class="f-16 fw-500 flex aic mt-8">
                  <span class="c-[#666] ">类型：在线课程</span>
                </div>
                <div class="f-16 fw-500 flex aic mt-8">
                  时长：120分钟
                </div>
                <div class="mt-8">
                  <el-text type="primary">查看课程</el-text>
                </div>
              </div>
            </el-card> -->
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关计划
                </div>
              </template>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 aic flex fw-500">
                  <span class="c-[#111827]">2023年第四季度合规培训计划</span>
                </div>
                <div class="f-16 aic mt-8 flex fw-500">
                  <span class="c-[#666]">2023-10-01至2023-12-31</span>
                </div>
                <!-- <div class="mt-8"> -->
                <!-- <el-tag>Tag 1</el-tag> -->
                <!-- <el-tag class="ml-2" type="success">已完成</el-tag> -->
                <!-- <el-tag class="ml-2" type="info">Tag 3</el-tag> -->
                <!-- <el-tag class="ml-2" type="warning">Tag 4</el-tag> -->
                <!-- <el-tag class="ml-2" type="danger">Tag 5</el-tag> -->
                <!-- </div> -->
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 aic flex fw-500">
                  <span class="c-[#111827]">2023年第四季度合规培训计划</span>
                </div>
                <div class="f-16 aic mt-8 flex fw-500">
                  <span class="c-[#666]">2023-10-01至2023-12-31</span>
                </div>
                <!-- <div class="mt-8">
                  <el-tag class="ml-2" type="info">未开始</el-tag>
                </div> -->
              </div>
            </el-card>
          </el-col>
        </el-row>
      </page-main>
    </el-form>

    <HDialog v-model="responseMeasures" :title="form.id ? '创建应对措施' : '创建应对措施'" modulewidth="50vw">
      <el-form ref="formRef" :model="form" class="px-2" :rules="formRules" label-position="top" label-width="66px">
        <div class="mb-10">
          <el-button color="#1677FF">
            AI辅助
          </el-button>
        </div>
        <el-form-item label="措施名称：" prop="name">
          <el-input v-model="form.name" size="large" placeholder="请输入对应措施名称" clearable />
        </el-form-item>
        <el-form-item label="措施描述:" prop="name">
          <el-select v-model="value" class="m-2" placeholder="请选择措施描述">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="措施类型:" prop="name">
          <el-select v-model="value" class="m-2" placeholder="请选择措施类型">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="责任部门:" prop="name">
          <el-select v-model="value" class="m-2" placeholder="请选择责任部门">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="完成期限：" prop="name" label-width="100">
          <el-date-picker
            v-model="form.send_time" value-format="x" style="width: 100%;" type="date"
            placeholder="请选择完成期限"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="responseMeasures = false, form = {} ">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
