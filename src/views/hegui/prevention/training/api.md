---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 18-合规培训服务/培训计划管理

## POST 多条件分页查询培训计划

POST /api/training/plans/query

根据条件查询培训计划（多条件分页查询）
{@code POST  /training/plans/query} : query trainingPlans by criteria.
多条件分页查询培训计划
根据指定条件进行分页查询培训计划，支持多种筛选条件和排序

> Body 请求参数

```json
{
  "planName": "string",
  "planType": "string",
  "planStatus": "string",
  "responsiblePerson": "string",
  "trainingTarget": "string",
  "creatorType": "string",
  "priority": "string",
  "startDateFrom": {
    "seconds": 0,
    "nanos": 0
  },
  "startDateTo": {
    "seconds": 0,
    "nanos": 0
  },
  "endDateFrom": {
    "seconds": 0,
    "nanos": 0
  },
  "endDateTo": {
    "seconds": 0,
    "nanos": 0
  },
  "createdAtFrom": {
    "seconds": 0,
    "nanos": 0
  },
  "createdAtTo": {
    "seconds": 0,
    "nanos": 0
  },
  "createdBy": "string",
  "searchTerm": "string",
  "includeCourses": false,
  "page": 0,
  "size": 0,
  "sort": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[TrainingPlanCriteria](#schematrainingplancriteria)| 否 |none|

> 返回示例

> 500 Response

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "planCode": "",
      "planName": "",
      "planDescription": "",
      "planType": "",
      "planStatus": "",
      "startDate": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "endDate": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "trainingTarget": "",
      "responsiblePerson": "",
      "trainingObjective": "",
      "implementationPlan": "",
      "resourceRequirements": "",
      "riskManagement": "",
      "targetCompletionRate": 0,
      "creatorType": "",
      "priority": "",
      "coverImageUrl": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false,
      "trainingPlanCourses": [
        {
          "id": 0,
          "tenantId": 0,
          "trainingPlanId": 0,
          "courseId": 0,
          "courseName": "",
          "courseType": "",
          "courseDurationMinutes": 0,
          "courseOrder": 0,
          "isRequired": false,
          "estimatedDays": 0,
          "startDate": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "endDate": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "assessmentRequirement": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false,
          "trainingPlan": {
            "id": 0,
            "tenantId": 0,
            "planCode": "",
            "planName": "",
            "planDescription": "",
            "planType": "",
            "planStatus": "",
            "startDate": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "endDate": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "trainingTarget": "",
            "responsiblePerson": "",
            "trainingObjective": "",
            "implementationPlan": "",
            "resourceRequirements": "",
            "riskManagement": "",
            "targetCompletionRate": 0,
            "creatorType": "",
            "priority": "",
            "coverImageUrl": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "isDeleted": false,
            "trainingPlanCourses": [
              {
                "id": 0,
                "tenantId": 0,
                "trainingPlanId": 0,
                "courseId": 0,
                "courseName": "",
                "courseType": "",
                "courseDurationMinutes": 0,
                "courseOrder": 0,
                "isRequired": false,
                "estimatedDays": 0,
                "startDate": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "endDate": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "assessmentRequirement": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "isDeleted": false,
                "trainingPlan": {}
              }
            ]
          }
        }
      ]
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "empty": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|none|[ResponseEntityPageTrainingPlanDTO](#schemaresponseentitypagetrainingplandto)|

# 数据模型

<h2 id="tocS_TrainingPlanDTO">TrainingPlanDTO</h2>

<a id="schematrainingplandto"></a>
<a id="schema_TrainingPlanDTO"></a>
<a id="tocStrainingplandto"></a>
<a id="tocstrainingplandto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "planCode": "PLAN20240115001",
  "planName": "string",
  "planDescription": "string",
  "planType": "string",
  "planStatus": "DRAFT",
  "startDate": {
    "seconds": 0,
    "nanos": 0
  },
  "endDate": {
    "seconds": 0,
    "nanos": 0
  },
  "trainingTarget": "string",
  "responsiblePerson": "string",
  "trainingObjective": "string",
  "implementationPlan": "string",
  "resourceRequirements": "string",
  "riskManagement": "string",
  "targetCompletionRate": 0,
  "creatorType": "string",
  "priority": "string",
  "coverImageUrl": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "trainingPlanCourses": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|false|none||租户ID|
|planCode|string|false|none||计划编号|
|planName|string|true|none||培训计划名称|
|planDescription|string|false|none||培训计划描述|
|planType|string|true|none||计划类型（个人计划、部门计划、公司计划）|
|planStatus|string|true|none||计划状态（草稿、待启动、进行中、已完成、已取消）|
|startDate|[Instant](#schemainstant)|false|none||开始时间|
|endDate|[Instant](#schemainstant)|false|none||结束时间|
|trainingTarget|string|false|none||培训对象|
|responsiblePerson|string|false|none||负责人|
|trainingObjective|string|false|none||培训目标|
|implementationPlan|string|false|none||实施方案|
|resourceRequirements|string|false|none||资源需求|
|riskManagement|string|false|none||风险管理|
|targetCompletionRate|number|false|none||目标完成率|
|creatorType|string|false|none||创建者类型（系统推荐、管理员创建、用户自定义）|
|priority|string|false|none||优先级（高、中、低）|
|coverImageUrl|string|false|none||封面图片URL|
|metadata|string|false|none||补充字段|
|version|integer|false|none||版本号|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||是否删除|
|trainingPlanCourses|[[TrainingPlanCourseDTO](#schematrainingplancoursedto)]|false|none||培训计划包含的课程列表|

#### 枚举值

|属性|值|
|---|---|
|planStatus|DRAFT|
|planStatus|PENDING|
|planStatus|IN_PROGRESS|
|planStatus|COMPLETED|
|planStatus|CANCELLED|

<h2 id="tocS_TrainingPlanCourseDTO">TrainingPlanCourseDTO</h2>

<a id="schematrainingplancoursedto"></a>
<a id="schema_TrainingPlanCourseDTO"></a>
<a id="tocStrainingplancoursedto"></a>
<a id="tocstrainingplancoursedto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "trainingPlanId": 0,
  "courseId": 0,
  "courseName": "string",
  "courseType": "string",
  "courseDurationMinutes": 0,
  "courseOrder": 0,
  "isRequired": true,
  "estimatedDays": 0,
  "startDate": {
    "seconds": 0,
    "nanos": 0
  },
  "endDate": {
    "seconds": 0,
    "nanos": 0
  },
  "assessmentRequirement": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "trainingPlan": {
    "id": 0,
    "tenantId": 0,
    "planCode": "PLAN20240115001",
    "planName": "string",
    "planDescription": "string",
    "planType": "string",
    "planStatus": "DRAFT",
    "startDate": {
      "seconds": 0,
      "nanos": 0
    },
    "endDate": {
      "seconds": 0,
      "nanos": 0
    },
    "trainingTarget": "string",
    "responsiblePerson": "string",
    "trainingObjective": "string",
    "implementationPlan": "string",
    "resourceRequirements": "string",
    "riskManagement": "string",
    "targetCompletionRate": 0,
    "creatorType": "string",
    "priority": "string",
    "coverImageUrl": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "trainingPlanCourses": "new ArrayList<>()"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|false|none||租户ID|
|trainingPlanId|integer(int64)|true|none||培训计划ID|
|courseId|integer(int64)|true|none||课程ID|
|courseName|string|false|none||课程名称（冗余字段，便于显示）|
|courseType|string|false|none||课程类型（冗余字段，便于显示）|
|courseDurationMinutes|integer|false|none||课程时长（分钟）（冗余字段，便于显示）|
|courseOrder|integer|true|none||课程在计划中的顺序|
|isRequired|boolean|false|none||是否必修|
|estimatedDays|integer|false|none||预计完成时间（天）|
|startDate|[Instant](#schemainstant)|false|none||开始日期|
|endDate|[Instant](#schemainstant)|false|none||截止日期|
|assessmentRequirement|string|false|none||考核要求|
|metadata|string|false|none||补充字段|
|version|integer|false|none||版本号|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||是否删除|
|trainingPlan|[TrainingPlanDTO](#schematrainingplandto)|false|none||none|

<h2 id="tocS_TrainingPlanCriteria">TrainingPlanCriteria</h2>

<a id="schematrainingplancriteria"></a>
<a id="schema_TrainingPlanCriteria"></a>
<a id="tocStrainingplancriteria"></a>
<a id="tocstrainingplancriteria"></a>

```json
{
  "planName": "string",
  "planType": "string",
  "planStatus": "string",
  "responsiblePerson": "string",
  "trainingTarget": "string",
  "creatorType": "string",
  "priority": "string",
  "startDateFrom": {
    "seconds": 0,
    "nanos": 0
  },
  "startDateTo": {
    "seconds": 0,
    "nanos": 0
  },
  "endDateFrom": {
    "seconds": 0,
    "nanos": 0
  },
  "endDateTo": {
    "seconds": 0,
    "nanos": 0
  },
  "createdAtFrom": {
    "seconds": 0,
    "nanos": 0
  },
  "createdAtTo": {
    "seconds": 0,
    "nanos": 0
  },
  "createdBy": "string",
  "searchTerm": "string",
  "includeCourses": false,
  "page": 0,
  "size": 0,
  "sort": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|planName|string|false|none||培训计划名称（模糊查询）|
|planType|string|false|none||计划类型|
|planStatus|string|false|none||计划状态|
|responsiblePerson|string|false|none||负责人（模糊查询）|
|trainingTarget|string|false|none||培训对象（模糊查询）|
|creatorType|string|false|none||创建者类型|
|priority|string|false|none||优先级|
|startDateFrom|[Instant](#schemainstant)|false|none||开始时间-起始|
|startDateTo|[Instant](#schemainstant)|false|none||开始时间-结束|
|endDateFrom|[Instant](#schemainstant)|false|none||结束时间-起始|
|endDateTo|[Instant](#schemainstant)|false|none||结束时间-结束|
|createdAtFrom|[Instant](#schemainstant)|false|none||创建时间-起始|
|createdAtTo|[Instant](#schemainstant)|false|none||创建时间-结束|
|createdBy|string|false|none||创建者|
|searchTerm|string|false|none||通用搜索词（在多个字段中进行模糊搜索）|
|includeCourses|boolean|false|none||是否包含课程信息|
|page|integer|false|none||页码（从0开始）|
|size|integer|false|none||每页记录数|
|sort|string|false|none||排序字段和方向，格式为：property,direction，例如：planName,asc|

<h2 id="tocS_ResponseEntityPageTrainingPlanDTO">ResponseEntityPageTrainingPlanDTO</h2>

<a id="schemaresponseentitypagetrainingplandto"></a>
<a id="schema_ResponseEntityPageTrainingPlanDTO"></a>
<a id="tocSresponseentitypagetrainingplandto"></a>
<a id="tocsresponseentitypagetrainingplandto"></a>

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "planCode": "PLAN20240115001",
      "planName": "string",
      "planDescription": "string",
      "planType": "string",
      "planStatus": "DRAFT",
      "startDate": {
        "seconds": 0,
        "nanos": 0
      },
      "endDate": {
        "seconds": 0,
        "nanos": 0
      },
      "trainingTarget": "string",
      "responsiblePerson": "string",
      "trainingObjective": "string",
      "implementationPlan": "string",
      "resourceRequirements": "string",
      "riskManagement": "string",
      "targetCompletionRate": 0,
      "creatorType": "string",
      "priority": "string",
      "coverImageUrl": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "trainingPlanCourses": "new ArrayList<>()"
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "empty": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|content|[[TrainingPlanDTO](#schematrainingplandto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE",
  "ascending": true,
  "descending": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|
|ascending|boolean|false|none||Returns whether sorting for this property shall be ascending.|
|descending|boolean|false|none||Returns whether sorting for this property shall be descending.|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

