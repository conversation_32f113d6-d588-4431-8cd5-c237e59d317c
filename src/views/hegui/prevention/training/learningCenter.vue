<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
// 路由实例
const router = useRouter()
const itemsPerPage = 4
const stats = ref([
  { value: '12', label: '已完成课程' },
  { value: '5', label: '学习进行中' },
  { value: '8', label: '获得证书' },
  { value: '36', label: '学习总时长' },
])
const tasks = ref([
  { id: 1, name: 'Python 数据分析基础', deadline: '2023-12-15', progress: 65, priority: '高' },
  { id: 2, name: 'Vue 3 高级开发实战', deadline: '2023-12-20', progress: 30, priority: '中' },
  { id: 3, name: '产品设计思维训练', deadline: '2023-12-25', progress: 15, priority: '低' },
  { id: 4, name: '机器学习入门', deadline: '2023-12-10', progress: 80, priority: '高' },
  { id: 5, name: 'React 高级模式', deadline: '2023-12-18', progress: 45, priority: '中' },
  { id: 6, name: 'Node.js 实战', deadline: '2023-12-22', progress: 70, priority: '高' },
  { id: 7, name: '数据库设计原理', deadline: '2023-12-28', progress: 25, priority: '低' },
  { id: 8, name: '数据结构与算法', deadline: '2023-12-12', progress: 90, priority: '高' },
])
const taskCurrentPage = ref(1)
const taskTotalPages = computed(() => Math.ceil(tasks.value.length / itemsPerPage))
const paginatedTasks = computed(() => {
  const start = (taskCurrentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return tasks.value.slice(start, end)
})
function prevTaskPage() {
  if (taskCurrentPage.value > 1) {
    taskCurrentPage.value--
  }
}
function nextTaskPage() {
  if (taskCurrentPage.value < taskTotalPages.value) {
    taskCurrentPage.value++
  }
}
// 学习详情
function goDetail(row) {
  if (!row || !row.id) {
    console.warn('无效的数据对象或缺少ID')
    return
  }
  router.push({
    name: '/training/learningCenter/detail',
    query: { id: row.id },
  })
}
const courses = ref([
  {
    id: 1,
    name: 'React 18 新特性详解',
    type: '前端开发',
    duration: '8小时',
    reason: '基于您的学习历史推荐',
    cover: 'https://ai-public.mastergo.com/ai/img_res/950e2bffd0faa178e74e4bc91bfaae1e.jpg',
  },
  {
    id: 2,
    name: 'TypeScript 高级类型系统',
    type: '前端开发',
    duration: '6小时',
    reason: '热门课程',
    cover: 'https://ai-public.mastergo.com/ai/img_res/da4dcfb33d20696f98c0e0bf31859b5c.jpg',
  },
  {
    id: 3,
    name: 'UI/UX 设计基础',
    type: '设计',
    duration: '10小时',
    reason: '跨领域学习推荐',
    cover: 'https://ai-public.mastergo.com/ai/img_res/50bb6044781bb5fe6f29a6c34e93e85c.jpg',
  },
  {
    id: 4,
    name: 'Node.js 后端开发',
    type: '后端开发',
    duration: '12小时',
    reason: '技能拓展推荐',
    cover: 'https://ai-public.mastergo.com/ai/img_res/83a8506612c267415729aabc663e0e1b.jpg',
  },
  {
    id: 5,
    name: 'Flutter 跨平台开发',
    type: '移动开发',
    duration: '9小时',
    reason: '新技术趋势',
    cover: 'https://ai-public.mastergo.com/ai/img_res/988e20a54d734bd14ee22577b79b4dd0.jpg',
  },
  {
    id: 6,
    name: 'Docker 容器化实践',
    type: '运维',
    duration: '7小时',
    reason: '热门技术',
    cover: 'https://ai-public.mastergo.com/ai/img_res/6eee88b0cc2246bcf031aeda5fc8148d.jpg',
  },
  {
    id: 7,
    name: '人工智能基础',
    type: 'AI',
    duration: '15小时',
    reason: '前沿技术',
    cover: 'https://ai-public.mastergo.com/ai/img_res/043ba2f60982ba70f13c7a146aa4743c.jpg',
  },
  {
    id: 8,
    name: '网络安全入门',
    type: '安全',
    duration: '6小时',
    reason: '实用技能',
    cover: 'https://ai-public.mastergo.com/ai/img_res/f1a074467c9385f084a555a0f10e53b3.jpg',
  },
])
// 移除推荐课程的分页相关代码
const records = ref([
  { id: 1, time: '12月5日 14:30', course: 'Python 数据分析基础', progress: 65 },
  { id: 2, time: '12月4日 09:15', course: 'Vue 3 高级开发实战', progress: 30 },
  { id: 3, time: '12月3日 16:45', course: '产品设计思维训练', progress: 15 },
  { id: 4, time: '12月2日 19:20', course: '机器学习入门', progress: 80 },
  { id: 5, time: '12月1日 10:10', course: 'React 高级模式', progress: 45 },
  { id: 6, time: '11月30日 15:30', course: 'Node.js 实战', progress: 70 },
  { id: 7, time: '11月29日 08:45', course: '数据库设计原理', progress: 25 },
  { id: 8, time: '11月28日 20:15', course: '数据结构与算法', progress: 90 },
])
const recordCurrentPage = ref(1)
const recordTotalPages = computed(() => Math.ceil(records.value.length / itemsPerPage))
const paginatedRecords = computed(() => {
  const start = (recordCurrentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return records.value.slice(start, end)
})
function prevRecordPage() {
  if (recordCurrentPage.value > 1) {
    recordCurrentPage.value--
  }
}
function nextRecordPage() {
  if (recordCurrentPage.value < recordTotalPages.value) {
    recordCurrentPage.value++
  }
}
function isDeadlineApproaching(deadline: string) {
  const today = new Date()
  const deadlineDate = new Date(deadline)
  const diffTime = deadlineDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 3
}
function getPriorityColor(priority: string) {
  switch (priority) {
    case '高': return 'bg-red-500'
    case '中': return 'bg-orange-500'
    case '低': return 'bg-blue-500'
    default: return 'bg-gray-500'
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <!-- 个人学习统计区 -->
    <div class="grid grid-cols-4 mb-8 gap-6">
      <div v-for="(stat, index) in stats" :key="index" class="rounded-lg bg-white p-6 shadow-sm">
        <div class="mb-2 text-3xl text-blue-600 font-bold">
          {{ stat.value }}
        </div>
        <div class="text-gray-500">
          {{ stat.label }}
        </div>
      </div>
    </div>
    <!-- 学习任务区 -->
    <div class="mb-8 rounded-lg bg-white p-6 shadow-sm">
      <div class="mb-6 flex items-center justify-between">
        <h2 class="text-lg font-bold">
          我的学习任务
        </h2>
        <div class="flex items-center space-x-2">
          <button :disabled="taskCurrentPage === 1" class="!rounded-button whitespace-nowrap border px-3 py-1 text-sm hover:bg-gray-50" :class="{ 'opacity-50 cursor-not-allowed': taskCurrentPage === 1 }" @click="prevTaskPage">
            上一页
          </button>
          <span class="text-sm text-gray-600">{{ taskCurrentPage }} / {{ taskTotalPages }}</span>
          <button :disabled="taskCurrentPage === taskTotalPages" class="!rounded-button whitespace-nowrap border px-3 py-1 text-sm hover:bg-gray-50" :class="{ 'opacity-50 cursor-not-allowed': taskCurrentPage === taskTotalPages }" @click="nextTaskPage">
            下一页
          </button>
        </div>
      </div>
      <div class="grid grid-cols-4 gap-6">
        <div v-for="(task, index) in paginatedTasks" :key="index" class="border rounded-lg p-4">
          <div class="mb-3 flex items-start justify-between">
            <h3 class="font-medium">
              {{ task.name }}
            </h3>
            <span
              :class="{
                'text-red-500': isDeadlineApproaching(task.deadline),
                'text-gray-500': !isDeadlineApproaching(task.deadline),
              }"
            >{{ task.deadline }}</span>
          </div>
          <div class="mb-4">
            <div class="h-2 overflow-hidden rounded-full bg-gray-200">
              <div class="h-full" :class="[getPriorityColor(task.priority)]" :style="{ width: `${task.progress}%` }" />
            </div>
            <div class="mt-1 text-xs text-gray-500">
              {{ task.progress }}% 完成
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span
              :class="{
                'bg-red-100 text-red-800': task.priority === '高',
                'bg-orange-100 text-orange-800': task.priority === '中',
                'bg-blue-100 text-blue-800': task.priority === '低',
              }" class="rounded px-2 py-1 text-xs"
            >{{ task.priority }}优先级</span>
            <button class="!rounded-button whitespace-nowrap bg-blue-500 px-3 py-1 text-sm text-white hover:bg-blue-600" @click="goDetail(task)">
              继续学习
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- 学习记录区 -->
    <div class="mb-8 rounded-lg bg-white p-6 shadow-sm">
      <div class="mb-6 flex items-center justify-between">
        <h2 class="text-lg font-bold">
          最近学习
        </h2>
        <div class="flex items-center space-x-2">
          <button :disabled="recordCurrentPage === 1" class="!rounded-button whitespace-nowrap border px-3 py-1 text-sm hover:bg-gray-50" :class="{ 'opacity-50 cursor-not-allowed': recordCurrentPage === 1 }" @click="prevRecordPage">
            上一页
          </button>
          <span class="text-sm text-gray-600">{{ recordCurrentPage }} / {{ recordTotalPages }}</span>
          <button :disabled="recordCurrentPage === recordTotalPages" class="!rounded-button whitespace-nowrap border px-3 py-1 text-sm hover:bg-gray-50" :class="{ 'opacity-50 cursor-not-allowed': recordCurrentPage === recordTotalPages }" @click="nextRecordPage">
            下一页
          </button>
        </div>
      </div>
      <div class="grid grid-cols-4 gap-6">
        <div v-for="(record, index) in paginatedRecords" :key="index" class="border rounded-lg p-4">
          <div class="mb-3 flex items-center justify-between">
            <h3 class="font-medium">
              {{ record.course }}
            </h3>
            <span class="text-sm text-gray-500">{{ record.time }}</span>
          </div>
          <div class="mb-4">
            <div class="h-2 overflow-hidden rounded-full bg-gray-200">
              <div class="h-full bg-blue-500" :style="{ width: `${record.progress}%` }" />
            </div>
            <div class="mt-1 text-xs text-gray-500">
              {{ record.progress }}% 完成
            </div>
          </div>
          <button class="!rounded-button w-full whitespace-nowrap bg-blue-500 py-2 text-sm text-white hover:bg-blue-600" @click="goDetail(record)">
            继续学习
          </button>
        </div>
      </div>
    </div>
    <!-- 推荐课程区 -->
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <h2 class="mb-6 text-lg font-bold">
        推荐课程
      </h2>
      <div class="grid grid-cols-4 gap-6">
        <div v-for="(course, index) in courses" :key="index" class="overflow-hidden border rounded-lg" @click="goDetail(course)">
          <div class="h-40 overflow-hidden bg-gray-200">
            <img :src="course.cover" class="h-full w-full object-cover" alt="课程封面">
          </div>
          <div class="p-4">
            <h3 class="line-clamp-2 mb-2 font-medium">
              {{ course.name }}
            </h3>
            <div class="mb-2 flex items-center">
              <span class="mr-2 rounded bg-purple-100 px-2 py-1 text-xs text-purple-800">{{ course.type }}</span>
              <span class="text-xs text-gray-500">{{ course.duration }}</span>
            </div>
            <div class="mb-3 text-xs text-gray-500">
              {{ course.reason }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

  <style scoped>
  .line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  }
  </style>
