<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  ArrowLeft,
  ArrowRight,
  Bell,
  Clock,
  Delete,
  Document,
  DocumentAdd,
  HomeFilled,
  Monitor,
  Phone,
  Reading,
  Refresh,
  Search,
  Star,
  StarFilled,
  Tickets,
  Trophy,
  Upload,
  Warning,
} from '@element-plus/icons-vue'

const activeMenu = ref('2-1')
const currentQuestion = ref(1)
const answers = ref<(number | null)[]>(Array(20).fill(null))
const markedQuestions = ref<number[]>([])
const remainingTime = ref(45 * 60 + 30) // 45分30秒
const submitDialogVisible = ref(false)
const markedDialogVisible = ref(false)
const unansweredDialogVisible = ref(false)
const resultDialogVisible = ref(false)
const score = ref(85)
const chartRef = ref<HTMLElement>()
const showAnswer = ref(false)
const answeredCount = computed(() => {
  return answers.value.filter(answer => answer !== null).length
})
const unansweredQuestions = computed(() => {
  const result: number[] = []
  for (let i = 0; i < 20; i++) {
    if (answers.value[i] === null) {
      result.push(i + 1)
    }
  }
  return result
})
const wrongQuestions = ref([
  {
    id: 5,
    question: '金融机构应当向中国反洗钱监测分析中心报告的大额交易标准是？',
    userAnswer: 'A. 单笔或者当日累计人民币交易20万元以上',
    correctAnswer: 'B. 单笔或者当日累计人民币交易5万元以上',
    explanation:
  '根据《金融机构大额交易和可疑交易报告管理办法》规定，金融机构应当报告的大额交易标准为单笔或者当日累计人民币交易5万元以上。',
  },
  {
    id: 12,
    question: '下列哪项不属于客户身份资料和交易记录保存的基本要求？',
    userAnswer: 'D. 保存客户交易记录至少10年',
    correctAnswer: 'C. 保存客户身份资料至少10年',
    explanation:
  '客户身份资料应当自业务关系结束当年或者一次性交易记账当年计起至少保存5年，交易记录应当自交易记账当年计起至少保存5年。',
  },
])
function formatTime(seconds: number) {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs
  .toString()
  .padStart(2, '0')}`
}
function toggleMarkQuestion() {
  const index = markedQuestions.value.indexOf(currentQuestion.value)
  if (index === -1) {
    markedQuestions.value.push(currentQuestion.value)
  }
  else {
    markedQuestions.value.splice(index, 1)
  }
}
function clearAnswer() {
  answers.value[currentQuestion.value - 1] = null
}
function prevQuestion() {
  if (currentQuestion.value > 1) {
    currentQuestion.value--
  }
}
function nextQuestion() {
  if (currentQuestion.value < 20) {
    currentQuestion.value++
  }
}
function showSubmitDialog() {
  submitDialogVisible.value = true
}
function showMarkedQuestions() {
  markedDialogVisible.value = true
}
function showUnansweredQuestions() {
  unansweredDialogVisible.value = true
  submitDialogVisible.value = false
}
function submitExam() {
  submitDialogVisible.value = false
  // 计算分数等逻辑
  setTimeout(() => {
    resultDialogVisible.value = true
    nextTick(initChart)
  }, 500)
}
function initChart() {
  if (!chartRef.value) { return }
  const chart = echarts.init(chartRef.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: '5%',
      left: 'center',
    },
    series: [
      {
        name: '答题情况',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 17, name: '正确' },
          { value: 3, name: '错误' },
        ],
      },
    ],
  }
  chart.setOption(option)
}
// 倒计时
onMounted(() => {
  const timer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    }
    else {
      clearInterval(timer)
      submitExam()
    }
  }, 1000)
})
</script>

<template>
  <div>
    <!-- 主内容区 -->
    <div class="flex flex-1 flex-col">
      <!-- 考试信息区 -->
      <div class="flex-1 p-4">
        <div class="mb-6 flex items-center justify-between">
          <div>
            <h1 class="text-xl font-bold">
              反洗钱合规知识考核
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <el-tag type="primary">
              进行中
            </el-tag>
            <div class="flex items-center text-lg text-red-500 font-bold">
              <el-icon class="mr-1">
                <Clock />
              </el-icon>
              剩余 <span class="countdown">{{ formatTime(remainingTime) }}</span>
            </div>
          </div>
        </div>
        <!-- 考试基本信息 -->
        <el-card class="mb-6">
          <div class="flex flex-wrap gap-4 text-sm">
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Tickets />
              </el-icon>
              <span>考核类型：在线考试</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Clock />
              </el-icon>
              <span>考核时长：60分钟</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Document />
              </el-icon>
              <span>题目数量：20题</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Star />
              </el-icon>
              <span>及格分数：80分</span>
            </div>
          </div>
          <div class="mt-4 text-sm">
            <el-icon class="mr-2 text-blue-500">
              <Warning />
            </el-icon>
            考试规则：不允许切换窗口，不允许复制粘贴，系统会自动记录异常行为
          </div>
        </el-card>
        <!-- 考试内容区 -->
        <div class="flex gap-6">
          <!-- 左侧题目导航 -->
          <el-card class="w-1/5">
            <div class="grid grid-cols-5 gap-2">
              <div
                v-for="i in 20"
                :key="i"
                :class="{
                  'bg-blue-100': answers[i - 1],
                  'border-blue-500': currentQuestion === i,
                  'border-2': currentQuestion === i,
                }"
                class="h-10 flex cursor-pointer items-center justify-center border border-gray-200 rounded"
                @click="currentQuestion = i"
              >
                <span>{{ i }}</span>
                <el-icon
                  v-if="markedQuestions.includes(i)"
                  class="ml-1 text-yellow-500"
                >
                  <StarFilled />
                </el-icon>
              </div>
            </div>
            <div class="mt-4 text-center text-gray-600">
              已答 {{ answeredCount }}/20 题
            </div>
          </el-card>
          <!-- 右侧题目内容 -->
          <el-card class="flex-1">
            <div class="mb-4 flex items-center justify-between">
              <div>
                <span class="text-gray-600">第 {{ currentQuestion }} 题/共 20 题</span>
                <el-tag class="ml-2" type="success">
                  单选题
                </el-tag>
                <el-tag class="ml-2" type="info">
                  分值：5分
                </el-tag>
              </div>
              <div>
                <el-button
                  :type="markedQuestions.includes(currentQuestion) ? 'warning' : ''"
                  size="small"
                  @click="toggleMarkQuestion"
                >
                  <el-icon class="mr-1">
                    <Star />
                  </el-icon>
                  {{ markedQuestions.includes(currentQuestion) ? '取消标记' : '标记本题' }}
                </el-button>
                <el-button size="small" @click="clearAnswer">
                  <el-icon class="mr-1">
                    <Delete />
                  </el-icon>
                  清除答案
                </el-button>
              </div>
            </div>
            <!-- 题目内容 -->
            <div class="mb-6">
              <h3 class="mb-4 text-lg font-medium">
                根据《反洗钱法》规定，金融机构应当建立客户身份识别制度，以下哪项不属于客户身份识别的基本要求？
              </h3>
              <el-radio-group v-model="answers[currentQuestion - 1]" class="w-full">
                <el-radio
                  :label="1"
                  class="mb-3 block w-full py-2"
                  :class="{ 'text-green-500': showAnswer && 1 === 3 }"
                >
                  A. 核对客户的有效身份证件或者其他身份证明文件
                </el-radio>
                <el-radio
                  :label="2"
                  class="mb-3 block w-full py-2"
                  :class="{ 'text-green-500': showAnswer && 2 === 3 }"
                >
                  B. 登记客户身份基本信息
                </el-radio>
                <el-radio
                  :label="3"
                  class="mb-3 block w-full py-2"
                  :class="{ 'text-green-500': showAnswer && 3 === 3 }"
                >
                  C. 了解客户的职业背景和收入来源
                </el-radio>
                <el-radio
                  :label="4"
                  class="mb-3 block w-full py-2"
                  :class="{ 'text-green-500': showAnswer && 4 === 3 }"
                >
                  D. 留存客户有效身份证件或者其他身份证明文件的复印件或者影印件
                </el-radio>
              </el-radio-group>
            </div>
            <!-- 参考资料 -->
            <el-collapse class="mb-6">
              <el-collapse-item title="参考资料">
                <div class="text-gray-600">
                  <p>
                    《金融机构反洗钱规定》第十二条：金融机构应当按照规定建立客户身份识别制度。
                  </p>
                  <p class="mt-2">
                    客户身份识别的基本要求包括：核对、登记、留存客户身份信息，不包括了解客户的职业背景和收入来源。
                  </p>
                </div>
              </el-collapse-item>
            </el-collapse>
            <!-- 题目操作按钮 -->
            <div class="flex justify-between">
              <el-button
                :disabled="currentQuestion === 1"
                @click="prevQuestion"
              >
                <el-icon class="mr-1">
                  <ArrowLeft />
                </el-icon>
                上一题
              </el-button>
              <el-button
                :disabled="currentQuestion === 20"
                @click="nextQuestion"
              >
                下一题
                <el-icon class="ml-1">
                  <ArrowRight />
                </el-icon>
              </el-button>
            </div>
          </el-card>
          <!-- 右侧辅助区 -->
          <el-card class="w-1/5">
            <h3 class="mb-4 text-lg font-bold">
              考试技巧
            </h3>
            <ul class="text-sm text-gray-600 space-y-3">
              <li>1. 先做会做的题目，标记不确定的题目</li>
              <li>2. 注意题目中的关键词和限定词</li>
              <li>3. 多选题至少有两个正确答案</li>
              <li>4. 简答题要分点作答，条理清晰</li>
            </ul>
            <h3 class="mb-4 mt-6 text-lg font-bold">
              常见问题
            </h3>
            <el-collapse>
              <el-collapse-item title="考试中断怎么办？">
                <div class="text-sm text-gray-600">
                  系统会自动保存答题进度，重新登录后可继续考试
                </div>
              </el-collapse-item>
              <el-collapse-item title="如何查看已标记题目？">
                <div class="text-sm text-gray-600">
                  点击底部操作栏的"查看标记题"按钮
                </div>
              </el-collapse-item>
            </el-collapse>
            <h3 class="mb-4 mt-6 text-lg font-bold">
              技术支持
            </h3>
            <div class="text-sm text-gray-600 space-y-1">
              <div>电话：400-888-8888</div>
              <div>工作时间：9:00-18:00</div>
            </div>
          </el-card>
        </div>
      </div>
      <!-- 底部操作栏 -->
      <div
        class="bottom-0 flex items-center justify-between border-t border-gray-200 bg-white p-3"
      >
        <div class="w-1/4">
          <el-progress
            :percentage="(answeredCount / 20) * 100"
            :show-text="false"
          />
          <div class="mt-1 text-sm text-gray-600">
            已完成 {{ answeredCount }}/20 题
          </div>
        </div>
        <div class="space-x-3">
          <el-button>
            <el-icon class="mr-1">
              <DocumentAdd />
            </el-icon>
            保存进度
          </el-button>
          <el-button @click="showMarkedQuestions">
            <el-icon class="mr-1">
              <Star />
            </el-icon>
            查看标记题 ({{ markedQuestions.length }})
          </el-button>
          <el-button @click="showUnansweredQuestions">
            <el-icon class="mr-1">
              <Warning />
            </el-icon>
            未答题 ({{ 20 - answeredCount }})
          </el-button>
        </div>
        <el-button type="primary" @click="showSubmitDialog">
          <el-icon class="mr-1">
            <Upload />
          </el-icon>
          提交考试
        </el-button>
      </div>
    </div>
    <!-- 提交确认对话框 -->
    <el-dialog v-model="submitDialogVisible" title="提交确认" width="500px">
      <div class="mb-4 text-lg font-medium">
        确定要提交考试吗？提交后将无法修改答案
      </div>
      <div class="mb-6 text-gray-600">
        <p>已做题数: {{ answeredCount }}/20</p>
        <p>未答题数: {{ 20 - answeredCount }}</p>
      </div>
      <template #footer>
        <el-button @click="submitDialogVisible = false">
          继续答题
        </el-button>
        <el-button type="warning" @click="showUnansweredQuestions">
          检查未答题
        </el-button>
        <el-button type="primary" @click="submitExam">
          确认提交
        </el-button>
      </template>
    </el-dialog>
    <!-- 标记题目对话框 -->
    <el-dialog v-model="markedDialogVisible" title="标记题目" width="500px">
      <div class="grid grid-cols-5 gap-2">
        <div
          v-for="i in markedQuestions"
          :key="i"
          class="h-10 flex cursor-pointer items-center justify-center border border-gray-200 rounded bg-yellow-100"
          @click="currentQuestion = i"
        >
          {{ i }}
        </div>
      </div>
      <template #footer>
        <el-button @click="markedDialogVisible = false">
          关闭
        </el-button>
      </template>
    </el-dialog>
    <!-- 未答题对话框 -->
    <el-dialog v-model="unansweredDialogVisible" title="未答题" width="500px">
      <div class="grid grid-cols-5 gap-2">
        <div
          v-for="i in unansweredQuestions"
          :key="i"
          class="h-10 flex cursor-pointer items-center justify-center border border-gray-200 rounded bg-red-100"
          @click="currentQuestion = i"
        >
          {{ i }}
        </div>
      </div>
      <template #footer>
        <el-button @click="unansweredDialogVisible = false">
          关闭
        </el-button>
      </template>
    </el-dialog>
    <!-- 考试结果对话框 -->
    <el-dialog v-model="resultDialogVisible" title="考试结果" width="800px">
      <div class="text-center">
        <el-tag :type="score >= 80 ? 'success' : 'danger'" size="large">
          {{ score >= 80 ? '通过' : '未通过' }}
        </el-tag>
        <div class="my-4 text-4xl font-bold">
          {{ score }}/100
        </div>
        <el-progress
          :percentage="score"
          :stroke-width="20"
          :text-inside="true"
          class="mb-6"
        />
        <div class="mb-6 text-gray-600">
          考试用时: 45分30秒
        </div>
      </div>
      <el-tabs>
        <el-tab-pane label="答题分析">
          <div class="flex justify-between">
            <div class="w-1/3">
              <div ref="chartRef" style="width: 100%; height: 300px" />
            </div>
            <div class="w-2/3 pl-6">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="单选题正确率">
                  <el-progress
                    :percentage="85"
                    :format="(p) => `${p}% (17/20)`"
                  />
                </el-descriptions-item>
                <el-descriptions-item label="多选题正确率">
                  <el-progress
                    :percentage="75"
                    :format="(p) => `${p}% (6/8)`"
                  />
                </el-descriptions-item>
                <el-descriptions-item label="判断题正确率">
                  <el-progress
                    :percentage="90"
                    :format="(p) => `${p}% (9/10)`"
                  />
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="错题分析">
          <el-collapse>
            <el-collapse-item
              v-for="(item, index) in wrongQuestions"
              :key="index"
              :title="`第 ${item.id} 题`"
            >
              <div class="mb-4">
                <p class="font-medium">
                  {{ item.question }}
                </p>
                <p class="mt-2 text-red-500">
                  您的答案: {{ item.userAnswer }}
                </p>
                <p class="text-green-500">
                  正确答案: {{ item.correctAnswer }}
                </p>
                <p class="mt-2 text-gray-600">
                  解析: {{ item.explanation }}
                </p>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <el-button v-if="score >= 80" type="primary">
          <el-icon class="mr-1">
            <Trophy />
          </el-icon>
          查看考试证书
        </el-button>
        <el-button v-else type="warning">
          <el-icon class="mr-1">
            <Refresh />
          </el-icon>
          再次学习
        </el-button>
        <el-button @click="resultDialogVisible = false">
          返回
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

  <style scoped>
  .countdown {
  font-family: monospace;
  }
  :deep(.el-progress-bar__outer) {
  background-color: #f5f7fa;
  }
  :deep(.el-collapse-item__header) {
  font-weight: 500;
  }
  :deep(.el-descriptions__body) {
  background-color: #f9fafc;
  }
  </style>
