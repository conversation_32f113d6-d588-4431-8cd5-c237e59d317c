<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowDown, Bell, Bottom, Check, Close,
  Delete, Edit, Link, List, Search,
  Top, User,
} from '@element-plus/icons-vue'
// 路由实例
const router = useRouter()
const courses = ref([
  { name: '合规基础知识', type: '必修', duration: '2小时', required: true, startDate: '2023-06-01', endDate: '2023-06-30', exam: '80分以上合格' },
  { name: '数据安全法规', type: '选修', duration: '1.5小时', required: false, startDate: '2023-06-15', endDate: '2023-07-15', exam: '完成问卷' },
  { name: '反洗钱培训', type: '必修', duration: '3小时', required: true, startDate: '2023-07-01', endDate: '2023-07-31', exam: '90分以上合格' },
])
const evalItems = ref([
  { name: '知识掌握度', desc: '考核培训内容的掌握程度', target: '80%', method: '考试成绩', weight: '40%' },
  { name: '应用能力', desc: '实际工作中的合规应用', target: '85%', method: '案例分析', weight: '30%' },
  { name: '满意度', desc: '培训满意度评价', target: '90%', method: '问卷调查', weight: '20%' },
  { name: '参与度', desc: '培训参与积极性', target: '95%', method: '考勤记录', weight: '10%' },
])
</script>

<template>
  <div>
    <FaPageMain>
      <div class="min-h-screen flex">
        <!-- 主内容区 -->
        <div class="flex flex-1 flex-col">
          <!-- 内容区 -->
          <div class="flex-1 bg-gray-100 p-6">
            <page-header>
              <template #content>
                <div class="aic jcsb flex">
                  <div class="f-20 c-['#000000']">
                    <h2 class="text-xl text-gray-800 font-semibold">
                      新增培训计划
                    </h2>
                  </div>
                  <div class="aic flex">
                    <div>
                      <button class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">
                        保存
                      </button>
                      <button class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50">
                        保存为草稿
                      </button>
                      <button class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50">
                        取消
                      </button>
                      <button class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50">
                        预览
                      </button>
                    </div>
                  </div>
                </div>
              </template>
            </page-header>
            <!-- 表单卡片 -->
            <div class="mb-6 rounded bg-white p-6 shadow">
              <h2 class="mb-4 text-lg font-bold">
                基本信息
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div class="flex items-center">
                  <label class="mr-4 w-24 text-right text-sm text-gray-600">计划名称<span class="text-red-500">*</span></label>
                  <input
                    type="text"
                    class="flex-1 border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="请输入计划名称"
                  >
                </div>
                <div class="flex items-center">
                  <label class="mr-4 w-24 text-right text-sm text-gray-600">计划编号</label>
                  <div class="flex flex-1">
                    <input
                      type="text"
                      class="flex-1 border border-gray-300 rounded-l px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="系统自动生成"
                    >
                    <button class="!rounded-button whitespace-nowrap border border-gray-300 rounded-r bg-gray-100 px-3 py-2 text-sm hover:bg-gray-200">
                      自动生成
                    </button>
                  </div>
                </div>
                <div class="flex items-center">
                  <label class="mr-4 w-24 text-right text-sm text-gray-600">计划类型</label>
                  <select class="flex-1 border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                    <option value="">
                      请选择计划类型
                    </option>
                    <option value="定期培训">
                      定期培训
                    </option>
                    <option value="专项培训">
                      专项培训
                    </option>
                    <option value="新员工培训">
                      新员工培训
                    </option>
                    <option value="法规更新培训">
                      法规更新培训
                    </option>
                    <option value="其他">
                      其他
                    </option>
                  </select>
                </div>
                <div class="flex items-center">
                  <label class="mr-4 w-24 text-right text-sm text-gray-600">计划周期<span class="text-red-500">*</span></label>
                  <el-date-picker
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    class="flex-1"
                  />
                </div>
                <div class="flex items-center">
                  <label class="mr-4 w-24 text-right text-sm text-gray-600">培训对象<span class="text-red-500">*</span></label>
                  <el-select
                    multiple
                    class="flex-1"
                    placeholder="请选择培训对象"
                  >
                    <el-option label="全体员工" value="全体员工" />
                    <el-option label="管理层" value="管理层" />
                    <el-option label="特定部门" value="特定部门" />
                    <el-option label="特定岗位" value="特定岗位" />
                    <el-option label="新员工" value="新员工" />
                    <el-option label="其他" value="其他" />
                  </el-select>
                </div>
                <div class="flex items-center">
                  <label class="mr-4 w-24 text-right text-sm text-gray-600">负责人<span class="text-red-500">*</span></label>
                  <div class="flex flex-1 items-center">
                    <input
                      type="text"
                      class="flex-1 border border-gray-300 rounded-l px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="请选择负责人"
                    >
                    <button class="!rounded-button whitespace-nowrap border border-gray-300 rounded-r bg-gray-100 px-3 py-2 text-sm hover:bg-gray-200">
                      <el-icon><User /></el-icon>
                    </button>
                  </div>
                </div>
                <div class="flex items-center">
                  <label class="mr-4 w-24 text-right text-sm text-gray-600">计划状态</label>
                  <div class="flex flex-1 space-x-4">
                    <label class="flex items-center">
                      <input type="radio" name="status" class="mr-1" checked>
                      <span class="text-sm">草稿</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="status" class="mr-1">
                      <span class="text-sm">待启动</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="status" class="mr-1">
                      <span class="text-sm">进行中</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="status" class="mr-1">
                      <span class="text-sm">已完成</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="status" class="mr-1">
                      <span class="text-sm">已取消</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <!-- 课程设置 -->
            <div class="mb-6 rounded bg-white p-6 shadow">
              <div class="mb-4 flex items-center justify-between">
                <h2 class="text-lg font-bold">
                  课程设置
                </h2>
                <button class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">
                  添加课程
                </button>
              </div>
              <el-table :data="courses" class="mb-4 w-full">
                <el-table-column prop="name" label="课程名称" width="180" />
                <el-table-column prop="type" label="课程类型" width="120" />
                <el-table-column prop="duration" label="课程时长" width="100" />
                <el-table-column prop="required" label="必修标识" width="80">
                  <template #default="{ row }">
                    <el-icon v-if="row.required" class="text-green-500">
                      <Check />
                    </el-icon>
                    <el-icon v-else class="text-gray-400">
                      <Close />
                    </el-icon>
                  </template>
                </el-table-column>
                <el-table-column prop="startDate" label="开始日期" width="120" />
                <el-table-column prop="endDate" label="截止日期" width="120" />
                <el-table-column prop="exam" label="考核要求" />
                <el-table-column label="操作" width="180">
                  <template #default>
                    <div class="flex space-x-2">
                      <el-icon class="cursor-pointer text-blue-500">
                        <Top />
                      </el-icon>
                      <el-icon class="cursor-pointer text-blue-500">
                        <Bottom />
                      </el-icon>
                      <el-icon class="cursor-pointer text-blue-500">
                        <Edit />
                      </el-icon>
                      <el-icon class="cursor-pointer text-red-500">
                        <Delete />
                      </el-icon>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="flex space-x-2">
                <button class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50">
                  批量设置时间
                </button>
                <button class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50">
                  自动排序
                </button>
              </div>
            </div>
            <!-- 计划概述 -->
            <div class="mb-6 rounded bg-white p-6 shadow">
              <h2 class="mb-4 text-lg font-bold">
                计划概述
              </h2>
              <div class="space-y-6">
                <div>
                  <label class="mb-2 block text-sm text-gray-600">培训目标<span class="text-red-500">*</span></label>
                  <textarea
                    class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                    rows="3"
                    placeholder="请输入培训目标"
                  />
                </div>
                <div>
                  <label class="mb-2 block text-sm text-gray-600">实施方案</label>
                  <div class="border border-gray-300 rounded p-2">
                    <div class="mb-2 flex space-x-2">
                      <button class="rounded p-1 hover:bg-gray-100">
                        <!-- <el-icon><Bold /></el-icon> -->
                      </button>
                      <button class="rounded p-1 hover:bg-gray-100">
                        <!-- <el-icon><Italic /></el-icon> -->
                      </button>
                      <button class="rounded p-1 hover:bg-gray-100">
                        <el-icon><Underline /></el-icon>
                      </button>
                      <button class="rounded p-1 hover:bg-gray-100">
                        <el-icon><List /></el-icon>
                      </button>
                      <button class="rounded p-1 hover:bg-gray-100">
                        <el-icon><link></el-icon>
                      </button>
                    </div>
                    <textarea
                      class="w-full border-none px-2 py-1 text-sm focus:outline-none"
                      rows="5"
                      placeholder="请输入实施方案"
                    />
                  </div>
                </div>
                <div>
                  <label class="mb-2 block text-sm text-gray-600">资源需求</label>
                  <textarea
                    class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                    rows="3"
                    placeholder="请输入资源需求"
                  />
                </div>
                <div>
                  <label class="mb-2 block text-sm text-gray-600">风险管理</label>
                  <textarea
                    class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                    rows="3"
                    placeholder="请输入风险管理措施"
                  />
                </div>
              </div>
            </div>
            <!-- 通知设置 -->
            <div class="mb-6 rounded bg-white p-6 shadow">
              <h2 class="mb-4 text-lg font-bold">
                通知设置
              </h2>
              <div class="space-y-6">
                <div>
                  <label class="mb-2 block text-sm text-gray-600">通知方式</label>
                  <div class="flex space-x-6">
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2">
                      <span class="text-sm">电子邮件</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2" checked>
                      <span class="text-sm">系统消息</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2">
                      <span class="text-sm">短信通知</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2">
                      <span class="text-sm">其他</span>
                    </label>
                  </div>
                </div>
                <div>
                  <label class="mb-2 block text-sm text-gray-600">提醒设置</label>
                  <div class="space-y-4">
                    <div class="flex items-center">
                      <label class="w-32 flex items-center">
                        <el-switch class="mr-2" />
                        <span class="text-sm">开始提醒</span>
                      </label>
                      <input
                        type="number"
                        class="mr-2 w-20 border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                      <select class="border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                        <option>天</option>
                        <option>小时</option>
                      </select>
                    </div>
                    <div class="flex items-center">
                      <label class="w-32 flex items-center">
                        <el-switch class="mr-2" />
                        <span class="text-sm">截止提醒</span>
                      </label>
                      <input
                        type="number"
                        class="mr-2 w-20 border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                      <select class="border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                        <option>天</option>
                        <option>小时</option>
                      </select>
                    </div>
                    <div class="flex items-center">
                      <label class="w-32 flex items-center">
                        <el-switch class="mr-2" />
                        <span class="text-sm">进度提醒</span>
                      </label>
                      <input
                        type="number"
                        class="mr-2 w-20 border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="百分比"
                      >
                      <span class="text-sm">%</span>
                    </div>
                  </div>
                </div>
                <div>
                  <label class="mb-2 block text-sm text-gray-600">通知模板</label>
                  <div class="flex">
                    <select class="border border-gray-300 rounded-l px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                      <option>默认模板</option>
                      <option>培训开始通知</option>
                      <option>培训截止提醒</option>
                      <option>自定义模板</option>
                    </select>
                    <button class="!rounded-button whitespace-nowrap border border-gray-300 rounded-r bg-gray-100 px-3 py-2 text-sm hover:bg-gray-200">
                      预览
                    </button>
                  </div>
                </div>
                <div>
                  <label class="mb-2 block text-sm text-gray-600">通知对象</label>
                  <div class="flex items-center">
                    <input
                      type="text"
                      class="flex-1 border border-gray-300 rounded-l px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="请选择通知对象"
                    >
                    <button class="!rounded-button whitespace-nowrap border border-gray-300 rounded-r bg-gray-100 px-3 py-2 text-sm hover:bg-gray-200">
                      <el-icon><User /></el-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <!-- 评估设置 -->
            <div class="rounded bg-white p-6 shadow">
              <h2 class="mb-4 text-lg font-bold">
                评估设置
              </h2>
              <div class="space-y-6">
                <div>
                  <label class="mb-2 block text-sm text-gray-600">评估方式</label>
                  <div class="flex space-x-6">
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2" checked>
                      <span class="text-sm">考核成绩</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2">
                      <span class="text-sm">问卷调查</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2">
                      <span class="text-sm">实际应用</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2">
                      <span class="text-sm">其他</span>
                    </label>
                  </div>
                </div>
                <div>
                  <label class="mb-2 block text-sm text-gray-600">评估周期</label>
                  <div class="flex space-x-4">
                    <label class="flex items-center">
                      <input type="radio" name="evalCycle" class="mr-1" checked>
                      <span class="text-sm">培训结束后立即评估</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="evalCycle" class="mr-1">
                      <span class="text-sm">定期评估</span>
                    </label>
                  </div>
                </div>
                <div>
                  <div class="mb-2 flex items-center justify-between">
                    <label class="block text-sm text-gray-600">评估指标</label>
                    <button class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50">
                      添加指标
                    </button>
                  </div>
                  <el-table :data="evalItems" class="w-full">
                    <el-table-column prop="name" label="指标名称" width="180" />
                    <el-table-column prop="desc" label="指标描述" />
                    <el-table-column prop="target" label="目标值" width="100" />
                    <el-table-column prop="method" label="计算方式" width="120" />
                    <el-table-column prop="weight" label="权重" width="80" />
                    <el-table-column label="操作" width="80">
                      <template #default>
                        <el-icon class="cursor-pointer text-red-500">
                          <Delete />
                        </el-icon>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div>
                  <label class="mb-2 block text-sm text-gray-600">评估报告</label>
                  <div class="space-y-4">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <span class="mr-4 text-sm">是否生成报告</span>
                        <label class="mr-4 flex items-center">
                          <input type="radio" name="report" class="mr-1" checked>
                          <span class="text-sm">是</span>
                        </label>
                        <label class="flex items-center">
                          <input type="radio" name="report" class="mr-1">
                          <span class="text-sm">否</span>
                        </label>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <span class="mr-4 text-sm">报告周期</span>
                        <label class="mr-4 flex items-center">
                          <input type="radio" name="reportCycle" class="mr-1" checked>
                          <span class="text-sm">结束后一次性</span>
                        </label>
                        <label class="mr-4 flex items-center">
                          <input type="radio" name="reportCycle" class="mr-1">
                          <span class="text-sm">每月</span>
                        </label>
                        <label class="mr-4 flex items-center">
                          <input type="radio" name="reportCycle" class="mr-1">
                          <span class="text-sm">每季度</span>
                        </label>
                        <label class="flex items-center">
                          <input type="radio" name="reportCycle" class="mr-1">
                          <span class="text-sm">自定义</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </FaPageMain>
  </div>
</template>

  <style scoped>
  /* 自定义样式 */
  :deep(.el-table) {
  --el-table-border-color: #e0e0e0;
  --el-table-header-bg-color: #f5f7fa;
  }
  :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #e0e0e0 inset;
  }
  :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
  }
  :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #1E88E5 inset;
  }
  :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #1E88E5;
  border-color: #1E88E5;
  }
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #1E88E5;
  border-color: #1E88E5;
  }
  :deep(.el-switch.is-checked .el-switch__core) {
  background-color: #1E88E5;
  border-color: #1E88E5;
  }
  </style>
