---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 18-合规培训服务/课程信息管理

## POST 创建新的课程信息

POST /whiskerguardtrainingservice/api/course/infos

{@code POST  /course/infos} : Create a new courseInfo.

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "applicableRole": "string",
  "instructor": "string",
  "producer": "string",
  "releaseDate": {},
  "lastUpdate": {},
  "status": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseDescription": {},
  "courseContent": {},
  "playbackSetting": {},
  "interactiveFeature": {},
  "relatedCourses": "new HashSet<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseInfoDTO](#schemacourseinfodto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "",
  "courseName": "",
  "courseType": "",
  "trainingTheme": "",
  "difficultyLevel": "",
  "applicableRole": "",
  "instructor": "",
  "producer": "",
  "releaseDate": {
    "seconds": 0,
    "nanos": 0
  },
  "lastUpdate": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "",
  "durationMinutes": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseDescription": {
    "id": 0,
    "courseId": "",
    "courseOverview": "",
    "learningObjective": "",
    "prerequisites": "",
    "certificationInfo": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "courseContent": {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "playbackSetting": {
    "id": 0,
    "courseId": "",
    "autoPlay": false,
    "rememberPlayback": false,
    "continuousPlayback": false,
    "defaultClarity": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "interactiveFeature": {
    "id": 0,
    "courseId": "",
    "commentEnabled": false,
    "likeEnabled": false,
    "shareEnabled": false,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "relatedCourses": [
    {
      "id": 0,
      "relatedCourseId": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseInfos": [
        {
          "id": 0,
          "tenantId": 0,
          "courseCode": "",
          "courseName": "",
          "courseType": "",
          "trainingTheme": "",
          "difficultyLevel": "",
          "applicableRole": "",
          "instructor": "",
          "producer": "",
          "releaseDate": {
            "seconds": 0,
            "nanos": 0
          },
          "lastUpdate": {
            "seconds": 0,
            "nanos": 0
          },
          "status": "",
          "durationMinutes": 0,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseDescription": {
            "id": 0,
            "courseId": "",
            "courseOverview": "",
            "learningObjective": "",
            "prerequisites": "",
            "certificationInfo": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "courseContent": {
            "id": 0,
            "courseId": "",
            "contentType": "",
            "contentTitle": "",
            "contentDescription": "",
            "contentUrl": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "playbackSetting": {
            "id": 0,
            "courseId": "",
            "autoPlay": false,
            "rememberPlayback": false,
            "continuousPlayback": false,
            "defaultClarity": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "interactiveFeature": {
            "id": 0,
            "courseId": "",
            "commentEnabled": false,
            "likeEnabled": false,
            "shareEnabled": false,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "relatedCourses": [
            {
              "id": 0,
              "relatedCourseId": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseInfos": [
                {
                  "id": 0,
                  "tenantId": 0,
                  "courseCode": "",
                  "courseName": "",
                  "courseType": "",
                  "trainingTheme": "",
                  "difficultyLevel": "",
                  "applicableRole": "",
                  "instructor": "",
                  "producer": "",
                  "releaseDate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "lastUpdate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "status": "",
                  "durationMinutes": 0,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseDescription": {
                    "id": 0,
                    "courseId": "",
                    "courseOverview": "",
                    "learningObjective": "",
                    "prerequisites": "",
                    "certificationInfo": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "courseContent": {
                    "id": 0,
                    "courseId": "",
                    "contentType": "",
                    "contentTitle": "",
                    "contentDescription": "",
                    "contentUrl": "",
                    "durationMinutes": 0,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "playbackSetting": {
                    "id": 0,
                    "courseId": "",
                    "autoPlay": false,
                    "rememberPlayback": false,
                    "continuousPlayback": false,
                    "defaultClarity": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "interactiveFeature": {
                    "id": 0,
                    "courseId": "",
                    "commentEnabled": false,
                    "likeEnabled": false,
                    "shareEnabled": false,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "relatedCourses": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseInfoDTO](#schemaresponseentitycourseinfodto)|

## PUT 更新现有的课程信息

PUT /whiskerguardtrainingservice/api/course/infos/{id}

{@code PUT  /course/infos/:id} : Updates an existing courseInfo.

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "applicableRole": "string",
  "instructor": "string",
  "producer": "string",
  "releaseDate": {},
  "lastUpdate": {},
  "status": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseDescription": {},
  "courseContent": {},
  "playbackSetting": {},
  "interactiveFeature": {},
  "relatedCourses": "new HashSet<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要保存的课程信息DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseInfoDTO](#schemacourseinfodto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "",
  "courseName": "",
  "courseType": "",
  "trainingTheme": "",
  "difficultyLevel": "",
  "applicableRole": "",
  "instructor": "",
  "producer": "",
  "releaseDate": {
    "seconds": 0,
    "nanos": 0
  },
  "lastUpdate": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "",
  "durationMinutes": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseDescription": {
    "id": 0,
    "courseId": "",
    "courseOverview": "",
    "learningObjective": "",
    "prerequisites": "",
    "certificationInfo": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "courseContent": {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "playbackSetting": {
    "id": 0,
    "courseId": "",
    "autoPlay": false,
    "rememberPlayback": false,
    "continuousPlayback": false,
    "defaultClarity": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "interactiveFeature": {
    "id": 0,
    "courseId": "",
    "commentEnabled": false,
    "likeEnabled": false,
    "shareEnabled": false,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "relatedCourses": [
    {
      "id": 0,
      "relatedCourseId": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseInfos": [
        {
          "id": 0,
          "tenantId": 0,
          "courseCode": "",
          "courseName": "",
          "courseType": "",
          "trainingTheme": "",
          "difficultyLevel": "",
          "applicableRole": "",
          "instructor": "",
          "producer": "",
          "releaseDate": {
            "seconds": 0,
            "nanos": 0
          },
          "lastUpdate": {
            "seconds": 0,
            "nanos": 0
          },
          "status": "",
          "durationMinutes": 0,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseDescription": {
            "id": 0,
            "courseId": "",
            "courseOverview": "",
            "learningObjective": "",
            "prerequisites": "",
            "certificationInfo": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "courseContent": {
            "id": 0,
            "courseId": "",
            "contentType": "",
            "contentTitle": "",
            "contentDescription": "",
            "contentUrl": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "playbackSetting": {
            "id": 0,
            "courseId": "",
            "autoPlay": false,
            "rememberPlayback": false,
            "continuousPlayback": false,
            "defaultClarity": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "interactiveFeature": {
            "id": 0,
            "courseId": "",
            "commentEnabled": false,
            "likeEnabled": false,
            "shareEnabled": false,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "relatedCourses": [
            {
              "id": 0,
              "relatedCourseId": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseInfos": [
                {
                  "id": 0,
                  "tenantId": 0,
                  "courseCode": "",
                  "courseName": "",
                  "courseType": "",
                  "trainingTheme": "",
                  "difficultyLevel": "",
                  "applicableRole": "",
                  "instructor": "",
                  "producer": "",
                  "releaseDate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "lastUpdate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "status": "",
                  "durationMinutes": 0,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseDescription": {
                    "id": 0,
                    "courseId": "",
                    "courseOverview": "",
                    "learningObjective": "",
                    "prerequisites": "",
                    "certificationInfo": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "courseContent": {
                    "id": 0,
                    "courseId": "",
                    "contentType": "",
                    "contentTitle": "",
                    "contentDescription": "",
                    "contentUrl": "",
                    "durationMinutes": 0,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "playbackSetting": {
                    "id": 0,
                    "courseId": "",
                    "autoPlay": false,
                    "rememberPlayback": false,
                    "continuousPlayback": false,
                    "defaultClarity": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "interactiveFeature": {
                    "id": 0,
                    "courseId": "",
                    "commentEnabled": false,
                    "likeEnabled": false,
                    "shareEnabled": false,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "relatedCourses": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseInfoDTO](#schemaresponseentitycourseinfodto)|

## PATCH 部分更新课程信息的指定字段，忽略空值字段

PATCH /whiskerguardtrainingservice/api/course/infos/id

{@code PATCH  /course/infos/:id} : Partial updates given fields of an existing courseInfo, field will ignore if it is null

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "applicableRole": "string",
  "instructor": "string",
  "producer": "string",
  "releaseDate": {},
  "lastUpdate": {},
  "status": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseDescription": {},
  "courseContent": {},
  "playbackSetting": {},
  "interactiveFeature": {},
  "relatedCourses": "new HashSet<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseInfoDTO](#schemacourseinfodto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "",
  "courseName": "",
  "courseType": "",
  "trainingTheme": "",
  "difficultyLevel": "",
  "applicableRole": "",
  "instructor": "",
  "producer": "",
  "releaseDate": {
    "seconds": 0,
    "nanos": 0
  },
  "lastUpdate": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "",
  "durationMinutes": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseDescription": {
    "id": 0,
    "courseId": "",
    "courseOverview": "",
    "learningObjective": "",
    "prerequisites": "",
    "certificationInfo": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "courseContent": {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "playbackSetting": {
    "id": 0,
    "courseId": "",
    "autoPlay": false,
    "rememberPlayback": false,
    "continuousPlayback": false,
    "defaultClarity": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "interactiveFeature": {
    "id": 0,
    "courseId": "",
    "commentEnabled": false,
    "likeEnabled": false,
    "shareEnabled": false,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "relatedCourses": [
    {
      "id": 0,
      "relatedCourseId": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseInfos": [
        {
          "id": 0,
          "tenantId": 0,
          "courseCode": "",
          "courseName": "",
          "courseType": "",
          "trainingTheme": "",
          "difficultyLevel": "",
          "applicableRole": "",
          "instructor": "",
          "producer": "",
          "releaseDate": {
            "seconds": 0,
            "nanos": 0
          },
          "lastUpdate": {
            "seconds": 0,
            "nanos": 0
          },
          "status": "",
          "durationMinutes": 0,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseDescription": {
            "id": 0,
            "courseId": "",
            "courseOverview": "",
            "learningObjective": "",
            "prerequisites": "",
            "certificationInfo": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "courseContent": {
            "id": 0,
            "courseId": "",
            "contentType": "",
            "contentTitle": "",
            "contentDescription": "",
            "contentUrl": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "playbackSetting": {
            "id": 0,
            "courseId": "",
            "autoPlay": false,
            "rememberPlayback": false,
            "continuousPlayback": false,
            "defaultClarity": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "interactiveFeature": {
            "id": 0,
            "courseId": "",
            "commentEnabled": false,
            "likeEnabled": false,
            "shareEnabled": false,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "relatedCourses": [
            {
              "id": 0,
              "relatedCourseId": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseInfos": [
                {
                  "id": 0,
                  "tenantId": 0,
                  "courseCode": "",
                  "courseName": "",
                  "courseType": "",
                  "trainingTheme": "",
                  "difficultyLevel": "",
                  "applicableRole": "",
                  "instructor": "",
                  "producer": "",
                  "releaseDate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "lastUpdate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "status": "",
                  "durationMinutes": 0,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseDescription": {
                    "id": 0,
                    "courseId": "",
                    "courseOverview": "",
                    "learningObjective": "",
                    "prerequisites": "",
                    "certificationInfo": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "courseContent": {
                    "id": 0,
                    "courseId": "",
                    "contentType": "",
                    "contentTitle": "",
                    "contentDescription": "",
                    "contentUrl": "",
                    "durationMinutes": 0,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "playbackSetting": {
                    "id": 0,
                    "courseId": "",
                    "autoPlay": false,
                    "rememberPlayback": false,
                    "continuousPlayback": false,
                    "defaultClarity": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "interactiveFeature": {
                    "id": 0,
                    "courseId": "",
                    "commentEnabled": false,
                    "likeEnabled": false,
                    "shareEnabled": false,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "relatedCourses": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseInfoDTO](#schemaresponseentitycourseinfodto)|

## GET 根据ID获取课程信息

GET /whiskerguardtrainingservice/api/course/infos/id

{@code GET  /course/infos/:id} : get the "id" courseInfo.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "",
  "courseName": "",
  "courseType": "",
  "trainingTheme": "",
  "difficultyLevel": "",
  "applicableRole": "",
  "instructor": "",
  "producer": "",
  "releaseDate": {
    "seconds": 0,
    "nanos": 0
  },
  "lastUpdate": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "",
  "durationMinutes": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseDescription": {
    "id": 0,
    "courseId": "",
    "courseOverview": "",
    "learningObjective": "",
    "prerequisites": "",
    "certificationInfo": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "courseContent": {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "playbackSetting": {
    "id": 0,
    "courseId": "",
    "autoPlay": false,
    "rememberPlayback": false,
    "continuousPlayback": false,
    "defaultClarity": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "interactiveFeature": {
    "id": 0,
    "courseId": "",
    "commentEnabled": false,
    "likeEnabled": false,
    "shareEnabled": false,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "relatedCourses": [
    {
      "id": 0,
      "relatedCourseId": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseInfos": [
        {
          "id": 0,
          "tenantId": 0,
          "courseCode": "",
          "courseName": "",
          "courseType": "",
          "trainingTheme": "",
          "difficultyLevel": "",
          "applicableRole": "",
          "instructor": "",
          "producer": "",
          "releaseDate": {
            "seconds": 0,
            "nanos": 0
          },
          "lastUpdate": {
            "seconds": 0,
            "nanos": 0
          },
          "status": "",
          "durationMinutes": 0,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseDescription": {
            "id": 0,
            "courseId": "",
            "courseOverview": "",
            "learningObjective": "",
            "prerequisites": "",
            "certificationInfo": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "courseContent": {
            "id": 0,
            "courseId": "",
            "contentType": "",
            "contentTitle": "",
            "contentDescription": "",
            "contentUrl": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "playbackSetting": {
            "id": 0,
            "courseId": "",
            "autoPlay": false,
            "rememberPlayback": false,
            "continuousPlayback": false,
            "defaultClarity": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "interactiveFeature": {
            "id": 0,
            "courseId": "",
            "commentEnabled": false,
            "likeEnabled": false,
            "shareEnabled": false,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "relatedCourses": [
            {
              "id": 0,
              "relatedCourseId": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseInfos": [
                {
                  "id": 0,
                  "tenantId": 0,
                  "courseCode": "",
                  "courseName": "",
                  "courseType": "",
                  "trainingTheme": "",
                  "difficultyLevel": "",
                  "applicableRole": "",
                  "instructor": "",
                  "producer": "",
                  "releaseDate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "lastUpdate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "status": "",
                  "durationMinutes": 0,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseDescription": {
                    "id": 0,
                    "courseId": "",
                    "courseOverview": "",
                    "learningObjective": "",
                    "prerequisites": "",
                    "certificationInfo": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "courseContent": {
                    "id": 0,
                    "courseId": "",
                    "contentType": "",
                    "contentTitle": "",
                    "contentDescription": "",
                    "contentUrl": "",
                    "durationMinutes": 0,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "playbackSetting": {
                    "id": 0,
                    "courseId": "",
                    "autoPlay": false,
                    "rememberPlayback": false,
                    "continuousPlayback": false,
                    "defaultClarity": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "interactiveFeature": {
                    "id": 0,
                    "courseId": "",
                    "commentEnabled": false,
                    "likeEnabled": false,
                    "shareEnabled": false,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "relatedCourses": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseInfoDTO](#schemaresponseentitycourseinfodto)|

## DELETE 根据ID逻辑删除课程信息

DELETE /whiskerguardtrainingservice/api/course/infos/id

{@code DELETE  /course/infos/:id} : logically delete the "id" courseInfo.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 根据查询条件获取课程信息

POST /whiskerguardtrainingservice/api/course/infos/query

{@code POST  /course/infos/query} : query courseInfos by criteria.

> Body 请求参数

```json
{
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "status": "string",
  "instructor": "string",
  "startDate": "string",
  "endDate": "string",
  "searchTerm": "string",
  "page": 0,
  "size": 0,
  "sort": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseInfoCriteria](#schemacourseinfocriteria)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "courseCode": "",
      "courseName": "",
      "courseType": "",
      "trainingTheme": "",
      "difficultyLevel": "",
      "applicableRole": "",
      "instructor": "",
      "producer": "",
      "releaseDate": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "lastUpdate": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "status": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false,
      "courseDescription": {
        "id": 0,
        "courseId": "",
        "courseOverview": "",
        "learningObjective": "",
        "prerequisites": "",
        "certificationInfo": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "isDeleted": false
      },
      "courseContent": {
        "id": 0,
        "courseId": "",
        "contentType": "",
        "contentTitle": "",
        "contentDescription": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "isDeleted": false
      },
      "playbackSetting": {
        "id": 0,
        "courseId": "",
        "autoPlay": false,
        "rememberPlayback": false,
        "continuousPlayback": false,
        "defaultClarity": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "isDeleted": false
      },
      "interactiveFeature": {
        "id": 0,
        "courseId": "",
        "commentEnabled": false,
        "likeEnabled": false,
        "shareEnabled": false,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "isDeleted": false
      },
      "relatedCourses": [
        {
          "id": 0,
          "relatedCourseId": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false,
          "courseInfos": [
            {
              "id": 0,
              "tenantId": 0,
              "courseCode": "",
              "courseName": "",
              "courseType": "",
              "trainingTheme": "",
              "difficultyLevel": "",
              "applicableRole": "",
              "instructor": "",
              "producer": "",
              "releaseDate": {
                "seconds": 0,
                "nanos": 0,
                "epochSecond": 0,
                "nano": 0
              },
              "lastUpdate": {
                "seconds": 0,
                "nanos": 0,
                "epochSecond": 0,
                "nano": 0
              },
              "status": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0,
                "epochSecond": 0,
                "nano": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0,
                "epochSecond": 0,
                "nano": 0
              },
              "isDeleted": false,
              "courseDescription": {
                "id": 0,
                "courseId": "",
                "courseOverview": "",
                "learningObjective": "",
                "prerequisites": "",
                "certificationInfo": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "isDeleted": false
              },
              "courseContent": {
                "id": 0,
                "courseId": "",
                "contentType": "",
                "contentTitle": "",
                "contentDescription": "",
                "contentUrl": "",
                "durationMinutes": 0,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "isDeleted": false
              },
              "playbackSetting": {
                "id": 0,
                "courseId": "",
                "autoPlay": false,
                "rememberPlayback": false,
                "continuousPlayback": false,
                "defaultClarity": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "isDeleted": false
              },
              "interactiveFeature": {
                "id": 0,
                "courseId": "",
                "commentEnabled": false,
                "likeEnabled": false,
                "shareEnabled": false,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "isDeleted": false
              },
              "relatedCourses": [
                {
                  "id": 0,
                  "relatedCourseId": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "isDeleted": false,
                  "courseInfos": [
                    "[Object]"
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "empty": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageCourseInfoDTO](#schemaresponseentitypagecourseinfodto)|

# 18-合规培训服务/课程内容管理

## POST 创建新的课程内容

POST /whiskerguardtrainingservice/api/course/contents

{@code POST  /course/contents} : Create a new courseContent.

> Body 请求参数

```json
{
  "id": 0,
  "courseId": "string",
  "contentType": "string",
  "contentTitle": "string",
  "contentDescription": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseContentDTO](#schemacoursecontentdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "courseId": "",
  "contentType": "",
  "contentTitle": "",
  "contentDescription": "",
  "contentUrl": "",
  "durationMinutes": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseContentDTO](#schemaresponseentitycoursecontentdto)|

## PUT 更新现有的课程内容

PUT /whiskerguardtrainingservice/api/course/contents/{id}

{@code PUT  /course/contents/:id} : Updates an existing courseContent.

> Body 请求参数

```json
{
  "id": 0,
  "courseId": "string",
  "contentType": "string",
  "contentTitle": "string",
  "contentDescription": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要保存的课程内容DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseContentDTO](#schemacoursecontentdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "courseId": "",
  "contentType": "",
  "contentTitle": "",
  "contentDescription": "",
  "contentUrl": "",
  "durationMinutes": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseContentDTO](#schemaresponseentitycoursecontentdto)|

## PATCH 部分更新课程内容的指定字段，忽略空值字段

PATCH /whiskerguardtrainingservice/api/course/contents/id

{@code PATCH  /course/contents/:id} : Partial updates given fields of an existing courseContent, field will ignore if it is null

> Body 请求参数

```json
{
  "id": 0,
  "courseId": "string",
  "contentType": "string",
  "contentTitle": "string",
  "contentDescription": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseContentDTO](#schemacoursecontentdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "courseId": "",
  "contentType": "",
  "contentTitle": "",
  "contentDescription": "",
  "contentUrl": "",
  "durationMinutes": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseContentDTO](#schemaresponseentitycoursecontentdto)|

## GET 根据ID获取课程内容

GET /whiskerguardtrainingservice/api/course/contents/id

{@code GET  /course/contents/:id} : get the "id" courseContent.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "courseId": "",
  "contentType": "",
  "contentTitle": "",
  "contentDescription": "",
  "contentUrl": "",
  "durationMinutes": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseContentDTO](#schemaresponseentitycoursecontentdto)|

## DELETE 根据ID逻辑删除课程内容

DELETE /whiskerguardtrainingservice/api/course/contents/id

{@code DELETE  /course/contents/:id} : logically delete the "id" courseContent.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获取所有课程内容

GET /api/course/contents

{@code GET  /course/contents} : get all the courseContents.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|filter|query|string| 否 |请求的过滤条件|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*状态为 {@code 200 (OK)} 的响应实体，包含课程内容列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListCourseContentDTO](#schemalistcoursecontentdto)]|false|none||状态为 {@code 200 (OK)} 的响应实体，包含课程内容列表|
|» id|integer(int64)|true|none||none|
|» courseId|string|true|none||外键关联 CourseInfo|
|» contentType|string|false|none||内容类型|
|» contentTitle|string|false|none||内容标题|
|» contentDescription|string|false|none||内容描述|
|» contentUrl|string|false|none||内容链接（如视频/文档地址）|
|» durationMinutes|integer|false|none||内容时长（分钟）|
|» metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|» version|integer|false|none||当前版本号|
|» createdBy|string|false|none||创建者账号或姓名|
|» createdAt|object|false|none||创建时间（由系统自动填充）|
|» updatedBy|string|false|none||最后修改者账号或姓名|
|» updatedAt|object|false|none||创建时间（由系统自动填充）|
|» isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

## POST 根据查询条件获取课程内容信息

POST /whiskerguardtrainingservice/api/course/contents/query

{@code POST  /course/contents/query} : query courseContents by criteria.

> Body 请求参数

```json
{
  "courseId": "string",
  "contentTitle": "string",
  "contentType": "string",
  "chapterNumber": 0,
  "isDeleted": false,
  "searchTerm": "string",
  "courseInfoIsNull": false,
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseContentCriteria](#schemacoursecontentcriteria)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "empty": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageCourseContentDTO](#schemaresponseentitypagecoursecontentdto)|

# 18-合规培训服务/相关课程管理

## POST 创建新的相关课程

POST /whiskerguardtrainingservice/api/related/courses

{@code POST  /related/courses} : Create a new relatedCourse.

> Body 请求参数

```json
{
  "id": 0,
  "relatedCourseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfos": "new HashSet<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[RelatedCourseDTO](#schemarelatedcoursedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "relatedCourseId": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfos": [
    {
      "id": 0,
      "tenantId": 0,
      "courseCode": "",
      "courseName": "",
      "courseType": "",
      "trainingTheme": "",
      "difficultyLevel": "",
      "applicableRole": "",
      "instructor": "",
      "producer": "",
      "releaseDate": {
        "seconds": 0,
        "nanos": 0
      },
      "lastUpdate": {
        "seconds": 0,
        "nanos": 0
      },
      "status": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseDescription": {
        "id": 0,
        "courseId": "",
        "courseOverview": "",
        "learningObjective": "",
        "prerequisites": "",
        "certificationInfo": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "courseContent": {
        "id": 0,
        "courseId": "",
        "contentType": "",
        "contentTitle": "",
        "contentDescription": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "playbackSetting": {
        "id": 0,
        "courseId": "",
        "autoPlay": false,
        "rememberPlayback": false,
        "continuousPlayback": false,
        "defaultClarity": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "interactiveFeature": {
        "id": 0,
        "courseId": "",
        "commentEnabled": false,
        "likeEnabled": false,
        "shareEnabled": false,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "relatedCourses": [
        {
          "id": 0,
          "relatedCourseId": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseInfos": [
            {
              "id": 0,
              "tenantId": 0,
              "courseCode": "",
              "courseName": "",
              "courseType": "",
              "trainingTheme": "",
              "difficultyLevel": "",
              "applicableRole": "",
              "instructor": "",
              "producer": "",
              "releaseDate": {
                "seconds": 0,
                "nanos": 0
              },
              "lastUpdate": {
                "seconds": 0,
                "nanos": 0
              },
              "status": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseDescription": {
                "id": 0,
                "courseId": "",
                "courseOverview": "",
                "learningObjective": "",
                "prerequisites": "",
                "certificationInfo": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "courseContent": {
                "id": 0,
                "courseId": "",
                "contentType": "",
                "contentTitle": "",
                "contentDescription": "",
                "contentUrl": "",
                "durationMinutes": 0,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "playbackSetting": {
                "id": 0,
                "courseId": "",
                "autoPlay": false,
                "rememberPlayback": false,
                "continuousPlayback": false,
                "defaultClarity": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "interactiveFeature": {
                "id": 0,
                "courseId": "",
                "commentEnabled": false,
                "likeEnabled": false,
                "shareEnabled": false,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "relatedCourses": [
                {
                  "id": 0,
                  "relatedCourseId": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseInfos": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityRelatedCourseDTO](#schemaresponseentityrelatedcoursedto)|

## PUT 更新现有的相关课程

PUT /whiskerguardtrainingservice/api/related/courses/{id}

{@code PUT  /related/courses/:id} : Updates an existing relatedCourse.

> Body 请求参数

```json
{
  "id": 0,
  "relatedCourseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfos": "new HashSet<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要保存的相关课程DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[RelatedCourseDTO](#schemarelatedcoursedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "relatedCourseId": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfos": [
    {
      "id": 0,
      "tenantId": 0,
      "courseCode": "",
      "courseName": "",
      "courseType": "",
      "trainingTheme": "",
      "difficultyLevel": "",
      "applicableRole": "",
      "instructor": "",
      "producer": "",
      "releaseDate": {
        "seconds": 0,
        "nanos": 0
      },
      "lastUpdate": {
        "seconds": 0,
        "nanos": 0
      },
      "status": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseDescription": {
        "id": 0,
        "courseId": "",
        "courseOverview": "",
        "learningObjective": "",
        "prerequisites": "",
        "certificationInfo": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "courseContent": {
        "id": 0,
        "courseId": "",
        "contentType": "",
        "contentTitle": "",
        "contentDescription": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "playbackSetting": {
        "id": 0,
        "courseId": "",
        "autoPlay": false,
        "rememberPlayback": false,
        "continuousPlayback": false,
        "defaultClarity": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "interactiveFeature": {
        "id": 0,
        "courseId": "",
        "commentEnabled": false,
        "likeEnabled": false,
        "shareEnabled": false,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "relatedCourses": [
        {
          "id": 0,
          "relatedCourseId": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseInfos": [
            {
              "id": 0,
              "tenantId": 0,
              "courseCode": "",
              "courseName": "",
              "courseType": "",
              "trainingTheme": "",
              "difficultyLevel": "",
              "applicableRole": "",
              "instructor": "",
              "producer": "",
              "releaseDate": {
                "seconds": 0,
                "nanos": 0
              },
              "lastUpdate": {
                "seconds": 0,
                "nanos": 0
              },
              "status": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseDescription": {
                "id": 0,
                "courseId": "",
                "courseOverview": "",
                "learningObjective": "",
                "prerequisites": "",
                "certificationInfo": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "courseContent": {
                "id": 0,
                "courseId": "",
                "contentType": "",
                "contentTitle": "",
                "contentDescription": "",
                "contentUrl": "",
                "durationMinutes": 0,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "playbackSetting": {
                "id": 0,
                "courseId": "",
                "autoPlay": false,
                "rememberPlayback": false,
                "continuousPlayback": false,
                "defaultClarity": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "interactiveFeature": {
                "id": 0,
                "courseId": "",
                "commentEnabled": false,
                "likeEnabled": false,
                "shareEnabled": false,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "relatedCourses": [
                {
                  "id": 0,
                  "relatedCourseId": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseInfos": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityRelatedCourseDTO](#schemaresponseentityrelatedcoursedto)|

## PATCH 部分更新相关课程的指定字段，忽略空值字段

PATCH /whiskerguardtrainingservice/api/related/courses/id

{@code PATCH  /related/courses/:id} : Partial updates given fields of an existing relatedCourse, field will ignore if it is null

> Body 请求参数

```json
{
  "id": 0,
  "relatedCourseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfos": "new HashSet<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[RelatedCourseDTO](#schemarelatedcoursedto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "relatedCourseId": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfos": [
    {
      "id": 0,
      "tenantId": 0,
      "courseCode": "",
      "courseName": "",
      "courseType": "",
      "trainingTheme": "",
      "difficultyLevel": "",
      "applicableRole": "",
      "instructor": "",
      "producer": "",
      "releaseDate": {
        "seconds": 0,
        "nanos": 0
      },
      "lastUpdate": {
        "seconds": 0,
        "nanos": 0
      },
      "status": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseDescription": {
        "id": 0,
        "courseId": "",
        "courseOverview": "",
        "learningObjective": "",
        "prerequisites": "",
        "certificationInfo": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "courseContent": {
        "id": 0,
        "courseId": "",
        "contentType": "",
        "contentTitle": "",
        "contentDescription": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "playbackSetting": {
        "id": 0,
        "courseId": "",
        "autoPlay": false,
        "rememberPlayback": false,
        "continuousPlayback": false,
        "defaultClarity": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "interactiveFeature": {
        "id": 0,
        "courseId": "",
        "commentEnabled": false,
        "likeEnabled": false,
        "shareEnabled": false,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "relatedCourses": [
        {
          "id": 0,
          "relatedCourseId": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseInfos": [
            {
              "id": 0,
              "tenantId": 0,
              "courseCode": "",
              "courseName": "",
              "courseType": "",
              "trainingTheme": "",
              "difficultyLevel": "",
              "applicableRole": "",
              "instructor": "",
              "producer": "",
              "releaseDate": {
                "seconds": 0,
                "nanos": 0
              },
              "lastUpdate": {
                "seconds": 0,
                "nanos": 0
              },
              "status": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseDescription": {
                "id": 0,
                "courseId": "",
                "courseOverview": "",
                "learningObjective": "",
                "prerequisites": "",
                "certificationInfo": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "courseContent": {
                "id": 0,
                "courseId": "",
                "contentType": "",
                "contentTitle": "",
                "contentDescription": "",
                "contentUrl": "",
                "durationMinutes": 0,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "playbackSetting": {
                "id": 0,
                "courseId": "",
                "autoPlay": false,
                "rememberPlayback": false,
                "continuousPlayback": false,
                "defaultClarity": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "interactiveFeature": {
                "id": 0,
                "courseId": "",
                "commentEnabled": false,
                "likeEnabled": false,
                "shareEnabled": false,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "relatedCourses": [
                {
                  "id": 0,
                  "relatedCourseId": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseInfos": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityRelatedCourseDTO](#schemaresponseentityrelatedcoursedto)|

## GET 根据ID获取相关课程

GET /whiskerguardtrainingservice/api/related/courses/id

{@code GET  /related/courses/:id} : get the "id" relatedCourse.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "relatedCourseId": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfos": [
    {
      "id": 0,
      "tenantId": 0,
      "courseCode": "",
      "courseName": "",
      "courseType": "",
      "trainingTheme": "",
      "difficultyLevel": "",
      "applicableRole": "",
      "instructor": "",
      "producer": "",
      "releaseDate": {
        "seconds": 0,
        "nanos": 0
      },
      "lastUpdate": {
        "seconds": 0,
        "nanos": 0
      },
      "status": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseDescription": {
        "id": 0,
        "courseId": "",
        "courseOverview": "",
        "learningObjective": "",
        "prerequisites": "",
        "certificationInfo": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "courseContent": {
        "id": 0,
        "courseId": "",
        "contentType": "",
        "contentTitle": "",
        "contentDescription": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "playbackSetting": {
        "id": 0,
        "courseId": "",
        "autoPlay": false,
        "rememberPlayback": false,
        "continuousPlayback": false,
        "defaultClarity": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "interactiveFeature": {
        "id": 0,
        "courseId": "",
        "commentEnabled": false,
        "likeEnabled": false,
        "shareEnabled": false,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "relatedCourses": [
        {
          "id": 0,
          "relatedCourseId": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseInfos": [
            {
              "id": 0,
              "tenantId": 0,
              "courseCode": "",
              "courseName": "",
              "courseType": "",
              "trainingTheme": "",
              "difficultyLevel": "",
              "applicableRole": "",
              "instructor": "",
              "producer": "",
              "releaseDate": {
                "seconds": 0,
                "nanos": 0
              },
              "lastUpdate": {
                "seconds": 0,
                "nanos": 0
              },
              "status": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseDescription": {
                "id": 0,
                "courseId": "",
                "courseOverview": "",
                "learningObjective": "",
                "prerequisites": "",
                "certificationInfo": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "courseContent": {
                "id": 0,
                "courseId": "",
                "contentType": "",
                "contentTitle": "",
                "contentDescription": "",
                "contentUrl": "",
                "durationMinutes": 0,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "playbackSetting": {
                "id": 0,
                "courseId": "",
                "autoPlay": false,
                "rememberPlayback": false,
                "continuousPlayback": false,
                "defaultClarity": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "interactiveFeature": {
                "id": 0,
                "courseId": "",
                "commentEnabled": false,
                "likeEnabled": false,
                "shareEnabled": false,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "relatedCourses": [
                {
                  "id": 0,
                  "relatedCourseId": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseInfos": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityRelatedCourseDTO](#schemaresponseentityrelatedcoursedto)|

## DELETE 根据ID逻辑删除相关课程

DELETE /whiskerguardtrainingservice/api/related/courses/id

{@code DELETE  /related/courses/:id} : logically delete the "id" relatedCourse.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获取所有相关课程

GET /api/related/courses

{@code GET  /related/courses} : get all the relatedCourses.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|eagerload|query|boolean| 否 |是否急切加载关系实体的标志（适用于多对多关系）|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "relatedCourseId": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseInfos": [
      {
        "id": 0,
        "tenantId": 0,
        "courseCode": "",
        "courseName": "",
        "courseType": "",
        "trainingTheme": "",
        "difficultyLevel": "",
        "applicableRole": "",
        "instructor": "",
        "producer": "",
        "releaseDate": {
          "seconds": 0,
          "nanos": 0
        },
        "lastUpdate": {
          "seconds": 0,
          "nanos": 0
        },
        "status": "",
        "durationMinutes": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseDescription": {
          "id": 0,
          "courseId": "",
          "courseOverview": "",
          "learningObjective": "",
          "prerequisites": "",
          "certificationInfo": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false
        },
        "courseContent": {
          "id": 0,
          "courseId": "",
          "contentType": "",
          "contentTitle": "",
          "contentDescription": "",
          "contentUrl": "",
          "durationMinutes": 0,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false
        },
        "playbackSetting": {
          "id": 0,
          "courseId": "",
          "autoPlay": false,
          "rememberPlayback": false,
          "continuousPlayback": false,
          "defaultClarity": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false
        },
        "interactiveFeature": {
          "id": 0,
          "courseId": "",
          "commentEnabled": false,
          "likeEnabled": false,
          "shareEnabled": false,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false
        },
        "relatedCourses": [
          {
            "id": 0,
            "relatedCourseId": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "courseInfos": [
              {
                "id": 0,
                "tenantId": 0,
                "courseCode": "",
                "courseName": "",
                "courseType": "",
                "trainingTheme": "",
                "difficultyLevel": "",
                "applicableRole": "",
                "instructor": "",
                "producer": "",
                "releaseDate": {
                  "seconds": 0,
                  "nanos": 0
                },
                "lastUpdate": {
                  "seconds": 0,
                  "nanos": 0
                },
                "status": "",
                "durationMinutes": 0,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseDescription": {
                  "id": 0,
                  "courseId": "",
                  "courseOverview": "",
                  "learningObjective": "",
                  "prerequisites": "",
                  "certificationInfo": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false
                },
                "courseContent": {
                  "id": 0,
                  "courseId": "",
                  "contentType": "",
                  "contentTitle": "",
                  "contentDescription": "",
                  "contentUrl": "",
                  "durationMinutes": 0,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false
                },
                "playbackSetting": {
                  "id": 0,
                  "courseId": "",
                  "autoPlay": false,
                  "rememberPlayback": false,
                  "continuousPlayback": false,
                  "defaultClarity": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false
                },
                "interactiveFeature": {
                  "id": 0,
                  "courseId": "",
                  "commentEnabled": false,
                  "likeEnabled": false,
                  "shareEnabled": false,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false
                },
                "relatedCourses": [
                  {
                    "id": 0,
                    "relatedCourseId": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false,
                    "courseInfos": "[Object]"
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*状态为 {@code 200 (OK)} 的响应实体，包含相关课程列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListRelatedCourseDTO](#schemalistrelatedcoursedto)]|false|none||状态为 {@code 200 (OK)} 的响应实体，包含相关课程列表|
|» id|integer(int64)|true|none||none|
|» relatedCourseId|string|true|none||外键关联 CourseInfo|
|» metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|» version|integer|false|none||当前版本号|
|» createdBy|string|false|none||创建者账号或姓名|
|» createdAt|object|false|none||创建时间（由系统自动填充）|
|» updatedBy|string|false|none||最后修改者账号或姓名|
|» updatedAt|object|false|none||创建时间（由系统自动填充）|
|» isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|» courseInfos|[object]|false|none||none|

## POST 根据查询条件获取相关课程信息

POST /whiskerguardtrainingservice/api/related/courses/query

{@code POST  /related/courses/query} : query relatedCourses by criteria.

> Body 请求参数

```json
{
  "courseId": "string",
  "relationType": "string",
  "relationDescription": "string",
  "isDeleted": false,
  "searchTerm": "string",
  "eagerload": false,
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[RelatedCourseCriteria](#schemarelatedcoursecriteria)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "relatedCourseId": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false,
      "courseInfos": [
        {
          "id": 0,
          "tenantId": 0,
          "courseCode": "",
          "courseName": "",
          "courseType": "",
          "trainingTheme": "",
          "difficultyLevel": "",
          "applicableRole": "",
          "instructor": "",
          "producer": "",
          "releaseDate": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "lastUpdate": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "status": "",
          "durationMinutes": 0,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false,
          "courseDescription": {
            "id": 0,
            "courseId": "",
            "courseOverview": "",
            "learningObjective": "",
            "prerequisites": "",
            "certificationInfo": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "isDeleted": false
          },
          "courseContent": {
            "id": 0,
            "courseId": "",
            "contentType": "",
            "contentTitle": "",
            "contentDescription": "",
            "contentUrl": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "isDeleted": false
          },
          "playbackSetting": {
            "id": 0,
            "courseId": "",
            "autoPlay": false,
            "rememberPlayback": false,
            "continuousPlayback": false,
            "defaultClarity": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "isDeleted": false
          },
          "interactiveFeature": {
            "id": 0,
            "courseId": "",
            "commentEnabled": false,
            "likeEnabled": false,
            "shareEnabled": false,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "isDeleted": false
          },
          "relatedCourses": [
            {
              "id": 0,
              "relatedCourseId": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0,
                "epochSecond": 0,
                "nano": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0,
                "epochSecond": 0,
                "nano": 0
              },
              "isDeleted": false,
              "courseInfos": [
                {
                  "id": 0,
                  "tenantId": 0,
                  "courseCode": "",
                  "courseName": "",
                  "courseType": "",
                  "trainingTheme": "",
                  "difficultyLevel": "",
                  "applicableRole": "",
                  "instructor": "",
                  "producer": "",
                  "releaseDate": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "lastUpdate": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "status": "",
                  "durationMinutes": 0,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "isDeleted": false,
                  "courseDescription": {
                    "id": 0,
                    "courseId": "",
                    "courseOverview": "",
                    "learningObjective": "",
                    "prerequisites": "",
                    "certificationInfo": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "courseContent": {
                    "id": 0,
                    "courseId": "",
                    "contentType": "",
                    "contentTitle": "",
                    "contentDescription": "",
                    "contentUrl": "",
                    "durationMinutes": 0,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "playbackSetting": {
                    "id": 0,
                    "courseId": "",
                    "autoPlay": false,
                    "rememberPlayback": false,
                    "continuousPlayback": false,
                    "defaultClarity": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "interactiveFeature": {
                    "id": 0,
                    "courseId": "",
                    "commentEnabled": false,
                    "likeEnabled": false,
                    "shareEnabled": false,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "relatedCourses": [
                    "[Object]"
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "empty": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageRelatedCourseDTO](#schemaresponseentitypagerelatedcoursedto)|

# 18-合规培训服务/课程标签管理

## POST 添加课程相关标签

POST /whiskerguardtrainingservice/api/tag/managements

> Body 请求参数

```json
{
  "id": 0,
  "tagName": "string",
  "courseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfo": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[TagManagementDTO](#schematagmanagementdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tagName": "",
  "courseId": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfo": {
    "id": 0,
    "tenantId": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseDescription": {
      "id": 0,
      "courseId": "",
      "courseOverview": "",
      "learningObjective": "",
      "prerequisites": "",
      "certificationInfo": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "tenantId": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "courseDescription": {
              "id": 0,
              "courseId": "",
              "courseOverview": "",
              "learningObjective": "",
              "prerequisites": "",
              "certificationInfo": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityTagManagementDTO](#schemaresponseentitytagmanagementdto)|

## PUT 根据ID编辑已有标签

PUT /whiskerguardtrainingservice/api/tag/managements/{id}

> Body 请求参数

```json
{
  "id": 0,
  "tagName": "string",
  "courseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfo": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |the id of the tagManagementDTO to save.|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[TagManagementDTO](#schematagmanagementdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tagName": "",
  "courseId": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfo": {
    "id": 0,
    "tenantId": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseDescription": {
      "id": 0,
      "courseId": "",
      "courseOverview": "",
      "learningObjective": "",
      "prerequisites": "",
      "certificationInfo": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "tenantId": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "courseDescription": {
              "id": 0,
              "courseId": "",
              "courseOverview": "",
              "learningObjective": "",
              "prerequisites": "",
              "certificationInfo": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityTagManagementDTO](#schemaresponseentitytagmanagementdto)|

## PATCH 根据ID部分更新已有标签

PATCH /whiskerguardtrainingservice/api/tag/managements/id

> Body 请求参数

```json
{
  "id": 0,
  "tagName": "string",
  "courseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfo": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[TagManagementDTO](#schematagmanagementdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tagName": "",
  "courseId": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfo": {
    "id": 0,
    "tenantId": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseDescription": {
      "id": 0,
      "courseId": "",
      "courseOverview": "",
      "learningObjective": "",
      "prerequisites": "",
      "certificationInfo": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "tenantId": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "courseDescription": {
              "id": 0,
              "courseId": "",
              "courseOverview": "",
              "learningObjective": "",
              "prerequisites": "",
              "certificationInfo": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityTagManagementDTO](#schemaresponseentitytagmanagementdto)|

## GET 根据ID获取单个标签

GET /whiskerguardtrainingservice/api/tag/managements/id

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tagName": "",
  "courseId": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfo": {
    "id": 0,
    "tenantId": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseDescription": {
      "id": 0,
      "courseId": "",
      "courseOverview": "",
      "learningObjective": "",
      "prerequisites": "",
      "certificationInfo": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "tenantId": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "courseDescription": {
              "id": 0,
              "courseId": "",
              "courseOverview": "",
              "learningObjective": "",
              "prerequisites": "",
              "certificationInfo": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityTagManagementDTO](#schemaresponseentitytagmanagementdto)|

## DELETE 根据ID逻辑删除标签

DELETE /whiskerguardtrainingservice/api/tag/managements/id

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 根据查询条件获取标签信息

POST /whiskerguardtrainingservice/api/tag/managements/query

{@code POST  /tag/managements/query} : query tagManagements by criteria.

> Body 请求参数

```json
{
  "tagName": "string",
  "tagType": "string",
  "tagDescription": "string",
  "courseId": "string",
  "createdAtMin": {
    "seconds": 0,
    "nanos": 0
  },
  "createdAtMax": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "searchTerm": "string",
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[TagManagementCriteria](#schematagmanagementcriteria)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "tagName": "",
      "courseId": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false,
      "courseInfo": {
        "id": 0,
        "tenantId": 0,
        "courseCode": "",
        "courseName": "",
        "courseType": "",
        "trainingTheme": "",
        "difficultyLevel": "",
        "applicableRole": "",
        "instructor": "",
        "producer": "",
        "releaseDate": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "lastUpdate": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "status": "",
        "durationMinutes": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "isDeleted": false,
        "courseDescription": {
          "id": 0,
          "courseId": "",
          "courseOverview": "",
          "learningObjective": "",
          "prerequisites": "",
          "certificationInfo": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        },
        "courseContent": {
          "id": 0,
          "courseId": "",
          "contentType": "",
          "contentTitle": "",
          "contentDescription": "",
          "contentUrl": "",
          "durationMinutes": 0,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        },
        "playbackSetting": {
          "id": 0,
          "courseId": "",
          "autoPlay": false,
          "rememberPlayback": false,
          "continuousPlayback": false,
          "defaultClarity": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        },
        "interactiveFeature": {
          "id": 0,
          "courseId": "",
          "commentEnabled": false,
          "likeEnabled": false,
          "shareEnabled": false,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        },
        "relatedCourses": [
          {
            "id": 0,
            "relatedCourseId": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0,
              "epochSecond": 0,
              "nano": 0
            },
            "isDeleted": false,
            "courseInfos": [
              {
                "id": 0,
                "tenantId": 0,
                "courseCode": "",
                "courseName": "",
                "courseType": "",
                "trainingTheme": "",
                "difficultyLevel": "",
                "applicableRole": "",
                "instructor": "",
                "producer": "",
                "releaseDate": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "lastUpdate": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "status": "",
                "durationMinutes": 0,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0,
                  "epochSecond": 0,
                  "nano": 0
                },
                "isDeleted": false,
                "courseDescription": {
                  "id": 0,
                  "courseId": "",
                  "courseOverview": "",
                  "learningObjective": "",
                  "prerequisites": "",
                  "certificationInfo": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "isDeleted": false
                },
                "courseContent": {
                  "id": 0,
                  "courseId": "",
                  "contentType": "",
                  "contentTitle": "",
                  "contentDescription": "",
                  "contentUrl": "",
                  "durationMinutes": 0,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "isDeleted": false
                },
                "playbackSetting": {
                  "id": 0,
                  "courseId": "",
                  "autoPlay": false,
                  "rememberPlayback": false,
                  "continuousPlayback": false,
                  "defaultClarity": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "isDeleted": false
                },
                "interactiveFeature": {
                  "id": 0,
                  "courseId": "",
                  "commentEnabled": false,
                  "likeEnabled": false,
                  "shareEnabled": false,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0,
                    "epochSecond": 0,
                    "nano": 0
                  },
                  "isDeleted": false
                },
                "relatedCourses": [
                  {
                    "id": 0,
                    "relatedCourseId": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false,
                    "courseInfos": "[Object]"
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "empty": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageTagManagementDTO](#schemaresponseentitypagetagmanagementdto)|

## GET 根据课程ID获取对应的所有标签

GET /whiskerguardtrainingservice/api/tag/managements/course/courseId

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "tagName": "",
    "courseId": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseInfo": {
      "id": 0,
      "tenantId": 0,
      "courseCode": "",
      "courseName": "",
      "courseType": "",
      "trainingTheme": "",
      "difficultyLevel": "",
      "applicableRole": "",
      "instructor": "",
      "producer": "",
      "releaseDate": {
        "seconds": 0,
        "nanos": 0
      },
      "lastUpdate": {
        "seconds": 0,
        "nanos": 0
      },
      "status": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseDescription": {
        "id": 0,
        "courseId": "",
        "courseOverview": "",
        "learningObjective": "",
        "prerequisites": "",
        "certificationInfo": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "courseContent": {
        "id": 0,
        "courseId": "",
        "contentType": "",
        "contentTitle": "",
        "contentDescription": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "playbackSetting": {
        "id": 0,
        "courseId": "",
        "autoPlay": false,
        "rememberPlayback": false,
        "continuousPlayback": false,
        "defaultClarity": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "interactiveFeature": {
        "id": 0,
        "courseId": "",
        "commentEnabled": false,
        "likeEnabled": false,
        "shareEnabled": false,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "relatedCourses": [
        {
          "id": 0,
          "relatedCourseId": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseInfos": [
            {
              "id": 0,
              "tenantId": 0,
              "courseCode": "",
              "courseName": "",
              "courseType": "",
              "trainingTheme": "",
              "difficultyLevel": "",
              "applicableRole": "",
              "instructor": "",
              "producer": "",
              "releaseDate": {
                "seconds": 0,
                "nanos": 0
              },
              "lastUpdate": {
                "seconds": 0,
                "nanos": 0
              },
              "status": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseDescription": {
                "id": 0,
                "courseId": "",
                "courseOverview": "",
                "learningObjective": "",
                "prerequisites": "",
                "certificationInfo": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "courseContent": {
                "id": 0,
                "courseId": "",
                "contentType": "",
                "contentTitle": "",
                "contentDescription": "",
                "contentUrl": "",
                "durationMinutes": 0,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "playbackSetting": {
                "id": 0,
                "courseId": "",
                "autoPlay": false,
                "rememberPlayback": false,
                "continuousPlayback": false,
                "defaultClarity": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "interactiveFeature": {
                "id": 0,
                "courseId": "",
                "commentEnabled": false,
                "likeEnabled": false,
                "shareEnabled": false,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "relatedCourses": [
                {
                  "id": 0,
                  "relatedCourseId": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseInfos": [
                    "[Object]"
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*the [Extension of{@link HttpEntity} that adds an{@link HttpStatusCode} status code.
Used in{@code RestTemplate} as well as in{@code @Controller} methods.

<p>In{@code RestTemplate}, this class is returned by
{@link org.springframework.web.client.RestTemplate#getForEntity getForEntity()} and
{@link org.springframework.web.client.RestTemplate#exchange exchange()}:
<pre class="code">
ResponseEntity&lt;String&gt; entity = template.getForEntity("https://example.com", String.class);
String body = entity.getBody();
MediaType contentType = entity.getHeaders().getContentType();
HttpStatus statusCode = entity.getStatusCode();
</pre>

<p>This can also be used in Spring MVC as the return value from an
{@code @Controller} method:
<pre class="code">
&#64;RequestMapping("/handle")
public ResponseEntity&lt;String&gt; handle(){
  URI location = ...;
  HttpHeaders responseHeaders = new HttpHeaders();
  responseHeaders.setLocation(location);
  responseHeaders.set("MyResponseHeader", "MyValue");
  return new ResponseEntity&lt;String&gt;("Hello World", responseHeaders, HttpStatus.CREATED);
}
</pre>

Or, by using a builder accessible via static methods:
<pre class="code">
&#64;RequestMapping("/handle")
public ResponseEntity&lt;String&gt; handle(){
  URI location = ...;
  return ResponseEntity.created(location).header("MyResponseHeader", "MyValue").body("Hello World");
}
</pre>] with status {@code 200 (OK)} and the list of tagManagements in body.*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListTagManagementDTO](#schemalisttagmanagementdto)]|false|none||the [Extension of{@link HttpEntity} that adds an{@link HttpStatusCode} status code.<br />Used in{@code RestTemplate} as well as in{@code @Controller} methods.<br /><br /><p>In{@code RestTemplate}, this class is returned by<br />{@link org.springframework.web.client.RestTemplate#getForEntity getForEntity()} and<br />{@link org.springframework.web.client.RestTemplate#exchange exchange()}:<br /><pre class="code"><br />ResponseEntity&lt;String&gt; entity = template.getForEntity("https://example.com", String.class);<br />String body = entity.getBody();<br />MediaType contentType = entity.getHeaders().getContentType();<br />HttpStatus statusCode = entity.getStatusCode();<br /></pre><br /><br /><p>This can also be used in Spring MVC as the return value from an<br />{@code @Controller} method:<br /><pre class="code"><br />&#64;RequestMapping("/handle")<br />public ResponseEntity&lt;String&gt; handle(){<br />  URI location = ...;<br />  HttpHeaders responseHeaders = new HttpHeaders();<br />  responseHeaders.setLocation(location);<br />  responseHeaders.set("MyResponseHeader", "MyValue");<br />  return new ResponseEntity&lt;String&gt;("Hello World", responseHeaders, HttpStatus.CREATED);<br />}<br /></pre><br /><br />Or, by using a builder accessible via static methods:<br /><pre class="code"><br />&#64;RequestMapping("/handle")<br />public ResponseEntity&lt;String&gt; handle(){<br />  URI location = ...;<br />  return ResponseEntity.created(location).header("MyResponseHeader", "MyValue").body("Hello World");<br />}<br /></pre>] with status {@code 200 (OK)} and the list of tagManagements in body.|
|» id|integer(int64)|true|none||none|
|» tagName|string|true|none||标签名称（如“性能优化”）|
|» courseId|string|true|none||外键关联 CourseInfo|
|» metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|» version|integer|false|none||当前版本号|
|» createdBy|string|false|none||创建者账号或姓名|
|» createdAt|object|false|none||创建时间（由系统自动填充）|
|» updatedBy|string|false|none||最后修改者账号或姓名|
|» updatedAt|object|false|none||创建时间（由系统自动填充）|
|» isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|» courseInfo|object|false|none||创建时间（由系统自动填充）|

# 18-合规培训服务/课程附件管理

## POST 创建新的课程附件

POST /whiskerguardtrainingservice/api/course/attachments

{@code POST  /course/attachments} : Create a new courseAttachment.

> Body 请求参数

```json
{
  "id": 0,
  "courseId": "string",
  "contentId": "string",
  "fileId": "string",
  "fileType": "string",
  "filePath": "string",
  "fileName": "string",
  "fileSize": "string",
  "uploadTime": {
    "dateTime": "string",
    "offset": {},
    "zone": {}
  },
  "description": null,
  "metadata": null,
  "version": null,
  "createdBy": null,
  "createdAt": null,
  "updatedBy": null,
  "updatedAt": null,
  "isDeleted": null,
  "courseInfo": null,
  "courseContent": null
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseAttachmentDTO](#schemacourseattachmentdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "courseId": "",
  "contentId": "",
  "fileId": "",
  "fileType": "",
  "filePath": "",
  "fileName": "",
  "fileSize": "",
  "uploadTime": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "description": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfo": {
    "id": 0,
    "tenantId": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseDescription": {
      "id": 0,
      "courseId": "",
      "courseOverview": "",
      "learningObjective": "",
      "prerequisites": "",
      "certificationInfo": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "tenantId": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "courseDescription": {
              "id": 0,
              "courseId": "",
              "courseOverview": "",
              "learningObjective": "",
              "prerequisites": "",
              "certificationInfo": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  "courseContent": {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseAttachmentDTO](#schemaresponseentitycourseattachmentdto)|

## PUT 更新现有的课程附件

PUT /whiskerguardtrainingservice/api/course/attachments/{id}

{@code PUT  /course/attachments/:id} : Updates an existing courseAttachment.

> Body 请求参数

```json
{
  "id": 0,
  "courseId": "string",
  "contentId": "string",
  "fileId": "string",
  "fileType": "string",
  "filePath": "string",
  "fileName": "string",
  "fileSize": "string",
  "uploadTime": {
    "dateTime": "string",
    "offset": {},
    "zone": {}
  },
  "description": null,
  "metadata": null,
  "version": null,
  "createdBy": null,
  "createdAt": null,
  "updatedBy": null,
  "updatedAt": null,
  "isDeleted": null,
  "courseInfo": null,
  "courseContent": null
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要保存的课程附件DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseAttachmentDTO](#schemacourseattachmentdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "courseId": "",
  "contentId": "",
  "fileId": "",
  "fileType": "",
  "filePath": "",
  "fileName": "",
  "fileSize": "",
  "uploadTime": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "description": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfo": {
    "id": 0,
    "tenantId": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseDescription": {
      "id": 0,
      "courseId": "",
      "courseOverview": "",
      "learningObjective": "",
      "prerequisites": "",
      "certificationInfo": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "tenantId": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "courseDescription": {
              "id": 0,
              "courseId": "",
              "courseOverview": "",
              "learningObjective": "",
              "prerequisites": "",
              "certificationInfo": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  "courseContent": {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseAttachmentDTO](#schemaresponseentitycourseattachmentdto)|

## PATCH 部分更新课程附件的指定字段，忽略空值字段

PATCH /whiskerguardtrainingservice/api/course/attachments/id

{@code PATCH  /course/attachments/:id} : Partial updates given fields of an existing courseAttachment, field will ignore if it is null

> Body 请求参数

```json
{
  "id": 0,
  "courseId": "string",
  "contentId": "string",
  "fileId": "string",
  "fileType": "string",
  "filePath": "string",
  "fileName": "string",
  "fileSize": "string",
  "uploadTime": {
    "dateTime": "string",
    "offset": {},
    "zone": {}
  },
  "description": null,
  "metadata": null,
  "version": null,
  "createdBy": null,
  "createdAt": null,
  "updatedBy": null,
  "updatedAt": null,
  "isDeleted": null,
  "courseInfo": null,
  "courseContent": null
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseAttachmentDTO](#schemacourseattachmentdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "courseId": "",
  "contentId": "",
  "fileId": "",
  "fileType": "",
  "filePath": "",
  "fileName": "",
  "fileSize": "",
  "uploadTime": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "description": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfo": {
    "id": 0,
    "tenantId": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseDescription": {
      "id": 0,
      "courseId": "",
      "courseOverview": "",
      "learningObjective": "",
      "prerequisites": "",
      "certificationInfo": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "tenantId": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "courseDescription": {
              "id": 0,
              "courseId": "",
              "courseOverview": "",
              "learningObjective": "",
              "prerequisites": "",
              "certificationInfo": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  "courseContent": {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseAttachmentDTO](#schemaresponseentitycourseattachmentdto)|

## GET 根据ID获取课程附件

GET /whiskerguardtrainingservice/api/course/attachments/id

{@code GET  /course/attachments/:id} : get the "id" courseAttachment.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "courseId": "",
  "contentId": "",
  "fileId": "",
  "fileType": "",
  "filePath": "",
  "fileName": "",
  "fileSize": "",
  "uploadTime": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "description": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfo": {
    "id": 0,
    "tenantId": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseDescription": {
      "id": 0,
      "courseId": "",
      "courseOverview": "",
      "learningObjective": "",
      "prerequisites": "",
      "certificationInfo": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "tenantId": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "courseDescription": {
              "id": 0,
              "courseId": "",
              "courseOverview": "",
              "learningObjective": "",
              "prerequisites": "",
              "certificationInfo": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  "courseContent": {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseAttachmentDTO](#schemaresponseentitycourseattachmentdto)|

## DELETE 根据ID逻辑删除课程附件

DELETE /whiskerguardtrainingservice/api/course/attachments/id

{@code DELETE  /course/attachments/:id} : logically delete the "id" courseAttachment.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获取所有课程附件

GET /api/course/attachments

{@code GET  /course/attachments} : get all the courseAttachments.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "courseId": "",
    "contentId": "",
    "fileId": "",
    "fileType": "",
    "filePath": "",
    "fileName": "",
    "fileSize": "",
    "uploadTime": {
      "dateTime": "",
      "offset": {
        "totalSeconds": 0
      },
      "zone": {}
    },
    "description": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "courseInfo": {
      "id": 0,
      "tenantId": 0,
      "courseCode": "",
      "courseName": "",
      "courseType": "",
      "trainingTheme": "",
      "difficultyLevel": "",
      "applicableRole": "",
      "instructor": "",
      "producer": "",
      "releaseDate": {
        "seconds": 0,
        "nanos": 0
      },
      "lastUpdate": {
        "seconds": 0,
        "nanos": 0
      },
      "status": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseDescription": {
        "id": 0,
        "courseId": "",
        "courseOverview": "",
        "learningObjective": "",
        "prerequisites": "",
        "certificationInfo": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "courseContent": {
        "id": 0,
        "courseId": "",
        "contentType": "",
        "contentTitle": "",
        "contentDescription": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "playbackSetting": {
        "id": 0,
        "courseId": "",
        "autoPlay": false,
        "rememberPlayback": false,
        "continuousPlayback": false,
        "defaultClarity": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "interactiveFeature": {
        "id": 0,
        "courseId": "",
        "commentEnabled": false,
        "likeEnabled": false,
        "shareEnabled": false,
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false
      },
      "relatedCourses": [
        {
          "id": 0,
          "relatedCourseId": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseInfos": [
            {
              "id": 0,
              "tenantId": 0,
              "courseCode": "",
              "courseName": "",
              "courseType": "",
              "trainingTheme": "",
              "difficultyLevel": "",
              "applicableRole": "",
              "instructor": "",
              "producer": "",
              "releaseDate": {
                "seconds": 0,
                "nanos": 0
              },
              "lastUpdate": {
                "seconds": 0,
                "nanos": 0
              },
              "status": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseDescription": {
                "id": 0,
                "courseId": "",
                "courseOverview": "",
                "learningObjective": "",
                "prerequisites": "",
                "certificationInfo": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "courseContent": {
                "id": 0,
                "courseId": "",
                "contentType": "",
                "contentTitle": "",
                "contentDescription": "",
                "contentUrl": "",
                "durationMinutes": 0,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "playbackSetting": {
                "id": 0,
                "courseId": "",
                "autoPlay": false,
                "rememberPlayback": false,
                "continuousPlayback": false,
                "defaultClarity": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "interactiveFeature": {
                "id": 0,
                "courseId": "",
                "commentEnabled": false,
                "likeEnabled": false,
                "shareEnabled": false,
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false
              },
              "relatedCourses": [
                {
                  "id": 0,
                  "relatedCourseId": "",
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseInfos": [
                    "[Object]"
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*状态为 {@code 200 (OK)} 的响应实体，包含课程附件列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListCourseAttachmentDTO](#schemalistcourseattachmentdto)]|false|none||状态为 {@code 200 (OK)} 的响应实体，包含课程附件列表|
|» id|integer(int64)|true|none||附件ID|
|» courseId|string|false|none||课程ID|
|» contentId|string|false|none||课程内容ID|
|» fileId|string|false|none||文件ID|
|» fileType|string|false|none||文件类型|
|» filePath|string|false|none||文件路径|
|» fileName|string|false|none||文件名称|
|» fileSize|string|false|none||文件大小（字节）|
|» uploadTime|object|false|none||创建时间（由系统自动填充）|
|» description|string|false|none||文件描述|
|» metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|» version|integer|false|none||当前版本号|
|» createdBy|string|false|none||创建者账号或姓名|
|» createdAt|object|false|none||创建时间（由系统自动填充）|
|» updatedBy|string|false|none||最后修改者账号或姓名|
|» updatedAt|object|false|none||创建时间（由系统自动填充）|
|» isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|» courseInfo|object|false|none||创建时间（由系统自动填充）|
|» courseContent|object|false|none||创建时间（由系统自动填充）|

## POST 根据查询条件获取课程附件信息

POST /whiskerguardtrainingservice/api/course/attachments/query

{@code POST  /course/attachments/query} : query courseAttachments by criteria.

> Body 请求参数

```json
{
  "courseId": "string",
  "attachmentName": "string",
  "attachmentType": "string",
  "attachmentDescription": "string",
  "fileSizeMin": 0,
  "fileSizeMax": 0,
  "isDeleted": false,
  "searchTerm": "string",
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseAttachmentCriteria](#schemacourseattachmentcriteria)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "courseId": "",
      "contentId": "",
      "fileId": "",
      "fileType": "",
      "filePath": "",
      "fileName": "",
      "fileSize": "",
      "uploadTime": {
        "dateTime": "",
        "offset": {
          "totalSeconds": 0,
          "rules": {
            "standardTransitions": [
              0
            ],
            "standardOffsets": [
              {
                "totalSeconds": 0,
                "rules": {
                  "standardTransitions": [
                    0
                  ],
                  "standardOffsets": [
                    "[Object]"
                  ],
                  "savingsInstantTransitions": [
                    0
                  ],
                  "savingsLocalTransitions": [
                    ""
                  ],
                  "wallOffsets": [
                    "[Object]"
                  ],
                  "lastRules": [
                    "[Object]"
                  ],
                  "fixedOffset": false,
                  "transitions": [
                    "[Object]"
                  ],
                  "transitionRules": [
                    "[Object]"
                  ]
                }
              }
            ],
            "savingsInstantTransitions": [
              0
            ],
            "savingsLocalTransitions": [
              ""
            ],
            "wallOffsets": [
              {
                "totalSeconds": 0,
                "rules": {
                  "standardTransitions": [
                    0
                  ],
                  "standardOffsets": [
                    "[Object]"
                  ],
                  "savingsInstantTransitions": [
                    0
                  ],
                  "savingsLocalTransitions": [
                    ""
                  ],
                  "wallOffsets": [
                    "[Object]"
                  ],
                  "lastRules": [
                    "[Object]"
                  ],
                  "fixedOffset": false,
                  "transitions": [
                    "[Object]"
                  ],
                  "transitionRules": [
                    "[Object]"
                  ]
                }
              }
            ],
            "lastRules": [
              {
                "month": "",
                "dom": 0,
                "dow": "",
                "time": "",
                "timeEndOfDay": false,
                "timeDefinition": "",
                "standardOffset": {
                  "totalSeconds": 0,
                  "rules": {
                    "standardTransitions": "[Object]",
                    "standardOffsets": "[Object]",
                    "savingsInstantTransitions": "[Object]",
                    "savingsLocalTransitions": "[Object]",
                    "wallOffsets": "[Object]",
                    "lastRules": "[Object]",
                    "fixedOffset": false,
                    "transitions": "[Object]",
                    "transitionRules": "[Object]"
                  }
                },
                "offsetBefore": {
                  "totalSeconds": 0,
                  "rules": {
                    "standardTransitions": "[Object]",
                    "standardOffsets": "[Object]",
                    "savingsInstantTransitions": "[Object]",
                    "savingsLocalTransitions": "[Object]",
                    "wallOffsets": "[Object]",
                    "lastRules": "[Object]",
                    "fixedOffset": false,
                    "transitions": "[Object]",
                    "transitionRules": "[Object]"
                  }
                },
                "offsetAfter": {
                  "totalSeconds": 0,
                  "rules": {
                    "standardTransitions": "[Object]",
                    "standardOffsets": "[Object]",
                    "savingsInstantTransitions": "[Object]",
                    "savingsLocalTransitions": "[Object]",
                    "wallOffsets": "[Object]",
                    "lastRules": "[Object]",
                    "fixedOffset": false,
                    "transitions": "[Object]",
                    "transitionRules": "[Object]"
                  }
                },
                "dayOfMonthIndicator": 0,
                "dayOfWeek": "",
                "localTime": "",
                "midnightEndOfDay": false
              }
            ],
            "fixedOffset": false,
            "transitions": [
              {
                "epochSecond": 0,
                "transition": "",
                "offsetBefore": {
                  "totalSeconds": 0,
                  "rules": {
                    "standardTransitions": "[Object]",
                    "standardOffsets": "[Object]",
                    "savingsInstantTransitions": "[Object]",
                    "savingsLocalTransitions": "[Object]",
                    "wallOffsets": "[Object]",
                    "lastRules": "[Object]",
                    "fixedOffset": null,
                    "transitions": null,
                    "transitionRules": null
                  }
                },
                "offsetAfter": null,
                "instant": null,
                "dateTimeBefore": null,
                "dateTimeAfter": null,
                "duration": null,
                "gap": null,
                "overlap": null
              }
            ],
            "transitionRules": null
          }
        },
        "zone": null,
        "year": null,
        "monthValue": null,
        "month": null,
        "dayOfMonth": null,
        "dayOfYear": null,
        "dayOfWeek": null,
        "hour": null,
        "minute": null,
        "second": null,
        "nano": null,
        "chronology": null
      },
      "description": null,
      "metadata": null,
      "version": null,
      "createdBy": null,
      "createdAt": null,
      "updatedBy": null,
      "updatedAt": null,
      "isDeleted": null,
      "courseInfo": null,
      "courseContent": null
    }
  ],
  "pageable": null,
  "totalPages": null,
  "totalElements": null,
  "number": null,
  "size": null,
  "numberOfElements": null,
  "sort": null,
  "first": null,
  "last": null,
  "empty": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageCourseAttachmentDTO](#schemaresponseentitypagecourseattachmentdto)|

# 18-合规培训服务/课程章节管理

## POST 根据查询条件获取课程章节信息

POST /whiskerguardtrainingservice/api/course/chapters/query

{@code POST  /course/chapters/query} : query courseChapters by criteria.

> Body 请求参数

```json
{
  "tenantId": 0,
  "courseId": "string",
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterType": "string",
  "chapterOrderMin": 0,
  "chapterOrderMax": 0,
  "durationMinutesMin": 0,
  "durationMinutesMax": 0,
  "isRequired": true,
  "status": "string",
  "isDeleted": false,
  "searchTerm": "string",
  "topLevelOnly": false,
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseChapterCriteria](#schemacoursechaptercriteria)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "courseId": "",
      "parentChapterId": 0,
      "chapterTitle": "",
      "chapterDescription": "",
      "chapterOrder": 0,
      "chapterType": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "isRequired": false,
      "status": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false,
      "parentChapter": {
        "id": 0,
        "tenantId": 0,
        "courseId": "",
        "parentChapterId": 0,
        "chapterTitle": "",
        "chapterDescription": "",
        "chapterOrder": 0,
        "chapterType": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "isRequired": false,
        "status": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0,
          "epochSecond": 0,
          "nano": 0
        },
        "isDeleted": false,
        "parentChapter": {}
      }
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "empty": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageCourseChapterDTO](#schemaresponseentitypagecoursechapterdto)|

## POST 创建课程章节

POST /whiskerguardtrainingservice/api/course/chapters

{@code POST  /course-chapters} : Create a new courseChapter.

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "string",
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterDescription": "string",
  "chapterOrder": 0,
  "chapterType": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "isRequired": true,
  "status": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentChapter": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseChapterDTO](#schemacoursechapterdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "",
  "parentChapterId": 0,
  "chapterTitle": "",
  "chapterDescription": "",
  "chapterOrder": 0,
  "chapterType": "",
  "contentUrl": "",
  "durationMinutes": 0,
  "isRequired": false,
  "status": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parentChapter": {
    "id": 0,
    "tenantId": 0,
    "courseId": "",
    "parentChapterId": 0,
    "chapterTitle": "",
    "chapterDescription": "",
    "chapterOrder": 0,
    "chapterType": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "isRequired": false,
    "status": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parentChapter": {
      "id": 0,
      "tenantId": 0,
      "courseId": "",
      "parentChapterId": 0,
      "chapterTitle": "",
      "chapterDescription": "",
      "chapterOrder": 0,
      "chapterType": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "isRequired": false,
      "status": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentChapter": {}
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseChapterDTO](#schemaresponseentitycoursechapterdto)|

## PUT 更新课程章节

PUT /api/course/chapters/{id}

{@code PUT  /course-chapters/:id} : Updates an existing courseChapter.

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "string",
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterDescription": "string",
  "chapterOrder": 0,
  "chapterType": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "isRequired": true,
  "status": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentChapter": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要保存的课程章节DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseChapterDTO](#schemacoursechapterdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "",
  "parentChapterId": 0,
  "chapterTitle": "",
  "chapterDescription": "",
  "chapterOrder": 0,
  "chapterType": "",
  "contentUrl": "",
  "durationMinutes": 0,
  "isRequired": false,
  "status": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parentChapter": {
    "id": 0,
    "tenantId": 0,
    "courseId": "",
    "parentChapterId": 0,
    "chapterTitle": "",
    "chapterDescription": "",
    "chapterOrder": 0,
    "chapterType": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "isRequired": false,
    "status": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parentChapter": {
      "id": 0,
      "tenantId": 0,
      "courseId": "",
      "parentChapterId": 0,
      "chapterTitle": "",
      "chapterDescription": "",
      "chapterOrder": 0,
      "chapterType": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "isRequired": false,
      "status": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentChapter": {}
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseChapterDTO](#schemaresponseentitycoursechapterdto)|

## GET 根据ID获取课程章节

GET /api/course/chapters/{id}

{@code GET  /course-chapters/:id} : get the "id" courseChapter.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要获取的课程章节DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "",
  "parentChapterId": 0,
  "chapterTitle": "",
  "chapterDescription": "",
  "chapterOrder": 0,
  "chapterType": "",
  "contentUrl": "",
  "durationMinutes": 0,
  "isRequired": false,
  "status": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parentChapter": {
    "id": 0,
    "tenantId": 0,
    "courseId": "",
    "parentChapterId": 0,
    "chapterTitle": "",
    "chapterDescription": "",
    "chapterOrder": 0,
    "chapterType": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "isRequired": false,
    "status": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parentChapter": {
      "id": 0,
      "tenantId": 0,
      "courseId": "",
      "parentChapterId": 0,
      "chapterTitle": "",
      "chapterDescription": "",
      "chapterOrder": 0,
      "chapterType": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "isRequired": false,
      "status": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentChapter": {}
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseChapterDTO](#schemaresponseentitycoursechapterdto)|

## DELETE 根据ID删除课程章节

DELETE /api/course/chapters/{id}

{@code DELETE  /course-chapters/:id} : delete the "id" courseChapter.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要删除的课程章节DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## PATCH 部分更新课程章节

PATCH /whiskerguardtrainingservice/api/course/chapters/{id}

{@code PATCH  /course-chapters/:id} : Partial updates given fields of an existing courseChapter, field will ignore if it is null

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "string",
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterDescription": "string",
  "chapterOrder": 0,
  "chapterType": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "isRequired": true,
  "status": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentChapter": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要保存的课程章节DTO的ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[CourseChapterDTO](#schemacoursechapterdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "",
  "parentChapterId": 0,
  "chapterTitle": "",
  "chapterDescription": "",
  "chapterOrder": 0,
  "chapterType": "",
  "contentUrl": "",
  "durationMinutes": 0,
  "isRequired": false,
  "status": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "parentChapter": {
    "id": 0,
    "tenantId": 0,
    "courseId": "",
    "parentChapterId": 0,
    "chapterTitle": "",
    "chapterDescription": "",
    "chapterOrder": 0,
    "chapterType": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "isRequired": false,
    "status": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parentChapter": {
      "id": 0,
      "tenantId": 0,
      "courseId": "",
      "parentChapterId": 0,
      "chapterTitle": "",
      "chapterDescription": "",
      "chapterOrder": 0,
      "chapterType": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "isRequired": false,
      "status": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentChapter": {}
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseChapterDTO](#schemaresponseentitycoursechapterdto)|

## GET 获取所有课程章节

GET /api/course/chapters

{@code GET  /course-chapters} : get all the courseChapters.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "tenantId": 0,
    "courseId": "",
    "parentChapterId": 0,
    "chapterTitle": "",
    "chapterDescription": "",
    "chapterOrder": 0,
    "chapterType": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "isRequired": false,
    "status": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "parentChapter": {
      "id": 0,
      "tenantId": 0,
      "courseId": "",
      "parentChapterId": 0,
      "chapterTitle": "",
      "chapterDescription": "",
      "chapterOrder": 0,
      "chapterType": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "isRequired": false,
      "status": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "parentChapter": {
        "id": 0,
        "tenantId": 0,
        "courseId": "",
        "parentChapterId": 0,
        "chapterTitle": "",
        "chapterDescription": "",
        "chapterOrder": 0,
        "chapterType": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "isRequired": false,
        "status": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "parentChapter": {}
      }
    }
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*状态为 {@code 200 (OK)} 的响应实体，包含课程章节列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListCourseChapterDTO](#schemalistcoursechapterdto)]|false|none||状态为 {@code 200 (OK)} 的响应实体，包含课程章节列表|
|» id|integer(int64)|true|none||none|
|» tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|» courseId|string|true|none||关联课程ID|
|» parentChapterId|integer(int64)|false|none||父章节ID（支持多级章节）|
|» chapterTitle|string|true|none||章节标题|
|» chapterDescription|string|false|none||章节描述|
|» chapterOrder|integer|true|none||章节顺序|
|» chapterType|string|true|none||章节类型（视频、文档、测试等）|
|» contentUrl|string|false|none||视频/内容URL|
|» durationMinutes|integer|false|none||章节时长（分钟）|
|» isRequired|boolean|false|none||是否必修|
|» status|string|false|none||章节状态（正常、维护中、已下线）|
|» metadata|string|false|none||补充字段|
|» version|integer|false|none||版本号|
|» createdBy|string|false|none||创建者|
|» createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|» updatedBy|string|false|none||更新者|
|» updatedAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|» isDeleted|boolean|false|none||是否删除|
|» parentChapter|[CourseChapterDTO](#schemacoursechapterdto)|false|none||none|
|»» id|integer(int64)|false|none||none|
|»» tenantId|integer(int64)|false|none||租户ID，标识不同公司的数据隔离|
|»» courseId|string|true|none||关联课程ID|
|»» parentChapterId|integer(int64)|false|none||父章节ID（支持多级章节）|
|»» chapterTitle|string|true|none||章节标题|
|»» chapterDescription|string|false|none||章节描述|
|»» chapterOrder|integer|false|none||章节顺序|
|»» chapterType|string|false|none||章节类型（视频、文档、测试等）|
|»» contentUrl|string|false|none||视频/内容URL|
|»» durationMinutes|integer|false|none||章节时长（分钟）|
|»» isRequired|boolean|false|none||是否必修|
|»» status|string|false|none||章节状态（正常、维护中、已下线）|
|»» metadata|string|false|none||补充字段|
|»» version|integer|false|none||版本号|
|»» createdBy|string|false|none||创建者|
|»» createdAt|object|false|none||创建时间（由系统自动填充）|
|»» updatedBy|string|false|none||更新者|
|»» updatedAt|object|false|none||创建时间（由系统自动填充）|
|»» isDeleted|boolean|false|none||是否删除|
|»» parentChapter|object|false|none||创建时间（由系统自动填充）|

## GET 根据课程ID获取所有章节信息，包含课时和学习进度

GET /whiskerguardtrainingservice/api/course/chapters/course/{courseId}/with/lessons

{@code GET  /course-chapters/course/:courseId/with-lessons} : get all chapters with lessons by course ID.

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|courseId|path|string| 是 |课程ID|
|userId|query|string| 否 |用户ID（用于获取学习进度，可选参数）|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "tenantId": 0,
    "courseId": "",
    "parentChapterId": 0,
    "chapterTitle": "",
    "chapterDescription": "",
    "chapterOrder": 0,
    "chapterType": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "isRequired": false,
    "status": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "lessons": [
      {
        "id": 0,
        "chapterTitle": "",
        "chapterDescription": "",
        "chapterOrder": 0,
        "chapterType": "",
        "contentUrl": "",
        "durationMinutes": 0,
        "isRequired": false,
        "status": "",
        "progressId": 0,
        "startTime": {
          "seconds": 0,
          "nanos": 0
        },
        "lastAccessTime": {
          "seconds": 0,
          "nanos": 0
        },
        "studyDurationSeconds": 0,
        "playbackPosition": 0,
        "completionStatus": "",
        "completedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "accessCount": 0
      }
    ]
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*状态为 {@code 200 (OK)} 的响应实体，包含章节与课时信息列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListCourseChapterWithLessonsDTO](#schemalistcoursechapterwithlessonsdto)]|false|none||状态为 {@code 200 (OK)} 的响应实体，包含章节与课时信息列表|
|» id|integer(int64)|false|none||章节ID|
|» tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|» courseId|string|true|none||关联课程ID|
|» parentChapterId|integer(int64)|false|none||父章节ID（支持多级章节）|
|» chapterTitle|string|true|none||章节标题|
|» chapterDescription|string|false|none||章节描述|
|» chapterOrder|integer|true|none||章节顺序|
|» chapterType|string|true|none||章节类型（视频、文档、测试等）|
|» contentUrl|string|false|none||视频/内容URL|
|» durationMinutes|integer|false|none||章节时长（分钟）|
|» isRequired|boolean|false|none||是否必修|
|» status|string|false|none||章节状态（正常、维护中、已下线）|
|» metadata|string|false|none||补充字段|
|» version|integer|false|none||版本号|
|» createdBy|string|false|none||创建者|
|» createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|» updatedBy|string|false|none||更新者|
|» updatedAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|» isDeleted|boolean|false|none||是否删除|
|» lessons|[[ChapterLessonDTO](#schemachapterlessondto)]|false|none||该章节下的所有课时列表|
|»» id|integer(int64)|false|none||课时ID|
|»» chapterTitle|string|true|none||课时标题|
|»» chapterDescription|string|false|none||课时描述|
|»» chapterOrder|integer|true|none||课时顺序|
|»» chapterType|string|true|none||课时类型（视频、文档、测试等）|
|»» contentUrl|string|false|none||视频/内容URL|
|»» durationMinutes|integer|false|none||课时时长（分钟）|
|»» isRequired|boolean|false|none||是否必修|
|»» status|string|false|none||课时状态（正常、维护中、已下线）|
|»» progressId|integer(int64)|false|none||学习进度信息<br />学习进度ID|
|»» startTime|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|»»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|»» lastAccessTime|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|»»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|»» studyDurationSeconds|integer|false|none||学习时长（秒）|
|»» playbackPosition|integer|false|none||播放进度（秒）|
|»» completionStatus|string|false|none||完成状态（未开始、进行中、已完成）|
|»» completedAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|»»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|»» accessCount|integer|false|none||学习次数|

# 数据模型

<h2 id="tocS_ZoneId">ZoneId</h2>

<a id="schemazoneid"></a>
<a id="schema_ZoneId"></a>
<a id="tocSzoneid"></a>
<a id="tocszoneid"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_CourseDescriptionDTO">CourseDescriptionDTO</h2>

<a id="schemacoursedescriptiondto"></a>
<a id="schema_CourseDescriptionDTO"></a>
<a id="tocScoursedescriptiondto"></a>
<a id="tocscoursedescriptiondto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "courseOverview": "string",
  "learningObjective": "string",
  "prerequisites": "string",
  "certificationInfo": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|courseId|string|true|none||外键关联 CourseInfo|
|courseOverview|string|false|none||课程概述|
|learningObjective|string|false|none||学习目标|
|prerequisites|string|false|none||先修要求|
|certificationInfo|string|false|none||认证信息（如 {"cert_name": "高级前端认证", "pass_score": 80}）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_CourseChapterDTO">CourseChapterDTO</h2>

<a id="schemacoursechapterdto"></a>
<a id="schema_CourseChapterDTO"></a>
<a id="tocScoursechapterdto"></a>
<a id="tocscoursechapterdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "string",
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterDescription": "string",
  "chapterOrder": 0,
  "chapterType": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "isRequired": true,
  "status": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentChapter": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|false|none||租户ID，标识不同公司的数据隔离|
|courseId|string|true|none||关联课程ID|
|parentChapterId|integer(int64)|false|none||父章节ID（支持多级章节）|
|chapterTitle|string|true|none||章节标题|
|chapterDescription|string|false|none||章节描述|
|chapterOrder|integer|false|none||章节顺序|
|chapterType|string|false|none||章节类型（视频、文档、测试等）|
|contentUrl|string|false|none||视频/内容URL|
|durationMinutes|integer|false|none||章节时长（分钟）|
|isRequired|boolean|false|none||是否必修|
|status|string|false|none||章节状态（正常、维护中、已下线）|
|metadata|string|false|none||补充字段|
|version|integer|false|none||版本号|
|createdBy|string|false|none||创建者|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||更新者|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除|
|parentChapter|object|false|none||创建时间（由系统自动填充）|

<h2 id="tocS_ZonedDateTime">ZonedDateTime</h2>

<a id="schemazoneddatetime"></a>
<a id="schema_ZonedDateTime"></a>
<a id="tocSzoneddatetime"></a>
<a id="tocszoneddatetime"></a>

```json
{
  "dateTime": "string",
  "offset": {},
  "zone": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|dateTime|string|false|none||The local date-time.|
|offset|object|false|none||The offset from UTC/Greenwich.|
|zone|[ZoneId](#schemazoneid)|false|none||The time-zone.|

<h2 id="tocS_ZoneOffset">ZoneOffset</h2>

<a id="schemazoneoffset"></a>
<a id="schema_ZoneOffset"></a>
<a id="tocSzoneoffset"></a>
<a id="tocszoneoffset"></a>

```json
{
  "totalSeconds": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|totalSeconds|integer|false|none||The total offset in seconds.|

<h2 id="tocS_CourseContentDTO">CourseContentDTO</h2>

<a id="schemacoursecontentdto"></a>
<a id="schema_CourseContentDTO"></a>
<a id="tocScoursecontentdto"></a>
<a id="tocscoursecontentdto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "contentType": "string",
  "contentTitle": "string",
  "contentDescription": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|courseId|string|true|none||外键关联 CourseInfo|
|contentType|string|false|none||内容类型|
|contentTitle|string|false|none||内容标题|
|contentDescription|string|false|none||内容描述|
|contentUrl|string|false|none||内容链接（如视频/文档地址）|
|durationMinutes|integer|false|none||内容时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_ResponseEntityCourseChapterDTO">ResponseEntityCourseChapterDTO</h2>

<a id="schemaresponseentitycoursechapterdto"></a>
<a id="schema_ResponseEntityCourseChapterDTO"></a>
<a id="tocSresponseentitycoursechapterdto"></a>
<a id="tocsresponseentitycoursechapterdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "string",
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterDescription": "string",
  "chapterOrder": 0,
  "chapterType": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "isRequired": true,
  "status": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "parentChapter": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|false|none||租户ID，标识不同公司的数据隔离|
|courseId|string|true|none||关联课程ID|
|parentChapterId|integer(int64)|false|none||父章节ID（支持多级章节）|
|chapterTitle|string|true|none||章节标题|
|chapterDescription|string|false|none||章节描述|
|chapterOrder|integer|false|none||章节顺序|
|chapterType|string|false|none||章节类型（视频、文档、测试等）|
|contentUrl|string|false|none||视频/内容URL|
|durationMinutes|integer|false|none||章节时长（分钟）|
|isRequired|boolean|false|none||是否必修|
|status|string|false|none||章节状态（正常、维护中、已下线）|
|metadata|string|false|none||补充字段|
|version|integer|false|none||版本号|
|createdBy|string|false|none||创建者|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||更新者|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除|
|parentChapter|object|false|none||创建时间（由系统自动填充）|

<h2 id="tocS_CourseInfoDTO">CourseInfoDTO</h2>

<a id="schemacourseinfodto"></a>
<a id="schema_CourseInfoDTO"></a>
<a id="tocScourseinfodto"></a>
<a id="tocscourseinfodto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "applicableRole": "string",
  "instructor": "string",
  "producer": "string",
  "releaseDate": {},
  "lastUpdate": {},
  "status": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseDescription": {},
  "courseContent": {},
  "playbackSetting": {},
  "interactiveFeature": {},
  "relatedCourses": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|courseCode|string|true|none||课程代码（如 COURSE20240115001）|
|courseName|string|true|none||课程名称|
|courseType|string|true|none||课程类型|
|trainingTheme|string|false|none||培训主题（如“前端开发”）|
|difficultyLevel|string|false|none||难度等级|
|applicableRole|string|false|none||适用角色（如 ["frontend_dev", "backend_dev"]）|
|instructor|string|false|none||讲师姓名|
|producer|string|false|none||制作人|
|releaseDate|object|false|none||创建时间（由系统自动填充）|
|lastUpdate|object|false|none||创建时间（由系统自动填充）|
|status|string|false|none||课程状态|
|durationMinutes|integer|false|none||课程总时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseDescription|object|false|none||创建时间（由系统自动填充）|
|courseContent|object|false|none||创建时间（由系统自动填充）|
|playbackSetting|object|false|none||创建时间（由系统自动填充）|
|interactiveFeature|object|false|none||创建时间（由系统自动填充）|
|relatedCourses|[object]|false|none||none|

<h2 id="tocS_RelatedCourseDTO">RelatedCourseDTO</h2>

<a id="schemarelatedcoursedto"></a>
<a id="schema_RelatedCourseDTO"></a>
<a id="tocSrelatedcoursedto"></a>
<a id="tocsrelatedcoursedto"></a>

```json
{
  "id": 0,
  "relatedCourseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfos": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|relatedCourseId|string|true|none||外键关联 CourseInfo|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfos|[object]|false|none||none|

<h2 id="tocS_InteractiveFeatureDTO">InteractiveFeatureDTO</h2>

<a id="schemainteractivefeaturedto"></a>
<a id="schema_InteractiveFeatureDTO"></a>
<a id="tocSinteractivefeaturedto"></a>
<a id="tocsinteractivefeaturedto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "commentEnabled": true,
  "likeEnabled": true,
  "shareEnabled": true,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|courseId|string|true|none||外键关联 CourseInfo|
|commentEnabled|boolean|false|none||是否允许评论|
|likeEnabled|boolean|false|none||是否允许点赞|
|shareEnabled|boolean|false|none||是否允许分享|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_PlaybackSettingDTO">PlaybackSettingDTO</h2>

<a id="schemaplaybacksettingdto"></a>
<a id="schema_PlaybackSettingDTO"></a>
<a id="tocSplaybacksettingdto"></a>
<a id="tocsplaybacksettingdto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "autoPlay": true,
  "rememberPlayback": true,
  "continuousPlayback": true,
  "defaultClarity": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|courseId|string|true|none||外键关联 CourseInfo|
|autoPlay|boolean|false|none||是否自动播放|
|rememberPlayback|boolean|false|none||是否记忆播放进度|
|continuousPlayback|boolean|false|none||是否连续播放|
|defaultClarity|string|false|none||默认清晰度|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_ResponseEntityCourseInfoDTO">ResponseEntityCourseInfoDTO</h2>

<a id="schemaresponseentitycourseinfodto"></a>
<a id="schema_ResponseEntityCourseInfoDTO"></a>
<a id="tocSresponseentitycourseinfodto"></a>
<a id="tocsresponseentitycourseinfodto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "applicableRole": "string",
  "instructor": "string",
  "producer": "string",
  "releaseDate": {},
  "lastUpdate": {},
  "status": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseDescription": {},
  "courseContent": {},
  "playbackSetting": {},
  "interactiveFeature": {},
  "relatedCourses": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|courseCode|string|true|none||课程代码（如 COURSE20240115001）|
|courseName|string|true|none||课程名称|
|courseType|string|true|none||课程类型|
|trainingTheme|string|false|none||培训主题（如“前端开发”）|
|difficultyLevel|string|false|none||难度等级|
|applicableRole|string|false|none||适用角色（如 ["frontend_dev", "backend_dev"]）|
|instructor|string|false|none||讲师姓名|
|producer|string|false|none||制作人|
|releaseDate|object|false|none||创建时间（由系统自动填充）|
|lastUpdate|object|false|none||创建时间（由系统自动填充）|
|status|string|false|none||课程状态|
|durationMinutes|integer|false|none||课程总时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseDescription|object|false|none||创建时间（由系统自动填充）|
|courseContent|object|false|none||创建时间（由系统自动填充）|
|playbackSetting|object|false|none||创建时间（由系统自动填充）|
|interactiveFeature|object|false|none||创建时间（由系统自动填充）|
|relatedCourses|[object]|false|none||none|

<h2 id="tocS_ListCourseChapterDTO">ListCourseChapterDTO</h2>

<a id="schemalistcoursechapterdto"></a>
<a id="schema_ListCourseChapterDTO"></a>
<a id="tocSlistcoursechapterdto"></a>
<a id="tocslistcoursechapterdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "string",
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterDescription": "string",
  "chapterOrder": 0,
  "chapterType": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "isRequired": true,
  "status": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "parentChapter": {
    "id": 0,
    "tenantId": 0,
    "courseId": "string",
    "parentChapterId": 0,
    "chapterTitle": "string",
    "chapterDescription": "string",
    "chapterOrder": 0,
    "chapterType": "string",
    "contentUrl": "string",
    "durationMinutes": 0,
    "isRequired": true,
    "status": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {},
    "updatedBy": "string",
    "updatedAt": {},
    "isDeleted": true,
    "parentChapter": {}
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|courseId|string|true|none||关联课程ID|
|parentChapterId|integer(int64)|false|none||父章节ID（支持多级章节）|
|chapterTitle|string|true|none||章节标题|
|chapterDescription|string|false|none||章节描述|
|chapterOrder|integer|true|none||章节顺序|
|chapterType|string|true|none||章节类型（视频、文档、测试等）|
|contentUrl|string|false|none||视频/内容URL|
|durationMinutes|integer|false|none||章节时长（分钟）|
|isRequired|boolean|false|none||是否必修|
|status|string|false|none||章节状态（正常、维护中、已下线）|
|metadata|string|false|none||补充字段|
|version|integer|false|none||版本号|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除|
|parentChapter|[CourseChapterDTO](#schemacoursechapterdto)|false|none||none|

<h2 id="tocS_ChapterLessonDTO">ChapterLessonDTO</h2>

<a id="schemachapterlessondto"></a>
<a id="schema_ChapterLessonDTO"></a>
<a id="tocSchapterlessondto"></a>
<a id="tocschapterlessondto"></a>

```json
{
  "id": 0,
  "chapterTitle": "string",
  "chapterDescription": "string",
  "chapterOrder": 0,
  "chapterType": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "isRequired": true,
  "status": "string",
  "progressId": 0,
  "startTime": {
    "seconds": 0,
    "nanos": 0
  },
  "lastAccessTime": {
    "seconds": 0,
    "nanos": 0
  },
  "studyDurationSeconds": 0,
  "playbackPosition": 0,
  "completionStatus": "string",
  "completedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "accessCount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||课时ID|
|chapterTitle|string|true|none||课时标题|
|chapterDescription|string|false|none||课时描述|
|chapterOrder|integer|true|none||课时顺序|
|chapterType|string|true|none||课时类型（视频、文档、测试等）|
|contentUrl|string|false|none||视频/内容URL|
|durationMinutes|integer|false|none||课时时长（分钟）|
|isRequired|boolean|false|none||是否必修|
|status|string|false|none||课时状态（正常、维护中、已下线）|
|progressId|integer(int64)|false|none||学习进度信息<br />学习进度ID|
|startTime|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|lastAccessTime|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|studyDurationSeconds|integer|false|none||学习时长（秒）|
|playbackPosition|integer|false|none||播放进度（秒）|
|completionStatus|string|false|none||完成状态（未开始、进行中、已完成）|
|completedAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|accessCount|integer|false|none||学习次数|

<h2 id="tocS_ResponseEntityPageCourseChapterDTO">ResponseEntityPageCourseChapterDTO</h2>

<a id="schemaresponseentitypagecoursechapterdto"></a>
<a id="schema_ResponseEntityPageCourseChapterDTO"></a>
<a id="tocSresponseentitypagecoursechapterdto"></a>
<a id="tocsresponseentitypagecoursechapterdto"></a>

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "courseId": "string",
      "parentChapterId": 0,
      "chapterTitle": "string",
      "chapterDescription": "string",
      "chapterOrder": 0,
      "chapterType": "string",
      "contentUrl": "string",
      "durationMinutes": 0,
      "isRequired": true,
      "status": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "parentChapter": {}
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "empty": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|content|[[CourseChapterDTO](#schemacoursechapterdto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE",
  "ascending": true,
  "descending": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|
|ascending|boolean|false|none||none|
|descending|boolean|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

<h2 id="tocS_ListCourseChapterWithLessonsDTO">ListCourseChapterWithLessonsDTO</h2>

<a id="schemalistcoursechapterwithlessonsdto"></a>
<a id="schema_ListCourseChapterWithLessonsDTO"></a>
<a id="tocSlistcoursechapterwithlessonsdto"></a>
<a id="tocslistcoursechapterwithlessonsdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "string",
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterDescription": "string",
  "chapterOrder": 0,
  "chapterType": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "isRequired": true,
  "status": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "lessons": [
    {
      "id": 0,
      "chapterTitle": "string",
      "chapterDescription": "string",
      "chapterOrder": 0,
      "chapterType": "string",
      "contentUrl": "string",
      "durationMinutes": 0,
      "isRequired": true,
      "status": "string",
      "progressId": 0,
      "startTime": {
        "seconds": 0,
        "nanos": 0
      },
      "lastAccessTime": {
        "seconds": 0,
        "nanos": 0
      },
      "studyDurationSeconds": 0,
      "playbackPosition": 0,
      "completionStatus": "string",
      "completedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "accessCount": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||章节ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|courseId|string|true|none||关联课程ID|
|parentChapterId|integer(int64)|false|none||父章节ID（支持多级章节）|
|chapterTitle|string|true|none||章节标题|
|chapterDescription|string|false|none||章节描述|
|chapterOrder|integer|true|none||章节顺序|
|chapterType|string|true|none||章节类型（视频、文档、测试等）|
|contentUrl|string|false|none||视频/内容URL|
|durationMinutes|integer|false|none||章节时长（分钟）|
|isRequired|boolean|false|none||是否必修|
|status|string|false|none||章节状态（正常、维护中、已下线）|
|metadata|string|false|none||补充字段|
|version|integer|false|none||版本号|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除|
|lessons|[[ChapterLessonDTO](#schemachapterlessondto)]|false|none||该章节下的所有课时列表|

<h2 id="tocS_CourseChapterCriteria">CourseChapterCriteria</h2>

<a id="schemacoursechaptercriteria"></a>
<a id="schema_CourseChapterCriteria"></a>
<a id="tocScoursechaptercriteria"></a>
<a id="tocscoursechaptercriteria"></a>

```json
{
  "tenantId": 0,
  "courseId": "string",
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterType": "string",
  "chapterOrderMin": 0,
  "chapterOrderMax": 0,
  "durationMinutesMin": 0,
  "durationMinutesMax": 0,
  "isRequired": true,
  "status": "string",
  "isDeleted": false,
  "searchTerm": "string",
  "topLevelOnly": false,
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|integer(int64)|false|none||租户ID（精确匹配）|
|courseId|string|false|none||课程ID（精确匹配）|
|parentChapterId|integer(int64)|false|none||父章节ID（精确匹配）|
|chapterTitle|string|false|none||章节标题（模糊匹配）|
|chapterType|string|false|none||章节类型（精确匹配）|
|chapterOrderMin|integer|false|none||章节顺序最小值（范围匹配）|
|chapterOrderMax|integer|false|none||章节顺序最大值（范围匹配）|
|durationMinutesMin|integer|false|none||章节时长最小值（分钟）（范围匹配）|
|durationMinutesMax|integer|false|none||章节时长最大值（分钟）（范围匹配）|
|isRequired|boolean|false|none||是否必修|
|status|string|false|none||章节状态（精确匹配）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|searchTerm|string|false|none||通用搜索词（在章节标题和描述字段中进行模糊搜索）|
|topLevelOnly|boolean|false|none||是否只查询顶级章节（父章节ID为空）|
|page|integer|false|none||页码|
|size|integer|false|none||每页记录数|
|sort|string|false|none||排序字段和方向，格式为：property,direction，例如：chapterOrder,asc|

<h2 id="tocS_ResponseEntityPageCourseInfoDTO">ResponseEntityPageCourseInfoDTO</h2>

<a id="schemaresponseentitypagecourseinfodto"></a>
<a id="schema_ResponseEntityPageCourseInfoDTO"></a>
<a id="tocSresponseentitypagecourseinfodto"></a>
<a id="tocsresponseentitypagecourseinfodto"></a>

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "courseCode": "string",
      "courseName": "string",
      "courseType": "string",
      "trainingTheme": "string",
      "difficultyLevel": "string",
      "applicableRole": "string",
      "instructor": "string",
      "producer": "string",
      "releaseDate": {},
      "lastUpdate": {},
      "status": "string",
      "durationMinutes": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "courseDescription": {},
      "courseContent": {},
      "playbackSetting": {},
      "interactiveFeature": {},
      "relatedCourses": "new HashSet<>()"
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "empty": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|content|[[CourseInfoDTO](#schemacourseinfodto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_CourseInfoCriteria">CourseInfoCriteria</h2>

<a id="schemacourseinfocriteria"></a>
<a id="schema_CourseInfoCriteria"></a>
<a id="tocScourseinfocriteria"></a>
<a id="tocscourseinfocriteria"></a>

```json
{
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "status": "string",
  "instructor": "string",
  "startDate": "string",
  "endDate": "string",
  "searchTerm": "string",
  "page": 0,
  "size": 0,
  "sort": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|courseCode|string|false|none||课程代码（精确匹配）|
|courseName|string|false|none||课程名称（模糊匹配）|
|courseType|string|false|none||课程类型（精确匹配）|
|trainingTheme|string|false|none||培训主题（模糊匹配）|
|difficultyLevel|string|false|none||难度级别（精确匹配）|
|status|string|false|none||状态（精确匹配）|
|instructor|string|false|none||讲师（模糊匹配）|
|startDate|string|false|none||开始日期（可选，如果只提供开始日期，则查询大于等于该日期的记录）|
|endDate|string|false|none||结束日期（可选，如果只提供结束日期，则查询小于等于该日期的记录）|
|searchTerm|string|false|none||通用搜索词（在课程名称、培训主题和讲师字段中进行模糊搜索）|
|page|integer|false|none||页码|
|size|integer|false|none||每页记录数|
|sort|string|false|none||排序字段和方向，格式为：property,direction，例如：courseName,asc|

<h2 id="tocS_ResponseEntityCourseContentDTO">ResponseEntityCourseContentDTO</h2>

<a id="schemaresponseentitycoursecontentdto"></a>
<a id="schema_ResponseEntityCourseContentDTO"></a>
<a id="tocSresponseentitycoursecontentdto"></a>
<a id="tocsresponseentitycoursecontentdto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "contentType": "string",
  "contentTitle": "string",
  "contentDescription": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|courseId|string|true|none||外键关联 CourseInfo|
|contentType|string|false|none||内容类型|
|contentTitle|string|false|none||内容标题|
|contentDescription|string|false|none||内容描述|
|contentUrl|string|false|none||内容链接（如视频/文档地址）|
|durationMinutes|integer|false|none||内容时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_ListCourseContentDTO">ListCourseContentDTO</h2>

<a id="schemalistcoursecontentdto"></a>
<a id="schema_ListCourseContentDTO"></a>
<a id="tocSlistcoursecontentdto"></a>
<a id="tocslistcoursecontentdto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "contentType": "string",
  "contentTitle": "string",
  "contentDescription": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|courseId|string|true|none||外键关联 CourseInfo|
|contentType|string|false|none||内容类型|
|contentTitle|string|false|none||内容标题|
|contentDescription|string|false|none||内容描述|
|contentUrl|string|false|none||内容链接（如视频/文档地址）|
|durationMinutes|integer|false|none||内容时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_ResponseEntityPageCourseContentDTO">ResponseEntityPageCourseContentDTO</h2>

<a id="schemaresponseentitypagecoursecontentdto"></a>
<a id="schema_ResponseEntityPageCourseContentDTO"></a>
<a id="tocSresponseentitypagecoursecontentdto"></a>
<a id="tocsresponseentitypagecoursecontentdto"></a>

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "courseId": "string",
      "contentType": "string",
      "contentTitle": "string",
      "contentDescription": "string",
      "contentUrl": "string",
      "durationMinutes": 0,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "empty": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|content|[[CourseContentDTO](#schemacoursecontentdto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_CourseContentCriteria">CourseContentCriteria</h2>

<a id="schemacoursecontentcriteria"></a>
<a id="schema_CourseContentCriteria"></a>
<a id="tocScoursecontentcriteria"></a>
<a id="tocscoursecontentcriteria"></a>

```json
{
  "courseId": "string",
  "contentTitle": "string",
  "contentType": "string",
  "chapterNumber": 0,
  "isDeleted": false,
  "searchTerm": "string",
  "courseInfoIsNull": false,
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|courseId|string|false|none||课程ID（精确匹配）|
|contentTitle|string|false|none||内容标题（模糊匹配）|
|contentType|string|false|none||内容类型（精确匹配）|
|chapterNumber|integer|false|none||章节序号（精确匹配）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|searchTerm|string|false|none||通用搜索词（在内容标题和描述字段中进行模糊搜索）|
|courseInfoIsNull|boolean|false|none||是否只查询未关联课程的内容|
|page|integer|false|none||页码|
|size|integer|false|none||每页记录数|
|sort|string|false|none||排序字段和方向，格式为：property,direction，例如：contentTitle,asc|

<h2 id="tocS_ResponseEntityRelatedCourseDTO">ResponseEntityRelatedCourseDTO</h2>

<a id="schemaresponseentityrelatedcoursedto"></a>
<a id="schema_ResponseEntityRelatedCourseDTO"></a>
<a id="tocSresponseentityrelatedcoursedto"></a>
<a id="tocsresponseentityrelatedcoursedto"></a>

```json
{
  "id": 0,
  "relatedCourseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfos": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|relatedCourseId|string|true|none||外键关联 CourseInfo|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfos|[object]|false|none||none|

<h2 id="tocS_ListRelatedCourseDTO">ListRelatedCourseDTO</h2>

<a id="schemalistrelatedcoursedto"></a>
<a id="schema_ListRelatedCourseDTO"></a>
<a id="tocSlistrelatedcoursedto"></a>
<a id="tocslistrelatedcoursedto"></a>

```json
{
  "id": 0,
  "relatedCourseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfos": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|relatedCourseId|string|true|none||外键关联 CourseInfo|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfos|[object]|false|none||none|

<h2 id="tocS_ResponseEntityPageRelatedCourseDTO">ResponseEntityPageRelatedCourseDTO</h2>

<a id="schemaresponseentitypagerelatedcoursedto"></a>
<a id="schema_ResponseEntityPageRelatedCourseDTO"></a>
<a id="tocSresponseentitypagerelatedcoursedto"></a>
<a id="tocsresponseentitypagerelatedcoursedto"></a>

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "relatedCourseId": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "courseInfos": "new HashSet<>()"
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "empty": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|content|[[RelatedCourseDTO](#schemarelatedcoursedto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_RelatedCourseCriteria">RelatedCourseCriteria</h2>

<a id="schemarelatedcoursecriteria"></a>
<a id="schema_RelatedCourseCriteria"></a>
<a id="tocSrelatedcoursecriteria"></a>
<a id="tocsrelatedcoursecriteria"></a>

```json
{
  "courseId": "string",
  "relationType": "string",
  "relationDescription": "string",
  "isDeleted": false,
  "searchTerm": "string",
  "eagerload": false,
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|courseId|string|false|none||课程ID（精确匹配）|
|relationType|string|false|none||关系类型（精确匹配）|
|relationDescription|string|false|none||关系描述（模糊匹配）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|searchTerm|string|false|none||通用搜索词（在关系描述字段中进行模糊搜索）|
|eagerload|boolean|false|none||是否加载关联课程信息|
|page|integer|false|none||页码|
|size|integer|false|none||每页记录数|
|sort|string|false|none||排序字段和方向，格式为：property,direction，例如：relationType,asc|

<h2 id="tocS_ResponseEntityTagManagementDTO">ResponseEntityTagManagementDTO</h2>

<a id="schemaresponseentitytagmanagementdto"></a>
<a id="schema_ResponseEntityTagManagementDTO"></a>
<a id="tocSresponseentitytagmanagementdto"></a>
<a id="tocsresponseentitytagmanagementdto"></a>

```json
{
  "id": 0,
  "tagName": "string",
  "courseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfo": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|tagName|string|true|none||标签名称（如“性能优化”）|
|courseId|string|true|none||外键关联 CourseInfo|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfo|object|false|none||创建时间（由系统自动填充）|

<h2 id="tocS_TagManagementDTO">TagManagementDTO</h2>

<a id="schematagmanagementdto"></a>
<a id="schema_TagManagementDTO"></a>
<a id="tocStagmanagementdto"></a>
<a id="tocstagmanagementdto"></a>

```json
{
  "id": 0,
  "tagName": "string",
  "courseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfo": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|tagName|string|true|none||标签名称（如“性能优化”）|
|courseId|string|true|none||外键关联 CourseInfo|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfo|object|false|none||创建时间（由系统自动填充）|

<h2 id="tocS_ResponseEntityPageTagManagementDTO">ResponseEntityPageTagManagementDTO</h2>

<a id="schemaresponseentitypagetagmanagementdto"></a>
<a id="schema_ResponseEntityPageTagManagementDTO"></a>
<a id="tocSresponseentitypagetagmanagementdto"></a>
<a id="tocsresponseentitypagetagmanagementdto"></a>

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "tagName": "string",
      "courseId": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "courseInfo": {}
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "totalPages": 0,
  "totalElements": 0,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "empty": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|content|[[TagManagementDTO](#schematagmanagementdto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_TagManagementCriteria">TagManagementCriteria</h2>

<a id="schematagmanagementcriteria"></a>
<a id="schema_TagManagementCriteria"></a>
<a id="tocStagmanagementcriteria"></a>
<a id="tocstagmanagementcriteria"></a>

```json
{
  "tagName": "string",
  "tagType": "string",
  "tagDescription": "string",
  "courseId": "string",
  "createdAtMin": {
    "seconds": 0,
    "nanos": 0
  },
  "createdAtMax": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "searchTerm": "string",
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tagName|string|false|none||标签名称（模糊匹配）|
|tagType|string|false|none||标签类型（精确匹配）|
|tagDescription|string|false|none||标签描述（模糊匹配）|
|courseId|string|false|none||课程ID（精确匹配）|
|createdAtMin|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|createdAtMax|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|searchTerm|string|false|none||通用搜索词（在标签名称和元数据字段中进行模糊搜索）|
|page|integer|false|none||页码|
|size|integer|false|none||每页记录数|
|sort|string|false|none||排序字段和方向，格式为：property,direction，例如：tagName,asc|

<h2 id="tocS_ListTagManagementDTO">ListTagManagementDTO</h2>

<a id="schemalisttagmanagementdto"></a>
<a id="schema_ListTagManagementDTO"></a>
<a id="tocSlisttagmanagementdto"></a>
<a id="tocslisttagmanagementdto"></a>

```json
{
  "id": 0,
  "tagName": "string",
  "courseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfo": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|tagName|string|true|none||标签名称（如“性能优化”）|
|courseId|string|true|none||外键关联 CourseInfo|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfo|object|false|none||创建时间（由系统自动填充）|

<h2 id="tocS_ResponseEntityCourseAttachmentDTO">ResponseEntityCourseAttachmentDTO</h2>

<a id="schemaresponseentitycourseattachmentdto"></a>
<a id="schema_ResponseEntityCourseAttachmentDTO"></a>
<a id="tocSresponseentitycourseattachmentdto"></a>
<a id="tocsresponseentitycourseattachmentdto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "contentId": "string",
  "fileId": "string",
  "fileType": "string",
  "filePath": "string",
  "fileName": "string",
  "fileSize": "string",
  "uploadTime": {},
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfo": {},
  "courseContent": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||附件ID|
|courseId|string|false|none||课程ID|
|contentId|string|false|none||课程内容ID|
|fileId|string|false|none||文件ID|
|fileType|string|false|none||文件类型|
|filePath|string|false|none||文件路径|
|fileName|string|false|none||文件名称|
|fileSize|string|false|none||文件大小（字节）|
|uploadTime|object|false|none||创建时间（由系统自动填充）|
|description|string|false|none||文件描述|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfo|object|false|none||创建时间（由系统自动填充）|
|courseContent|object|false|none||创建时间（由系统自动填充）|

<h2 id="tocS_CourseAttachmentDTO">CourseAttachmentDTO</h2>

<a id="schemacourseattachmentdto"></a>
<a id="schema_CourseAttachmentDTO"></a>
<a id="tocScourseattachmentdto"></a>
<a id="tocscourseattachmentdto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "contentId": "string",
  "fileId": "string",
  "fileType": "string",
  "filePath": "string",
  "fileName": "string",
  "fileSize": "string",
  "uploadTime": {
    "dateTime": "string",
    "offset": {},
    "zone": {}
  },
  "description": null,
  "metadata": null,
  "version": null,
  "createdBy": null,
  "createdAt": null,
  "updatedBy": null,
  "updatedAt": null,
  "isDeleted": null,
  "courseInfo": null,
  "courseContent": null
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||附件ID|
|courseId|string|false|none||课程ID|
|contentId|string|false|none||课程内容ID|
|fileId|string|false|none||文件ID|
|fileType|string|false|none||文件类型|
|filePath|string|false|none||文件路径|
|fileName|string|false|none||文件名称|
|fileSize|string|false|none||文件大小（字节）|
|uploadTime|[ZonedDateTime](#schemazoneddatetime)|false|none||上传时间|
|description|null|false|none||文件描述|
|metadata|null|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|null|false|none||当前版本号|
|createdBy|null|false|none||创建者账号或姓名|
|createdAt|null|false|none||创建时间（由系统自动填充）|
|updatedBy|null|false|none||最后修改者账号或姓名|
|updatedAt|null|false|none||最后更新时间（由系统自动填充）|
|isDeleted|null|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfo|null|false|none||none|
|courseContent|null|false|none||none|

<h2 id="tocS_ListCourseAttachmentDTO">ListCourseAttachmentDTO</h2>

<a id="schemalistcourseattachmentdto"></a>
<a id="schema_ListCourseAttachmentDTO"></a>
<a id="tocSlistcourseattachmentdto"></a>
<a id="tocslistcourseattachmentdto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "contentId": "string",
  "fileId": "string",
  "fileType": "string",
  "filePath": "string",
  "fileName": "string",
  "fileSize": "string",
  "uploadTime": {},
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "courseInfo": {},
  "courseContent": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||附件ID|
|courseId|string|false|none||课程ID|
|contentId|string|false|none||课程内容ID|
|fileId|string|false|none||文件ID|
|fileType|string|false|none||文件类型|
|filePath|string|false|none||文件路径|
|fileName|string|false|none||文件名称|
|fileSize|string|false|none||文件大小（字节）|
|uploadTime|object|false|none||创建时间（由系统自动填充）|
|description|string|false|none||文件描述|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfo|object|false|none||创建时间（由系统自动填充）|
|courseContent|object|false|none||创建时间（由系统自动填充）|

<h2 id="tocS_ResponseEntityPageCourseAttachmentDTO">ResponseEntityPageCourseAttachmentDTO</h2>

<a id="schemaresponseentitypagecourseattachmentdto"></a>
<a id="schema_ResponseEntityPageCourseAttachmentDTO"></a>
<a id="tocSresponseentitypagecourseattachmentdto"></a>
<a id="tocsresponseentitypagecourseattachmentdto"></a>

```json
{
  "total": 0,
  "content": [
    {
      "id": 0,
      "courseId": "string",
      "contentId": "string",
      "fileId": "string",
      "fileType": "string",
      "filePath": "string",
      "fileName": "string",
      "fileSize": "string",
      "uploadTime": {
        "dateTime": "string",
        "offset": {},
        "zone": {}
      },
      "description": null,
      "metadata": null,
      "version": null,
      "createdBy": null,
      "createdAt": null,
      "updatedBy": null,
      "updatedAt": null,
      "isDeleted": null,
      "courseInfo": null,
      "courseContent": null
    }
  ],
  "pageable": null,
  "totalPages": null,
  "totalElements": null,
  "number": null,
  "size": null,
  "numberOfElements": null,
  "sort": null,
  "first": null,
  "last": null,
  "empty": null
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||none|
|content|[[CourseAttachmentDTO](#schemacourseattachmentdto)]|false|none||none|
|pageable|null|false|none||none|
|totalPages|null|false|none||none|
|totalElements|null(int64)|false|none||none|
|number|null|false|none||none|
|size|null|false|none||none|
|numberOfElements|null|false|none||none|
|sort|null|false|none||none|
|first|null|false|none||none|
|last|null|false|none||none|
|empty|null|false|none||none|

<h2 id="tocS_CourseAttachmentCriteria">CourseAttachmentCriteria</h2>

<a id="schemacourseattachmentcriteria"></a>
<a id="schema_CourseAttachmentCriteria"></a>
<a id="tocScourseattachmentcriteria"></a>
<a id="tocscourseattachmentcriteria"></a>

```json
{
  "courseId": "string",
  "attachmentName": "string",
  "attachmentType": "string",
  "attachmentDescription": "string",
  "fileSizeMin": 0,
  "fileSizeMax": 0,
  "isDeleted": false,
  "searchTerm": "string",
  "page": 0,
  "size": 10,
  "sort": "id,asc"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|courseId|string|false|none||课程ID（精确匹配）|
|attachmentName|string|false|none||附件名称（模糊匹配）|
|attachmentType|string|false|none||附件类型（精确匹配）|
|attachmentDescription|string|false|none||附件描述（模糊匹配）|
|fileSizeMin|integer(int64)|false|none||文件大小最小值（范围匹配）|
|fileSizeMax|integer(int64)|false|none||文件大小最大值（范围匹配）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|searchTerm|string|false|none||通用搜索词（在附件名称和描述字段中进行模糊搜索）|
|page|integer|false|none||页码|
|size|integer|false|none||每页记录数|
|sort|string|false|none||排序字段和方向，格式为：property,direction，例如：attachmentName,asc|

