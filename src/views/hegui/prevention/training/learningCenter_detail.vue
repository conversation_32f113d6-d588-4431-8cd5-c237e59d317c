<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Bell,
  Calendar,
  CircleCheck,
  Clock,
  DataBoard,
  Document,
  Fold,
  Folder,
  FullScreen,
  Headset,
  InfoFilled,
  Platform,
  Search,
  Trophy,
  User,
  VideoPause,
  VideoPlay } from '@element-plus/icons-vue'
// 路由实例
const router = useRouter()
const route = useRoute()

const activeTab = ref('courseware')
const currentChapter = ref(1)
const showTestDialog = ref(false)
const chapters = ref([
  {
    id: 1,
    label: '第一章 反洗钱概述',
    duration: 15,
    completed: true,
    children: [
      { id: 11, label: '1.1 反洗钱基本概念', duration: 5, completed: true },
      { id: 12, label: '1.2 反洗钱法律法规', duration: 10, completed: true },
    ],
  },
  {
    id: 2,
    label: '第二章 客户身份识别',
    duration: 30,
    completed: false,
    children: [
      { id: 21, label: '2.1 客户身份识别流程', duration: 15, completed: false },
      { id: 22, label: '2.2 高风险客户管理', duration: 15, completed: false },
    ],
  },
  {
    id: 3,
    label: '第三章 可疑交易监测',
    duration: 45,
    completed: false,
    children: [
      { id: 31, label: '3.1 可疑交易特征', duration: 20, completed: false },
      { id: 32, label: '3.2 可疑交易报告', duration: 25, completed: false },
    ],
  },
])
const materials = ref([
  { type: 'PDF', name: '反洗钱合规手册.pdf', size: '2.4MB' },
  { type: 'PPT', name: '反洗钱培训课件.pptx', size: '5.7MB' },
  { type: 'DOC', name: '客户身份识别指南.docx', size: '1.2MB' },
  { type: 'XLS', name: '可疑交易监测表.xlsx', size: '3.5MB' },
])
const defaultProps = {
  children: 'children',
  label: 'label',
}
function handleNodeClick(data: any) {
  currentChapter.value = data.id
}
function startTest() {
  router.push({
    name: '/training/learningCenter/test',
    query: { id: route.query.id },
  })
  // 跳转到测试页面
}
</script>

<template>
  <div class="min-h-screen flex flex-col bg-gray-50">
    <div class="flex flex-1">
      <!-- 主内容区 -->
      <div class="flex-1 p-6">
        <!-- 页面标题 -->
        <h1 class="mb-6 text-2xl text-gray-800 font-bold">
          反洗钱合规培训
        </h1>

        <!-- 课程信息卡片 -->
        <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
          <div class="flex flex-wrap gap-4 text-sm">
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Clock />
              </el-icon>
              <span>课程时长：2 小时</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <User />
              </el-icon>
              <span>课程讲师：王教授（法务部）</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Calendar />
              </el-icon>
              <span>学习期限：2024-05-15 截止</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Trophy />
              </el-icon>
              <span>考核要求：需完成课后测试，通过分数 80 分</span>
            </div>
          </div>
        </div>

        <!-- 课程内容区 -->
        <div class="flex gap-6">
          <!-- 左侧章节导航 -->
          <div class="w-64 rounded-lg bg-white p-4 shadow-sm">
            <div class="mb-4 flex items-center justify-between">
              <h3 class="text-gray-800 font-medium">
                课程章节
              </h3>
              <el-icon class="cursor-pointer">
                <Fold />
              </el-icon>
            </div>
            <div class="space-y-1">
              <div class="rounded bg-blue-50 px-3 py-2 text-blue-600">
                <div class="flex items-center">
                  <el-icon class="mr-2">
                    <VideoPlay />
                  </el-icon>
                  <span>第一章 反洗钱概述</span>
                  <span class="ml-auto text-xs text-gray-500">15 分钟</span>
                </div>
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-50">
                <div class="flex items-center">
                  <el-icon class="mr-2 text-green-500">
                    <CircleCheck />
                  </el-icon>
                  <span>1.1 反洗钱基本概念</span>
                  <span class="ml-auto text-xs text-gray-500">8 分钟</span>
                </div>
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-50">
                <div class="flex items-center">
                  <el-icon class="mr-2 text-green-500">
                    <CircleCheck />
                  </el-icon>
                  <span>1.2 反洗钱法律法规</span>
                  <span class="ml-auto text-xs text-gray-500">7 分钟</span>
                </div>
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-50">
                <div class="flex items-center">
                  <el-icon class="mr-2">
                    <VideoPlay />
                  </el-icon>
                  <span>第二章 客户身份识别</span>
                  <span class="ml-auto text-xs text-gray-500">25 分钟</span>
                </div>
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-50">
                <div class="flex items-center">
                  <el-icon class="mr-2">
                    <Document />
                  </el-icon>
                  <span>2.1 客户身份识别流程</span>
                  <span class="ml-auto text-xs text-gray-500">12 分钟</span>
                </div>
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-50">
                <div class="flex items-center">
                  <el-icon class="mr-2">
                    <Document />
                  </el-icon>
                  <span>2.2 高风险客户识别</span>
                  <span class="ml-auto text-xs text-gray-500">13 分钟</span>
                </div>
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-50">
                <div class="flex items-center">
                  <el-icon class="mr-2">
                    <VideoPlay />
                  </el-icon>
                  <span>第三章 可疑交易报告</span>
                  <span class="ml-auto text-xs text-gray-500">30 分钟</span>
                </div>
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-50">
                <div class="flex items-center">
                  <el-icon class="mr-2">
                    <Document />
                  </el-icon>
                  <span>3.1 可疑交易识别标准</span>
                  <span class="ml-auto text-xs text-gray-500">15 分钟</span>
                </div>
              </div>
              <div class="cursor-pointer rounded px-3 py-2 hover:bg-gray-50">
                <div class="flex items-center">
                  <el-icon class="mr-2">
                    <Document />
                  </el-icon>
                  <span>3.2 报告撰写规范</span>
                  <span class="ml-auto text-xs text-gray-500">15 分钟</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧内容区 -->

          <div class="flex-1 overflow-hidden rounded-lg bg-white shadow-sm">
            <!-- 内容标签页 -->
            <el-tabs v-model="activeTab" class="px-4 pt-4">
              <el-tab-pane label="课件" name="courseware">
                <!-- 视频播放器 -->
                <div class="aspect-w-16 aspect-h-9 mb-4 overflow-hidden rounded-lg bg-black">
                  <img
                    src="https://ai-public.mastergo.com/ai/img_res/344b6c721489b9d462195255c9a9c2bc.jpg"
                    alt="Video Thumbnail"
                    class="h-full w-full object-cover"
                  >
                </div>
                <!-- 视频控制栏 -->
                <div class="mt-4 flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <button class="h-8 w-8 flex items-center justify-center rounded-full bg-gray-100">
                      <el-icon><VideoPlay /></el-icon>
                    </button>
                    <div class="h-1 w-64 overflow-hidden rounded-full bg-gray-200">
                      <div class="h-full w-1/3 bg-blue-500" />
                    </div>
                    <span class="text-sm text-gray-500">8:24 / 25:00</span>
                  </div>
                  <div class="flex items-center space-x-4">
                    <button class="text-sm text-gray-600 hover:text-gray-800">
                      0.5x
                    </button>
                    <button class="text-sm text-gray-600 hover:text-gray-800">
                      1.0x
                    </button>
                    <button class="text-sm text-gray-600 hover:text-gray-800">
                      1.5x
                    </button>
                    <button class="text-sm text-gray-600 hover:text-gray-800">
                      2.0x
                    </button>
                    <button class="text-sm text-gray-600 hover:text-gray-800">
                      <el-icon><Headset /></el-icon>
                    </button>
                    <button class="text-sm text-gray-600 hover:text-gray-800">
                      <el-icon><FullScreen /></el-icon>
                    </button>
                  </div>
                </div>
                <!-- 课程内容文字 -->
                <div class="mb-6 pb-12 space-y-4">
                  <h2 class="text-xl font-bold">
                    2.1 客户身份识别流程
                  </h2>
                  <p class="text-gray-700">
                    客户身份识别（Customer Identification Program, CIP）是金融机构反洗钱工作的首要环节，也是防范洗钱风险的第一道防线。有效的客户身份识别能够帮助金融机构了解客户真实身份，评估客户风险等级，为后续的持续识别和交易监控奠定基础。
                  </p>
                  <h3 class="mt-6 text-lg font-medium">
                    基本流程
                  </h3>
                  <ul class="list-disc pl-6 text-gray-700 space-y-2">
                    <li>收集客户基本信息：包括姓名、证件类型及号码、联系方式、职业等</li>
                    <li>核实客户身份：通过官方渠道验证客户提供信息的真实性</li>
                    <li>了解客户背景：包括职业状况、收入来源、交易目的等</li>
                    <li>评估客户风险等级：根据客户身份、职业、交易特征等因素进行风险评估</li>
                    <li>建立客户档案：保存客户身份资料和交易记录</li>
                  </ul>
                  <h3 class="mt-6 text-lg font-medium">
                    关键注意事项
                  </h3>
                  <ul class="list-disc pl-6 text-gray-700 space-y-2">
                    <li>对于非自然人客户，需核实实际控制人信息</li>
                    <li>对于政治公众人物（PEPs）及其亲属和密切关系人，需采取强化识别措施</li>
                    <li>对于高风险客户，需提高审查频率和深度</li>
                    <li>识别过程中发现可疑情况，应及时上报反洗钱合规部门</li>
                  </ul>
                </div>
              </el-tab-pane>
              <el-tab-pane label="资料" name="materials">
                <el-table :data="materials" class="w-full">
                  <el-table-column prop="type" label="类型" width="120">
                    <template #default="{ row }">
                      <el-icon v-if="row.type === 'PDF'">
                        <Document />
                      </el-icon>
                      <el-icon v-else-if="row.type === 'PPT'">
                        <DataBoard />
                      </el-icon>
                      <el-icon v-else>
                        <Folder />
                      </el-icon>
                      <span class="ml-2">{{ row.type }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="资料名称" />
                  <el-table-column prop="size" label="大小" width="120" />
                  <el-table-column label="操作" width="120">
                    <template #default>
                      <el-button type="primary" text size="small">
                        下载
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部控制栏 -->

    <footer class="fixed bottom-0 left-55 right-0 border-t border-gray-200 bg-white p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600">进度：3/12 章节</span>
          <el-progress :percentage="25" :show-text="false" class="w-40" />
        </div>
        <div class="flex items-center space-x-4">
          <el-button :icon="ArrowLeft">
            上一节
          </el-button>
          <el-button type="primary" :icon="CircleCheck" @click="showTestDialog = true">
            标记完成
          </el-button>
          <el-button>下一节</el-button>
        </div>
      </div>
    </footer>
    <!-- 测试弹窗 -->
    <el-dialog v-model="showTestDialog" title="课程测试" width="500px">
      <div class="rounded-lg bg-white p-6 shadow-sm">
        <div class="space-y-6">
          <div class="mb-4 text-lg text-gray-800 font-medium">
            考核测试说明
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Document />
              </el-icon>
              <span class="text-gray-700">测试题数：20 题</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Clock />
              </el-icon>
              <span class="text-gray-700">测试时长：30 分钟</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <CircleCheck />
              </el-icon>
              <span class="text-gray-700">通过分数：80 分</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Refresh />
              </el-icon>
              <span class="text-gray-700">考试次数：1 次机会</span>
            </div>
          </div>
          <div class="mt-4 border-t border-gray-200 pt-4">
            <div class="mb-2 text-sm text-gray-600 font-medium">
              考试规则说明：
            </div>
            <ul class="text-sm text-gray-500 space-y-2">
              <li>1. 测试开始后不能暂停</li>
              <li>2. 提交后立即显示成绩</li>
              <li>3. 未通过可重新测试</li>
              <li>4. 测试过程中禁止切换页面</li>
              <li>5. 系统将自动记录测试时间</li>
            </ul>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button class="!rounded-button whitespace-nowrap" @click="showTestDialog = false">
            稍后测试
          </el-button>
          <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="startTest">
            开始测试
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
