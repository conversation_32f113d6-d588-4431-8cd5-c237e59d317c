<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import planApi from '@/api/complianceApi/prevention/plan'

// 定义组件名称，解决 keep-alive 缓存问题
defineOptions({
  name: 'TrainingPlan',
})

// 路由实例
const router = useRouter()

// 接口类型定义
interface TrainingPlan {
  id: number
  planCode: string
  planName: string
  planDescription: string
  planType: string
  planStatus: string
  startDate: any
  endDate: any
  trainingTarget: string
  responsiblePerson: string
  trainingObjective: string
  implementationPlan: string
  resourceRequirements: string
  riskManagement: string
  targetCompletionRate: number
  creatorType: string
  priority: string
  coverImageUrl: string
  metadata: string
  version: number
  createdBy: string
  createdAt: any
  updatedBy: string
  updatedAt: any
  isDeleted: boolean
  trainingPlanCourses: any[]
}

interface TrainingFilters {
  planStatus: string
  trainingTarget: string
  dateRange: string[]
  searchTerm: string
  planType: string
  priority: string
  responsiblePerson: string
}

// 响应式数据
const loading = ref(false)
const trainingPage = ref(1)
const trainingPageSize = ref(10)
const trainingTotal = ref(0)
const trainingFilters = ref<TrainingFilters>({
  planStatus: '',
  trainingTarget: '',
  dateRange: [],
  searchTerm: '',
  planType: '',
  priority: '',
  responsiblePerson: '',
})

// 培训计划列表数据
const trainingPlans = ref<TrainingPlan[]>([])

// 获取培训计划列表
async function getTrainingPlanList() {
  try {
    loading.value = true
    
    // 构建查询参数
    const queryParams: any = {
      page: trainingPage.value - 1, // 后端页码从0开始
      size: trainingPageSize.value,
      includeCourses: true, // 包含课程信息
    }

    // 添加筛选条件
    if (trainingFilters.value.planStatus) {
      queryParams.planStatus = trainingFilters.value.planStatus
    }
    if (trainingFilters.value.trainingTarget) {
      queryParams.trainingTarget = trainingFilters.value.trainingTarget
    }
    if (trainingFilters.value.searchTerm.trim()) {
      queryParams.searchTerm = trainingFilters.value.searchTerm.trim()
    }
    if (trainingFilters.value.planType) {
      queryParams.planType = trainingFilters.value.planType
    }
    if (trainingFilters.value.priority) {
      queryParams.priority = trainingFilters.value.priority
    }
    if (trainingFilters.value.responsiblePerson) {
      queryParams.responsiblePerson = trainingFilters.value.responsiblePerson
    }

    // 处理日期范围
    if (trainingFilters.value.dateRange && trainingFilters.value.dateRange.length === 2) {
      const [startDate, endDate] = trainingFilters.value.dateRange
      queryParams.startDateFrom = {
        seconds: Math.floor(new Date(startDate).getTime() / 1000),
        nanos: 0
      }
      queryParams.startDateTo = {
        seconds: Math.floor(new Date(endDate).getTime() / 1000),
        nanos: 0
      }
    }

    const response = await planApi.getLearningPlanList(queryParams)
    
    if (response && response.content) {
      trainingPlans.value = response.content
      trainingTotal.value = response.totalElements || 0
    } else {
      trainingPlans.value = []
      trainingTotal.value = 0
    }
  } catch (error) {
    console.error('获取培训计划列表失败:', error)
    ElMessage.error('获取培训计划列表失败')
    trainingPlans.value = []
    trainingTotal.value = 0
  } finally {
    loading.value = false
  }
}

// 进度条颜色配置
const progressColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 },
]

// 工具函数
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    DRAFT: '草稿',
    PENDING: '待启动',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消',
  }
  return statusMap[status] || status
}

function getStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    DRAFT: 'info',
    PENDING: 'warning',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger',
  }
  return typeMap[status] || 'info'
}

// 格式化日期
function formatDate(dateObj: any): string {
  if (!dateObj) return '-'
  
  let date: Date
  if (dateObj.seconds) {
    // 处理后端返回的时间格式
    date = new Date(dateObj.seconds * 1000)
  } else if (typeof dateObj === 'string') {
    date = new Date(dateObj)
  } else {
    return '-'
  }
  
  return date.toLocaleDateString('zh-CN')
}

// 计算课程数量
function getCourseCount(plan: TrainingPlan): number {
  return plan.trainingPlanCourses ? plan.trainingPlanCourses.length : 0
}

// 事件处理函数
function goAddEdit(item: any) {
  if (item?.id) {
    // 编辑计划
    router.push({
      name: '/training/plan/edit',
      query: { id: item.id },
    })
  }
  else {
    // 新增计划
    router.push({
      name: '/training/plan/edit',
    })
  }
}

// 查看计划详情
function goDetail(row: TrainingPlan) {
  router.push({
    name: '/training/plan/detail',
    query: { id: row.id },
  })
}

async function handleDelete(row: TrainingPlan) {
  try {
    await ElMessageBox.confirm(
      `确定要删除培训计划"${row.planName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // TODO: 调用删除接口
    ElMessage.success('删除成功')
    // 重新加载数据
    await getTrainingPlanList()
  }
  catch {
    ElMessage.info('已取消删除')
  }
}

function resetFilters() {
  trainingFilters.value = {
    planStatus: '',
    trainingTarget: '',
    dateRange: [],
    searchTerm: '',
    planType: '',
    priority: '',
    responsiblePerson: '',
  }
  
  // 重置页码并查询
  trainingPage.value = 1
  getTrainingPlanList()
}

// 手动查询函数
function handleSearch() {
  trainingPage.value = 1
  getTrainingPlanList()
}

// 处理分页变化
function handlePageChange() {
  getTrainingPlanList()
}

function handleSizeChange() {
  trainingPage.value = 1
  getTrainingPlanList()
}

// 组件挂载时的初始化
onMounted(() => {
  getTrainingPlanList()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <page-header>
      <template #content>
        <div class="aic jcsb flex">
          <div class="f-20 c-['#000000']">
            <h2 class="text-xl text-gray-800 font-semibold">
              培训计划
            </h2>
          </div>
          <div class="aic flex">
            <div>
              <el-button type="primary" @click="goAddEdit(null)">
                <svg-icon name="ep:plus" />
                <span class="ml-4">新增培训计划</span>
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </page-header>
    <!-- Training Plan Filter -->
    <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
      <div class="flex flex-wrap items-center gap-4">
        <el-select
          v-model="trainingFilters.planStatus"
          placeholder="计划状态"
          class="w-40"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option label="草稿" value="DRAFT" />
          <el-option label="待启动" value="PENDING" />
          <el-option label="进行中" value="IN_PROGRESS" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>

        <el-select
          v-model="trainingFilters.planType"
          placeholder="计划类型"
          class="w-40"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option label="个人计划" value="个人计划" />
          <el-option label="部门计划" value="部门计划" />
          <el-option label="公司计划" value="公司计划" />
        </el-select>

        <el-select
          v-model="trainingFilters.priority"
          placeholder="优先级"
          class="w-40"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option label="高" value="高" />
          <el-option label="中" value="中" />
          <el-option label="低" value="低" />
        </el-select>

        <el-input
          v-model="trainingFilters.trainingTarget"
          placeholder="培训对象"
          class="w-40"
          clearable
        />

        <el-input
          v-model="trainingFilters.responsiblePerson"
          placeholder="负责人"
          class="w-40"
          clearable
        />

        <el-date-picker
          v-model="trainingFilters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="w-64"
          clearable
        />

        <el-input
          v-model="trainingFilters.searchTerm"
          placeholder="搜索计划名称或关键词"
          class="w-64"
          clearable
          @keyup.enter="handleSearch"
        />

        <el-button type="primary" @click="handleSearch">
          <svg-icon name="ep:search" />
          查询
        </el-button>

        <el-button class="ml-2" @click="resetFilters">
          <svg-icon name="ep:refresh" />
          重置
        </el-button>
      </div>
    </div>

    <!-- Training Plan Table -->
    <div class="overflow-hidden rounded-lg bg-white shadow-sm">
      <el-table
        v-loading="loading"
        :data="trainingPlans"
        style="width: 100%"
        border
        element-loading-text="加载中..."
      >
        <el-table-column prop="planCode" label="计划编号" width="150" show-overflow-tooltip />
        
        <el-table-column prop="planName" label="计划名称" min-width="200" show-overflow-tooltip />

        <el-table-column prop="period" label="计划周期" width="200">
          <template #default="{ row }">
            <div class="text-sm">
              <div>{{ formatDate(row.startDate) }}</div>
              <div class="text-gray-500">
                至 {{ formatDate(row.endDate) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="trainingTarget" label="培训对象" width="120" show-overflow-tooltip />

        <el-table-column prop="responsiblePerson" label="负责人" width="100" show-overflow-tooltip />

        <el-table-column prop="courseCount" label="包含课程数" width="100" align="center">
          <template #default="{ row }">
            <el-tag size="small" effect="plain">
              {{ getCourseCount(row) }} 门
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="targetCompletionRate" label="目标完成率" width="150">
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-progress
                :percentage="row.targetCompletionRate || 0"
                :color="progressColors"
                :stroke-width="8"
                class="flex-1"
              />
              <span class="min-w-[40px] text-sm font-medium">{{ row.targetCompletionRate || 0 }}%</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="priority" label="优先级" width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.priority === '高' ? 'danger' : row.priority === '中' ? 'warning' : 'info'"
              effect="light"
              size="small"
            >
              {{ row.priority || '-' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="planStatus" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.planStatus)"
              effect="light"
              size="small"
            >
              {{ getStatusText(row.planStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="goDetail(row)"
            >
              查看
            </el-button>
            <el-button
              type="primary"
              link
              size="small"
              @click="goAddEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="flex items-center justify-between p-4">
        <span class="text-sm text-gray-600">
          共 {{ trainingTotal }} 条记录
        </span>
        <el-pagination
          v-model:current-page="trainingPage"
          v-model:page-size="trainingPageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="trainingTotal"
          layout="total, sizes, prev, pager, next, jumper"
          class="justify-end"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>
