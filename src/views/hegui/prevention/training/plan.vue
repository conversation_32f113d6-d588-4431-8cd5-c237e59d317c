<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

// 定义组件名称，解决 keep-alive 缓存问题
defineOptions({
  name: 'TrainingPlan',
})

// 路由实例
const router = useRouter()

// 接口类型定义
interface TrainingPlan {
  id: number
  name: string
  startDate: string
  endDate: string
  target: string
  courseCount: number
  completionRate: number
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue'
}

interface TrainingFilters {
  status: string
  target: string
  dateRange: string[]
  keyword: string
}

// 响应式数据
const loading = ref(false)
const trainingPage = ref(1)
const trainingPageSize = ref(10)
const trainingTotal = ref(35)
const trainingFilters = ref<TrainingFilters>({
  status: 'all',
  target: 'all',
  dateRange: [],
  keyword: '',
})

// 原始数据
const originalTrainingPlans = ref<TrainingPlan[]>([
  {
    id: 1,
    name: '2024年第一季度合规培训计划',
    startDate: '2024-01-01',
    endDate: '2024-03-31',
    target: '全体员工',
    courseCount: 5,
    completionRate: 45,
    status: 'in_progress',
  },
  {
    id: 2,
    name: '新员工合规培训计划',
    startDate: '2024-01-15',
    endDate: '长期计划',
    target: '新员工',
    courseCount: 3,
    completionRate: 85,
    status: 'in_progress',
  },
  {
    id: 3,
    name: '反洗钱专项培训计划',
    startDate: '2024-02-15',
    endDate: '2024-03-15',
    target: '财务部门',
    courseCount: 2,
    completionRate: 100,
    status: 'completed',
  },
  {
    id: 4,
    name: '信息安全意识培训',
    startDate: '2024-03-01',
    endDate: '2024-03-31',
    target: '全体员工',
    courseCount: 4,
    completionRate: 32,
    status: 'in_progress',
  },
  {
    id: 5,
    name: '管理层领导力培训',
    startDate: '2024-01-10',
    endDate: '2024-02-10',
    target: '管理层',
    courseCount: 6,
    completionRate: 100,
    status: 'completed',
  },
  {
    id: 6,
    name: '产品知识培训',
    startDate: '2024-04-01',
    endDate: '2024-04-30',
    target: '销售部门',
    courseCount: 3,
    completionRate: 0,
    status: 'not_started',
  },
  {
    id: 7,
    name: '客户服务培训',
    startDate: '2024-02-01',
    endDate: '2024-02-28',
    target: '客服部门',
    courseCount: 5,
    completionRate: 78,
    status: 'in_progress',
  },
])

// 计算属性：过滤后的培训计划
const trainingPlans = computed(() => {
  let filtered = [...originalTrainingPlans.value]

  // 状态过滤
  if (trainingFilters.value.status !== 'all') {
    filtered = filtered.filter(plan => plan.status === trainingFilters.value.status)
  }

  // 培训对象过滤
  if (trainingFilters.value.target !== 'all') {
    const targetMap: Record<string, string> = {
      all_employees: '全体员工',
      management: '管理层',
      new_employees: '新员工',
    }
    const targetText = targetMap[trainingFilters.value.target]
    if (targetText) {
      filtered = filtered.filter(plan => plan.target === targetText)
    }
  }

  // 关键词搜索
  if (trainingFilters.value.keyword.trim()) {
    const keyword = trainingFilters.value.keyword.trim().toLowerCase()
    filtered = filtered.filter(plan =>
      plan.name.toLowerCase().includes(keyword)
      || plan.target.toLowerCase().includes(keyword),
    )
  }

  // 日期范围过滤
  if (trainingFilters.value.dateRange && trainingFilters.value.dateRange.length === 2) {
    const [startDate, endDate] = trainingFilters.value.dateRange
    filtered = filtered.filter((plan) => {
      const planStart = new Date(plan.startDate)
      const planEnd = new Date(plan.endDate === '长期计划' ? '2099-12-31' : plan.endDate)
      const filterStart = new Date(startDate)
      const filterEnd = new Date(endDate)

      return (planStart >= filterStart && planStart <= filterEnd)
        || (planEnd >= filterStart && planEnd <= filterEnd)
        || (planStart <= filterStart && planEnd >= filterEnd)
    })
  }

  return filtered
})

// 进度条颜色配置
const progressColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 },
]

// 工具函数
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    not_started: '未开始',
    in_progress: '进行中',
    completed: '已完成',
    overdue: '已逾期',
  }
  return statusMap[status] || ''
}

function getStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    not_started: 'info',
    in_progress: 'warning',
    completed: 'success',
    overdue: 'danger',
  }
  return typeMap[status] || 'info'
}

// 事件处理函数

function goAddEdit(item: any) {
  if (item?.id) {
    // 编辑计划
    router.push({
      name: '/training/plan/edit',
      query: { id: item.id },
    })
  }
  else {
    // 新增计划
    router.push({
      name: '/training/plan/edit',
    })
  }
}
// 查看计划详情
function goDetail(row) {
  router.push({
    name: '/training/plan/detail',
    query: { id: row.id },
  })
}

async function handleDelete(row: TrainingPlan) {
  try {
    await ElMessageBox.confirm(
      `确定要删除培训计划"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 模拟删除操作
    const index = originalTrainingPlans.value.findIndex(plan => plan.id === row.id)
    if (index > -1) {
      originalTrainingPlans.value.splice(index, 1)
      trainingTotal.value--
      ElMessage.success('删除成功')
    }
  }
  catch {
    ElMessage.info('已取消删除')
  }
}

function resetFilters() {
  trainingFilters.value = {
    status: 'all',
    target: 'all',
    dateRange: [],
    keyword: '',
  }
}

// 监听过滤条件变化，重置分页
watch(
  () => trainingFilters.value,
  () => {
    trainingPage.value = 1
  },
  { deep: true },
)

// 组件挂载时的初始化
onMounted(() => {
  // 这里可以添加数据加载逻辑
  console.log('培训计划页面已加载')
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <page-header>
      <template #content>
        <div class="aic jcsb flex">
          <div class="f-20 c-['#000000']">
            <h2 class="text-xl text-gray-800 font-semibold">
              培训计划
            </h2>
          </div>
          <div class="aic flex">
            <div>
              <el-button type="primary" @click="goAddEdit(null)">
                <svg-icon name="ep:plus" />
                <span class="ml-4">新增培训计划</span>
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </page-header>
    <!-- Training Plan Filter -->
    <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
      <div class="flex flex-wrap items-center gap-4">
        <el-select
          v-model="trainingFilters.status"
          placeholder="计划状态"
          class="w-40"
          clearable
        >
          <el-option label="全部" value="all" />
          <el-option label="未开始" value="not_started" />
          <el-option label="进行中" value="in_progress" />
          <el-option label="已完成" value="completed" />
          <el-option label="已逾期" value="overdue" />
        </el-select>

        <el-select
          v-model="trainingFilters.target"
          placeholder="培训对象"
          class="w-40"
          clearable
        >
          <el-option label="全部" value="all" />
          <el-option label="全体员工" value="all_employees" />
          <el-option label="管理层" value="management" />
          <el-option label="特定部门" value="specific_department" />
          <el-option label="新员工" value="new_employees" />
        </el-select>

        <el-date-picker
          v-model="trainingFilters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="w-64"
          clearable
        />

        <el-input
          v-model="trainingFilters.keyword"
          placeholder="搜索计划名称或关键词"
          class="w-64"
          clearable
        />

        <el-button class="ml-2" @click="resetFilters">
          重置
        </el-button>
      </div>
    </div>

    <!-- Training Plan Table -->
    <div class="overflow-hidden rounded-lg bg-white shadow-sm">
      <el-table
        v-loading="loading"
        :data="trainingPlans"
        style="width: 100%"
        border
        element-loading-text="加载中..."
      >
        <el-table-column prop="name" label="计划名称" show-overflow-tooltip />

        <el-table-column prop="period" label="计划周期">
          <template #default="{ row }">
            <div class="text-sm">
              <div>{{ row.startDate }}</div>
              <div class="text-gray-500">
                至 {{ row.endDate }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="target" label="培训对象" show-overflow-tooltip />

        <el-table-column prop="courseCount" label="包含课程数" align="center">
          <template #default="{ row }">
            <el-tag size="small" effect="plain">
              {{ row.courseCount }} 门
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="completionRate" label="完成率">
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-progress
                :percentage="row.completionRate"
                :color="progressColors"
                :stroke-width="8"
                class="flex-1"
              />
              <span class="min-w-[40px] text-sm font-medium">{{ row.completionRate }}%</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              effect="light"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="goDetail(row)"
            >
              查看
            </el-button>
            <el-button
              type="primary"
              link
              size="small"
              @click="goAddEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="flex items-center justify-between p-4">
        <span class="text-sm text-gray-600">
          共 {{ trainingPlans.length }} 条记录，总计 {{ trainingTotal }} 条
        </span>
        <el-pagination
          v-model:current-page="trainingPage"
          v-model:page-size="trainingPageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="trainingTotal"
          layout="total, sizes, prev, pager, next, jumper"
          class="justify-end"
        />
      </div>
    </div>
  </div>
</template>
