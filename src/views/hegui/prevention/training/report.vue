<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { Delete, Download, Search, View } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
// 筛选条件
const reportType = ref('all')
const dateRange = ref([])
const searchKeyword = ref('')
const hasPermission = ref(true) // 模拟权限
// 路由实例
const router = useRouter()
// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 统计卡片数据
const stats = ref([
  { label: '总课程数', value: 42, unit: '门课程' },
  { label: '总学习时长', value: 156, unit: '小时' },
  { label: '总参与人次', value: 842, unit: '人次' },
  { label: '平均完成率', value: 78, unit: '%', progress: 78 },
])

// 报告列表数据
const reports = ref([
  {
    id: 1,
    name: '2024年第一季度合规培训报告',
    date: '2024-04-01',
    type: '计划报告',
    scope: '全体员工',
    generator: '王经理',
  },
  {
    id: 2,
    name: '反洗钱合规培训课程报告',
    date: '2024-02-01',
    type: '课程报告',
    scope: '财务部门',
    generator: '李经理',
  },
  {
    id: 3,
    name: '法务部2024年3月培训报告',
    date: '2024-04-02',
    type: '部门报告',
    scope: '法务部',
    generator: '张经理',
  },
  {
    id: 4,
    name: '信息安全培训月度报告',
    date: '2024-03-15',
    type: '课程报告',
    scope: '技术部',
    generator: '赵主管',
  },
  {
    id: 5,
    name: '新员工入职培训总结',
    date: '2024-04-05',
    type: '个人报告',
    scope: '新员工',
    generator: '陈HR',
  },
  {
    id: 6,
    name: '销售团队产品知识培训',
    date: '2024-03-20',
    type: '部门报告',
    scope: '销售部',
    generator: '吴总监',
  },
  {
    id: 7,
    name: '领导力发展计划季度报告',
    date: '2024-04-10',
    type: '计划报告',
    scope: '管理层',
    generator: '郑总',
  },
])

// 图表引用
const pieChart = ref<HTMLElement>()
const lineChart = ref<HTMLElement>()
const barChart = ref<HTMLElement>()

// 过滤后的报告列表
const filteredReports = computed(() => {
  let result = [...reports.value]

  // 按报告类型过滤
  if (reportType.value !== 'all') {
    result = result.filter(report => report.type.includes(reportType.value))
  }

  // 按日期范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [start, end] = dateRange.value
    result = result.filter(
      report =>
        new Date(report.date) >= new Date(start)
          && new Date(report.date) <= new Date(end),
    )
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      report =>
        report.name.toLowerCase().includes(keyword)
        || report.scope.toLowerCase().includes(keyword)
        || report.generator.toLowerCase().includes(keyword),
    )
  }

  return result
})

// 获取标签类型
function getTagType(type: string) {
  switch (type) {
    case '课程报告':
      return 'success'
    case '计划报告':
      return 'primary'
    case '部门报告':
      return 'warning'
    case '个人报告':
      return 'info'
    default:
      return ''
  }
}

// 操作函数
function generateReport() {
  console.log('生成报告')
}

function viewReport(report: any) {
  console.log('查看报告', report)
}

function exportReport(report: any) {
  console.log('导出报告', report)
}

function deleteReport(report: any) {
  console.log('删除报告', report)
}

// 初始化图表
function initCharts() {
  // 饼图
  const pieInstance = echarts.init(pieChart.value)
  pieInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '课程类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 35, name: '合规培训' },
          { value: 25, name: '技术培训' },
          { value: 20, name: '管理培训' },
          { value: 15, name: '产品培训' },
          { value: 5, name: '其他' },
        ],
      },
    ],
  })

  // 折线图
  const lineInstance = echarts.init(lineChart.value)
  lineInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['参与人数', '完成人数'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2023-11', '2023-12', '2024-01', '2024-02', '2024-03', '2024-04'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '参与人数',
        type: 'line',
        data: [120, 132, 101, 134, 90, 230],
        smooth: true,
      },
      {
        name: '完成人数',
        type: 'line',
        data: [80, 110, 75, 100, 70, 180],
        smooth: true,
      },
    ],
  })

  // 柱状图
  const barInstance = echarts.init(barChart.value)
  barInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      max: 100,
    },
    yAxis: {
      type: 'category',
      data: ['财务部', '技术部', '销售部', '市场部', '人事部', '法务部'],
    },
    series: [
      {
        name: '完成率',
        type: 'bar',
        data: [78, 82, 65, 89, 92, 76],
        itemStyle: {
          color: '#1E88E5',
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%',
        },
      },
    ],
  })

  // 响应式调整
  window.addEventListener('resize', () => {
    pieInstance.resize()
    lineInstance.resize()
    barInstance.resize()
  })
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})

// function goAddEdit(item: any) {
//   if (item?.id) {
//     // 编辑考核
//     router.push({
//       name: '/training/report/edit',
//       query: { id: item.id },
//     })
//   }
//   else {
//     // 新增考核
//     router.push({
//       name: '/training/report/edit',
//     })
//   }
// }
// 查看报告详情
function goDetail(row) {
  router.push({
    name: '/training/report/detail',
    query: { id: row.id },
  })
}
</script>
<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <!-- 筛选区 -->
    <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
      <div class="flex flex-wrap items-center gap-4">
        <!-- 报告类型下拉框 -->
        <div class="w-full md:w-auto">
          <label class="mb-1 block text-sm text-gray-700 font-medium">报告类型</label>
          <el-select v-model="reportType" placeholder="全部" class="w-full md:w-48">
            <el-option label="全部" value="all" />
            <el-option label="课程报告" value="course" />
            <el-option label="计划报告" value="plan" />
            <el-option label="部门报告" value="department" />
            <el-option label="个人报告" value="personal" />
          </el-select>
        </div>

        <!-- 时间范围选择器 -->
        <div class="w-full md:w-auto">
          <label class="mb-1 block text-sm text-gray-700 font-medium">时间范围</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="w-full md:w-64"
          />
        </div>

        <!-- 搜索框 -->
        <div class="w-full flex-1 md:w-auto">
          <label class="mb-1 block text-sm text-gray-700 font-medium">搜索</label>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索报告名称或关键词"
            class="w-full"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 生成报告按钮 -->
        <div v-if="hasPermission" class="w-full self-end md:w-auto">
          <el-button
            type="primary"
            class="!rounded-button whitespace-nowrap"
            @click="generateReport"
          >
            生成报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 报告概览区 -->
    <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
      <h2 class="mb-4 text-lg text-gray-800 font-semibold">
        报告概览
      </h2>

      <!-- 统计卡片组 -->
      <div class="grid grid-cols-1 mb-6 gap-4 lg:grid-cols-4 md:grid-cols-2">
        <div
          v-for="(stat, index) in stats"
          :key="index"
          class="border border-gray-100 rounded-lg bg-gray-50 p-4"
        >
          <div class="mb-1 text-sm text-gray-500">
            {{ stat.label }}
          </div>
          <div class="flex items-end justify-between">
            <div class="text-2xl text-gray-800 font-bold">
              {{ stat.value }}
            </div>
            <div class="text-sm text-gray-500">
              {{ stat.unit }}
            </div>
          </div>
          <div v-if="stat.progress" class="mt-2">
            <el-progress :percentage="stat.progress" :show-text="false" />
          </div>
        </div>
      </div>

      <!-- 统计图表区 -->
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
        <!-- 课程类型分布饼图 -->
        <div class="border border-gray-100 rounded-lg bg-gray-50 p-4">
          <h3 class="mb-2 text-sm text-gray-700 font-medium">
            课程类型分布
          </h3>
          <div ref="pieChart" class="h-64" />
        </div>

        <!-- 月度学习趋势折线图 -->
        <div class="border border-gray-100 rounded-lg bg-gray-50 p-4">
          <h3 class="mb-2 text-sm text-gray-700 font-medium">
            月度学习趋势
          </h3>
          <div ref="lineChart" class="h-64" />
        </div>

        <!-- 部门完成率对比柱状图 -->
        <div class="border border-gray-100 rounded-lg bg-gray-50 p-4 lg:col-span-2">
          <h3 class="mb-2 text-sm text-gray-700 font-medium">
            部门完成率对比
          </h3>
          <div ref="barChart" class="h-64" />
        </div>
      </div>
    </div>

    <!-- 详细报告列表 -->
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <h2 class="mb-4 text-lg text-gray-800 font-semibold">
        报告列表
      </h2>
      <el-table :data="filteredReports" style="width: 100%">
        <el-table-column prop="name" label="报告名称" />
        <el-table-column prop="date" label="生成日期" />
        <el-table-column prop="type" label="报告类型">
          <template #default="{ row }">
            <el-tag :type="getTagType(row.type)" size="small">
              {{ row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scope" label="覆盖范围" />
        <el-table-column prop="generator" label="生成人" />
        <el-table-column label="操作" width="250">
          <template #default="{ row }">
            <el-button size="small" @click="viewReport(row)">
              <svg-icon name="ep:view" />
              <span class="ml-1" @click="goDetail(row)">查看</span>
            </el-button>
            <el-button size="small" @click="exportReport(row)">
              <svg-icon name="ep:download" />
              <span class="ml-1">导出</span>
            </el-button>
            <el-button
              v-if="hasPermission"
              size="small"
              type="danger"
              @click="deleteReport(row)"
            >
              <el-icon><Delete /></el-icon>
              <span class="ml-1">删除</span>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <div class="mt-4 flex items-center justify-between">
        <div class="text-sm text-gray-500">
          共 {{ filteredReports.length }} 条记录
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="prev, pager, next, sizes"
          :total="filteredReports.length"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-table {
  --el-table-border-color: #f0f0f0;
  --el-table-header-bg-color: #f8f9fa;
}

.el-table :deep(.el-table__row) {
  --el-table-row-hover-bg-color: #f5f7fa;
}

.el-table :deep(.el-table__row--striped) {
  --el-table-tr-bg-color: #fafafa;
}

.el-progress {
  --el-progress-text-color: #666;
}
</style>
