---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 18-合规培训服务/课程信息管理

## POST 创建新的课程信息

POST /whiskerguardtrainingservice/api/course/infos

{@code POST  /course/infos} : Create a new courseInfo.

> Body 请求参数

```json
{
  "id": 0,
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "applicableRole": "string",
  "instructor": "string",
  "coursePoint": 0,
  "coverImageUrl": "string",
  "producer": "string",
  "releaseDate": {
    "seconds": 0,
    "nanos": 0
  },
  "lastUpdate": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "averageRating": 0,
  "ratingCount": 0,
  "bookmarkCount": 0,
  "shareCount": 0,
  "learnerCount": 0,
  "completionCount": 0,
  "courseOverview": "string",
  "learningObjective": "string",
  "prerequisites": "string",
  "certificationInfo": "string",
  "courseContent": {
    "id": 0,
    "courseId": "string",
    "contentType": "string",
    "contentTitle": "string",
    "contentDescription": "string",
    "contentUrl": "string",
    "durationMinutes": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "playbackSetting": {
    "id": 0,
    "courseId": "string",
    "autoPlay": true,
    "rememberPlayback": true,
    "continuousPlayback": true,
    "defaultClarity": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "interactiveFeature": {
    "id": 0,
    "courseId": "string",
    "commentEnabled": true,
    "likeEnabled": true,
    "shareEnabled": true,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "relatedCourses": "new HashSet<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[CourseInfoDTO](#schemacourseinfodto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "",
  "courseName": "",
  "courseType": "",
  "trainingTheme": "",
  "difficultyLevel": "",
  "applicableRole": "",
  "instructor": "",
  "producer": "",
  "releaseDate": {
    "seconds": 0,
    "nanos": 0
  },
  "lastUpdate": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "",
  "durationMinutes": 0,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseDescription": {
    "id": 0,
    "courseId": "",
    "courseOverview": "",
    "learningObjective": "",
    "prerequisites": "",
    "certificationInfo": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "courseContent": {
    "id": 0,
    "courseId": "",
    "contentType": "",
    "contentTitle": "",
    "contentDescription": "",
    "contentUrl": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "playbackSetting": {
    "id": 0,
    "courseId": "",
    "autoPlay": false,
    "rememberPlayback": false,
    "continuousPlayback": false,
    "defaultClarity": "",
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "interactiveFeature": {
    "id": 0,
    "courseId": "",
    "commentEnabled": false,
    "likeEnabled": false,
    "shareEnabled": false,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false
  },
  "relatedCourses": [
    {
      "id": 0,
      "relatedCourseId": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false,
      "courseInfos": [
        {
          "id": 0,
          "tenantId": 0,
          "courseCode": "",
          "courseName": "",
          "courseType": "",
          "trainingTheme": "",
          "difficultyLevel": "",
          "applicableRole": "",
          "instructor": "",
          "producer": "",
          "releaseDate": {
            "seconds": 0,
            "nanos": 0
          },
          "lastUpdate": {
            "seconds": 0,
            "nanos": 0
          },
          "status": "",
          "durationMinutes": 0,
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0
          },
          "isDeleted": false,
          "courseDescription": {
            "id": 0,
            "courseId": "",
            "courseOverview": "",
            "learningObjective": "",
            "prerequisites": "",
            "certificationInfo": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "courseContent": {
            "id": 0,
            "courseId": "",
            "contentType": "",
            "contentTitle": "",
            "contentDescription": "",
            "contentUrl": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "playbackSetting": {
            "id": 0,
            "courseId": "",
            "autoPlay": false,
            "rememberPlayback": false,
            "continuousPlayback": false,
            "defaultClarity": "",
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "interactiveFeature": {
            "id": 0,
            "courseId": "",
            "commentEnabled": false,
            "likeEnabled": false,
            "shareEnabled": false,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false
          },
          "relatedCourses": [
            {
              "id": 0,
              "relatedCourseId": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false,
              "courseInfos": [
                {
                  "id": 0,
                  "tenantId": 0,
                  "courseCode": "",
                  "courseName": "",
                  "courseType": "",
                  "trainingTheme": "",
                  "difficultyLevel": "",
                  "applicableRole": "",
                  "instructor": "",
                  "producer": "",
                  "releaseDate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "lastUpdate": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "status": "",
                  "durationMinutes": 0,
                  "metadata": "",
                  "version": 0,
                  "createdBy": "",
                  "createdAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "updatedBy": "",
                  "updatedAt": {
                    "seconds": 0,
                    "nanos": 0
                  },
                  "isDeleted": false,
                  "courseDescription": {
                    "id": 0,
                    "courseId": "",
                    "courseOverview": "",
                    "learningObjective": "",
                    "prerequisites": "",
                    "certificationInfo": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "courseContent": {
                    "id": 0,
                    "courseId": "",
                    "contentType": "",
                    "contentTitle": "",
                    "contentDescription": "",
                    "contentUrl": "",
                    "durationMinutes": 0,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "playbackSetting": {
                    "id": 0,
                    "courseId": "",
                    "autoPlay": false,
                    "rememberPlayback": false,
                    "continuousPlayback": false,
                    "defaultClarity": "",
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "interactiveFeature": {
                    "id": 0,
                    "courseId": "",
                    "commentEnabled": false,
                    "likeEnabled": false,
                    "shareEnabled": false,
                    "metadata": "",
                    "version": 0,
                    "createdBy": "",
                    "createdAt": "[Object]",
                    "updatedBy": "",
                    "updatedAt": "[Object]",
                    "isDeleted": false
                  },
                  "relatedCourses": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityCourseInfoDTO](#schemaresponseentitycourseinfodto)|

# 数据模型

<h2 id="tocS_CourseContentDTO">CourseContentDTO</h2>

<a id="schemacoursecontentdto"></a>
<a id="schema_CourseContentDTO"></a>
<a id="tocScoursecontentdto"></a>
<a id="tocscoursecontentdto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "contentType": "string",
  "contentTitle": "string",
  "contentDescription": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseId|string|true|none||外键关联 CourseInfo|
|contentType|string|false|none||内容类型|
|contentTitle|string|false|none||内容标题|
|contentDescription|string|false|none||内容描述|
|contentUrl|string|false|none||内容链接（如视频/文档地址）|
|durationMinutes|integer|false|none||内容时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_PlaybackSettingDTO">PlaybackSettingDTO</h2>

<a id="schemaplaybacksettingdto"></a>
<a id="schema_PlaybackSettingDTO"></a>
<a id="tocSplaybacksettingdto"></a>
<a id="tocsplaybacksettingdto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "autoPlay": true,
  "rememberPlayback": true,
  "continuousPlayback": true,
  "defaultClarity": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseId|string|true|none||外键关联 CourseInfo|
|autoPlay|boolean|false|none||是否自动播放|
|rememberPlayback|boolean|false|none||是否记忆播放进度|
|continuousPlayback|boolean|false|none||是否连续播放|
|defaultClarity|string|false|none||默认清晰度|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_InteractiveFeatureDTO">InteractiveFeatureDTO</h2>

<a id="schemainteractivefeaturedto"></a>
<a id="schema_InteractiveFeatureDTO"></a>
<a id="tocSinteractivefeaturedto"></a>
<a id="tocsinteractivefeaturedto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "commentEnabled": true,
  "likeEnabled": true,
  "shareEnabled": true,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseId|string|true|none||外键关联 CourseInfo|
|commentEnabled|boolean|false|none||是否允许评论|
|likeEnabled|boolean|false|none||是否允许点赞|
|shareEnabled|boolean|false|none||是否允许分享|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_CourseInfoDTO">CourseInfoDTO</h2>

<a id="schemacourseinfodto"></a>
<a id="schema_CourseInfoDTO"></a>
<a id="tocScourseinfodto"></a>
<a id="tocscourseinfodto"></a>

```json
{
  "id": 0,
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "applicableRole": "string",
  "instructor": "string",
  "coursePoint": 0,
  "coverImageUrl": "string",
  "producer": "string",
  "releaseDate": {
    "seconds": 0,
    "nanos": 0
  },
  "lastUpdate": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "averageRating": 0,
  "ratingCount": 0,
  "bookmarkCount": 0,
  "shareCount": 0,
  "learnerCount": 0,
  "completionCount": 0,
  "courseOverview": "string",
  "learningObjective": "string",
  "prerequisites": "string",
  "certificationInfo": "string",
  "courseContent": {
    "id": 0,
    "courseId": "string",
    "contentType": "string",
    "contentTitle": "string",
    "contentDescription": "string",
    "contentUrl": "string",
    "durationMinutes": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "playbackSetting": {
    "id": 0,
    "courseId": "string",
    "autoPlay": true,
    "rememberPlayback": true,
    "continuousPlayback": true,
    "defaultClarity": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "interactiveFeature": {
    "id": 0,
    "courseId": "string",
    "commentEnabled": true,
    "likeEnabled": true,
    "shareEnabled": true,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "relatedCourses": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseCode|string|false|none||课程代码（如 COURSE20240115001）|
|courseName|string|true|none||课程名称|
|courseType|string|true|none||课程类型|
|trainingTheme|string|false|none||培训主题（如“前端开发”）|
|difficultyLevel|string|false|none||难度等级|
|applicableRole|string|false|none||适用角色（如 ["frontend_dev", "backend_dev"]）|
|instructor|string|false|none||讲师姓名|
|coursePoint|integer(int64)|false|none||课程积分|
|coverImageUrl|string|false|none||封面图片地址|
|producer|string|false|none||制作人|
|releaseDate|[Instant](#schemainstant)|false|none||发布日期|
|lastUpdate|[Instant](#schemainstant)|false|none||最后更新时间|
|status|string|false|none||课程状态|
|durationMinutes|integer|false|none||课程总时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|averageRating|number|false|none||平均评分（1-5星）|
|ratingCount|integer|false|none||评分总数|
|bookmarkCount|integer|false|none||收藏总数|
|shareCount|integer|false|none||分享总数|
|learnerCount|integer|false|none||学习人数|
|completionCount|integer|false|none||完成人数|
|courseOverview|string|false|none||课程概述|
|learningObjective|string|false|none||学习目标|
|prerequisites|string|false|none||先修要求|
|certificationInfo|string|false|none||认证信息|
|courseContent|[CourseContentDTO](#schemacoursecontentdto)|false|none||none|
|playbackSetting|[PlaybackSettingDTO](#schemaplaybacksettingdto)|false|none||none|
|interactiveFeature|[InteractiveFeatureDTO](#schemainteractivefeaturedto)|false|none||none|
|relatedCourses|[[RelatedCourseDTO](#schemarelatedcoursedto)]|false|none||none|

<h2 id="tocS_RelatedCourseDTO">RelatedCourseDTO</h2>

<a id="schemarelatedcoursedto"></a>
<a id="schema_RelatedCourseDTO"></a>
<a id="tocSrelatedcoursedto"></a>
<a id="tocsrelatedcoursedto"></a>

```json
{
  "id": 0,
  "relatedCourseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "courseInfos": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|relatedCourseId|string|true|none||外键关联 CourseInfo|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfos|[[CourseInfoDTO](#schemacourseinfodto)]|false|none||none|

<h2 id="tocS_ResponseEntityCourseInfoDTO">ResponseEntityCourseInfoDTO</h2>

<a id="schemaresponseentitycourseinfodto"></a>
<a id="schema_ResponseEntityCourseInfoDTO"></a>
<a id="tocSresponseentitycourseinfodto"></a>
<a id="tocsresponseentitycourseinfodto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "applicableRole": "string",
  "instructor": "string",
  "coursePoint": 0,
  "coverImageUrl": "string",
  "producer": "string",
  "releaseDate": {
    "seconds": 0,
    "nanos": 0
  },
  "lastUpdate": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "averageRating": 0,
  "ratingCount": 0,
  "bookmarkCount": 0,
  "shareCount": 0,
  "learnerCount": 0,
  "completionCount": 0,
  "courseOverview": "string",
  "learningObjective": "string",
  "prerequisites": "string",
  "certificationInfo": "string",
  "courseContent": {
    "id": 0,
    "courseId": "string",
    "contentType": "string",
    "contentTitle": "string",
    "contentDescription": "string",
    "contentUrl": "string",
    "durationMinutes": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "playbackSetting": {
    "id": 0,
    "courseId": "string",
    "autoPlay": true,
    "rememberPlayback": true,
    "continuousPlayback": true,
    "defaultClarity": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "interactiveFeature": {
    "id": 0,
    "courseId": "string",
    "commentEnabled": true,
    "likeEnabled": true,
    "shareEnabled": true,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "relatedCourses": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|courseCode|string|false|none||课程代码（如 COURSE20240115001）|
|courseName|string|true|none||课程名称|
|courseType|string|true|none||课程类型|
|trainingTheme|string|false|none||培训主题（如“前端开发”）|
|difficultyLevel|string|false|none||难度等级|
|applicableRole|string|false|none||适用角色（如 ["frontend_dev", "backend_dev"]）|
|instructor|string|false|none||讲师姓名|
|coursePoint|integer(int64)|false|none||课程积分|
|coverImageUrl|string|false|none||封面图片地址|
|producer|string|false|none||制作人|
|releaseDate|[Instant](#schemainstant)|false|none||发布日期|
|lastUpdate|[Instant](#schemainstant)|false|none||最后更新时间|
|status|string|false|none||课程状态|
|durationMinutes|integer|false|none||课程总时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|averageRating|number|false|none||平均评分（1-5星）|
|ratingCount|integer|false|none||评分总数|
|bookmarkCount|integer|false|none||收藏总数|
|shareCount|integer|false|none||分享总数|
|learnerCount|integer|false|none||学习人数|
|completionCount|integer|false|none||完成人数|
|courseOverview|string|false|none||课程概述|
|learningObjective|string|false|none||学习目标|
|prerequisites|string|false|none||先修要求|
|certificationInfo|string|false|none||认证信息|
|courseContent|[CourseContentDTO](#schemacoursecontentdto)|false|none||none|
|playbackSetting|[PlaybackSettingDTO](#schemaplaybacksettingdto)|false|none||none|
|interactiveFeature|[InteractiveFeatureDTO](#schemainteractivefeaturedto)|false|none||none|
|relatedCourses|[[RelatedCourseDTO](#schemarelatedcoursedto)]|false|none||none|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

