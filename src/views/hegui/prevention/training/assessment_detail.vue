<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

onMounted(() => {

})
const form = ref({})

const responseMeasures = ref(false)
const rateNum = ref(3.5)

const activeName = ref(1)

function goEdit(item: any) {
  console.log(item)
}
</script>

<template>
  <div>
    <el-form ref="formRef" :rules="formRules" :model="form" label-width="110px" label-position="left">
      <page-header title="" content="">
        <template #content>
          <div class="aic jcsb flex">
            <div class="f-28">
              <span class="mr-10 c-[#000]">反洗钱合规培训</span>
              <!-- <el-button type="danger">高风险流程</el-button> -->
            </div>
            <div>
              <div class="aic flex">
                <div>
                  <el-button type="primary" @click="goAddEdit(null)">
                    <svg-icon name="ep:plus" />
                    <span class="ml-4">开始学习</span>
                  </el-button>
                </div>
                <div class="ml-14">
                  <el-button type="primary" plain>
                    <svg-icon name="ep:download" />
                    <span class="ml-4">编辑课程</span>
                  </el-button>
                </div>
                <div class="ml-14">
                  <el-button type="primary" plain>
                    <svg-icon name="ep:download" />
                    <span class="ml-4">删除课程</span>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </page-header>
      <page-main style="background-color: transparent;">
        <el-row>
          <el-col :span="16" class="pr-10">
            <el-card shadow="hover">
              <div class="flex">
                <!--                <div style="width: 200px;height: 112px;">
                  <img style="width: 100%;height: 100%;border-radius: 8px 8px;"
                    src="https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg" alt="" />
                </div> -->
                <div class="f-14 ml-20 flex-1">
                  <el-row>
                    <el-col :span="12">
                      <div>
                        <span class="c-[#666666]">考核名称：</span>
                        <span class="c-[#000]">反洗钱合规知识考核</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">考核类型：</span>
                        <span class="c-[#000]">在线考试</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">关联课程：</span>
                        <span class="c-[#000]">反洗钱合规培训</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">考核对象：</span>
                        <span class="c-[#000]">全体员工</span>
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div>
                        <span class="c-[#666666]">开始时间：</span>
                        <span class="c-[#000]">2024-02-01 09:00</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">结束时间：</span>
                        <span class="c-[#000]">2024-02-15 18:00</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">考核类型：</span>
                        <span class="c-[#000]">在线考试</span>
                      </div>
                      <div class="mt-16">
                        <span class="c-[#666666]">及格分数：</span>
                        <span class="c-[#000]">85分</span>
                      </div>
                    </el-col>
                  </el-row>
                  <!-- <div class="mt-16">
                    学习进度
                  </div>
                  <div class="mt-6">
                    <el-progress :percentage="75" :show-text="true" />
                  </div> -->
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
                <el-tab-pane label="考核说明" :name="1">
                  考核说明
                </el-tab-pane>
                <el-tab-pane label="考题预览" name="second">
                  考题预览
                </el-tab-pane>
                <el-tab-pane label="考核结果" name="third">
                  考核结果
                </el-tab-pane>
                <el-tab-pane label="考核统计" name="third">
                  考核统计
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="8" class="pl-10">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  考核管理员
                </div>
              </template>
              <div class="flex">
                <div class="ml-8" style="width: 32px;height: 32px;">
                  <img
                    style="width: 100%;height: 100%;border-radius: 50%;"
                    src="https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg" alt=""
                  >
                </div>
                <div class="ml-8">
                  <div class="f-14">
                    王管理
                  </div>
                  <div class="f-14 c-[#666666]">
                    电话：1234567890
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关课程
                </div>
              </template>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 aic flex fw-500">
                  <span class="c-[#111827]">反洗钱合规培训</span>
                </div>
                <div class="f-16 aic mt-8 flex fw-500">
                  <span class="c-[#666]">类型：在线课程</span>
                </div>
                <div class="f-16 aic mt-8 flex fw-500">
                  时长：120分钟
                </div>
                <div class="mt-8">
                  <el-text type="primary">
                    查看课程
                  </el-text>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关考核
                </div>
              </template>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 aic flex fw-500">
                  <span class="c-[#111827]">反洗钱合规培训</span>
                </div>
                <div class="f-16 aic mt-8 flex fw-500">
                  <span class="c-[#666]">2024-01-15 ~ 2024-01-30</span>
                </div>
                <div class="mt-8">
                  <!-- <el-tag>Tag 1</el-tag> -->
                  <el-tag class="ml-2" type="success">
                    已完成
                  </el-tag>
                  <!-- <el-tag class="ml-2" type="info">Tag 3</el-tag> -->
                  <!-- <el-tag class="ml-2" type="warning">Tag 4</el-tag> -->
                  <!-- <el-tag class="ml-2" type="danger">Tag 5</el-tag> -->
                </div>
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 aic flex fw-500">
                  <span class="c-[#111827]">反洗钱合规培训</span>
                </div>
                <div class="f-16 aic mt-8 flex fw-500">
                  <span class="c-[#666]">2024-01-15 ~ 2024-01-30</span>
                </div>
                <div class="mt-8">
                  <el-tag class="ml-2" type="info">
                    未开始
                  </el-tag>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </page-main>
    </el-form>

    <HDialog v-model="responseMeasures" :title="form.id ? '创建应对措施' : '创建应对措施'" modulewidth="50vw">
      <el-form ref="formRef" :model="form" class="px-2" :rules="formRules" label-position="top" label-width="66px">
        <div class="mb-10">
          <el-button color="#1677FF">
            AI辅助
          </el-button>
        </div>
        <el-form-item label="措施名称：" prop="name">
          <el-input v-model="form.name" size="large" placeholder="请输入对应措施名称" clearable />
        </el-form-item>
        <el-form-item label="措施描述:" prop="name">
          <el-select v-model="value" class="m-2" placeholder="请选择措施描述">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="措施类型:" prop="name">
          <el-select v-model="value" class="m-2" placeholder="请选择措施类型">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="责任部门:" prop="name">
          <el-select v-model="value" class="m-2" placeholder="请选择责任部门">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="完成期限：" prop="name" label-width="100">
          <el-date-picker
            v-model="form.send_time" value-format="x" style="width: 100%;" type="date"
            placeholder="请选择完成期限"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="responseMeasures = false, form = {} ">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
