<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowRight, Bottom, Check, Close, Delete, Edit, Plus, Search, Top, Upload, View,
} from '@element-plus/icons-vue'
import trainingCurriculum from '@/api/complianceApi/prevention/trainingCurriculum'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const isEdit = ref(false)

// 课程表单数据
const courseForm = ref({
  id: null,
  courseName: '',
  courseCode: '',
  courseType: '',
  durationMinutes: 0,
  applicableRole: [],
  isMandatory: false,
  status: 'draft',
  trainingTheme: '',
  difficultyLevel: '',
  instructor: '',
  producer: '',
  metadata: '',
  description: '',
})

// 保存课程
async function saveCourse() {
  try {
    loading.value = true
    const params = {
      ...courseForm.value,
      applicableRole: Array.isArray(courseForm.value.applicableRole)
        ? courseForm.value.applicableRole.join(',')
        : courseForm.value.applicableRole,
    }

    let res
    if (isEdit.value && courseForm.value.id) {
      res = await trainingCurriculum.courseInfo({}, params, 'update')
    }
    else {
      res = await trainingCurriculum.courseInfo({}, params, 'create')
    }

    if (res) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      router.push('/training/course')
    }
  }
  catch (error) {
    console.error('保存课程失败', error)
    ElMessage.error('保存失败')
  }
  finally {
    loading.value = false
  }
}

// 保存并新增
async function saveAndNew() {
  try {
    await saveCourse()
    // 重置表单
    courseForm.value = {
      id: null,
      courseName: '',
      courseCode: '',
      courseType: '',
      durationMinutes: 0,
      applicableRole: [],
      isMandatory: false,
      status: 'draft',
      trainingTheme: '',
      difficultyLevel: '',
      instructor: '',
      producer: '',
      metadata: '',
      description: '',
    }
    isEdit.value = false
  }
  catch (error) {
    console.error('保存并新增失败', error)
  }
}

// 取消
function cancel() {
  router.push('/training/course')
}

// 获取课程详情（编辑模式）
async function getCourseDetail() {
  if (route.query.id) {
    try {
      isEdit.value = true
      const res = await trainingCurriculum.courseInfo({}, { id: route.query.id }, 'info')
      if (res) {
        courseForm.value = {
          ...res,
          applicableRole: res.applicableRole ? res.applicableRole.split(',') : [],
        }
      }
    }
    catch (error) {
      console.error('获取课程详情失败', error)
      ElMessage.error('获取课程详情失败')
    }
  }
}

onMounted(() => {
  getCourseDetail()
})

const chapterData = ref([
  {
    id: 1,
    label: '第一章 合规基础知识',
    children: [
      {
        id: 11,
        label: '1.1 合规概念',
      },
      {
        id: 12,
        label: '1.2 合规重要性',
      },
    ],
  },
  {
    id: 2,
    label: '第二章 法律法规',
    children: [
      {
        id: 21,
        label: '2.1 相关法律',
      },
      {
        id: 22,
        label: '2.2 法规解读',
      },
    ],
  },
])

const materialData = ref([
  { name: '合规手册', type: 'pdf', description: '' },
  { name: '案例集', type: 'word', description: '' },
])

const linkData = ref([
  { name: '合规官网', url: 'https://example.com', description: '公司合规官方网站' },
])

const passScore = ref(60)

const questionData = ref([
  { type: '单选题', difficulty: '中等', score: 5 },
  { type: '多选题', difficulty: '较难', score: 10 },
])

const lawData = ref([
  { name: '中华人民共和国公司法', version: '2021版' },
])

const policyData = ref([
  { name: '员工行为规范', version: '2023版' },
])

const courseData = ref([
  { name: '合规意识培训', type: '合规意识' },
])
</script>

<template>
  <div class="min-h-screen flex bg-gray-100">
    <!-- 主内容区 -->
    <div class="ml-60 flex-1 p-6">
      <!-- 标题和操作按钮 -->
      <div class="mb-6 flex items-center justify-between">
        <h1 class="text-xl text-gray-800 font-bold">
          {{ isEdit ? '编辑培训课程' : '新增培训课程' }}
        </h1>
        <div class="flex space-x-3">
          <el-button type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="saveCourse">
            <el-icon class="mr-1">
              <Check />
            </el-icon>保存
          </el-button>
          <el-button class="!rounded-button whitespace-nowrap" :disabled="isEdit" @click="saveAndNew">
            <el-icon class="mr-1">
              <Plus />
            </el-icon>保存并新增
          </el-button>
          <el-button class="!rounded-button whitespace-nowrap" @click="cancel">
            <el-icon class="mr-1">
              <Close />
            </el-icon>取消
          </el-button>
          <el-button class="!rounded-button whitespace-nowrap">
            <el-icon class="mr-1">
              <view />
            </el-icon>预览
          </el-button>
        </div>
      </div>

      <!-- 表单内容 -->
      <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
        <!-- 基本信息 -->
        <div class="mb-8">
          <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
            基本信息
          </h2>
          <div class="grid grid-cols-2 gap-6">
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">课程名称<span class="text-red-500">*</span></label>
              <el-input v-model="courseForm.courseName" placeholder="请输入课程名称" class="flex-1" />
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">课程编号</label>
              <el-input v-model="courseForm.courseCode" placeholder="系统自动生成" class="flex-1" />
              <el-button class="!rounded-button ml-2 whitespace-nowrap">
                自动生成
              </el-button>
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">课程类型<span class="text-red-500">*</span></label>
              <el-select v-model="courseForm.courseType" placeholder="请选择课程类型" class="flex-1">
                <el-option label="法律法规" value="1" />
                <el-option label="合规意识" value="2" />
                <el-option label="业务技能" value="3" />
                <el-option label="管理能力" value="4" />
                <el-option label="其他" value="5" />
              </el-select>
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">课程时长<span class="text-red-500">*</span></label>
              <el-input-number v-model="courseForm.durationMinutes" :min="0" class="flex-1" />
              <el-select class="ml-2 w-24">
                <el-option label="小时" value="hour" />
                <el-option label="分钟" value="minute" />
              </el-select>
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">培训对象<span class="text-red-500">*</span></label>
              <el-select v-model="courseForm.applicableRole" multiple placeholder="请选择培训对象" class="flex-1">
                <el-option label="全体员工" value="1" />
                <el-option label="管理层" value="2" />
                <el-option label="特定部门" value="3" />
                <el-option label="特定岗位" value="4" />
                <el-option label="新员工" value="5" />
                <el-option label="其他" value="6" />
              </el-select>
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">必修标识</label>
              <el-radio-group v-model="courseForm.isMandatory" class="flex-1">
                <el-radio :label="true">
                  是
                </el-radio>
                <el-radio :label="false">
                  否
                </el-radio>
              </el-radio-group>
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">课程状态</label>
              <el-radio-group v-model="courseForm.status" class="flex-1">
                <el-radio label="draft">
                  草稿
                </el-radio>
                <el-radio label="published">
                  已发布
                </el-radio>
                <el-radio label="offline">
                  已下线
                </el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>

        <!-- 课程信息 -->
        <div class="mb-8">
          <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
            课程信息
          </h2>
          <div class="grid grid-cols-2 gap-6">
            <div class="flex flex-col">
              <div class="mb-4 flex items-center">
                <label class="w-24 pr-4 text-right text-sm text-gray-600">课程简介<span class="text-red-500">*</span></label>
                <div class="flex-1">
                  <el-input v-model="courseForm.description" type="textarea" :rows="5" placeholder="请输入课程简介" />
                </div>
              </div>
              <div class="flex items-center">
                <label class="w-24 pr-4 text-right text-sm text-gray-600">适合人群</label>
                <div class="flex-1">
                  <el-input type="textarea" :rows="3" placeholder="请输入适合人群描述" />
                </div>
              </div>
            </div>
            <div>
              <div class="mb-4 flex items-center">
                <label class="w-24 pr-4 text-right text-sm text-gray-600">课程封面</label>
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :auto-upload="false"
                  class="flex-1"
                >
                  <el-icon><Plus /></el-icon>
                </el-upload>
              </div>
              <div class="ml-24 text-xs text-gray-500">
                建议尺寸：16:9比例，最小分辨率800x450px
              </div>
            </div>
          </div>

          <!-- 学习目标 -->
          <div class="mt-6">
            <div class="mb-2 flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">学习目标</label>
              <el-button size="small" class="!rounded-button whitespace-nowrap">
                <el-icon class="mr-1">
                  <Plus />
                </el-icon>添加目标
              </el-button>
            </div>
            <div class="ml-24">
              <div class="mb-2 flex items-center">
                <el-input placeholder="请输入学习目标" class="w-96" />
                <el-button type="text" class="ml-2">
                  <el-icon><Top /></el-icon>
                </el-button>
                <el-button type="text" class="ml-1">
                  <el-icon><Bottom /></el-icon>
                </el-button>
                <el-button type="text" class="ml-1 text-red-500">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 讲师信息 -->
          <div class="grid grid-cols-3 mt-6 gap-6">
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">讲师姓名</label>
              <el-input placeholder="请输入讲师姓名" class="flex-1" />
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">讲师职位</label>
              <el-input placeholder="请输入讲师职位" class="flex-1" />
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">讲师简介</label>
              <el-input type="textarea" :rows="2" placeholder="请输入讲师简介" class="flex-1" />
            </div>
          </div>
        </div>

        <!-- 课程内容 -->
        <div class="mb-8">
          <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
            课程内容
          </h2>
          <div class="flex">
            <div class="w-64 border-r border-gray-200 pr-4">
              <div class="mb-2 flex justify-between">
                <el-button size="small" class="!rounded-button whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Plus />
                  </el-icon>添加章节
                </el-button>
                <el-button size="small" class="!rounded-button whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Plus />
                  </el-icon>添加子章节
                </el-button>
              </div>
              <el-tree
                :data="chapterData"
                node-key="id"
                default-expand-all
                :expand-on-click-node="false"
                draggable
                class="border border-gray-200 rounded p-2"
              >
                <template #default="{ node, data }">
                  <span class="flex items-center">
                    <span>{{ node.label }}</span>
                    <span class="ml-auto flex">
                      <el-button type="text" size="small">
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button type="text" size="small" class="text-red-500">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </span>
                  </span>
                </template>
              </el-tree>
            </div>
            <div class="flex-1 pl-6">
              <div class="mb-4 flex items-center">
                <label class="w-20 pr-4 text-right text-sm text-gray-600">章节标题</label>
                <el-input placeholder="请输入章节标题" class="flex-1" />
              </div>
              <div class="mb-4 flex items-center">
                <label class="w-20 pr-4 text-right text-sm text-gray-600">章节时长</label>
                <el-input-number :min="0" class="w-32" />
                <el-select class="ml-2 w-24">
                  <el-option label="小时" value="hour" />
                  <el-option label="分钟" value="minute" />
                </el-select>
              </div>
              <div class="mb-4 flex items-center">
                <label class="w-20 pr-4 text-right text-sm text-gray-600">内容类型</label>
                <el-radio-group>
                  <el-radio label="video">
                    视频
                  </el-radio>
                  <el-radio label="document">
                    文档
                  </el-radio>
                  <el-radio label="ppt">
                    PPT
                  </el-radio>
                  <el-radio label="html">
                    HTML
                  </el-radio>
                  <el-radio label="other">
                    其他
                  </el-radio>
                </el-radio-group>
              </div>
              <div class="mb-4 flex items-center">
                <label class="w-20 pr-4 text-right text-sm text-gray-600">内容上传</label>
                <el-upload
                  action="#"
                  class="flex-1"
                >
                  <el-button type="primary" class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <Upload />
                    </el-icon>上传文件
                  </el-button>
                </el-upload>
              </div>
              <div class="mb-4 flex items-start">
                <label class="w-20 pr-4 pt-2 text-right text-sm text-gray-600">内容描述</label>
                <el-input type="textarea" :rows="4" placeholder="请输入内容描述" class="flex-1" />
              </div>
            </div>
          </div>
        </div>

        <!-- 学习资料 -->
        <div class="mb-8">
          <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
            学习资料
          </h2>
          <div class="mb-6">
            <div class="mb-2 flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">资料列表</label>
              <el-button size="small" class="!rounded-button whitespace-nowrap">
                <el-icon class="mr-1">
                  <Plus />
                </el-icon>添加资料
              </el-button>
            </div>
            <div class="ml-24">
              <el-table :data="materialData" border class="w-full">
                <el-table-column prop="name" label="资料名称" width="180" />
                <el-table-column prop="type" label="类型" width="120">
                  <template #default="{ row }">
                    <el-select v-model="row.type" size="small">
                      <el-option label="PDF" value="pdf" />
                      <el-option label="Word" value="word" />
                      <el-option label="Excel" value="excel" />
                      <el-option label="PPT" value="ppt" />
                      <el-option label="其他" value="other" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="文件" width="180">
                  <template #default="{ row }">
                    <el-upload
                      action="#"
                      :show-file-list="false"
                    >
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        <el-icon class="mr-1">
                          <Upload />
                        </el-icon>上传
                      </el-button>
                    </el-upload>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述">
                  <template #default="{ row }">
                    <el-input v-model="row.description" size="small" placeholder="请输入描述" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default>
                    <el-button type="text" size="small" class="text-red-500">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div>
            <div class="mb-2 flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">相关链接</label>
              <el-button size="small" class="!rounded-button whitespace-nowrap">
                <el-icon class="mr-1">
                  <Plus />
                </el-icon>添加链接
              </el-button>
            </div>
            <div class="ml-24">
              <el-table :data="linkData" border class="w-full">
                <el-table-column prop="name" label="链接名称" width="180" />
                <el-table-column prop="url" label="URL" />
                <el-table-column prop="description" label="描述" />
                <el-table-column label="操作" width="80">
                  <template #default>
                    <el-button type="text" size="small" class="text-red-500">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>

        <!-- 考核设置 -->
        <div class="mb-8">
          <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
            考核设置
          </h2>
          <div class="grid grid-cols-2 gap-6">
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">是否需要考核</label>
              <el-radio-group>
                <el-radio :label="true">
                  是
                </el-radio>
                <el-radio :label="false">
                  否
                </el-radio>
              </el-radio-group>
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">考核类型</label>
              <el-radio-group>
                <el-radio label="exam">
                  在线考试
                </el-radio>
                <el-radio label="homework">
                  作业提交
                </el-radio>
                <el-radio label="practice">
                  实操考核
                </el-radio>
                <el-radio label="other">
                  其他
                </el-radio>
              </el-radio-group>
            </div>
            <div class="flex items-start">
              <label class="w-24 pr-4 pt-2 text-right text-sm text-gray-600">考核要求</label>
              <el-input type="textarea" :rows="3" placeholder="请输入考核要求" class="flex-1" />
            </div>
            <div class="flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">及格分数</label>
              <el-slider v-model="passScore" :min="0" :max="100" show-input class="flex-1" />
            </div>
          </div>
          <div class="mt-6">
            <div class="mb-2 flex items-center">
              <label class="w-24 pr-4 text-right text-sm text-gray-600">题库选择</label>
              <el-button size="small" class="!rounded-button whitespace-nowrap">
                <el-icon class="mr-1">
                  <Plus />
                </el-icon>添加考题
              </el-button>
              <el-button size="small" class="!rounded-button ml-2 whitespace-nowrap">
                <el-icon class="mr-1">
                  <Search />
                </el-icon>从题库选择
              </el-button>
            </div>
            <div class="ml-24">
              <el-table :data="questionData" border class="w-full">
                <el-table-column prop="type" label="题目类型" width="120" />
                <el-table-column prop="difficulty" label="难度" width="100" />
                <el-table-column prop="score" label="分值" width="80" />
                <el-table-column label="操作" width="80">
                  <template #default>
                    <el-button type="text" size="small" class="text-red-500">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>

        <!-- 关联信息 -->
        <div>
          <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
            关联信息
          </h2>
          <div class="grid grid-cols-3 gap-6">
            <div>
              <div class="mb-2 flex items-center">
                <label class="w-24 pr-4 text-right text-sm text-gray-600">关联法规</label>
                <el-button size="small" class="!rounded-button whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Plus />
                  </el-icon>添加法规
                </el-button>
              </div>
              <div class="ml-24">
                <el-table :data="lawData" border class="w-full">
                  <el-table-column prop="name" label="法规名称" />
                  <el-table-column prop="version" label="版本" width="100" />
                  <el-table-column label="操作" width="80">
                    <template #default>
                      <el-button type="text" size="small" class="text-red-500">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div>
              <div class="mb-2 flex items-center">
                <label class="w-24 pr-4 text-right text-sm text-gray-600">关联制度</label>
                <el-button size="small" class="!rounded-button whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Plus />
                  </el-icon>添加制度
                </el-button>
              </div>
              <div class="ml-24">
                <el-table :data="policyData" border class="w-full">
                  <el-table-column prop="name" label="制度名称" />
                  <el-table-column prop="version" label="版本" width="100" />
                  <el-table-column label="操作" width="80">
                    <template #default>
                      <el-button type="text" size="small" class="text-red-500">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div>
              <div class="mb-2 flex items-center">
                <label class="w-24 pr-4 text-right text-sm text-gray-600">关联课程</label>
                <el-button size="small" class="!rounded-button whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Plus />
                  </el-icon>添加课程
                </el-button>
              </div>
              <div class="ml-24">
                <el-table :data="courseData" border class="w-full">
                  <el-table-column prop="name" label="课程名称" />
                  <el-table-column prop="type" label="类型" width="120" />
                  <el-table-column label="操作" width="80">
                    <template #default>
                      <el-button type="text" size="small" class="text-red-500">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";
</style>

<style scoped>
/* 自定义样式 */
:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-textarea__inner) {
  min-height: 150px;
}

:deep(.el-upload--picture-card) {
  width: 120px;
  height: 67.5px;
  line-height: 67.5px;
}

:deep(.el-tree-node__content) {
  height: 32px;
}
</style>
