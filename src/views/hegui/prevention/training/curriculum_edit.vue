<script setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowRight, Bottom, Check, Close, Delete, Edit, Plus, Search, Top, Upload, View,
} from '@element-plus/icons-vue'
import trainingCurriculum from '@/api/complianceApi/prevention/trainingCurriculum'
import UploadMbb from '@/components/uploadMbb/index.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const isEdit = ref(false)

// 课程表单数据
const courseForm = ref({
  id: null,
  courseCode: '',
  courseName: '',
  courseType: '',
  trainingTheme: '',
  difficultyLevel: '',
  applicableRole: [],
  instructor: '',
  coursePoint: 0,
  coverImageUrl: '',
  producer: '',
  releaseDate: null,
  lastUpdate: null,
  status: 'draft',
  durationMinutes: 0,
  metadata: '',
  description: '',
  courseOverview: '',
  learningObjective: '',
  prerequisites: '',
  certificationInfo: '',
  courseContent: {
    contentType: '',
    contentTitle: '',
    contentDescription: '',
    contentUrl: '',
    durationMinutes: 0,
    metadata: '',
  },
  playbackSetting: {
    autoPlay: false,
    rememberPlayback: false,
    continuousPlayback: false,
    defaultClarity: '',
  },
  interactiveFeature: {
    commentEnabled: false,
    likeEnabled: false,
    shareEnabled: false,
  },
})

// 保存课程
async function saveCourse() {
  try {
    loading.value = true
    const params = {
      ...courseForm.value,
      applicableRole: Array.isArray(courseForm.value.applicableRole)
        ? courseForm.value.applicableRole.join(',')
        : courseForm.value.applicableRole,
    }

    let res
    if (isEdit.value && courseForm.value.id) {
      res = await trainingCurriculum.courseInfo({}, params, 'update')
    }
    else {
      res = await trainingCurriculum.courseInfo({}, params, 'create')
    }

    if (res) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      router.push('/training/course')
    }
  }
  catch (error) {
    console.error('保存课程失败', error)
    ElMessage.error('保存失败')
  }
  finally {
    loading.value = false
  }
}

// 保存并新增
async function saveAndNew() {
  try {
    await saveCourse()
    // 重置表单
    courseForm.value = {
      id: null,
      courseName: '',
      courseCode: '',
      courseType: '',
      durationMinutes: 0,
      applicableRole: [],
      isMandatory: false,
      status: 'draft',
      trainingTheme: '',
      difficultyLevel: '',
      instructor: '',
      coursePoint: 0,
      coverImageUrl: '',
      producer: '',
      releaseDate: null,
      lastUpdate: null,
      metadata: '',
      description: '',
      courseOverview: '',
      learningObjective: '',
      prerequisites: '',
      certificationInfo: '',
      courseContent: {
        contentType: '',
        contentTitle: '',
        contentDescription: '',
        contentUrl: '',
        durationMinutes: 0,
        metadata: '',
      },
      playbackSetting: {
        autoPlay: false,
        rememberPlayback: false,
        continuousPlayback: false,
        defaultClarity: '',
      },
      interactiveFeature: {
        commentEnabled: false,
        likeEnabled: false,
        shareEnabled: false,
      },
    }
    isEdit.value = false
  }
  catch (error) {
    console.error('保存并新增失败', error)
  }
}

// 取消
function cancel() {
  router.push('/training/course')
}

// 获取课程详情（编辑模式）
async function getCourseDetail() {
  if (route.query.id) {
    try {
      isEdit.value = true
      const res = await trainingCurriculum.courseInfo({}, { id: route.query.id }, 'info')
      if (res) {
        courseForm.value = {
          ...res,
          applicableRole: res.applicableRole ? res.applicableRole.split(',') : [],
        }
      }
    }
    catch (error) {
      console.error('获取课程详情失败', error)
      ElMessage.error('获取课程详情失败')
    }
  }
}

// 文件上传相关处理函数
function handlePreview(file) {
  // 处理文件预览
  if (file.url) {
    // 如果是图片，可以使用预览组件
    if (file.url.match(/\.(jpeg|jpg|gif|png)$/i)) {
      // 这里可以使用 Element Plus 的图片预览组件
      // 或者打开新窗口预览
      window.open(file.url)
    }
    else {
      // 其他类型文件直接打开
      window.open(file.url)
    }
  }
}

function handleRemove(file, fileList) {
  // 处理文件移除
  console.log(file, fileList)
}

function beforeRemove(file, fileList) {
  return ElMessageBox.confirm(`确定移除 ${file.name}？`)
}

function handleExceed(files, fileList) {
  ElMessage.warning(
    `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`,
  )
}

// 课程封面上传成功回调
function handleAvatarSuccess(response, uploadFile) {
  // 假设上传成功后，服务器返回的数据中包含文件URL
  if (response && response.data && response.data.url) {
    courseForm.value.coverImageUrl = response.data.url
  }
  else {
    // 模拟上传成功，实际开发中应该使用服务器返回的URL
    courseForm.value.coverImageUrl = URL.createObjectURL(uploadFile.raw)
  }
}

// 课程封面上传前的校验
function beforeAvatarUpload(file) {
  const isJPG = file.type === 'image/jpeg'
  const isPNG = file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG && !isPNG) {
    ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 章节内容上传成功回调
function handleChapterContentUpload(response, uploadFile) {
  // 假设上传成功后，服务器返回的数据中包含文件URL
  if (response && response.data && response.data.url) {
    courseForm.value.courseContent.contentUrl = response.data.url
  }
  else {
    // 模拟上传成功，实际开发中应该使用服务器返回的URL
    courseForm.value.courseContent.contentUrl = URL.createObjectURL(uploadFile.raw)
  }
}

// 章节内容上传成功回调
function handleChapterContentUploadSuccess(files, row) {
  if (files && files.length > 0) {
    const file = files[0]
    courseForm.value.courseContent.contentUrl = file.url || file.filePath
    ElMessage.success('上传成功')
  }
}

const chapterFiles = ref([])

// 学习资料上传成功回调
function handleMaterialUploadSuccess(files, row) {
  // 处理学习资料上传成功后的逻辑
  if (files && files.length > 0) {
    const file = files[0]
    row.fileUrl = file.url || file.filePath
    row.fileName = file.name || file.fileName
    ElMessage.success('上传成功')
  }
}

// 添加学习资料
function addMaterial() {
  if (!Array.isArray(courseForm.value.metadata)) {
    courseForm.value.metadata = []
  }
  courseForm.value.metadata.push({
    name: '',
    type: 'pdf',
    description: '',
    fileUrl: '',
    fileName: '',
    files: [], // 用于uploadMbb组件的v-model绑定
  })
}

// 删除学习资料
function removeMaterial(index) {
  ElMessageBox.confirm('确定要删除该学习资料吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    courseForm.value.metadata.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除操作
  })
}

onMounted(() => {
  getCourseDetail()
})

const chapterData = ref([
  {
    id: 1,
    label: '第一章 合规基础知识',
    children: [
      {
        id: 11,
        label: '1.1 合规概念',
      },
      {
        id: 12,
        label: '1.2 合规重要性',
      },
    ],
  },
  {
    id: 2,
    label: '第二章 法律法规',
    children: [
      {
        id: 21,
        label: '2.1 相关法律',
      },
      {
        id: 22,
        label: '2.2 法规解读',
      },
    ],
  },
])

// 初始化学习资料数据
if (!courseForm.value.metadata || !Array.isArray(courseForm.value.metadata)) {
  courseForm.value.metadata = [
    { name: '合规手册', type: 'pdf', description: '', fileUrl: '', fileName: '' },
    { name: '案例集', type: 'word', description: '', fileUrl: '', fileName: '' },
  ]
}

const linkData = ref([
  { name: '合规官网', url: 'https://example.com', description: '公司合规官方网站' },
])

const passScore = ref(60)

const questionData = ref([
  { type: '单选题', difficulty: '中等', score: 5 },
  { type: '多选题', difficulty: '较难', score: 10 },
])

const lawData = ref([
  { name: '中华人民共和国公司法', version: '2021版' },
])

const policyData = ref([
  { name: '员工行为规范', version: '2023版' },
])

const courseData = ref([
  { name: '合规意识培训', type: '合规意识' },
])
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <!-- 标题和操作按钮 -->
        <div class="mb-6 flex items-center justify-between">
          <h1 class="text-xl text-gray-800 font-bold">
            {{ isEdit ? '编辑培训课程' : '新增培训课程' }}
          </h1>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="saveCourse">
              <el-icon class="mr-1">
                <Check />
              </el-icon>保存
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" :disabled="isEdit" @click="saveAndNew">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>保存并新增
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="cancel">
              <el-icon class="mr-1">
                <Close />
              </el-icon>取消
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <view />
              </el-icon>预览
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <!-- 主内容区 -->
    <PageMain style="background-color: transparent;">
      <div class="mx-5 flex-1 p-6">
        <!-- 表单内容 -->
        <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
          <el-form :model="courseForm" label-position="right" label-width="120px" class="course-form">
            <!-- 基本信息 -->
            <div class="mb-8">
              <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
                基本信息
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <el-form-item label="课程名称" required>
                  <el-input v-model="courseForm.courseName" placeholder="请输入课程名称" />
                </el-form-item>
                <el-form-item label="课程编号">
                  <div class="w-full flex items-center">
                    <el-input v-model="courseForm.courseCode" placeholder="系统自动生成" class="flex-1" />
                    <el-button class="!rounded-button ml-2 whitespace-nowrap">
                      自动生成
                    </el-button>
                  </div>
                </el-form-item>
                <el-form-item label="课程类型" required>
                  <el-select v-model="courseForm.courseType" placeholder="请选择课程类型" class="w-full">
                    <el-option label="视频课程" value="video" />
                    <el-option label="文档课程" value="document" />
                    <el-option label="混合课程" value="mixed" />
                    <el-option label="法律法规" value="1" />
                    <el-option label="合规意识" value="2" />
                    <el-option label="业务技能" value="3" />
                    <el-option label="管理能力" value="4" />
                    <el-option label="其他" value="5" />
                  </el-select>
                </el-form-item>
                <el-form-item label="课程时长" required>
                  <div class="w-full flex items-center">
                    <el-input-number v-model="courseForm.durationMinutes" :min="0" class="flex-1" />
                    <el-select class="ml-2 w-24" value="minute">
                      <el-option label="小时" value="hour" />
                      <el-option label="分钟" value="minute" />
                    </el-select>
                  </div>
                </el-form-item>
                <el-form-item label="培训对象" required>
                  <el-select v-model="courseForm.applicableRole" multiple placeholder="请选择培训对象" class="w-full">
                    <el-option label="全体员工" value="all" />
                    <el-option label="管理层" value="management" />
                    <el-option label="特定部门" value="department" />
                    <el-option label="特定岗位" value="position" />
                    <el-option label="新员工" value="new" />
                    <el-option label="其他" value="other" />
                  </el-select>
                </el-form-item>
                <el-form-item label="课程状态" required>
                  <el-select v-model="courseForm.status" placeholder="请选择课程状态" class="w-full">
                    <el-option label="草稿" value="draft" />
                    <el-option label="已发布" value="published" />
                    <el-option label="已下线" value="offline" />
                  </el-select>
                </el-form-item>
              </div>
            </div>

            <!-- 课程信息 -->
            <div class="mb-8">
              <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
                课程信息
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div class="flex flex-col">
                  <el-form-item label="课程简介" required>
                    <el-input v-model="courseForm.courseOverview" type="textarea" :rows="5" placeholder="请输入课程简介" />
                  </el-form-item>
                  <el-form-item label="适合人群">
                    <el-input v-model="courseForm.prerequisites" type="textarea" :rows="3" placeholder="请输入适合人群描述" />
                  </el-form-item>
                  <el-form-item label="学习目标">
                    <el-input v-model="courseForm.learningObjective" type="textarea" :rows="3" placeholder="请输入学习目标" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="课程封面">
                    <div class="flex flex-col">
                      <el-upload
                        class="avatar-uploader"
                        action="/api/upload"
                        :show-file-list="true"
                        list-type="picture-card"
                        :on-success="handleAvatarSuccess"
                        :before-upload="beforeAvatarUpload"
                      >
                        <img v-if="courseForm.coverImageUrl" :src="courseForm.coverImageUrl" class="avatar">
                        <el-icon v-else class="avatar-uploader-icon">
                          <Plus />
                        </el-icon>
                      </el-upload>
                      <div class="mt-2 text-xs text-gray-500">
                        建议尺寸：16:9比例，最小分辨率800x450px
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>

              <!-- 讲师信息 -->
              <div class="grid grid-cols-3 gap-6">
                <el-form-item label="讲师姓名">
                  <el-input v-model="courseForm.instructor" placeholder="请输入讲师姓名" />
                </el-form-item>
                <el-form-item label="制作人">
                  <el-input v-model="courseForm.producer" placeholder="请输入制作人" />
                </el-form-item>
              </div>
            </div>

            <!-- 课程内容 -->
            <div class="mb-8">
              <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
                课程内容
              </h2>
              <div class="flex">
                <div class="w-64 border-r border-gray-200 pr-4">
                  <div class="mb-2 flex justify-between">
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      <el-icon class="mr-1">
                        <Plus />
                      </el-icon>添加章节
                    </el-button>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      <el-icon class="mr-1">
                        <Plus />
                      </el-icon>添加子章节
                    </el-button>
                  </div>
                  <el-tree
                    :data="chapterData"
                    node-key="id"
                    default-expand-all
                    :expand-on-click-node="false"
                    draggable
                    class="border border-gray-200 rounded p-2"
                  >
                    <template #default="{ node, data }">
                      <span class="flex items-center">
                        <span>{{ node.label }}</span>
                        <span class="ml-auto flex">
                          <el-button type="text" size="small">
                            <el-icon><Edit /></el-icon>
                          </el-button>
                          <el-button type="text" size="small" class="text-red-500">
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </span>
                      </span>
                    </template>
                  </el-tree>
                </div>
                <div class="flex-1 pl-6">
                  <el-form-item label="章节标题" label-width="100px">
                    <el-input v-model="courseForm.courseContent.contentTitle" placeholder="请输入章节标题" />
                  </el-form-item>
                  <el-form-item label="章节时长" label-width="100px">
                    <div class="flex items-center">
                      <el-input-number v-model="courseForm.courseContent.durationMinutes" :min="0" class="w-32" />
                      <el-select class="ml-2 w-24" value="minute">
                        <el-option label="分钟" value="minute" />
                      </el-select>
                    </div>
                  </el-form-item>
                  <el-form-item label="内容类型" label-width="100px">
                    <el-radio-group v-model="courseForm.courseContent.contentType">
                      <el-radio label="video">
                        视频
                      </el-radio>
                      <el-radio label="document">
                        文档
                      </el-radio>
                      <el-radio label="ppt">
                        PPT
                      </el-radio>
                      <el-radio label="html">
                        HTML
                      </el-radio>
                      <el-radio label="other">
                        其他
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="内容上传" label-width="100px">
                    <UploadMbb
                      v-model="chapterFiles"
                      :max="1"
                      :size="10"
                      @upload-success="handleChapterContentUploadSuccess"
                    />
                  </el-form-item>
                  <el-form-item label="内容描述" label-width="100px">
                    <el-input v-model="courseForm.courseContent.contentDescription" type="textarea" :rows="4" placeholder="请输入内容描述" />
                  </el-form-item>
                </div>
              </div>
            </div>

            <!-- 学习资料 -->
            <div class="mb-8">
              <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
                学习资料
              </h2>
              <el-form-item label="资料列表">
                <div class="mb-2">
                  <el-button size="small" class="!rounded-button whitespace-nowrap" @click="addMaterial">
                    <el-icon class="mr-1">
                      <Plus />
                    </el-icon>添加资料
                  </el-button>
                </div>
                <el-table :data="courseForm.metadata" border class="w-full">
                  <el-table-column prop="name" label="资料名称" width="180" />
                  <el-table-column prop="type" label="类型" width="120">
                    <template #default="{ row }">
                      <el-select v-model="row.type" size="small">
                        <el-option label="PDF" value="pdf" />
                        <el-option label="Word" value="word" />
                        <el-option label="Excel" value="excel" />
                        <el-option label="PPT" value="ppt" />
                        <el-option label="其他" value="other" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="文件" width="400">
                    <template #default="{ row }">
                      <UploadMbb
                        v-model="row.files"
                        :max="1"
                        :size="10"
                        @upload-success="(files) => handleMaterialUploadSuccess(files, row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="描述">
                    <template #default="{ row }">
                      <el-input v-model="row.description" size="small" placeholder="请输入描述" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template #default="{ $index }">
                      <el-button type="text" size="small" class="text-red-500" @click="removeMaterial($index)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";
</style>

<style scoped>
/* 自定义样式 */
:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-textarea__inner) {
  min-height: 150px;
}

:deep(.el-upload--picture-card) {
  width: 120px;
  height: 67.5px;
  line-height: 67.5px;
}

:deep(.el-tree-node__content) {
  height: 32px;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 67.5px;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 67.5px;
  display: block;
}
</style>
