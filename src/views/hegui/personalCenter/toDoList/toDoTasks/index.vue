<script lang="ts" setup>
import { nextTick, onMounted, ref, computed } from 'vue'
import * as echarts from 'echarts'
import todoTaskApi from '@/api/todoTask/task'
import { ElMessage } from 'element-plus'

// 数据
const activeTab = ref('todo')
const viewMode = ref('table')
const loading = ref(false)
const filter = ref({
  eventType: '',
  eventStatus: '',
  dateRange: [],
  title: '',
})
const pagination = ref({
  current: 1,
  size: 10,
  total: 0,
})
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts

// 任务列表数据
const taskList = ref([])

// 事件类型映射
const eventTypeMap = {
  'CONTRACT_APPROVAL': '合同审批',
  'DECISION_APPROVAL': '决策审批',
  'SUPPLEMENTAL_APPROVAL': '补充审批',
  'REGULATORY_APPROVAL': '监管审批',
  'ENTERPRISE_APPROVAL': '企业审批',
  'VIOLATION_APPROVAL': '违规审批',
}

// 事件状态映射
const eventStatusMap = {
  'PENDING': '待处理',
  'COMPLETED': '已完成',
  'CANCELLED': '已取消',
  'EXPIRED': '已过期',
}

// 获取用户信息
const userInfo = computed(() => {
  const info = localStorage.getItem('userinfo')
  return info ? JSON.parse(info) : {}
})

// 获取租户ID
const tenantId = computed(() => {
  return localStorage.getItem('tenantId') || userInfo.value.tenantId || 0
})

// 获取当前用户ID
const currentUserId = computed(() => {
  return userInfo.value.employeeId || userInfo.value.userId || userInfo.value.id || 0
})

// 待办提醒数据
const reminders = ref([
  {
    id: 'R001',
    title: '季度财务报告审批',
    remaining: '2天3小时',
    status: 'urgent',
  },
  {
    id: 'R002',
    title: '数据安全合规检查',
    remaining: '已超时',
    status: 'overdue',
  },
  {
    id: 'R003',
    title: '年度预算调整审批',
    remaining: '1天5小时',
    status: 'urgent',
  },
  {
    id: 'R004',
    title: '部门团建方案确认',
    remaining: '已超时',
    status: 'overdue',
  },
])

// 获取待办事件列表
async function fetchTodoEvents() {
  try {
    loading.value = true
    const params = {
      tenantId: Number(tenantId.value),
      assigneeId: Number(currentUserId.value),
      ...(filter.value.eventType && { eventType: filter.value.eventType }),
      ...(filter.value.eventStatus && { eventStatus: filter.value.eventStatus }),
      ...(filter.value.title && { title: filter.value.title }),
    }
    
    const paging = {
      page: pagination.value.current - 1, // 后端从0开始
      size: pagination.value.size,
    }
    
    const response = await todoTaskApi.eventsApi(paging, params, 'list')
    
    if (response && response.content) {
      taskList.value = response.content.map((item: any) => ({
        ...item,
        eventTypeName: eventTypeMap[item.eventType] || item.eventType,
        statusName: eventStatusMap[item.status] || item.status,
        createTime: formatTime(item.createdAt),
        updateTime: formatTime(item.updatedAt),
        completedTime: item.completedTime ? formatTime(item.completedTime) : '',
      }))
      pagination.value.total = response.totalElements || 0
    }
  } catch (error) {
    console.error('获取待办事件失败:', error)
    ElMessage.error('获取待办事件失败')
  } finally {
    loading.value = false
  }
}

// 格式化时间
function formatTime(timeObj: any) {
  if (!timeObj) return ''
  if (typeof timeObj === 'string') return timeObj
  if (timeObj.seconds) {
    return new Date(timeObj.seconds * 1000).toLocaleString('zh-CN')
  }
  return ''
}

// 辅助函数
function getTypeName(type: string) {
  return eventTypeMap[type] || type
}

function getTypeTag(type: string) {
  const map: Record<string, string> = {
    'CONTRACT_APPROVAL': '',
    'DECISION_APPROVAL': 'warning',
    'SUPPLEMENTAL_APPROVAL': 'success',
    'REGULATORY_APPROVAL': 'info',
    'ENTERPRISE_APPROVAL': 'warning',
    'VIOLATION_APPROVAL': 'danger',
  }
  return map[type] || ''
}

function getStatusName(status: string) {
  return eventStatusMap[status] || status
}

function getStatusTag(status: string) {
  const map: Record<string, string> = {
    'PENDING': 'warning',
    'COMPLETED': 'success',
    'CANCELLED': 'info',
    'EXPIRED': 'danger',
  }
  return map[status] || ''
}

// 处理任务
async function handleTask(row: any) {
  try {
    // 根据事件类型跳转到对应的处理页面
    const routeMap = {
      'CONTRACT_APPROVAL': '/hegui/contract/approval',
      'DECISION_APPROVAL': '/hegui/decision/approval',
      'SUPPLEMENTAL_APPROVAL': '/hegui/supplemental/approval',
      'REGULATORY_APPROVAL': '/hegui/regulatory/approval',
      'ENTERPRISE_APPROVAL': '/hegui/enterprise/approval',
      'VIOLATION_APPROVAL': '/hegui/violation/approval',
    }
    
    const route = routeMap[row.eventType]
    if (route) {
      // 跳转到对应的处理页面，传递业务ID和审批实例ID
      window.open(`${route}?businessId=${row.businessId}&instanceId=${row.approvalInstanceId}&eventId=${row.id}`, '_blank')
    } else {
      ElMessage.warning('暂不支持该类型任务的处理')
    }
  } catch (error) {
    console.error('处理任务失败:', error)
    ElMessage.error('处理任务失败')
  }
}

// 查看详情
function viewDetail(row: any) {
  // 根据事件类型跳转到对应的详情页面
  const routeMap = {
    'CONTRACT_APPROVAL': '/hegui/contract/detail',
    'DECISION_APPROVAL': '/hegui/decision/detail',
    'SUPPLEMENTAL_APPROVAL': '/hegui/supplemental/detail',
    'REGULATORY_APPROVAL': '/hegui/regulatory/detail',
    'ENTERPRISE_APPROVAL': '/hegui/enterprise/detail',
    'VIOLATION_APPROVAL': '/hegui/violation/detail',
  }
  
  const route = routeMap[row.eventType]
  if (route) {
    window.open(`${route}?businessId=${row.businessId}&eventId=${row.id}`, '_blank')
  } else {
    ElMessage.warning('暂不支持该类型任务的详情查看')
  }
}

// 转交任务
function transferTask(row: any) {
  ElMessage.info('转交功能开发中...')
}

// 搜索
function handleSearch() {
  pagination.value.current = 1
  fetchTodoEvents()
}

// 重置搜索
function resetSearch() {
  filter.value = {
    eventType: '',
    eventStatus: '',
    dateRange: [],
    title: '',
  }
  pagination.value.current = 1
  fetchTodoEvents()
}

// 分页变化
function handlePageChange(page: number) {
  pagination.value.current = page
  fetchTodoEvents()
}

function handleSizeChange(size: number) {
  pagination.value.size = size
  pagination.value.current = 1
  fetchTodoEvents()
}

function isOverdue(deadline: string) {
  const now = new Date()
  const deadlineDate = new Date(deadline)
  return now > deadlineDate
}

// 初始化图表
function initChart() {
  if (!chartRef.value) { return }

  chart = echarts.init(chartRef.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '任务类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 4, name: '审批' },
          { value: 1, name: '合规检查' },
          { value: 1, name: '培训任务' },
          { value: 2, name: '其他' },
        ],
        color: ['#409EFF', '#E6A23C', '#67C23A', '#909399'],
      },
    ],
  }
  chart.setOption(option)
}

// 响应式调整图表大小
function resizeChart() {
  chart?.resize()
}

onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
    fetchTodoEvents()
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              待办任务
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <!-- <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1"></i>编辑资料
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- <template #header> -->
              <!-- <div class="f-16 fw-600">个人资料</div> -->
              <!-- </template> -->
              <!-- 任务列表区 -->
              <div class="flex-1">
                <div class="mb-4 rounded-lg bg-white p-4 shadow-sm">
                  <!-- 搜索筛选区 -->
                  <div class="mb-4 space-y-4">
                    <el-row :gutter="16">
                      <el-col :span="6">
                        <el-input
                          v-model="filter.title"
                          placeholder="请输入任务标题"
                          clearable
                          @keyup.enter="handleSearch"
                        >
                          <template #prefix>
                            <el-icon><Search /></el-icon>
                          </template>
                        </el-input>
                      </el-col>
                      <el-col :span="4">
                        <el-select v-model="filter.eventType" placeholder="事件类型" clearable>
                          <el-option label="合同审批" value="CONTRACT_APPROVAL" />
                          <el-option label="决策审批" value="DECISION_APPROVAL" />
                          <el-option label="补充审批" value="SUPPLEMENTAL_APPROVAL" />
                          <el-option label="监管审批" value="REGULATORY_APPROVAL" />
                          <el-option label="企业审批" value="ENTERPRISE_APPROVAL" />
                          <el-option label="违规审批" value="VIOLATION_APPROVAL" />
                        </el-select>
                      </el-col>
                      <el-col :span="4">
                        <el-select v-model="filter.eventStatus" placeholder="事件状态" clearable>
                          <el-option label="待处理" value="PENDING" />
                          <el-option label="已完成" value="COMPLETED" />
                          <el-option label="已取消" value="CANCELLED" />
                          <el-option label="已过期" value="EXPIRED" />
                        </el-select>
                      </el-col>
                      <el-col :span="6">
                        <el-date-picker
                          v-model="filter.dateRange"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                        />
                      </el-col>
                      <el-col :span="4">
                        <el-button type="primary" @click="handleSearch">
                          <el-icon><Search /></el-icon>
                          搜索
                        </el-button>
                        <el-button @click="resetSearch">
                          <el-icon><Refresh /></el-icon>
                          重置
                        </el-button>
                      </el-col>
                    </el-row>
                  </div>
                  
                  <div class="mb-4 flex items-center justify-between">
                    <div class="flex items-center">
                      <el-radio-group v-model="viewMode" size="small">
                        <el-radio-button label="table">
                          表格模式
                        </el-radio-button>
                        <el-radio-button label="card">
                          卡片模式
                        </el-radio-button>
                      </el-radio-group>
                    </div>
                    <div class="text-sm text-gray-500">
                      共 {{ pagination.total }} 项任务
                    </div>
                  </div>

                  <!-- 表格模式 -->
                  <div v-if="viewMode === 'table'">
                    <el-table :data="taskList" v-loading="loading" style="width: 100%;">
                      <el-table-column prop="id" label="事件ID" width="100" />
                      <el-table-column prop="title" label="事件标题" min-width="200" show-overflow-tooltip />
                      <el-table-column prop="eventType" label="事件类型" width="140">
                        <template #default="{ row }">
                          <el-tag :type="getTypeTag(row.eventType)" size="small">
                            {{ getTypeName(row.eventType) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="createdBy" label="创建人" width="120" />
                      <el-table-column prop="createTime" label="创建时间" width="180" />
                      <el-table-column prop="completedTime" label="完成时间" width="180">
                        <template #default="{ row }">
                          <span>{{ row.completedTime || '-' }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="status" label="状态" width="120">
                        <template #default="{ row }">
                          <el-tag :type="getStatusTag(row.status)" size="small">
                            {{ getStatusName(row.status) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip>
                        <template #default="{ row }">
                          <span>{{ row.description || '-' }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="200" fixed="right">
                        <template #default="{ row }">
                          <el-button
                            v-if="row.status === 'PENDING'" type="primary" size="small"
                            class="!rounded-button whitespace-nowrap"
                            @click="handleTask(row)"
                          >
                            处理
                          </el-button>
                          <el-button 
                            size="small" 
                            class="!rounded-button whitespace-nowrap"
                            @click="viewDetail(row)"
                          >
                            详情
                          </el-button>
                          <el-button 
                            size="small" 
                            class="!rounded-button whitespace-nowrap"
                            @click="transferTask(row)"
                          >
                            转交
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 卡片模式 -->
                  <div v-else v-loading="loading" class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
                    <div
                      v-for="task in taskList" :key="task.id"
                      class="border rounded-lg p-4 transition-shadow hover:shadow-md"
                    >
                      <div class="mb-2 flex items-start justify-between">
                        <h3 class="text-lg text-gray-800 font-medium">
                          {{ task.title }}
                        </h3>
                        <el-tag :type="getStatusTag(task.status)" size="small">
                          {{ getStatusName(task.status) }}
                        </el-tag>
                      </div>

                      <div class="mb-3 flex items-center text-sm text-gray-500">
                        <el-tag :type="getTypeTag(task.eventType)" size="small" class="mr-2">
                          {{ getTypeName(task.eventType) }}
                        </el-tag>
                        <span>{{ task.createdBy }} · {{ task.createTime }}</span>
                      </div>

                      <div class="mb-2 text-sm text-gray-600">
                        <div v-if="task.description" class="mb-2">
                          <span class="text-gray-500">描述：</span>
                          <span>{{ task.description }}</span>
                        </div>
                        <div v-if="task.completedTime">
                          <span class="text-gray-500">完成时间：</span>
                          <span>{{ task.completedTime }}</span>
                        </div>
                      </div>

                      <div class="mb-4 text-sm">
                        <span class="text-gray-500">事件ID：</span>
                        <span class="font-mono">{{ task.id }}</span>
                      </div>

                      <div class="flex justify-between">
                        <el-button
                          v-if="task.status === 'PENDING'" type="primary" size="small"
                          class="!rounded-button whitespace-nowrap"
                          @click="handleTask(task)"
                        >
                          处理
                        </el-button>
                        <el-button 
                          size="small" 
                          class="!rounded-button whitespace-nowrap"
                          @click="viewDetail(task)"
                        >
                          详情
                        </el-button>
                        <el-button 
                          size="small" 
                          class="!rounded-button whitespace-nowrap"
                          @click="transferTask(task)"
                        >
                          转交
                        </el-button>
                      </div>
                    </div>
                  </div>

                  <!-- 分页 -->
                  <div class="mt-4 flex items-center justify-between">
                    <el-pagination
                      v-model:current-page="pagination.current" 
                      v-model:page-size="pagination.size"
                      :page-sizes="[10, 20, 50, 100]" 
                      :total="pagination.total"
                      layout="total, sizes, prev, pager, next, jumper"
                      @current-change="handlePageChange"
                      @size-change="handleSizeChange"
                    />
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  任务统计
                </div>
              </template>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">总待办任务</span>
                  <span class="font-medium">28 项</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">紧急任务</span>
                  <span class="text-red-500 font-medium">5 项</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">即将超时</span>
                  <span class="text-orange-500 font-medium">3 项</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">已超时</span>
                  <span class="text-red-600 font-medium">2 项</span>
                </div>
              </div>
              <!-- </div> -->
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  任务类型分布
                </div>
              </template>
              <div ref="chartRef" style="height: 200px;" />
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  待办提醒
                </div>
              </template>
              <div class="space-y-3">
                <div
                  v-for="item in reminders" :key="item.id"
                  class="cursor-pointer border rounded p-3 hover:bg-gray-50"
                >
                  <div class="mb-1 flex items-center justify-between">
                    <span class="font-medium">{{ item.title }}</span>
                    <el-tag :type="item.status === 'overdue' ? 'danger' : 'warning'" size="small">
                      {{ item.status === 'overdue' ? '已超时' : '即将超时' }}
                    </el-tag>
                  </div>
                  <div class="text-sm text-gray-500">
                    <span>剩余时间：</span>
                    <span :class="{ 'text-red-500': item.status === 'overdue' }">{{ item.remaining }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .min-h-screen {
    min-height: 1024px;
  }

  :deep(.el-tabs__item) {
    padding: 0 20px;
  }

  :deep(.el-tabs__active-bar) {
    height: 3px;
  }

  :deep(.el-table .cell) {
    padding-right: 8px;
    padding-left: 8px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f8f8f9;
  }

  :deep(.el-table tr:hover td.el-table__cell) {
    background-color: #f5f7fa !important;
  }

  :deep(.el-pagination) {
    justify-content: flex-end;
  }
</style>
