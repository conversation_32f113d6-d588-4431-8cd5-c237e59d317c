<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'

// Filter data
const filter = ref({
  status: 'all',
  timeRange: 'week',
  priority: 'all',
})
const searchQuery = ref('')

// View mode
const viewMode = ref<'list' | 'timeline'>('list')

// Pagination
const currentPage = ref(1)
const pageSize = ref(10)
const totalMessages = ref(128)

// Messages data
const messages = ref([
  {
    id: 1,
    title: '系统维护通知',
    type: '系统通知',
    time: '2023-06-15 09:30',
    priority: 'urgent',
    status: 'unread',
    content: '系统将于今晚 23:00 至次日 02:00 进行维护升级，届时将无法访问系统，请提前做好工作安排。',
    expanded: false,
  },
  {
    id: 2,
    title: '新版本发布',
    type: '系统通知',
    time: '2023-06-14 14:15',
    priority: 'normal',
    status: 'read',
    content: '系统新版本 v2.3.0 已发布，新增了多项功能并修复了已知问题，建议尽快更新。',
    expanded: false,
  },
  {
    id: 3,
    title: '待办任务提醒',
    type: '待办通知',
    time: '2023-06-14 10:00',
    priority: 'urgent',
    status: 'unread',
    content: '您有 3 项待办任务即将到期，请及时处理。',
    expanded: false,
  },
  {
    id: 4,
    title: '会议邀请',
    type: '其他通知',
    time: '2023-06-13 16:45',
    priority: 'normal',
    status: 'read',
    content: '邀请您参加本周五 14:00 的产品需求评审会议，地点：3 楼会议室。',
    expanded: false,
  },
  {
    id: 5,
    title: '密码修改成功',
    type: '系统通知',
    time: '2023-06-13 11:20',
    priority: 'normal',
    status: 'read',
    content: '您的账户密码已成功修改，如非本人操作，请立即联系管理员。',
    expanded: false,
  },
  {
    id: 6,
    title: '项目进度报告',
    type: '待办通知',
    time: '2023-06-12 17:30',
    priority: 'normal',
    status: 'read',
    content: '请及时提交本周项目进度报告，截止时间：本周五 18:00。',
    expanded: false,
  },
  {
    id: 7,
    title: '系统异常告警',
    type: '系统通知',
    time: '2023-06-12 09:10',
    priority: 'urgent',
    status: 'unread',
    content: '检测到系统异常，技术人员正在处理中，给您带来的不便敬请谅解。',
    expanded: false,
  },
  {
    id: 8,
    title: '培训通知',
    type: '其他通知',
    time: '2023-06-11 15:00',
    priority: 'normal',
    status: 'read',
    content: '新员工入职培训将于下周一 9:30 开始，请准时参加。',
    expanded: false,
  },
  {
    id: 9,
    title: '数据备份完成',
    type: '系统通知',
    time: '2023-06-10 23:45',
    priority: 'normal',
    status: 'read',
    content: '系统自动备份已完成，备份时间：2023-06-10 23:30，备份大小：12.5GB。',
    expanded: false,
  },
  {
    id: 10,
    title: '权限变更通知',
    type: '系统通知',
    time: '2023-06-10 14:20',
    priority: 'normal',
    status: 'read',
    content: '您的账户权限已更新，新增了项目管理模块的访问权限。',
    expanded: false,
  },
])

// Recent notifications
const recentNotifications = ref([
  { id: 1, title: '系统维护通知', time: '09:30' },
  { id: 2, title: '新版本发布', time: '昨天 14:15' },
  { id: 3, title: '待办任务提醒', time: '昨天 10:00' },
  { id: 4, title: '会议邀请', time: '前天 16:45' },
  { id: 5, title: '密码修改成功', time: '前天 11:20' },
])

// Chart ref
const chart = ref<HTMLElement>()

// Group messages by date for timeline view
const groupedMessages = computed(() => {
  const groups: { date: string, messages: any[] }[] = []
  const dateMap = new Map<string, any[]>()

  messages.value.forEach((msg) => {
    const date = msg.time.split(' ')[0]
    if (!dateMap.has(date)) {
      dateMap.set(date, [])
    }
    dateMap.get(date)?.push(msg)
  })

  dateMap.forEach((msgs, date) => {
    groups.push({ date, messages: msgs })
  })

  // Sort by date descending
  groups.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

  return groups
})

// Toggle message status
function toggleStatus(message: any) {
  message.status = message.status === 'unread' ? 'read' : 'unread'
}

// Delete message
function deleteMessage(message: any) {
  const index = messages.value.findIndex(m => m.id === message.id)
  if (index !== -1) {
    messages.value.splice(index, 1)
    totalMessages.value -= 1
  }
}

// Toggle expand row
function toggleExpand(row: any) {
  row.expanded = !row.expanded
}

// Initialize chart
function initChart() {
  if (!chart.value) { return }

  const myChart = echarts.init(chart.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '消息类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 45, name: '系统通知' },
          { value: 30, name: '待办通知' },
          { value: 25, name: '其他通知' },
        ],
      },
    ],
  }

  myChart.setOption(option)

  // Resize chart on window resize
  const handleResize = () => {
    myChart.resize()
  }
  window.addEventListener('resize', handleResize)

  // Cleanup
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    myChart.dispose()
  })
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              消息通知
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <button
              class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-blue-500 hover:bg-blue-50"
            >
              全部标为已读
            </button>
            <button
              class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-gray-600 hover:bg-gray-50"
            >
              清空已读消息
            </button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- <template #header>
                 <div class="f-16 fw-600">个人资料</div>
               </template> -->
              <div class="flex items-center justify-between">
                <div class="flex space-x-4">
                  <el-select v-model="filter.status" placeholder="阅读状态" class="w-32">
                    <el-option label="全部" value="all" />
                    <el-option label="未读" value="unread" />
                    <el-option label="已读" value="read" />
                  </el-select>

                  <el-select v-model="filter.timeRange" placeholder="时间范围" class="w-32">
                    <el-option label="今天" value="today" />
                    <el-option label="本周" value="week" />
                    <el-option label="本月" value="month" />
                    <el-option label="自定义" value="custom" />
                  </el-select>

                  <el-select v-model="filter.priority" placeholder="紧急程度" class="w-32">
                    <el-option label="全部" value="all" />
                    <el-option label="紧急" value="urgent" />
                    <el-option label="普通" value="normal" />
                  </el-select>

                  <button class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">
                    筛选
                  </button>
                  <button
                    class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-gray-600 hover:bg-gray-50"
                  >
                    重置
                  </button>
                  <div class="inline-flex rounded-lg bg-white p-2 shadow-sm">
                    <button
                      :class="{ 'bg-blue-100 text-blue-500': viewMode === 'list' }"
                      class="!rounded-button whitespace-nowrap rounded-l-lg px-4 py-2" @click="viewMode = 'list'"
                    >
                      <el-icon class="mr-2">
                        <i class="fas fa-list" />
                      </el-icon>
                      列表模式
                    </button>
                    <button
                      :class="{ 'bg-blue-100 text-blue-500': viewMode === 'timeline' }"
                      class="!rounded-button whitespace-nowrap rounded-r-lg px-4 py-2" @click="viewMode = 'timeline'"
                    >
                      <el-icon class="mr-2">
                        <i class="fas fa-stream" />
                      </el-icon>
                      时间线模式
                    </button>
                  </div>
                </div>

                <div class="relative">
                  <el-input v-model="searchQuery" placeholder="搜索消息内容..." class="w-64">
                    <template #prefix>
                      <el-icon class="el-input__icon">
                        <i class="fas fa-search" />
                      </el-icon>
                    </template>
                  </el-input>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <!-- <template #header>
                <div class="f-16 fw-600">消息统计</div>
              </template> -->
              <!-- List View -->
              <div v-if="viewMode === 'list'">
                <el-table :data="messages" style="width: 100%;" @row-click="toggleExpand">
                  <el-table-column width="60">
                    <template #default="{ row }">
                      <el-icon v-if="row.status === 'unread'" class="text-blue-500">
                        <i
                          class="fas fa-circle"
                        />
                      </el-icon>
                      <el-icon v-else class="text-gray-400">
                        <i class="far fa-circle" />
                      </el-icon>
                    </template>
                  </el-table-column>
                  <el-table-column prop="title" label="标题" width="180" />
                  <el-table-column prop="type" label="类型" width="120" />
                  <el-table-column prop="time" label="发送时间" width="150" />
                  <el-table-column label="紧急程度" width="100">
                    <template #default="{ row }">
                      <span
                        v-if="row.priority === 'urgent'"
                        class="rounded bg-red-100 px-2 py-1 text-xs text-red-500"
                      >紧急</span>
                      <span v-else class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-500">普通</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                      <el-button type="text" size="small" @click.stop="toggleStatus(row)">
                        {{ row.status === 'unread' ? '标为已读' : '标为未读' }}
                      </el-button>
                      <el-button
                        type="text" size="small" class="text-red-500"
                        @click.stop="deleteMessage(row)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                  <el-table-column type="expand" width="1">
                    <template #default="{ row }">
                      <div v-if="row.expanded" class="bg-gray-50 p-4">
                        <div class="mb-2 font-medium">
                          {{ row.title }}
                        </div>
                        <div class="text-gray-600">
                          {{ row.content }}
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- Timeline View -->
              <div v-else class="p-6">
                <div v-for="(group, index) in groupedMessages" :key="index" class="mb-8">
                  <div class="mb-4 text-lg text-gray-700 font-medium">
                    {{ group.date }}
                  </div>
                  <div class="border-l-2 border-gray-200 pl-6 space-y-4">
                    <div
                      v-for="message in group.messages" :key="message.id"
                      class="cursor-pointer rounded-lg bg-gray-50 p-4 transition-shadow hover:shadow-sm"
                      @click="toggleStatus(message)"
                    >
                      <div class="flex items-start justify-between">
                        <div>
                          <div class="font-medium">
                            {{ message.title }}
                          </div>
                          <div class="mt-1 text-sm text-gray-500">
                            {{ message.content.substring(0, 60) }}...
                          </div>
                        </div>
                        <div class="flex items-center space-x-2">
                          <span
                            v-if="message.priority === 'urgent'"
                            class="rounded bg-red-100 px-2 py-1 text-xs text-red-500"
                          >紧急</span>
                          <span v-else class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-500">普通</span>
                          <span class="text-sm text-gray-400">{{ message.time }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Pagination -->
              <div class="flex items-center justify-between border-t p-4">
                <div class="text-sm text-gray-500">
                  共 {{ totalMessages }} 条消息
                </div>
                <el-pagination
                  v-model:current-page="currentPage" v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]" :total="totalMessages" layout="prev, pager, next, sizes"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  消息统计
                </div>
              </template>
              <div class="space-y-4">
                <div class="flex justify-between">
                  <span class="text-gray-500">总消息数</span>
                  <span class="font-medium">128 条</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">未读消息</span>
                  <span class="text-blue-500 font-medium">24 条</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">紧急消息</span>
                  <span class="text-red-500 font-medium">8 条</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  消息类型分布
                </div>
              </template>
              <div class="space-y-3">
                <div
                  v-for="item in recentNotifications" :key="item.id"
                  class="border-b border-gray-100 pb-3 last:border-0 last:pb-0"
                >
                  <div class="text-sm font-medium">
                    {{ item.title }}
                  </div>
                  <div class="mt-1 text-xs text-gray-400">
                    {{ item.time }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-table :deep(.el-table__row) {
    cursor: pointer;
  }

  .el-table :deep(.el-table__row:hover) {
    background-color: #f5f7fa;
  }

  .el-table :deep(.el-table__expanded-cell) {
    padding: 0;
  }
</style>
