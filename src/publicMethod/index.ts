import { ElMessage } from 'element-plus'

// # 图片上传目录集合：
// # adv（广告位模块）；
// # user（用户模块）：/user/merchant/（汽配商户），/user/member/（用户），/user/car/（汽修厂）
// # find（发现模块）；/find/trends/（动态），/find/memorabilia/（大事记），/find/business/（汽配商圈）
// # set（系统）；/set/vehiclemodel/（品牌），/set/carseries/（车系），/set/category/（类目），/set/agreement/（协议）

const pictureTypeList = [
  {
    id: 'picture',
    name: '图片',
    path: 'picture',
  },
  {
    id: 'member',
    name: '用户',
    path: 'user/member',
  },

]
// 公共方法
export default function tools() {
  // 图片回显 - ck
  function pictureEcho(url : any, type : any) {
    // console.log('url', url)
    // if (type && type.charAt(0) !== '/') {
    //   type = `-${type}`
    // }
    // else {
    //   type = '-ssw800'
    // }
    // 图片回显 - ck
    if (!url) {
      // console.error('资源路径不存在')
      // ElMessage.error('资源路径不存在')
      return null
    }
    const domainName = import.meta.env.VITE_APP_API_BASEURLIMG // 获取域名
    const array = [domainName, 'https://', 'http://']
    // 数组格式-Array
    if (Array.isArray(url)) {
      const newArr = url.map((i : string) => {
        // 不存在域名
        // console.log(domainName, i, type)
        if (!array.some(element => i.includes(element))) {
          if (i.charAt(0) !== '/') {
            i = `/${i}`
          }
          // i = domainName + i + type
          i = domainName + i
        }
        else {
          // i = i + type
          i = i
          // 存在域名
        }
        return i
      })
      // console.log('newArr', newArr)
      return newArr
    }
    else {
      // 普通格式-string
      const result = array.some(element => url.includes(element))
      if (result) {
        // 存在域名,暂不处理
        // return url + type
        return url
      }
      else {
        if (url.charAt(0) !== '/') {
          url = `/${url}`
        }
        // return domainName + url + type
        return domainName + url
      }
    }
  }
  // 根据id返回对应配置id项
  function pictureType(id : string) {
    const pictureHost = pictureTypeList.find((item : any) => item.id === id)
    if (!pictureHost) {
      console.error('未配置参数项')
      ElMessage.error('未配置参数项')
      return false
    }
    return pictureHost.path // 输出对应id的配置项
  }

  return {
    pictureEcho, // 图片回显
    pictureType, // 图片上传配置id项
  }
}
