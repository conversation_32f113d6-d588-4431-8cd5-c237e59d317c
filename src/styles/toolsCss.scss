// 公共css文件

// 盒子最外层的css
.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .page-header {
    margin-bottom: 0;
  }

  /* 让 page-main 的高度自适应 */
  .page-main {
    flex: 1;
    overflow-x: hidden;

    :deep(.main-container) {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: auto;
    }
  }

  ::v-deep(.flex-container) {
    padding-right: 40px !important;
  }

  // :deep(.flex-container) {

  // }
}
// :deep(.main-container) {
//   height:100%;
// }

/* 字体-粗细 */
.fw-normal {
  font-weight: normal;

  /* 普通 = 400 */
}

.fw-bold {
  font-weight: bold;

  /* 粗体 = 700 */
}

.fw-lighter {
  font-weight: lighter;

  /* 相对于父元素更细 */
}

.fw-bolder {
  font-weight: bolder;

  /* 相对于父元素更粗 */
}

/* flex布局 */
.flex {
  display: flex;
}

/* flex布局 */
.flex-1 {
  flex: 1;
}

.jcc {
  justify-content: center;
}

.jcsb {
  justify-content: space-between;
}

.jcse {
  justify-content: space-evenly;
}

.fcc {
  display: flex;
  align-items: center;
  justify-content: center;
}

.fdc {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.aic {
  align-items: center;
}

.aife {
  align-items: flex-end;
}

.aifs {
  align-items: flex-start;
}

.wsn {
  white-space: nowrap;
}

$customMinSize: 2; //最小
$customMaxSize: 100; // 最大

@for $i from $customMinSize through $customMaxSize {
  @if $i % 2 == 0 {
    .z-#{$i} {
      z-index: $i;
    }
    .m-#{$i} {
      margin: #{$i}px;
    }
    .mt-#{$i} {
      margin-top: #{$i}px;
    }
    .ml-#{$i} {
      margin-left: #{$i}px;
    }
    .mr-#{$i} {
      margin-right: #{$i}px;
    }
    .mb-#{$i} {
      margin-bottom: #{$i}px;
    }
    .p-#{$i} {
      padding: #{$i}px;
    }
    .pt-#{$i} {
      padding-top: #{$i}px;
    }
    .pb-#{$i} {
      padding-bottom: #{$i}px;
    }
    .pl-#{$i} {
      padding-left: #{$i}px;
    }
    .pr-#{$i} {
      padding-right: #{$i}px;
    }
    .f-#{$i} {
      font-size: #{$i}px;
    }
  }
}
