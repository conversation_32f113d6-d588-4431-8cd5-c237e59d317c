# 构建阶段
FROM node:20-alpine AS builder

WORKDIR /app

# 设置 Node.js 内存限制和其他环境变量
ENV NODE_OPTIONS="--max-old-space-size=8192"
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# 安装 pnpm
RUN corepack enable

# 复制依赖文件
COPY package.json pnpm-lock.yaml* ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建项目
RUN pnpm build

# 生产阶段
FROM node:20-alpine

WORKDIR /app

# 安装 http-server
RUN npm install -g http-server

# 复制构建产物
COPY --from=builder /app/dist /app/dist

EXPOSE 8780

# 启动 http-server
CMD ["http-server", "dist", "-p", "8780"]