// vite.config.ts
import fs2 from "node:fs";
import path3 from "node:path";
import process2 from "node:process";
import { defineConfig, loadEnv } from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.14_sass@1.87.0_terser@5.39.0/node_modules/vite/dist/node/index.js";
import dayjs2 from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js";

// package.json
var package_default = {
  type: "module",
  version: "4.7.0",
  engines: {
    node: "^18.0.0 || ^20.0.0"
  },
  scripts: {
    dev: "vite",
    build: "vite build",
    "build:test": "vite build --mode test",
    serve: "http-server ./dist -o",
    "serve:test": "http-server ./dist-test -o",
    svgo: "svgo -f src/assets/icons",
    new: "plop",
    "generate:icons": "esno ./scripts/generate.icons.ts",
    lint: "npm-run-all -s lint:tsc lint:eslint lint:stylelint",
    "lint:tsc": "vue-tsc",
    "lint:eslint": "eslint . --cache --fix",
    "lint:stylelint": 'stylelint "src/**/*.{css,scss,vue}" --cache --fix',
    postinstall: "simple-git-hooks",
    preinstall: "npx only-allow pnpm",
    commit: "git cz",
    release: "bumpp"
  },
  dependencies: {
    "@antv/g2plot": "^2.4.31",
    "@headlessui/vue": "^1.7.19",
    "@imengyu/vue3-context-menu": "^1.3.9",
    "@qiniu/wechat-miniprogram-upload": "^1.0.3",
    "@tinymce/tinymce-vue": "^5.1.1",
    "@vueuse/core": "^10.9.0",
    "@vueuse/integrations": "^10.9.0",
    "@wangeditor/editor": "^5.1.23",
    axios: "^1.6.8",
    "bignumber.js": "^9.1.2",
    dayjs: "^1.11.10",
    "decimal.js": "^10.4.3",
    defu: "^6.1.4",
    echarts: "^5.5.0",
    "element-plus": "^2.7.0",
    eruda: "^3.0.1",
    "floating-vue": "5.2.2",
    "hotkeys-js": "^3.13.7",
    html2canvas: "^1.4.1",
    "js-pinyin": "^0.2.5",
    "lodash-es": "^4.17.21",
    "medium-zoom": "^1.1.0",
    mitt: "^3.0.1",
    mockjs: "^1.1.0",
    moment: "^2.30.1",
    nprogress: "^0.2.0",
    overlayscrollbars: "^2.6.1",
    "overlayscrollbars-vue": "^0.5.8",
    "path-browserify": "^1.0.1",
    "path-to-regexp": "^6.2.1",
    pinia: "^2.1.7",
    "pinyin-pro": "^3.19.6",
    pnpm: "^10.8.1",
    "qiniu-js": "4.0.0-beta.4",
    qs: "^6.12.0",
    scule: "^1.3.0",
    sortablejs: "^1.15.2",
    spinkit: "^2.0.1",
    "timeago.js": "^4.0.2",
    tinymce: "^7.0.1",
    "v-wave": "^2.0.0",
    vconsole: "^3.15.1",
    vue: "^3.4.21",
    "vue-clipboard3": "^2.0.0",
    "vue-i18n": "^9.10.2",
    "vue-m-message": "^4.0.2",
    "vue-router": "^4.3.0",
    "vue-ueditor-wrap": "^3.0.8",
    vuedraggable: "^4.1.0"
  },
  devDependencies: {
    "@antfu/eslint-config": "2.11.6",
    "@iconify/json": "^2.2.196",
    "@iconify/vue": "^4.1.1",
    "@intlify/unplugin-vue-i18n": "^4.0.0",
    "@stylistic/stylelint-config": "^1.0.1",
    "@types/lodash-es": "^4.17.12",
    "@types/mockjs": "^1.0.10",
    "@types/nprogress": "^0.2.3",
    "@types/path-browserify": "^1.0.2",
    "@types/qs": "^6.9.14",
    "@types/sortablejs": "^1.15.8",
    "@unocss/eslint-plugin": "^0.58.8",
    "@vitejs/plugin-legacy": "^5.3.2",
    "@vitejs/plugin-vue": "^5.0.4",
    "@vitejs/plugin-vue-jsx": "^3.1.0",
    archiver: "^7.0.1",
    autoprefixer: "^10.4.19",
    boxen: "^7.1.1",
    bumpp: "^9.4.0",
    "cz-git": "^1.9.1",
    eslint: "^8.57.0",
    esno: "^4.7.0",
    "fs-extra": "^11.2.0",
    "http-server": "^14.1.1",
    inquirer: "^9.2.17",
    "lint-staged": "^15.2.2",
    "npm-run-all": "^4.1.5",
    picocolors: "^1.0.0",
    plop: "^4.0.1",
    sass: "^1.72.0",
    "simple-git-hooks": "^2.11.1",
    stylelint: "^16.3.1",
    "stylelint-config-recess-order": "^5.0.0",
    "stylelint-config-standard-scss": "^13.0.0",
    "stylelint-config-standard-vue": "^1.0.0",
    "stylelint-scss": "^6.2.1",
    svgo: "^3.2.0",
    terser: "^5.30.0",
    typescript: "^5.4.3",
    unocss: "^0.58.8",
    "unplugin-auto-import": "^0.17.5",
    "unplugin-turbo-console": "^1.5.1",
    "unplugin-vue-components": "^0.26.0",
    vite: "^5.2.7",
    "vite-plugin-banner": "^0.7.1",
    "vite-plugin-compression2": "^1.0.0",
    "vite-plugin-fake-server": "^2.1.1",
    "vite-plugin-pages": "^0.32.1",
    "vite-plugin-svg-icons": "^2.0.1",
    "vite-plugin-vue-devtools": "^7.0.25",
    "vite-plugin-vue-meta-layouts": "^0.4.2",
    "vue-tsc": "^2.0.7"
  },
  "simple-git-hooks": {
    "pre-commit": "pnpm lint-staged",
    preserveUnused: true
  },
  config: {
    commitizen: {
      path: "node_modules/cz-git"
    }
  }
};

// vite/plugins/index.ts
import vue from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/@vitejs+plugin-vue@5.2.3_vi_5960c829049eace3eb509c9d22a86fca/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/@vitejs+plugin-vue-jsx@3.1._fd1fc3bd4b6c69f570502946e18d5e0c/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import vueLegacy from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/@vitejs+plugin-legacy@5.4.3_aa2f7e9daefa38009b7721b5153d982f/node_modules/@vitejs/plugin-legacy/dist/index.mjs";

// vite/plugins/app-info.ts
import boxen from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/boxen@7.1.1/node_modules/boxen/index.js";
import picocolors from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/picocolors@1.1.1/node_modules/picocolors/picocolors.js";
function appInfo() {
  return {
    name: "appInfo",
    apply: "serve",
    async buildStart() {
      const { bold, green, magenta, bgGreen, underline } = picocolors;
      console.log(
        boxen(
          `${bold(green(`\u7531 ${bgGreen("Fantastic-admin")} \u9A71\u52A8`))}

${underline("https://fantastic-admin.gitee.io")}

\u5F53\u524D\u4F7F\u7528\uFF1A${magenta("\u4E13\u4E1A\u7248")}`,
          {
            padding: 1,
            margin: 1,
            borderStyle: "double",
            textAlignment: "center"
          }
        )
      );
    }
  };
}

// vite/plugins/devtools.ts
import VueDevTools from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-vue-devtools@7._f3a6fedceb8655d368e323ca6e0ad2d8/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
function createDevtools(env) {
  const { VITE_OPEN_DEVTOOLS } = env;
  return VITE_OPEN_DEVTOOLS === "true" && VueDevTools();
}

// vite/plugins/auto-import.ts
import autoImport from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/unplugin-auto-import@0.17.8_548a2333045b7a5305e1651499426e3d/node_modules/unplugin-auto-import/dist/vite.js";
function createAutoImport() {
  return autoImport({
    imports: [
      "vue",
      "vue-router",
      "pinia"
    ],
    dts: "./src/types/auto-imports.d.ts",
    dirs: [
      "./src/utils/composables/**"
    ]
  });
}

// vite/plugins/components.ts
import components from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/unplugin-vue-components@0.2_9780cee84be776be6aa399e4bcdeefca/node_modules/unplugin-vue-components/dist/vite.js";
function createComponents() {
  return components({
    dirs: [
      "src/components",
      "src/layouts/ui-kit"
    ],
    include: [/\.vue$/, /\.vue\?vue/, /\.tsx$/],
    dts: "./src/types/components.d.ts"
  });
}

// vite/plugins/unocss.ts
import Unocss from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/unocss@0.58.9_postcss@5.2.1_026d44bff71cf55c59c571dfee0862e8/node_modules/unocss/dist/vite.mjs";
function createUnocss() {
  return Unocss();
}

// vite/plugins/svg-icon.ts
import path from "node:path";
import process from "node:process";
import { createSvgIconsPlugin } from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_5721baa32c4bdf4404ff5a34c4a76c58/node_modules/vite-plugin-svg-icons/dist/index.mjs";
function createSvgIcon(isBuild) {
  return createSvgIconsPlugin({
    iconDirs: [path.resolve(process.cwd(), "src/assets/icons/")],
    symbolId: "icon-[dir]-[name]",
    svgoOptions: isBuild
  });
}

// vite/plugins/i18n.ts
import path2 from "node:path";
import vueI18n from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/@intlify+unplugin-vue-i18n@_5be3ae1591f407448b2c562a32ad7eea/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";
var __vite_injected_original_dirname = "D:\\newmaobobo\\mbb-system-admin\\whiskerguard-ui-system-admin\\vite\\plugins";
function createI18n() {
  return vueI18n({
    include: path2.resolve(__vite_injected_original_dirname, "../../src/locales/lang/**")
  });
}

// vite/plugins/mock.ts
import { vitePluginFakeServer } from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-fake-server@2.2.0/node_modules/vite-plugin-fake-server/dist/index.mjs";
function createMock(env, isBuild) {
  const { VITE_BUILD_MOCK } = env;
  return vitePluginFakeServer({
    logger: !isBuild,
    include: "src/mock",
    infixName: false,
    enableProd: isBuild && VITE_BUILD_MOCK === "true"
  });
}

// vite/plugins/layouts.ts
import Layouts from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-vue-meta-layout_744cef15d0cbd2870a95ddf5a1473bdb/node_modules/vite-plugin-vue-meta-layouts/dist/index.mjs";
function createLayouts() {
  return Layouts({
    defaultLayout: "index"
  });
}

// vite/plugins/pages.ts
import Pages from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-pages@0.32.5_@v_0170eb3df285a8dd9032f83c398d6032/node_modules/vite-plugin-pages/dist/index.js";
function createPages() {
  return Pages({
    dirs: "src/views",
    exclude: [
      "**/components/**/*.vue"
    ]
  });
}

// vite/plugins/compression.ts
import { compression } from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-compression2@1._23ab23b042bc4efc54d7303fd518934d/node_modules/vite-plugin-compression2/dist/index.mjs";
function createCompression(env, isBuild) {
  const plugin = [];
  if (isBuild) {
    const { VITE_BUILD_COMPRESS } = env;
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression()
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          exclude: [/\.(br)$/, /\.(gz)$/],
          algorithm: "brotliCompress"
        })
      );
    }
  }
  return plugin;
}

// vite/plugins/archiver.ts
import fs from "node:fs";
import dayjs from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js";
import archiver from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/archiver@7.0.1/node_modules/archiver/index.js";
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function createArchiver(env) {
  const { VITE_BUILD_ARCHIVE } = env;
  let outDir;
  return {
    name: "vite-plugin-archiver",
    apply: "build",
    configResolved(resolvedConfig) {
      outDir = resolvedConfig.build.outDir;
    },
    async closeBundle() {
      if (["zip", "tar"].includes(VITE_BUILD_ARCHIVE)) {
        await sleep(1e3);
        const archive = archiver(VITE_BUILD_ARCHIVE, {
          ...VITE_BUILD_ARCHIVE === "zip" && { zlib: { level: 9 } },
          ...VITE_BUILD_ARCHIVE === "tar" && { gzip: true, gzipOptions: { level: 9 } }
        });
        const output = fs.createWriteStream(`${outDir}.${dayjs().format("YYYY-MM-DD-HH-mm-ss")}.${VITE_BUILD_ARCHIVE === "zip" ? "zip" : "tar.gz"}`);
        archive.pipe(output);
        archive.directory(outDir, false);
        archive.finalize();
      }
    }
  };
}

// vite/plugins/banner.ts
import banner from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-banner@0.7.1/node_modules/vite-plugin-banner/dist/index.mjs";
function createBanner() {
  return banner(`
/**
 * \u7531 Fantastic-admin \u63D0\u4F9B\u6280\u672F\u652F\u6301
 * Powered by Fantastic-admin
 * Gitee  https://fantastic-admin.gitee.io
 * Github https://fantastic-admin.github.io
 */
`);
}

// vite/plugins/index.ts
function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [
    appInfo(),
    vue(),
    vueJsx(),
    vueLegacy({
      renderLegacyChunks: false,
      modernPolyfills: [
        "es.array.at",
        "es.array.find-last"
      ]
    })
  ];
  vitePlugins.push(createDevtools(viteEnv));
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createComponents());
  vitePlugins.push(createUnocss());
  vitePlugins.push(createSvgIcon(isBuild));
  vitePlugins.push(createI18n());
  vitePlugins.push(createMock(viteEnv, isBuild));
  vitePlugins.push(createLayouts());
  vitePlugins.push(createPages());
  vitePlugins.push(...createCompression(viteEnv, isBuild));
  vitePlugins.push(createArchiver(viteEnv));
  vitePlugins.push(createBanner());
  return vitePlugins;
}

// vite.config.ts
var __vite_injected_original_dirname2 = "D:\\newmaobobo\\mbb-system-admin\\whiskerguard-ui-system-admin";
var vite_config_default = async ({ mode, command }) => {
  const env = loadEnv(mode, process2.cwd());
  const scssResources = [];
  fs2.readdirSync("src/assets/styles/resources").forEach((dirname) => {
    if (fs2.statSync(`src/assets/styles/resources/${dirname}`).isFile()) {
      scssResources.push(`@use "src/assets/styles/resources/${dirname}" as *;`);
    }
  });
  return defineConfig({
    base: "./",
    // 开发服务器选项 https://cn.vitejs.dev/config/#server-options
    server: {
      open: true,
      port: 9e3,
      proxy: {
        "/proxy": {
          target: env.VITE_APP_API_BASEURL,
          // changeOrigin: command === 'serve' && env.VITE_OPEN_PROXY === 'true',
          changeOrigin: true,
          // secure: false,
          rewrite: (path4) => path4.replace(/\/proxy/, "")
        }
      }
    },
    // 构建选项 https://cn.vitejs.dev/config/#server-fsserve-root
    build: {
      outDir: mode === "production" ? "dist" : `dist-${mode}`,
      sourcemap: env.VITE_BUILD_SOURCEMAP === "true"
    },
    define: {
      __SYSTEM_INFO__: JSON.stringify({
        pkg: {
          version: package_default.version,
          dependencies: package_default.dependencies,
          devDependencies: package_default.devDependencies
        },
        lastBuildTime: dayjs2().format("YYYY-MM-DD HH:mm:ss")
      })
    },
    plugins: createVitePlugins(env, command === "build"),
    resolve: {
      alias: {
        "@": path3.resolve(__vite_injected_original_dirname2, "src"),
        "#": path3.resolve(__vite_injected_original_dirname2, "src/types")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: scssResources.join(""),
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
